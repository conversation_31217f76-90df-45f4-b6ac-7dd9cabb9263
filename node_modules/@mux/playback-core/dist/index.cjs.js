"use strict";var Lt=Object.create;var B=Object.defineProperty;var Nt=Object.getOwnPropertyDescriptor;var At=Object.getOwnPropertyNames;var It=Object.getPrototypeOf,St=Object.prototype.hasOwnProperty;var wt=(e,t)=>{for(var r in t)B(e,r,{get:t[r],enumerable:!0})},Pe=(e,t,r,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of At(t))!St.call(e,n)&&n!==r&&B(e,n,{get:()=>t[n],enumerable:!(o=Nt(t,n))||o.enumerable});return e};var _e=(e,t,r)=>(r=e!=null?Lt(It(e)):{},Pe(t||!e||!e.__esModule?B(r,"default",{value:e,enumerable:!0}):r,e)),Ot=e=>Pe(B({},"__esModule",{value:!0}),e);var lr={};wt(lr,{AutoplayTypes:()=>H,CmcdTypeValues:()=>Ht,CmcdTypes:()=>S,ExtensionMimeTypeMap:()=>N,Hls:()=>g,MaxResolution:()=>Wt,MediaError:()=>f,MimeTypeShorthandMap:()=>V,MinResolution:()=>Yt,MuxErrorCategory:()=>C,MuxErrorCode:()=>D,MuxJWTAud:()=>Q,PlaybackTypes:()=>j,RenditionOrder:()=>Ft,StreamTypes:()=>_,addChapters:()=>fe,addCuePoints:()=>ue,addTextTrack:()=>z,allMediaTypes:()=>Kt,errorCategoryToTokenNameOrPrefix:()=>U,fetchAndDispatchMuxMetadata:()=>te,generatePlayerInitTime:()=>rr,generateUUID:()=>ft,getActiveChapter:()=>Te,getActiveCuePoint:()=>de,getAppCertificate:()=>bt,getChapters:()=>Be,getCuePoints:()=>Fe,getCurrentPdt:()=>Je,getDRMConfig:()=>Dt,getEnded:()=>Et,getError:()=>Tt,getLicenseKey:()=>Ct,getLiveEdgeStart:()=>ir,getMediaPlaylistFromMultivariantPlaylist:()=>ot,getMetadata:()=>ar,getMultivariantPlaylistSessionData:()=>at,getSeekable:()=>xe,getStartDate:()=>je,getStreamInfoFromHlsjsLevelDetails:()=>dt,getStreamInfoFromPlaylist:()=>it,getStreamInfoFromSrcAndType:()=>ct,getStreamType:()=>Ee,getStreamTypeConfig:()=>Rt,getTargetLiveWindow:()=>sr,getTextTrack:()=>w,i18n:()=>M,initialize:()=>ur,isKeyOf:()=>O,isMuxVideoSrc:()=>Ce,isPseudoEnded:()=>mt,isStuckOnLastFragment:()=>De,loadMedia:()=>_t,mux:()=>$.default,muxMediaState:()=>P,parseJwt:()=>q,parseTagAttributes:()=>st,removeTextTrack:()=>Ke,setupChapters:()=>ye,setupCuePoints:()=>le,setupHls:()=>xt,setupMux:()=>Pt,setupNativeFairplayDRM:()=>vt,shorthandKeys:()=>Vt,teardown:()=>gt,toAppCertURL:()=>be,toDRMTypeFromKeySystem:()=>nt,toLicenseKeyURL:()=>F,toMuxVideoURL:()=>nr,toPlaybackIdFromSrc:()=>Me,toPlaybackIdParts:()=>K,updateStreamInfoFromHlsjsLevelDetails:()=>lt,updateStreamInfoFromSrc:()=>ut});module.exports=Ot(lr);var $=_e(require("mux-embed"));var ke=_e(require("hls.js")),g=ke.default;var C={VIDEO:"video",THUMBNAIL:"thumbnail",STORYBOARD:"storyboard",DRM:"drm"},D={NOT_AN_ERROR:0,NETWORK_OFFLINE:2000002,NETWORK_UNKNOWN_ERROR:2e6,NETWORK_NO_STATUS:2000001,NETWORK_INVALID_URL:24e5,NETWORK_NOT_FOUND:2404e3,NETWORK_NOT_READY:2412e3,NETWORK_GENERIC_SERVER_FAIL:25e5,NETWORK_TOKEN_MISSING:2403201,NETWORK_TOKEN_MALFORMED:2412202,NETWORK_TOKEN_EXPIRED:2403210,NETWORK_TOKEN_AUD_MISSING:2403221,NETWORK_TOKEN_AUD_MISMATCH:2403222,NETWORK_TOKEN_SUB_MISMATCH:2403232,ENCRYPTED_ERROR:5e6,ENCRYPTED_UNSUPPORTED_KEY_SYSTEM:5000001,ENCRYPTED_GENERATE_REQUEST_FAILED:5000002,ENCRYPTED_UPDATE_LICENSE_FAILED:5000003,ENCRYPTED_UPDATE_SERVER_CERT_FAILED:5000004,ENCRYPTED_CDM_ERROR:5000005,ENCRYPTED_OUTPUT_RESTRICTED:5000006,ENCRYPTED_MISSING_TOKEN:5000002},U=e=>e===C.VIDEO?"playback":e,L=class L extends Error{constructor(t,r=L.MEDIA_ERR_CUSTOM,o,n){var a;super(t),this.name="MediaError",this.code=r,this.context=n,this.fatal=o!=null?o:r>=L.MEDIA_ERR_NETWORK&&r<=L.MEDIA_ERR_ENCRYPTED,this.message||(this.message=(a=L.defaultMessages[this.code])!=null?a:"")}};L.MEDIA_ERR_ABORTED=1,L.MEDIA_ERR_NETWORK=2,L.MEDIA_ERR_DECODE=3,L.MEDIA_ERR_SRC_NOT_SUPPORTED=4,L.MEDIA_ERR_ENCRYPTED=5,L.MEDIA_ERR_CUSTOM=100,L.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail.",3:"A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format.",4:"An unsupported error occurred. The server or network failed, or your browser does not support this format.",5:"The media is encrypted and there are no keys to decrypt it."};var f=L;var Ut=e=>e==null,O=(e,t)=>Ut(t)?!1:e in t,H={ANY:"any",MUTED:"muted"},_={ON_DEMAND:"on-demand",LIVE:"live",UNKNOWN:"unknown"},j={MSE:"mse",NATIVE:"native"},S={HEADER:"header",QUERY:"query",NONE:"none"},Ht=Object.values(S),N={M3U8:"application/vnd.apple.mpegurl",MP4:"video/mp4"},V={HLS:N.M3U8},Vt=Object.keys(V),Kt=[...Object.values(N),"hls","HLS"],Wt={upTo720p:"720p",upTo1080p:"1080p",upTo1440p:"1440p",upTo2160p:"2160p"},Yt={noLessThan480p:"480p",noLessThan540p:"540p",noLessThan720p:"720p",noLessThan1080p:"1080p",noLessThan1440p:"1440p",noLessThan2160p:"2160p"},Ft={DESCENDING:"desc"};var $t="en",J={code:$t};var v=(e,t,r,o,n=e)=>{n.addEventListener(t,r,o),e.addEventListener("teardown",()=>{n.removeEventListener(t,r)},{once:!0})};function he(e,t,r){t&&r>t&&(r=t);for(let o=0;o<e.length;o++)if(e.start(o)<=r&&e.end(o)>=r)return!0;return!1}var K=e=>{let t=e.indexOf("?");if(t<0)return[e];let r=e.slice(0,t),o=e.slice(t);return[r,o]},W=e=>{let{type:t}=e;if(t){let r=t.toUpperCase();return O(r,V)?V[r]:t}return Bt(e)},oe=e=>e==="VOD"?_.ON_DEMAND:_.LIVE,ae=e=>e==="EVENT"?Number.POSITIVE_INFINITY:e==="VOD"?Number.NaN:0,Bt=e=>{let{src:t}=e;if(!t)return"";let r="";try{r=new URL(t).pathname}catch{console.error("invalid url")}let o=r.lastIndexOf(".");if(o<0)return Jt(e)?N.M3U8:"";let a=r.slice(o+1).toUpperCase();return O(a,N)?N[a]:""},jt="mux.com",Jt=({src:e,customDomain:t=jt})=>{let r;try{r=new URL(`${e}`)}catch{return!1}let o=r.protocol==="https:",n=r.hostname===`stream.${t}`.toLowerCase(),a=r.pathname.split("/"),i=a.length===2,c=!(a!=null&&a[1].includes("."));return o&&n&&i&&c},q=e=>{let t=(e!=null?e:"").split(".")[1];if(t)try{let r=t.replace(/-/g,"+").replace(/_/g,"/"),o=decodeURIComponent(atob(r).split("").map(function(n){return"%"+("00"+n.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(o)}catch{return}},Le=({exp:e},t=Date.now())=>!e||e*1e3<t,Ne=({sub:e},t)=>e!==t,Ae=({aud:e},t)=>!e,Ie=({aud:e},t)=>e!==t,Se="en";function M(e,t=!0){var n,a;let r=t&&(a=(n=J)==null?void 0:n[e])!=null?a:e,o=t?J.code:Se;return new ne(r,o)}var ne=class{constructor(t,r=(o=>(o=J)!=null?o:Se)()){this.message=t,this.locale=r}format(t){return this.message.replace(/\{(\w+)\}/g,(r,o)=>{var n;return(n=t[o])!=null?n:""})}toString(){return this.message}};var qt=Object.values(H),we=e=>typeof e=="boolean"||typeof e=="string"&&qt.includes(e),Oe=(e,t,r)=>{let{autoplay:o}=e,n=!1,a=!1,i=we(o)?o:!!o,c=()=>{n||v(t,"playing",()=>{n=!0},{once:!0})};if(c(),v(t,"loadstart",()=>{n=!1,c(),se(t,i)},{once:!0}),v(t,"loadstart",()=>{r||(e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=!Number.isFinite(t.duration)),se(t,i)},{once:!0}),r&&r.once(g.Events.LEVEL_LOADED,(u,s)=>{var p;e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=(p=s.details.live)!=null?p:!1}),!i){let u=()=>{!a||Number.isFinite(e.startTime)||(r!=null&&r.liveSyncPosition?t.currentTime=r.liveSyncPosition:Number.isFinite(t.seekable.end(0))&&(t.currentTime=t.seekable.end(0)))};r&&v(t,"play",()=>{t.preload==="metadata"?r.once(g.Events.LEVEL_UPDATED,u):u()},{once:!0})}return u=>{n||(i=we(u)?u:!!u,se(t,i))}},se=(e,t)=>{if(!t)return;let r=e.muted,o=()=>e.muted=r;switch(t){case H.ANY:e.play().catch(()=>{e.muted=!0,e.play().catch(o)});break;case H.MUTED:e.muted=!0,e.play().catch(o);break;default:e.play().catch(()=>{});break}};var Ue=({preload:e,src:t},r,o)=>{let n=p=>{p!=null&&["","none","metadata","auto"].includes(p)?r.setAttribute("preload",p):r.removeAttribute("preload")};if(!o)return n(e),n;let a=!1,i=!1,c=o.config.maxBufferLength,d=o.config.maxBufferSize,u=p=>{n(p);let l=p!=null?p:r.preload;i||l==="none"||(l==="metadata"?(o.config.maxBufferLength=1,o.config.maxBufferSize=1):(o.config.maxBufferLength=c,o.config.maxBufferSize=d),s())},s=()=>{!a&&t&&(a=!0,o.loadSource(t))};return v(r,"play",()=>{i=!0,o.config.maxBufferLength=c,o.config.maxBufferSize=d,s()},{once:!0}),u(e),u};function He(e,t){var c;if(!("videoTracks"in e))return;let r=new WeakMap;t.on(g.Events.MANIFEST_PARSED,function(d,u){i();let s=e.addVideoTrack("main");s.selected=!0;for(let[p,l]of u.levels.entries()){let T=s.addRendition(l.url[0],l.width,l.height,l.videoCodec,l.bitrate);r.set(l,`${p}`),T.id=`${p}`}}),t.on(g.Events.AUDIO_TRACKS_UPDATED,function(d,u){a();for(let s of u.audioTracks){let p=s.default?"main":"alternative",l=e.addAudioTrack(p,s.name,s.lang);l.id=`${s.id}`,s.default&&(l.enabled=!0)}}),e.audioTracks.addEventListener("change",()=>{var s;let d=+((s=[...e.audioTracks].find(p=>p.enabled))==null?void 0:s.id),u=t.audioTracks.map(p=>p.id);d!=t.audioTrack&&u.includes(d)&&(t.audioTrack=d)}),t.on(g.Events.LEVELS_UPDATED,function(d,u){var l;let s=e.videoTracks[(l=e.videoTracks.selectedIndex)!=null?l:0];if(!s)return;let p=u.levels.map(T=>r.get(T));for(let T of e.videoRenditions)T.id&&!p.includes(T.id)&&s.removeRendition(T)});let o=d=>{let u=d.target.selectedIndex;u!=t.nextLevel&&(t.nextLevel=u)};(c=e.videoRenditions)==null||c.addEventListener("change",o);let n=()=>{for(let d of e.videoTracks)e.removeVideoTrack(d)},a=()=>{for(let d of e.audioTracks)e.removeAudioTrack(d)},i=()=>{n(),a()};t.once(g.Events.DESTROYING,i)}var ie=e=>"time"in e?e.time:e.startTime;function Ve(e,t){t.on(g.Events.NON_NATIVE_TEXT_TRACKS_FOUND,(n,{tracks:a})=>{a.forEach(i=>{var s,p;let c=(s=i.subtitleTrack)!=null?s:i.closedCaptions,d=t.subtitleTracks.findIndex(({lang:l,name:T,type:m})=>l==(c==null?void 0:c.lang)&&T===i.label&&m.toLowerCase()===i.kind),u=((p=i._id)!=null?p:i.default)?"default":`${i.kind}${d}`;z(e,i.kind,i.label,c==null?void 0:c.lang,u,i.default)})});let r=()=>{if(!t.subtitleTracks.length)return;let n=Array.from(e.textTracks).find(c=>c.id&&c.mode==="showing"&&["subtitles","captions"].includes(c.kind));if(!n)return;let a=t.subtitleTracks[t.subtitleTrack],i=a?a.default?"default":`${t.subtitleTracks[t.subtitleTrack].type.toLowerCase()}${t.subtitleTrack}`:void 0;if(t.subtitleTrack<0||(n==null?void 0:n.id)!==i){let c=t.subtitleTracks.findIndex(({lang:d,name:u,type:s,default:p})=>n.id==="default"&&p||d==n.language&&u===n.label&&s.toLowerCase()===n.kind);t.subtitleTrack=c}(n==null?void 0:n.id)===i&&n.cues&&Array.from(n.cues).forEach(c=>{n.addCue(c)})};e.textTracks.addEventListener("change",r),t.on(g.Events.CUES_PARSED,(n,{track:a,cues:i})=>{let c=e.textTracks.getTrackById(a);if(!c)return;let d=c.mode==="disabled";d&&(c.mode="hidden"),i.forEach(u=>{var s;(s=c.cues)!=null&&s.getCueById(u.id)||c.addCue(u)}),d&&(c.mode="disabled")}),t.once(g.Events.DESTROYING,()=>{e.textTracks.removeEventListener("change",r),e.querySelectorAll("track[data-removeondestroy]").forEach(a=>{a.remove()})});let o=()=>{Array.from(e.textTracks).forEach(n=>{var a,i;if(!["subtitles","caption"].includes(n.kind)&&(n.label==="thumbnails"||n.kind==="chapters")){if(!((a=n.cues)!=null&&a.length)){let c="track";n.kind&&(c+=`[kind="${n.kind}"]`),n.label&&(c+=`[label="${n.label}"]`);let d=e.querySelector(c),u=(i=d==null?void 0:d.getAttribute("src"))!=null?i:"";d==null||d.removeAttribute("src"),setTimeout(()=>{d==null||d.setAttribute("src",u)},0)}n.mode!=="hidden"&&(n.mode="hidden")}})};t.once(g.Events.MANIFEST_LOADED,o),t.once(g.Events.MEDIA_ATTACHED,o)}function z(e,t,r,o,n,a){let i=document.createElement("track");return i.kind=t,i.label=r,o&&(i.srclang=o),n&&(i.id=n),a&&(i.default=!0),i.track.mode=["subtitles","captions"].includes(t)?"disabled":"hidden",i.setAttribute("data-removeondestroy",""),e.append(i),i.track}function Ke(e,t){let r=Array.prototype.find.call(e.querySelectorAll("track"),o=>o.track===t);r==null||r.remove()}function w(e,t,r){var o;return(o=Array.from(e.querySelectorAll("track")).find(n=>n.track.label===t&&n.track.kind===r))==null?void 0:o.track}async function We(e,t,r,o){let n=w(e,r,o);return n||(n=z(e,o,r),n.mode="hidden",await new Promise(a=>setTimeout(()=>a(void 0),0))),n.mode!=="hidden"&&(n.mode="hidden"),[...t].sort((a,i)=>ie(i)-ie(a)).forEach(a=>{var d,u;let i=a.value,c=ie(a);if("endTime"in a&&a.endTime!=null)n==null||n.addCue(new VTTCue(c,a.endTime,o==="chapters"?i:JSON.stringify(i!=null?i:null)));else{let s=Array.prototype.findIndex.call(n==null?void 0:n.cues,m=>m.startTime>=c),p=(d=n==null?void 0:n.cues)==null?void 0:d[s],l=p?p.startTime:Number.isFinite(e.duration)?e.duration:Number.MAX_SAFE_INTEGER,T=(u=n==null?void 0:n.cues)==null?void 0:u[s-1];T&&(T.endTime=c),n==null||n.addCue(new VTTCue(c,l,o==="chapters"?i:JSON.stringify(i!=null?i:null)))}}),e.textTracks.dispatchEvent(new Event("change",{bubbles:!0,composed:!0})),n}var ce="cuepoints",Ye=Object.freeze({label:ce});async function ue(e,t,r=Ye){return We(e,t,r.label,"metadata")}var G=e=>({time:e.startTime,value:JSON.parse(e.text)});function Fe(e,t={label:ce}){let r=w(e,t.label,"metadata");return r!=null&&r.cues?Array.from(r.cues,o=>G(o)):[]}function de(e,t={label:ce}){var a,i;let r=w(e,t.label,"metadata");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return G(r.activeCues[0]);let{currentTime:o}=e,n=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=o&&d>o);return G(n||r.activeCues[0])}async function le(e,t=Ye){return new Promise(r=>{v(e,"loadstart",async()=>{let o=await ue(e,[],t);v(e,"cuechange",()=>{let n=de(e);if(n){let a=new CustomEvent("cuepointchange",{composed:!0,bubbles:!0,detail:n});e.dispatchEvent(a)}},{},o),r(o)})})}var pe="chapters",$e=Object.freeze({label:pe}),X=e=>({startTime:e.startTime,endTime:e.endTime,value:e.text});async function fe(e,t,r=$e){return We(e,t,r.label,"chapters")}function Be(e,t={label:pe}){var o;let r=w(e,t.label,"chapters");return(o=r==null?void 0:r.cues)!=null&&o.length?Array.from(r.cues,n=>X(n)):[]}function Te(e,t={label:pe}){var a,i;let r=w(e,t.label,"chapters");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return X(r.activeCues[0]);let{currentTime:o}=e,n=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=o&&d>o);return X(n||r.activeCues[0])}async function ye(e,t=$e){return new Promise(r=>{v(e,"loadstart",async()=>{let o=await fe(e,[],t);v(e,"cuechange",()=>{let n=Te(e);if(n){let a=new CustomEvent("chapterchange",{composed:!0,bubbles:!0,detail:n});e.dispatchEvent(a)}},{},o),r(o)})})}function je(e,t){if(t){let r=t.playingDate;if(r!=null)return new Date(r.getTime()-e.currentTime*1e3)}return typeof e.getStartDate=="function"?e.getStartDate():new Date(NaN)}function Je(e,t){if(t&&t.playingDate)return t.playingDate;if(typeof e.getStartDate=="function"){let r=e.getStartDate();return new Date(r.getTime()+e.currentTime*1e3)}return new Date(NaN)}var Q={VIDEO:"v",THUMBNAIL:"t",STORYBOARD:"s",DRM:"d"},Gt=e=>{if(e===C.VIDEO)return Q.VIDEO;if(e===C.DRM)return Q.DRM},Xt=(e,t)=>{var n,a;let r=U(e),o=`${r}Token`;return(n=t.tokens)!=null&&n[r]?(a=t.tokens)==null?void 0:a[r]:O(o,t)?t[o]:void 0},Y=(e,t,r,o,n=!1,a=!(i=>(i=globalThis.navigator)==null?void 0:i.onLine)())=>{var x,h;if(a){let E=M("Your device appears to be offline",n),b=void 0,y=f.MEDIA_ERR_NETWORK,k=new f(E,y,!1,b);return k.errorCategory=t,k.muxCode=D.NETWORK_OFFLINE,k.data=e,k}let c="status"in e?e.status:e.code,d=Date.now(),u=f.MEDIA_ERR_NETWORK;if(c===200)return;let s=U(t),p=Xt(t,r),l=Gt(t),[T]=K((x=r.playbackId)!=null?x:"");if(!c||!T)return;let m=q(p);if(p&&!m){let E=M("The {tokenNamePrefix}-token provided is invalid or malformed.",n).format({tokenNamePrefix:s}),b=M("Compact JWT string: {token}",n).format({token:p}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MALFORMED,y.data=e,y}if(c>=500){let E=new f("",u,o!=null?o:!0);return E.errorCategory=t,E.muxCode=D.NETWORK_UNKNOWN_ERROR,E}if(c===403)if(m){if(Le(m,d)){let E={timeStyle:"medium",dateStyle:"medium"},b=M("The video\u2019s secured {tokenNamePrefix}-token has expired.",n).format({tokenNamePrefix:s}),y=M("Expired at: {expiredDate}. Current time: {currentDate}.",n).format({expiredDate:new Intl.DateTimeFormat("en",E).format((h=m.exp)!=null?h:0*1e3),currentDate:new Intl.DateTimeFormat("en",E).format(d)}),k=new f(b,u,!0,y);return k.errorCategory=t,k.muxCode=D.NETWORK_TOKEN_EXPIRED,k.data=e,k}if(Ne(m,T)){let E=M("The video\u2019s playback ID does not match the one encoded in the {tokenNamePrefix}-token.",n).format({tokenNamePrefix:s}),b=M("Specified playback ID: {playbackId} and the playback ID encoded in the {tokenNamePrefix}-token: {tokenPlaybackId}",n).format({tokenNamePrefix:s,playbackId:T,tokenPlaybackId:m.sub}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_SUB_MISMATCH,y.data=e,y}if(Ae(m,l)){let E=M("The {tokenNamePrefix}-token is formatted with incorrect information.",n).format({tokenNamePrefix:s}),b=M("The {tokenNamePrefix}-token has no aud value. aud value should be {expectedAud}.",n).format({tokenNamePrefix:s,expectedAud:l}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISSING,y.data=e,y}if(Ie(m,l)){let E=M("The {tokenNamePrefix}-token is formatted with incorrect information.",n).format({tokenNamePrefix:s}),b=M("The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.",n).format({tokenNamePrefix:s,expectedAud:l,aud:m.aud}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISMATCH,y.data=e,y}}else{let E=M("Authorization error trying to access this {category} URL. If this is a signed URL, you might need to provide a {tokenNamePrefix}-token.",n).format({tokenNamePrefix:s,category:t}),b=M("Specified playback ID: {playbackId}",n).format({playbackId:T}),y=new f(E,u,o!=null?o:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MISSING,y.data=e,y}if(c===412){let E=M("This playback-id may belong to a live stream that is not currently active or an asset that is not ready.",n),b=M("Specified playback ID: {playbackId}",n).format({playbackId:T}),y=new f(E,u,o!=null?o:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_READY,y.streamType=r.streamType===_.LIVE?"live":r.streamType===_.ON_DEMAND?"on-demand":"unknown",y.data=e,y}if(c===404){let E=M("This URL or playback-id does not exist. You may have used an Asset ID or an ID from a different resource.",n),b=M("Specified playback ID: {playbackId}",n).format({playbackId:T}),y=new f(E,u,o!=null?o:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_FOUND,y.data=e,y}if(c===400){let E=M("The URL or playback-id was invalid. You may have used an invalid value as a playback-id."),b=M("Specified playback ID: {playbackId}",n).format({playbackId:T}),y=new f(E,u,o!=null?o:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_INVALID_URL,y.data=e,y}let R=new f("",u,o!=null?o:!0);return R.errorCategory=t,R.muxCode=D.NETWORK_UNKNOWN_ERROR,R.data=e,R};var qe=g.DefaultConfig.capLevelController,Z=class Z extends qe{constructor(t){super(t)}get levels(){var t;return(t=this.hls.levels)!=null?t:[]}getValidLevels(t){return this.levels.filter((r,o)=>this.isLevelAllowed(r)&&o<=t)}getMaxLevel(t){let r=super.getMaxLevel(t),o=this.getValidLevels(t);if(!o[r])return r;let n=Math.min(o[r].width,o[r].height),a=Z.minMaxResolution;return n>=a?r:qe.getMaxLevelByMediaSize(o,a*(16/9),a)}};Z.minMaxResolution=720;var me=Z,Ge=me;var ee={FAIRPLAY:"fairplay",PLAYREADY:"playready",WIDEVINE:"widevine"},nt=e=>{if(e.includes("fps"))return ee.FAIRPLAY;if(e.includes("playready"))return ee.PLAYREADY;if(e.includes("widevine"))return ee.WIDEVINE},ot=e=>{let t=e.split(`
`).find((r,o,n)=>o&&n[o-1].startsWith("#EXT-X-STREAM-INF"));return fetch(t).then(r=>r.status!==200?Promise.reject(r):r.text())},at=e=>{let t=e.split(`
`).filter(o=>o.startsWith("#EXT-X-SESSION-DATA"));if(!t.length)return{};let r={};for(let o of t){let n=st(o),a=n["DATA-ID"];a&&(r[a]={...n})}return{sessionData:r}},zt=/([A-Z0-9-]+)="?(.*?)"?(?:,|$)/g;function st(e){let t=[...e.matchAll(zt)];return Object.fromEntries(t.map(([,r,o])=>[r,o]))}var it=e=>{var c,d,u;let t=e.split(`
`),o=(d=((c=t.find(s=>s.startsWith("#EXT-X-PLAYLIST-TYPE")))!=null?c:"").split(":")[1])==null?void 0:d.trim(),n=oe(o),a=ae(o),i;if(n===_.LIVE){let s=t.find(l=>l.startsWith("#EXT-X-PART-INF"));if(!!s)i=+s.split(":")[1].split("=")[1]*2;else{let l=t.find(R=>R.startsWith("#EXT-X-TARGETDURATION")),T=(u=l==null?void 0:l.split(":"))==null?void 0:u[1];i=+(T!=null?T:6)*3}}return{streamType:n,targetLiveWindow:a,liveEdgeStartOffset:i}},ct=async(e,t)=>{if(t===N.MP4)return{streamType:_.ON_DEMAND,targetLiveWindow:Number.NaN,liveEdgeStartOffset:void 0,sessionData:void 0};if(t===N.M3U8){let r=await fetch(e);if(!r.ok)return Promise.reject(r);let o=await r.text(),n=await ot(o);return{...at(o),...it(n)}}return console.error(`Media type ${t} is an unrecognized or unsupported type for src ${e}.`),{streamType:void 0,targetLiveWindow:void 0,liveEdgeStartOffset:void 0,sessionData:void 0}},ut=async(e,t,r=W({src:e}))=>{var d,u,s,p;let{streamType:o,targetLiveWindow:n,liveEdgeStartOffset:a,sessionData:i}=await ct(e,r),c=i==null?void 0:i["com.apple.hls.chapters"];(c!=null&&c.URI||c!=null&&c.VALUE.toLocaleLowerCase().startsWith("http"))&&te((d=c.URI)!=null?d:c.VALUE,t),((u=P.get(t))!=null?u:{}).liveEdgeStartOffset=a,((s=P.get(t))!=null?s:{}).targetLiveWindow=n,t.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0})),((p=P.get(t))!=null?p:{}).streamType=o,t.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}))},te=async(e,t)=>{var r,o;try{let n=await fetch(e);if(!n.ok)throw new Error(`Failed to fetch Mux metadata: ${n.status} ${n.statusText}`);let a=await n.json(),i={};if(!((r=a==null?void 0:a[0])!=null&&r.metadata))return;for(let d of a[0].metadata)d.key&&d.value&&(i[d.key]=d.value);((o=P.get(t))!=null?o:{}).metadata=i;let c=new CustomEvent("muxmetadata");t.dispatchEvent(c)}catch(n){console.error(n)}},dt=e=>{var i;let t=e.type,r=oe(t),o=ae(t),n,a=!!((i=e.partList)!=null&&i.length);return r===_.LIVE&&(n=a?e.partTarget*2:e.targetduration*3),{streamType:r,targetLiveWindow:o,liveEdgeStartOffset:n,lowLatency:a}},lt=(e,t,r)=>{var c,d,u,s,p,l,T,m;let{streamType:o,targetLiveWindow:n,liveEdgeStartOffset:a,lowLatency:i}=dt(e);if(o===_.LIVE){i?(r.config.backBufferLength=(c=r.userConfig.backBufferLength)!=null?c:4,r.config.maxFragLookUpTolerance=(d=r.userConfig.maxFragLookUpTolerance)!=null?d:.001,r.config.abrBandWidthUpFactor=(u=r.userConfig.abrBandWidthUpFactor)!=null?u:r.config.abrBandWidthFactor):r.config.backBufferLength=(s=r.userConfig.backBufferLength)!=null?s:8;let R=Object.freeze({get length(){return t.seekable.length},start(x){return t.seekable.start(x)},end(x){var h;return x>this.length||x<0||Number.isFinite(t.duration)?t.seekable.end(x):(h=r.liveSyncPosition)!=null?h:t.seekable.end(x)}});((p=P.get(t))!=null?p:{}).seekable=R}((l=P.get(t))!=null?l:{}).liveEdgeStartOffset=a,((T=P.get(t))!=null?T:{}).targetLiveWindow=n,t.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0})),((m=P.get(t))!=null?m:{}).streamType=o,t.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}))},Xe,ze,Qt=(ze=(Xe=globalThis==null?void 0:globalThis.navigator)==null?void 0:Xe.userAgent)!=null?ze:"",Qe,Ze,et,Zt=(et=(Ze=(Qe=globalThis==null?void 0:globalThis.navigator)==null?void 0:Qe.userAgentData)==null?void 0:Ze.platform)!=null?et:"",er=Qt.toLowerCase().includes("android")||["x11","android"].some(e=>Zt.toLowerCase().includes(e)),P=new WeakMap,I="mux.com",tt,rt,pt=(rt=(tt=g).isSupported)==null?void 0:rt.call(tt),tr=er,rr=()=>$.default.utils.now(),ft=$.default.utils.generateUUID,nr=({playbackId:e,customDomain:t=I,maxResolution:r,minResolution:o,renditionOrder:n,programStartTime:a,programEndTime:i,assetStartTime:c,assetEndTime:d,playbackToken:u,tokens:{playback:s=u}={},extraSourceParams:p={}}={})=>{if(!e)return;let[l,T=""]=K(e),m=new URL(`https://stream.${t}/${l}.m3u8${T}`);return s||m.searchParams.has("token")?(m.searchParams.forEach((R,x)=>{x!="token"&&m.searchParams.delete(x)}),s&&m.searchParams.set("token",s)):(r&&m.searchParams.set("max_resolution",r),o&&(m.searchParams.set("min_resolution",o),r&&+r.slice(0,-1)<+o.slice(0,-1)&&console.error("minResolution must be <= maxResolution","minResolution",o,"maxResolution",r)),n&&m.searchParams.set("rendition_order",n),a&&m.searchParams.set("program_start_time",`${a}`),i&&m.searchParams.set("program_end_time",`${i}`),c&&m.searchParams.set("asset_start_time",`${c}`),d&&m.searchParams.set("asset_end_time",`${d}`),Object.entries(p).forEach(([R,x])=>{x!=null&&m.searchParams.set(R,x)})),m.toString()},re=e=>{if(!e)return;let[t]=e.split("?");return t||void 0},Me=e=>{if(!e||!e.startsWith("https://stream."))return;let[t]=new URL(e).pathname.slice(1).split(/\.m3u8|\//);return t||void 0},or=e=>{var t,r,o;return(t=e==null?void 0:e.metadata)!=null&&t.video_id?e.metadata.video_id:Ce(e)&&(o=(r=re(e.playbackId))!=null?r:Me(e.src))!=null?o:e.src},Tt=e=>{var t;return(t=P.get(e))==null?void 0:t.error},ar=e=>{var t;return(t=P.get(e))==null?void 0:t.metadata},Ee=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.streamType)!=null?r:_.UNKNOWN},sr=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.targetLiveWindow)!=null?r:Number.NaN},xe=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.seekable)!=null?r:e.seekable},ir=e=>{var o;let t=(o=P.get(e))==null?void 0:o.liveEdgeStartOffset;if(typeof t!="number")return Number.NaN;let r=xe(e);return r.length?r.end(r.length-1)-t:Number.NaN},Re=.034,cr=(e,t,r=Re)=>Math.abs(e-t)<=r,yt=(e,t,r=Re)=>e>t||cr(e,t,r),mt=(e,t=Re)=>e.paused&&yt(e.currentTime,e.duration,t),De=(e,t)=>{var u,s,p;if(!t||!e.buffered.length)return;if(e.readyState>2)return!1;let r=t.currentLevel>=0?(s=(u=t.levels)==null?void 0:u[t.currentLevel])==null?void 0:s.details:(p=t.levels.find(l=>!!l.details))==null?void 0:p.details;if(!r||r.live)return;let{fragments:o}=r;if(!(o!=null&&o.length))return;if(e.currentTime<e.duration-(r.targetduration+.5))return!1;let n=o[o.length-1];if(e.currentTime<=n.start)return!1;let a=n.start+n.duration/2,i=e.buffered.start(e.buffered.length-1),c=e.buffered.end(e.buffered.length-1);return a>i&&a<c},Et=(e,t)=>e.ended||e.loop?e.ended:t&&De(e,t)?!0:mt(e),ur=(e,t,r)=>{gt(t,r,e);let{metadata:o={}}=e,{view_session_id:n=ft()}=o,a=or(e);o.view_session_id=n,o.video_id=a,e.metadata=o;let i=s=>{var p;(p=t.mux)==null||p.emit("hb",{view_drm_type:s})};e.drmTypeCb=i,P.set(t,{retryCount:0});let c=xt(e,t),d=Ue(e,t,c);e!=null&&e.muxDataKeepSession&&(t!=null&&t.mux)&&!t.mux.deleted?c&&t.mux.addHLSJS({hlsjs:c,Hls:c?g:void 0}):Pt(e,t,c),_t(e,t,c),le(t),ye(t);let u=Oe(e,t,c);return{engine:c,setAutoplay:u,setPreload:d}},gt=(e,t,r)=>{let o=t==null?void 0:t.engine;e!=null&&e.mux&&!e.mux.deleted&&(r!=null&&r.muxDataKeepSession?o&&e.mux.removeHLSJS():(e.mux.destroy(),delete e.mux)),o&&(o.detachMedia(),o.destroy()),e&&(e.hasAttribute("src")&&(e.removeAttribute("src"),e.load()),e.removeEventListener("error",ht),e.removeEventListener("error",ge),e.removeEventListener("durationchange",kt),P.delete(e),e.dispatchEvent(new Event("teardown")))};function Mt(e,t){var u;let r=W(e);if(!(r===N.M3U8))return!0;let n=!r||((u=t.canPlayType(r))!=null?u:!0),{preferPlayback:a}=e,i=a===j.MSE,c=a===j.NATIVE;return n&&(c||!(pt&&(i||tr)))}var xt=(e,t)=>{let{debug:r,streamType:o,startTime:n=-1,metadata:a,preferCmcd:i,_hlsConfig:c={}}=e,u=W(e)===N.M3U8,s=Mt(e,t);if(u&&!s&&pt){let p={backBufferLength:30,renderTextTracksNatively:!1,liveDurationInfinity:!0,capLevelToPlayerSize:!0,capLevelOnFPSDrop:!0},l=Rt(o),T=Dt(e),m=[S.QUERY,S.HEADER].includes(i)?{useHeaders:i===S.HEADER,sessionId:a==null?void 0:a.view_session_id,contentId:a==null?void 0:a.video_id}:void 0,R=new g({debug:r,startPosition:n,cmcd:m,xhrSetup:(x,h)=>{var y,k;if(i&&i!==S.QUERY)return;let E=new URL(h);if(!E.searchParams.has("CMCD"))return;let b=((k=(y=E.searchParams.get("CMCD"))==null?void 0:y.split(","))!=null?k:[]).filter(ve=>ve.startsWith("sid")||ve.startsWith("cid")).join(",");E.searchParams.set("CMCD",b),x.open("GET",E)},capLevelController:Ge,...p,...l,...T,...c});return R.on(g.Events.MANIFEST_PARSED,async function(x,h){var b,y;let E=(b=h.sessionData)==null?void 0:b["com.apple.hls.chapters"];(E!=null&&E.URI||E!=null&&E.VALUE.toLocaleLowerCase().startsWith("http"))&&te((y=E==null?void 0:E.URI)!=null?y:E==null?void 0:E.VALUE,t)}),R}},Rt=e=>e===_.LIVE?{backBufferLength:8}:{},Dt=e=>{let{tokens:{drm:t}={},playbackId:r,drmTypeCb:o}=e,n=re(r);return!t||!n?{}:{emeEnabled:!0,drmSystems:{"com.apple.fps":{licenseUrl:F(e,"fairplay"),serverCertificateUrl:be(e,"fairplay")},"com.widevine.alpha":{licenseUrl:F(e,"widevine")},"com.microsoft.playready":{licenseUrl:F(e,"playready")}},requestMediaKeySystemAccessFunc:(a,i)=>(a==="com.widevine.alpha"&&(i=[...i.map(c=>{var u;let d=(u=c.videoCapabilities)==null?void 0:u.map(s=>({...s,robustness:"HW_SECURE_ALL"}));return{...c,videoCapabilities:d}}),...i]),navigator.requestMediaKeySystemAccess(a,i).then(c=>{let d=nt(a);return o==null||o(d),c}))}},bt=async e=>{let t=await fetch(e);return t.status!==200?Promise.reject(t):await t.arrayBuffer()},Ct=async(e,t)=>{let r=await fetch(t,{method:"POST",headers:{"Content-type":"application/octet-stream"},body:e});if(r.status!==200)return Promise.reject(r);let o=await r.arrayBuffer();return new Uint8Array(o)},vt=(e,t)=>{v(t,"encrypted",async o=>{try{let n=o.initDataType;if(n!=="skd"){console.error(`Received unexpected initialization data type "${n}"`);return}if(!t.mediaKeys){let u=await navigator.requestMediaKeySystemAccess("com.apple.fps",[{initDataTypes:[n],videoCapabilities:[{contentType:"application/vnd.apple.mpegurl",robustness:""}],distinctiveIdentifier:"not-allowed",persistentState:"not-allowed",sessionTypes:["temporary"]}]).then(p=>{var l;return(l=e.drmTypeCb)==null||l.call(e,ee.FAIRPLAY),p}).catch(()=>{let p=M("Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser."),l=new f(p,f.MEDIA_ERR_ENCRYPTED,!0);l.errorCategory=C.DRM,l.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM,A(t,l)});if(!u)return;let s=await u.createMediaKeys();try{let p=await bt(be(e,"fairplay")).catch(l=>{if(l instanceof Response){let T=Y(l,C.DRM,e);return console.error("mediaError",T==null?void 0:T.message,T==null?void 0:T.context),T?Promise.reject(T):Promise.reject(new Error("Unexpected error in app cert request"))}return Promise.reject(l)});await s.setServerCertificate(p).catch(()=>{let l=M("Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate."),T=new f(l,f.MEDIA_ERR_ENCRYPTED,!0);return T.errorCategory=C.DRM,T.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED,Promise.reject(T)})}catch(p){A(t,p);return}await t.setMediaKeys(s)}let a=o.initData;if(a==null){console.error(`Could not start encrypted playback due to missing initData in ${o.type} event`);return}let i=t.mediaKeys.createSession();i.addEventListener("keystatuseschange",()=>{i.keyStatuses.forEach(u=>{let s;if(u==="internal-error"){let p=M("The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!0),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_CDM_ERROR}else if(u==="output-restricted"||u==="output-downscaled"){let p=M("DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!1),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}s&&A(t,s)})});let c=await Promise.all([i.generateRequest(n,a).catch(()=>{let u=M("Failed to generate a DRM license request. This may be an issue with the player or your protected content."),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED,A(t,s)}),new Promise(u=>{i.addEventListener("message",s=>{u(s.message)},{once:!0})})]).then(([,u])=>u),d=await Ct(c,F(e,"fairplay")).catch(u=>{if(u instanceof Response){let s=Y(u,C.DRM,e);return console.error("mediaError",s==null?void 0:s.message,s==null?void 0:s.context),s?Promise.reject(s):Promise.reject(new Error("Unexpected error in license key request"))}return Promise.reject(u)});await i.update(d).catch(()=>{let u=M("Failed to update DRM license. This may be an issue with the player or your protected content."),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);return s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED,Promise.reject(s)})}catch(n){A(t,n);return}})},F=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},o)=>{let n=re(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/license/${o}/${n}?token=${t}`},be=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},o)=>{let n=re(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/appcert/${o}/${n}?token=${t}`},Ce=({playbackId:e,src:t,customDomain:r})=>{if(e)return!0;if(typeof t!="string")return!1;let o=window==null?void 0:window.location.href,n=new URL(t,o).hostname.toLocaleLowerCase();return n.includes(I)||!!r&&n.includes(r.toLocaleLowerCase())},Pt=(e,t,r)=>{var d;let{envKey:o,disableTracking:n,muxDataSDK:a=$.default,muxDataSDKOptions:i={}}=e,c=Ce(e);if(!n&&(o||c)){let{playerInitTime:u,playerSoftwareName:s,playerSoftwareVersion:p,beaconCollectionDomain:l,debug:T,disableCookies:m}=e,R={...e.metadata,video_title:((d=e==null?void 0:e.metadata)==null?void 0:d.video_title)||void 0},x=h=>typeof h.player_error_code=="string"?!1:typeof e.errorTranslator=="function"?e.errorTranslator(h):h;a.monitor(t,{debug:T,beaconCollectionDomain:l,hlsjs:r,Hls:r?g:void 0,automaticErrorTracking:!1,errorTranslator:x,disableCookies:m,...i,data:{...o?{env_key:o}:{},player_software_name:s,player_software:s,player_software_version:p,player_init_time:u,...R}})}},_t=(e,t,r)=>{var s,p;let o=Mt(e,t),{src:n,customDomain:a=I}=e,i=()=>{t.ended||!Et(t,r)||(De(t,r)?t.currentTime=t.buffered.end(t.buffered.length-1):t.dispatchEvent(new Event("ended")))},c,d,u=()=>{let l=xe(t),T,m;l.length>0&&(T=l.start(0),m=l.end(0)),(d!==m||c!==T)&&t.dispatchEvent(new CustomEvent("seekablechange",{composed:!0})),c=T,d=m};if(v(t,"durationchange",u),t&&o){let l=W(e);if(typeof n=="string"){if(n.endsWith(".mp4")&&n.includes(a)){let R=Me(n),x=new URL(`https://stream.${a}/${R}/metadata.json`);te(x.toString(),t)}let T=()=>{if(Ee(t)!==_.LIVE||Number.isFinite(t.duration))return;let R=setInterval(u,1e3);t.addEventListener("teardown",()=>{clearInterval(R)},{once:!0}),v(t,"durationchange",()=>{Number.isFinite(t.duration)&&clearInterval(R)})},m=async()=>ut(n,t,l).then(T).catch(R=>{if(R instanceof Response){let x=Y(R,C.VIDEO,e);if(x){A(t,x);return}}else R instanceof Error});if(t.preload==="none"){let R=()=>{m(),t.removeEventListener("loadedmetadata",x)},x=()=>{m(),t.removeEventListener("play",R)};v(t,"play",R,{once:!0}),v(t,"loadedmetadata",x,{once:!0})}else m();(s=e.tokens)!=null&&s.drm?vt(e,t):v(t,"encrypted",()=>{let R=M("Attempting to play DRM-protected content without providing a DRM token."),x=new f(R,f.MEDIA_ERR_ENCRYPTED,!0);x.errorCategory=C.DRM,x.muxCode=D.ENCRYPTED_MISSING_TOKEN,A(t,x)},{once:!0}),t.setAttribute("src",n),e.startTime&&(((p=P.get(t))!=null?p:{}).startTime=e.startTime,t.addEventListener("durationchange",kt,{once:!0}))}else t.removeAttribute("src");t.addEventListener("error",ht),t.addEventListener("error",ge),t.addEventListener("emptied",()=>{t.querySelectorAll("track[data-removeondestroy]").forEach(m=>{m.remove()})},{once:!0}),v(t,"pause",i),v(t,"seeked",i),v(t,"play",()=>{t.ended||yt(t.currentTime,t.duration)&&(t.currentTime=t.seekable.length?t.seekable.start(0):0)})}else r&&n?(r.once(g.Events.LEVEL_LOADED,(l,T)=>{lt(T.details,t,r),u(),Ee(t)===_.LIVE&&!Number.isFinite(t.duration)&&(r.on(g.Events.LEVEL_UPDATED,u),v(t,"durationchange",()=>{Number.isFinite(t.duration)&&r.off(g.Events.LEVELS_UPDATED,u)}))}),r.on(g.Events.ERROR,(l,T)=>{var R,x;let m=dr(T,e);if(m.muxCode===D.NETWORK_NOT_READY){let E=(R=P.get(t))!=null?R:{},b=(x=E.retryCount)!=null?x:0;if(b<6){let y=b===0?5e3:6e4,k=new f(`Retrying in ${y/1e3} seconds...`,m.code,m.fatal);Object.assign(k,m),A(t,k),setTimeout(()=>{E.retryCount=b+1,T.details==="manifestLoadError"&&T.url&&r.loadSource(T.url)},y);return}else{E.retryCount=0;let y=new f('Try again later or <a href="#" onclick="window.location.reload(); return false;" style="color: #4a90e2;">click here to retry</a>',m.code,m.fatal);Object.assign(y,m),A(t,y);return}}A(t,m)}),r.on(g.Events.MANIFEST_LOADED,()=>{let l=P.get(t);l&&l.error&&(l.error=null,l.retryCount=0,t.dispatchEvent(new Event("emptied")),t.dispatchEvent(new Event("loadstart")))}),t.addEventListener("error",ge),v(t,"waiting",i),He(e,r),Ve(t,r),r.attachMedia(t)):console.error("It looks like the video you're trying to play will not work on this system! If possible, try upgrading to the newest versions of your browser or software.")};function kt(e){var o;let t=e.target,r=(o=P.get(t))==null?void 0:o.startTime;if(r&&he(t.seekable,t.duration,r)){let n=t.preload==="auto";n&&(t.preload="none"),t.currentTime=r,n&&(t.preload="auto")}}async function ht(e){if(!e.isTrusted)return;e.stopImmediatePropagation();let t=e.target;if(!(t!=null&&t.error))return;let{message:r,code:o}=t.error,n=new f(r,o);if(t.src&&o===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&t.readyState===HTMLMediaElement.HAVE_NOTHING){setTimeout(()=>{var i;let a=(i=Tt(t))!=null?i:t.error;(a==null?void 0:a.code)===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&A(t,n)},500);return}if(t.src&&(o!==f.MEDIA_ERR_DECODE||o!==void 0))try{let{status:a}=await fetch(t.src);n.data={response:{code:a}}}catch{}A(t,n)}function A(e,t){var r;t.fatal&&(((r=P.get(e))!=null?r:{}).error=t,e.dispatchEvent(new CustomEvent("error",{detail:t})))}function ge(e){var o,n;if(!(e instanceof CustomEvent)||!(e.detail instanceof f))return;let t=e.target,r=e.detail;!r||!r.fatal||(((o=P.get(t))!=null?o:{}).error=r,(n=t.mux)==null||n.emit("error",{player_error_code:r.code,player_error_message:r.message,player_error_context:r.context}))}var dr=(e,t)=>{var c,d,u;console.error("getErrorFromHlsErrorData()",e);let r={[g.ErrorTypes.NETWORK_ERROR]:f.MEDIA_ERR_NETWORK,[g.ErrorTypes.MEDIA_ERROR]:f.MEDIA_ERR_DECODE,[g.ErrorTypes.KEY_SYSTEM_ERROR]:f.MEDIA_ERR_ENCRYPTED},o=s=>[g.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED].includes(s.details)?f.MEDIA_ERR_NETWORK:r[s.type],n=s=>{if(s.type===g.ErrorTypes.KEY_SYSTEM_ERROR)return C.DRM;if(s.type===g.ErrorTypes.NETWORK_ERROR)return C.VIDEO},a,i=o(e);if(i===f.MEDIA_ERR_NETWORK&&e.response){let s=(c=n(e))!=null?c:C.VIDEO;a=(d=Y(e.response,s,t,e.fatal))!=null?d:new f("",i,e.fatal)}else if(i===f.MEDIA_ERR_ENCRYPTED)if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_CONFIGURED_LICENSE){let s=M("Attempting to play DRM-protected content without providing a DRM token.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_MISSING_TOKEN}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_ACCESS){let s=M("Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_SESSION){let s=M("Failed to generate a DRM license request. This may be an issue with the player or your protected content.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!0),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SESSION_UPDATE_FAILED){let s=M("Failed to update DRM license. This may be an issue with the player or your protected content.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED){let s=M("Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_INTERNAL_ERROR){let s=M("The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_CDM_ERROR}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED){let s=M("DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!1),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}else a=new f(e.error.message,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_ERROR;else a=new f("",i,e.fatal);return a.context||(a.context=`${e.url?`url: ${e.url}
`:""}${e.response&&(e.response.code||e.response.text)?`response: ${e.response.code}, ${e.response.text}
`:""}${e.reason?`failure reason: ${e.reason}
`:""}${e.level?`level: ${e.level}
`:""}${e.parent?`parent stream controller: ${e.parent}
`:""}${e.buffer?`buffer length: ${e.buffer}
`:""}${e.error?`error: ${e.error}
`:""}${e.event?`event: ${e.event}
`:""}${e.err?`error message: ${(u=e.err)==null?void 0:u.message}
`:""}`),a.data=e,a};
//# sourceMappingURL=index.cjs.js.map
