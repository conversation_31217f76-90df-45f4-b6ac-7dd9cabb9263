{"fileNames": ["../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/typescript/lib/lib.es2019.d.ts", "../../../node_modules/typescript/lib/lib.es2020.d.ts", "../../../node_modules/typescript/lib/lib.dom.d.ts", "../../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../node_modules/typescript/lib/lib.dom.asynciterable.d.ts", "../../../node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../../../node_modules/typescript/lib/lib.scripthost.d.ts", "../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../../node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../node_modules/typescript/lib/lib.decorators.d.ts", "../../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../../node_modules/typescript/lib/lib.es2020.full.d.ts", "../../../node_modules/mux-embed/dist/types/mux-embed.d.ts", "../src/errors.ts", "../node_modules/hls.js/dist/hls.d.mts", "../src/types.ts", "../lang/en.json", "../src/util.ts", "../src/hls.ts", "../src/autoplay.ts", "../src/preload.ts", "../src/media-tracks.ts", "../src/text-tracks.ts", "../src/pdt.ts", "../src/request-errors.ts", "../src/min-cap-level-controller.ts", "../src/index.ts", "../../../node_modules/media-tracks/dist/video-rendition.d.ts", "../../../node_modules/media-tracks/dist/video-track.d.ts", "../../../node_modules/media-tracks/dist/video-track-list.d.ts", "../../../node_modules/media-tracks/dist/audio-rendition.d.ts", "../../../node_modules/media-tracks/dist/audio-track.d.ts", "../../../node_modules/media-tracks/dist/audio-track-list.d.ts", "../../../node_modules/media-tracks/dist/video-rendition-list.d.ts", "../../../node_modules/media-tracks/dist/audio-rendition-list.d.ts", "../../../node_modules/media-tracks/dist/global.d.ts"], "fileIdsList": [[70, 71], [71], [70], [68, 69, 71, 72, 73, 74], [67, 68], [68], [67], [55, 57, 58], [54], [52, 53, 54, 55, 57, 58, 59, 60, 61, 62, 63, 64, 65], [58], [54, 58], [55], [55, 57], [53, 54, 55, 57], [52, 53, 54], [55, 56]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7a3c8b952931daebdfc7a2897c53c0a1c73624593fa070e46bd537e64dcd20a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "80e18897e5884b6723488d4f5652167e7bb5024f946743134ecc4aa4ee731f89", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "cd034f499c6cdca722b60c04b5b1b78e058487a7085a8e0d6fb50809947ee573", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1305d1e76ca44e30fb8b2b8075fa522b83f60c0bcf5d4326a9d2cf79b53724f8", "impliedFormat": 1}, {"version": "7397a08f5ba9c211b8b168dc14153c1a1ea7e1e0b1fc82122eb3f24de889aa8c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d0ebbdc27c1a71604ac47a77457479b0b04a30270259519f8ca5f4c4a5082b40", "signature": "c757b1b4e1ff177d5e8e3629822474c82bf4146156520876b6e3f706f9738eda", "affectsGlobalScope": true}, {"version": "bc62541d542d210ec0b990d734ae1c7ffb7b10c46cdb26cc81aadede5376bd0a", "impliedFormat": 99}, {"version": "402bcd5109f6ccad1c34672d456c13558135ae5e112dffe2185bb3f1e13c8562", "signature": "aa0671ff7db447a6e143569a4b64768594bda4d4513a348453653cc07473502f"}, "c8bef5230591734443cbf24371d9853d09e536648be3a6b0d8239c83b6aaaf4b", {"version": "00f9d84557a47a656d6deed9ce36a1cd7fc74769facbba01baaf071d10a43170", "signature": "f599538eb808fdff175709508823bff542d95bdd98c037696ce86a55a8a2eda9"}, "c13fb8c7fdb84f3c59470c672220c380ac26ffb099721ccebba3c53971cb700d", {"version": "89b90951a374603ba2102d1eeef328c9e6726f694698eb1ebba7ffebf639b463", "signature": "cc64cc2b871c6127ae5841dc6f8a2aa2064d73569f707d913ed76de3ebfc97e5"}, {"version": "33a7c8c6e9c79e94f4b71eddf5466737a12026eb1215daa85621b5c4f3524193", "signature": "bec076d074f786e91c7f1787c871e5eeb951c72e0415df4f6f1041cc540058b8"}, {"version": "41f7897ae2107b1a08f9dff9b6318e75fa9476a02fdecae2009f384c0ff4e019", "signature": "f9416c803360f1b7d50d5005aa62780607533c0f446f164f373711038b000b5f"}, {"version": "a62605c4448cc4ed59125fd1a7c512da58789d62878018ca8ea5f321c7b77b44", "signature": "3be94ff5800bf4567d511f1d716237ef30f753ef916bf855209a7f4c2cd741e1"}, {"version": "0357e4c609f9e3a89903899e8334cbcd44530e1b077329051846e6a1754eed93", "signature": "dccea7b665fe08ed006acd191a7e68123ba7ec49848ac7c28a164962bf21254e"}, {"version": "b8a696e6d4b719b00d3a0d62383077d9ad529eb16b9e4704779d4a38e2f27cba", "signature": "b00bb60d069436e8d5ed514413e95ec8b0cf31a3ac3132ed8e4e95571b4e78f9"}, {"version": "b954ed6036e122b79806db0be2b864c8e7b264bb2cd9c0eb353ff5fb9356583e", "signature": "01913b145303ae09748bad4e42b3f4d543dbd397331fcfffa008093e785d61e9"}, {"version": "dfc44f952e23fdc442913444b50783e04d92ff49914e4216dcf83d4aba5cd1ac", "signature": "81801c8187835d043c15bd3fa274725d2026f88fa85a1d009f7e27e30f26e680", "affectsGlobalScope": true}, {"version": "2ed351b90f48ecae9c627d41e2614c6a4fe670f958e8a5a5d3bb4bc21980bdb6", "impliedFormat": 99}, {"version": "8f358619bbe2b132a373c5e8381018bb1f11f55dbac8cb84c380d5279f18eb75", "impliedFormat": 99}, {"version": "ba0b6859e73b5fb85f18160a3b0d741968b1c4122fdd3436398dc99a6a9736c9", "impliedFormat": 99}, {"version": "9979bad516454ac2aaff1756fc7f664ca6ccfdd9b4077f306e99df3033edf0a2", "impliedFormat": 99}, {"version": "323cfa8826348d6f5269abeed193b53c1ee351a9c178858bce4dee6e9b6c3ac9", "impliedFormat": 99}, {"version": "24354cb3bc1d897f8321bce628352612d35d92a79dba42e6609f7f5ec7d3a697", "impliedFormat": 99}, {"version": "2ad77fd12d9bf99057909e40fecb502cb4429c617161f373339f294dfaa5d6f5", "impliedFormat": 99}, {"version": "8e4de702297e7c9173982f2e1af6bb29b5a0251811ac12de6beb48dba2288a9f", "impliedFormat": 99}, {"version": "2c512f7b30f704093ae23b2591a6e9e9cc852006be0bf8e4f770788d88b17796", "affectsGlobalScope": true, "impliedFormat": 99}], "root": [53, 55, [57, 66], 75], "options": {"composite": true, "declaration": true, "emitDeclarationOnly": true, "esModuleInterop": true, "jsx": 2, "module": 5, "noImplicitAny": true, "outDir": "./types", "rootDir": "../src", "skipLibCheck": false, "sourceMap": true, "strict": true, "target": 7}, "referencedMap": [[74, 1], [72, 2], [71, 3], [75, 4], [73, 5], [69, 6], [68, 7], [59, 8], [58, 9], [66, 10], [61, 11], [65, 12], [63, 13], [60, 14], [64, 15], [62, 8], [55, 16], [57, 17]], "latestChangedDtsFile": "./types/index.d.ts", "version": "5.8.3"}