# Playback Core

This is a collection of utility functions directly related to the playback and Mux Data integration with various mux elements.

It is currently used by `mux-video`, `mux-video-react`, `mux-audio`, and `mux-audio-react`.

You shouldn't need use this package directly, but if you're building something custom for your application you may find it handy.

Check the `src/index.ts` file to see the functions and types that get exported.
