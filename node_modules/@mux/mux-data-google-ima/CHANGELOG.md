# @mux/mux-data-google-ima

## 0.2.8

### Patch Changes

- 5c14949: feat(google-ima): Support dynamic playback engine (adding/removing) during monitor.

## 0.2.7

### Patch Changes

- Updated dependencies [eb2ebfa]
  - mux-embed@5.9.0

## 0.2.6

### Patch Changes

- Updated dependencies [8fa7827]
  - mux-embed@5.8.3

## 0.2.5

### Patch Changes

- Updated dependencies [38eeefe]
  - mux-embed@5.8.2

## 0.2.4

### Patch Changes

- Updated dependencies [cef9e40]
  - mux-embed@5.8.1

## 0.2.3

### Patch Changes

- Updated dependencies [049be75]
  - mux-embed@5.8.0

## 0.2.2

### Patch Changes

- Updated dependencies [41b0915]
  - mux-embed@5.7.0

## 0.2.1

### Patch Changes

- Updated dependencies [9cd7dbf]
  - mux-embed@5.6.0

## 0.2.0

### Minor Changes

- 18af18e: Update mechanism for generating unique IDs, used for `view_id` and others

### Patch Changes

- Updated dependencies [18af18e]
- Updated dependencies [157f957]
  - mux-embed@5.5.0

## 0.1.2

### Patch Changes

- 2d96231: [chore] internal build process fix (no functional changes)
- Updated dependencies [2d96231]
  - mux-embed@5.4.3

## 0.1.1

### Patch Changes

- 5104511: [chore] only publish minified code

## 0.1.0

### Minor Changes

- e5f3e65: feat(google-ima): Beta implementation of google-ima extension to mux-embed

### Patch Changes

- Updated dependencies [e5f3e65]
- Updated dependencies [fecba0b]
- Updated dependencies [40f531d]
  - mux-embed@5.4.2
