"use strict";var Ye=Object.defineProperty;var Ea=Object.getOwnPropertyDescriptor;var wa=Object.getOwnPropertyNames;var Da=Object.prototype.hasOwnProperty;var Aa=function(i,e){for(var t in e)Ye(i,t,{get:e[t],enumerable:!0})},ka=function(i,e,t,a){if(e&&typeof e=="object"||typeof e=="function")for(var r=wa(e),n=0,o=r.length,s;n<o;n++)s=r[n],!Da.call(i,s)&&s!==t&&Ye(i,s,{get:function(u){return e[u]}.bind(null,s),enumerable:!(a=Ea(e,s))||a.enumerable});return i};var xa=function(i){return ka(Ye({},"__esModule",{value:!0}),i)};var zi={};Aa(zi,{attachDashJSMonitorInstanceExtension:function(){return na},attachHLSJSMonitorInstanceExtension:function(){return ia},attachMonitorInstanceExtensionByOptions:function(){return oa},createDashJSMonitorInstanceExtension:function(){return aa},createHLSJSMonitorInstanceExtension:function(){return ta},createMonitorInstanceExtensionByOptions:function(){return ra},default:function(){return Wi},isDashJSOptions:function(){return Zt},isHLSJSOptions:function(){return ea}});module.exports=xa(zi);function ct(i){if(Array.isArray(i))return i}function vt(i,e){var t=i==null?null:typeof Symbol!="undefined"&&i[Symbol.iterator]||i["@@iterator"];if(t!=null){var a=[],r=!0,n=!1,o,s;try{for(t=t.call(i);!(r=(o=t.next()).done)&&(a.push(o.value),!(e&&a.length===e));r=!0);}catch(u){n=!0,s=u}finally{try{!r&&t.return!=null&&t.return()}finally{if(n)throw s}}return a}}function _t(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function we(i,e){(e==null||e>i.length)&&(e=i.length);for(var t=0,a=new Array(e);t<e;t++)a[t]=i[t];return a}function je(i,e){if(i){if(typeof i=="string")return we(i,e);var t=Object.prototype.toString.call(i).slice(8,-1);if(t==="Object"&&i.constructor&&(t=i.constructor.name),t==="Map"||t==="Set")return Array.from(t);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return we(i,e)}}function le(i,e){return ct(i)||vt(i,e)||je(i,e)||_t()}function pt(i){if(Array.isArray(i))return we(i)}function ft(i){if(typeof Symbol!="undefined"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function ht(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function De(i){return pt(i)||ft(i)||je(i)||ht()}var Sa=Object.create,At=Object.defineProperty,Ra=Object.getOwnPropertyDescriptor,Oa=Object.getOwnPropertyNames,Pa=Object.getPrototypeOf,Na=Object.prototype.hasOwnProperty,kt=function(e,t){return function(){return e&&(t=e(e=0)),t}},Q=function(e,t){return function(){return t||e((t={exports:{}}).exports,t),t.exports}},qa=function(e,t,a,r){if(t&&typeof t=="object"||typeof t=="function")for(var n=Oa(t),o=0,s=n.length,u;o<s;o++)u=n[o],!Na.call(e,u)&&u!==a&&At(e,u,{get:function(d){return t[d]}.bind(null,u),enumerable:!(r=Ra(t,u))||r.enumerable});return e},z=function(e,t,a){return a=e!=null?Sa(Pa(e)):{},qa(t||!e||!e.__esModule?At(a,"default",{value:e,enumerable:!0}):a,e)},ee=Q(function(i,e){var t;typeof window!="undefined"?t=window:typeof global!="undefined"?t=global:typeof self!="undefined"?t=self:t={},e.exports=t});function pe(i,e){return e!=null&&typeof Symbol!="undefined"&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](i):pe(i,e)}var fe=kt(function(){fe()});function xt(i){"@swc/helpers - typeof";return i&&typeof Symbol!="undefined"&&i.constructor===Symbol?"symbol":typeof i}var St=kt(function(){}),Rt=Q(function(i,e){var t=Array.prototype.slice;e.exports=a;function a(r,n){for(("length"in r)||(r=[r]),r=t.call(r);r.length;){var o=r.shift(),s=n(o);if(s)return s;o.childNodes&&o.childNodes.length&&(r=t.call(o.childNodes).concat(r))}}}),La=Q(function(i,e){fe(),e.exports=t;function t(a,r){if(!pe(this,t))return new t(a,r);this.data=a,this.nodeValue=a,this.length=a.length,this.ownerDocument=r||null}t.prototype.nodeType=8,t.prototype.nodeName="#comment",t.prototype.toString=function(){return"[object Comment]"}}),Ia=Q(function(i,e){fe(),e.exports=t;function t(a,r){if(!pe(this,t))return new t(a);this.data=a||"",this.length=this.data.length,this.ownerDocument=r||null}t.prototype.type="DOMTextNode",t.prototype.nodeType=3,t.prototype.nodeName="#text",t.prototype.toString=function(){return this.data},t.prototype.replaceData=function(a,r,n){var o=this.data,s=o.substring(0,a),u=o.substring(a+r,o.length);this.data=s+n+u,this.length=this.data.length}}),Ot=Q(function(i,e){e.exports=t;function t(a){var r=this,n=a.type;a.target||(a.target=r),r.listeners||(r.listeners={});var o=r.listeners[n];if(o)return o.forEach(function(s){a.currentTarget=r,typeof s=="function"?s(a):s.handleEvent(a)});r.parentNode&&r.parentNode.dispatchEvent(a)}}),Pt=Q(function(i,e){e.exports=t;function t(a,r){var n=this;n.listeners||(n.listeners={}),n.listeners[a]||(n.listeners[a]=[]),n.listeners[a].indexOf(r)===-1&&n.listeners[a].push(r)}}),Nt=Q(function(i,e){e.exports=t;function t(a,r){var n=this;if(n.listeners&&n.listeners[a]){var o=n.listeners[a],s=o.indexOf(r);s!==-1&&o.splice(s,1)}}}),Ca=Q(function(i,e){St(),e.exports=a;var t=["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr"];function a(c){switch(c.nodeType){case 3:return v(c.data);case 8:return"<!--"+c.data+"-->";default:return r(c)}}function r(c){var l=[],_=c.tagName;return c.namespaceURI==="http://www.w3.org/1999/xhtml"&&(_=_.toLowerCase()),l.push("<"+_+d(c)+s(c)),t.indexOf(_)>-1?l.push(" />"):(l.push(">"),c.childNodes.length?l.push.apply(l,c.childNodes.map(a)):c.textContent||c.innerText?l.push(v(c.textContent||c.innerText)):c.innerHTML&&l.push(c.innerHTML),l.push("</"+_+">")),l.join("")}function n(c,l){var _=xt(c[l]);return l==="style"&&Object.keys(c.style).length>0?!0:c.hasOwnProperty(l)&&(_==="string"||_==="boolean"||_==="number")&&l!=="nodeName"&&l!=="className"&&l!=="tagName"&&l!=="textContent"&&l!=="innerText"&&l!=="namespaceURI"&&l!=="innerHTML"}function o(c){if(typeof c=="string")return c;var l="";return Object.keys(c).forEach(function(_){var f=c[_];_=_.replace(/[A-Z]/g,function(k){return"-"+k.toLowerCase()}),l+=_+":"+f+";"}),l}function s(c){var l=c.dataset,_=[];for(var f in l)_.push({name:"data-"+f,value:l[f]});return _.length?u(_):""}function u(c){var l=[];return c.forEach(function(_){var f=_.name,k=_.value;f==="style"&&(k=o(k)),l.push(f+'="'+g(k)+'"')}),l.length?" "+l.join(" "):""}function d(c){var l=[];for(var _ in c)n(c,_)&&l.push({name:_,value:c[_]});for(var f in c._attributes)for(var k in c._attributes[f]){var x=c._attributes[f][k],T=(x.prefix?x.prefix+":":"")+k;l.push({name:T,value:x.value})}return c.className&&l.push({name:"class",value:c.className}),l.length?u(l):""}function v(c){var l="";return typeof c=="string"?l=c:c&&(l=c.toString()),l.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function g(c){return v(c).replace(/"/g,"&quot;")}}),qt=Q(function(i,e){fe();var t=Rt(),a=Ot(),r=Pt(),n=Nt(),o=Ca(),s="http://www.w3.org/1999/xhtml";e.exports=u;function u(d,v,g){if(!pe(this,u))return new u(d);var c=g===void 0?s:g||null;this.tagName=c===s?String(d).toUpperCase():d,this.nodeName=this.tagName,this.className="",this.dataset={},this.childNodes=[],this.parentNode=null,this.style={},this.ownerDocument=v||null,this.namespaceURI=c,this._attributes={},this.tagName==="INPUT"&&(this.type="text")}u.prototype.type="DOMElement",u.prototype.nodeType=1,u.prototype.appendChild=function(d){return d.parentNode&&d.parentNode.removeChild(d),this.childNodes.push(d),d.parentNode=this,d},u.prototype.replaceChild=function(d,v){d.parentNode&&d.parentNode.removeChild(d);var g=this.childNodes.indexOf(v);return v.parentNode=null,this.childNodes[g]=d,d.parentNode=this,v},u.prototype.removeChild=function(d){var v=this.childNodes.indexOf(d);return this.childNodes.splice(v,1),d.parentNode=null,d},u.prototype.insertBefore=function(d,v){d.parentNode&&d.parentNode.removeChild(d);var g=v==null?-1:this.childNodes.indexOf(v);return g>-1?this.childNodes.splice(g,0,d):this.childNodes.push(d),d.parentNode=this,d},u.prototype.setAttributeNS=function(d,v,g){var c=null,l=v,_=v.indexOf(":");if(_>-1&&(c=v.substr(0,_),l=v.substr(_+1)),this.tagName==="INPUT"&&v==="type")this.type=g;else{var f=this._attributes[d]||(this._attributes[d]={});f[l]={value:g,prefix:c}}},u.prototype.getAttributeNS=function(d,v){var g=this._attributes[d],c=g&&g[v]&&g[v].value;return this.tagName==="INPUT"&&v==="type"?this.type:typeof c!="string"?null:c},u.prototype.removeAttributeNS=function(d,v){var g=this._attributes[d];g&&delete g[v]},u.prototype.hasAttributeNS=function(d,v){var g=this._attributes[d];return!!g&&v in g},u.prototype.setAttribute=function(d,v){return this.setAttributeNS(null,d,v)},u.prototype.getAttribute=function(d){return this.getAttributeNS(null,d)},u.prototype.removeAttribute=function(d){return this.removeAttributeNS(null,d)},u.prototype.hasAttribute=function(d){return this.hasAttributeNS(null,d)},u.prototype.removeEventListener=n,u.prototype.addEventListener=r,u.prototype.dispatchEvent=a,u.prototype.focus=function(){},u.prototype.toString=function(){return o(this)},u.prototype.getElementsByClassName=function(d){var v=d.split(" "),g=[];return t(this,function(c){if(c.nodeType===1){var l=c.className||"",_=l.split(" ");v.every(function(f){return _.indexOf(f)!==-1})&&g.push(c)}}),g},u.prototype.getElementsByTagName=function(d){d=d.toLowerCase();var v=[];return t(this.childNodes,function(g){g.nodeType===1&&(d==="*"||g.tagName.toLowerCase()===d)&&v.push(g)}),v},u.prototype.contains=function(d){return t(this,function(v){return d===v})||!1}}),Ma=Q(function(i,e){fe();var t=qt();e.exports=a;function a(r){if(!pe(this,a))return new a;this.childNodes=[],this.parentNode=null,this.ownerDocument=r||null}a.prototype.type="DocumentFragment",a.prototype.nodeType=11,a.prototype.nodeName="#document-fragment",a.prototype.appendChild=t.prototype.appendChild,a.prototype.replaceChild=t.prototype.replaceChild,a.prototype.removeChild=t.prototype.removeChild,a.prototype.toString=function(){return this.childNodes.map(function(r){return String(r)}).join("")}}),ja=Q(function(i,e){e.exports=t;function t(a){}t.prototype.initEvent=function(a,r,n){this.type=a,this.bubbles=r,this.cancelable=n},t.prototype.preventDefault=function(){}}),Ha=Q(function(i,e){fe();var t=Rt(),a=La(),r=Ia(),n=qt(),o=Ma(),s=ja(),u=Ot(),d=Pt(),v=Nt();e.exports=g;function g(){if(!pe(this,g))return new g;this.head=this.createElement("head"),this.body=this.createElement("body"),this.documentElement=this.createElement("html"),this.documentElement.appendChild(this.head),this.documentElement.appendChild(this.body),this.childNodes=[this.documentElement],this.nodeType=9}var c=g.prototype;c.createTextNode=function(l){return new r(l,this)},c.createElementNS=function(l,_){var f=l===null?null:String(l);return new n(_,this,f)},c.createElement=function(l){return new n(l,this)},c.createDocumentFragment=function(){return new o(this)},c.createEvent=function(l){return new s(l)},c.createComment=function(l){return new a(l,this)},c.getElementById=function(l){l=String(l);var _=t(this.childNodes,function(f){if(String(f.id)===l)return f});return _||null},c.getElementsByClassName=n.prototype.getElementsByClassName,c.getElementsByTagName=n.prototype.getElementsByTagName,c.contains=n.prototype.contains,c.removeEventListener=v,c.addEventListener=d,c.dispatchEvent=u}),Ua=Q(function(i,e){var t=Ha();e.exports=new t}),Lt=Q(function(i,e){var t=typeof global!="undefined"?global:typeof window!="undefined"?window:{},a=Ua(),r;typeof document!="undefined"?r=document:(r=t["__GLOBAL_DOCUMENT_CACHE@4"],r||(r=t["__GLOBAL_DOCUMENT_CACHE@4"]=a)),e.exports=r});function Ba(i){if(Array.isArray(i))return i}function Fa(i,e){var t=i==null?null:typeof Symbol!="undefined"&&i[Symbol.iterator]||i["@@iterator"];if(t!=null){var a=[],r=!0,n=!1,o,s;try{for(t=t.call(i);!(r=(o=t.next()).done)&&(a.push(o.value),!(e&&a.length===e));r=!0);}catch(u){n=!0,s=u}finally{try{!r&&t.return!=null&&t.return()}finally{if(n)throw s}}return a}}function Ja(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function at(i,e){(e==null||e>i.length)&&(e=i.length);for(var t=0,a=new Array(e);t<e;t++)a[t]=i[t];return a}function It(i,e){if(i){if(typeof i=="string")return at(i,e);var t=Object.prototype.toString.call(i).slice(8,-1);if(t==="Object"&&i.constructor&&(t=i.constructor.name),t==="Map"||t==="Set")return Array.from(t);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return at(i,e)}}function re(i,e){return Ba(i)||Fa(i,e)||It(i,e)||Ja()}var xe=z(ee()),mt=z(ee()),Ga=z(ee()),Va={now:function(){var e=Ga.default.performance,t=e&&e.timing,a=t&&t.navigationStart,r=typeof a=="number"&&typeof e.now=="function"?a+e.now():Date.now();return Math.round(r)}},B=Va,Re=function(){var e,t,a;if(typeof((e=mt.default.crypto)===null||e===void 0?void 0:e.getRandomValues)=="function"){a=new Uint8Array(32),mt.default.crypto.getRandomValues(a);for(var r=0;r<32;r++)a[r]=a[r]%16}else{a=[];for(var n=0;n<32;n++)a[n]=Math.random()*16|0}var o=0;t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(d){var v=d==="x"?a[o]:a[o]&3|8;return o++,v.toString(16)});var s=B.now(),u=s==null?void 0:s.toString(16).substring(3);return u?t.substring(0,28)+u:t},Ct=function(){return("000000"+(Math.random()*Math.pow(36,6)<<0).toString(36)).slice(-6)},X=function(e){if(e&&typeof e.nodeName!="undefined")return e.muxId||(e.muxId=Ct()),e.muxId;var t;try{t=document.querySelector(e)}catch(a){}return t&&!t.muxId&&(t.muxId=e),(t==null?void 0:t.muxId)||e},Be=function(e){var t;e&&typeof e.nodeName!="undefined"?(t=e,e=X(t)):t=document.querySelector(e);var a=t&&t.nodeName?t.nodeName.toLowerCase():"";return[t,e,a]};function Qa(i){if(Array.isArray(i))return at(i)}function Wa(i){if(typeof Symbol!="undefined"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}function za(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $(i){return Qa(i)||Wa(i)||It(i)||za()}var ve={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},Ka=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:3,a,r,n,o,s,u=e?[console,e]:[console],d=(a=console.trace).bind.apply(a,$(u)),v=(r=console.info).bind.apply(r,$(u)),g=(n=console.debug).bind.apply(n,$(u)),c=(o=console.warn).bind.apply(o,$(u)),l=(s=console.error).bind.apply(s,$(u)),_=t;return{trace:function(){for(var k=arguments.length,x=new Array(k),T=0;T<k;T++)x[T]=arguments[T];if(!(_>ve.TRACE))return d.apply(void 0,$(x))},debug:function(){for(var k=arguments.length,x=new Array(k),T=0;T<k;T++)x[T]=arguments[T];if(!(_>ve.DEBUG))return g.apply(void 0,$(x))},info:function(){for(var k=arguments.length,x=new Array(k),T=0;T<k;T++)x[T]=arguments[T];if(!(_>ve.INFO))return v.apply(void 0,$(x))},warn:function(){for(var k=arguments.length,x=new Array(k),T=0;T<k;T++)x[T]=arguments[T];if(!(_>ve.WARN))return c.apply(void 0,$(x))},error:function(){for(var k=arguments.length,x=new Array(k),T=0;T<k;T++)x[T]=arguments[T];if(!(_>ve.ERROR))return l.apply(void 0,$(x))},get level(){return _},set level(f){f!==this.level&&(_=f!=null?f:t)}}},I=Ka("[mux]"),Xe=z(ee());function rt(){var i=Xe.default.doNotTrack||Xe.default.navigator&&Xe.default.navigator.doNotTrack;return i==="1"}function D(i){if(i===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i}fe();function F(i,e){if(!pe(i,e))throw new TypeError("Cannot call a class as a function")}function yt(i,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(i,a.key,a)}}function ae(i,e,t){return e&&yt(i.prototype,e),t&&yt(i,t),i}function y(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}function be(i){return be=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},be(i)}function Ya(i,e){for(;!Object.prototype.hasOwnProperty.call(i,e)&&(i=be(i),i!==null););return i}function Ue(i,e,t){return typeof Reflect!="undefined"&&Reflect.get?Ue=Reflect.get:Ue=function(r,n,o){var s=Ya(r,n);if(s){var u=Object.getOwnPropertyDescriptor(s,n);return u.get?u.get.call(o||r):u.value}},Ue(i,e,t||i)}function it(i,e){return it=Object.setPrototypeOf||function(t,a){return t.__proto__=a,t},it(i,e)}function Xa(i,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");i.prototype=Object.create(e&&e.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),e&&it(i,e)}function $a(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(i){return!1}}St();function Za(i,e){return e&&(xt(e)==="object"||typeof e=="function")?e:D(i)}function er(i){var e=$a();return function(){var t=be(i),a;if(e){var r=be(this).constructor;a=Reflect.construct(t,arguments,r)}else a=t.apply(this,arguments);return Za(this,a)}}var Z=function(e){return Oe(e)[0]},Oe=function(e){if(typeof e!="string"||e==="")return["localhost"];var t=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,a=e.match(t)||[],r=a[4],n;return r&&(n=(r.match(/[^\.]+\.[^\.]+$/)||[])[0]),[r,n]},$e=z(ee()),tr={exists:function(){var e=$e.default.performance,t=e&&e.timing;return t!==void 0},domContentLoadedEventEnd:function(){var e=$e.default.performance,t=e&&e.timing;return t&&t.domContentLoadedEventEnd},navigationStart:function(){var e=$e.default.performance,t=e&&e.timing;return t&&t.navigationStart}},Fe=tr;function U(i,e,t){t=t===void 0?1:t,i[e]=i[e]||0,i[e]+=t}function Je(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{},a=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(t).filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),a.forEach(function(r){y(i,r,t[r])})}return i}function ar(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);e&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),t.push.apply(t,a)}return t}function ot(i,e){return e=e!=null?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):ar(Object(e)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(e,t))}),i}var rr=["x-cdn","content-type"],Mt=["x-request-id","cf-ray","x-amz-cf-id","x-akamai-request-id"],ir=rr.concat(Mt);function st(i){i=i||"";var e={},t=i.trim().split(/[\r\n]+/);return t.forEach(function(a){if(a){var r=a.split(": "),n=r.shift();n&&(ir.indexOf(n.toLowerCase())>=0||n.toLowerCase().indexOf("x-litix-")===0)&&(e[n]=r.join(": "))}}),e}function Ge(i){if(i){var e=Mt.find(function(t){return i[t]!==void 0});return e?i[e]:void 0}}var nr=function(e){var t={};for(var a in e){var r=e[a],n=r["DATA-ID"].search("io.litix.data.");if(n!==-1){var o=r["DATA-ID"].replace("io.litix.data.","");t[o]=r.VALUE}}return t},jt=nr,He=function(e){if(!e)return{};var t=Fe.navigationStart(),a=e.loading,r=a?a.start:e.trequest,n=a?a.first:e.tfirst,o=a?a.end:e.tload;return{bytesLoaded:e.total,requestStart:Math.round(t+r),responseStart:Math.round(t+n),responseEnd:Math.round(t+o)}},Ae=function(e){if(!(!e||typeof e.getAllResponseHeaders!="function"))return st(e.getAllResponseHeaders())},or=function(e,t,a){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=arguments.length>4?arguments[4]:void 0,o=e.log,s=e.utils.secondsToMs,u=function(q){var b=parseInt(n.version),m;return b===1&&q.programDateTime!==null&&(m=q.programDateTime),b===0&&q.pdt!==null&&(m=q.pdt),m};if(!Fe.exists()){o.warn("performance timing not supported. Not tracking HLS.js.");return}var d=function(q,b){return e.emit(t,q,b)},v=function(q,b){var m=b.levels,p=b.audioTracks,w=b.url,E=b.stats,S=b.networkDetails,O=b.sessionData,L={},C={};m.forEach(function(V,te){L[te]={width:V.width,height:V.height,bitrate:V.bitrate,attrs:V.attrs}}),p.forEach(function(V,te){C[te]={name:V.name,language:V.lang,bitrate:V.bitrate}});var N=He(E),R=N.bytesLoaded,Y=N.requestStart,oe=N.responseStart,se=N.responseEnd;d("requestcompleted",ot(Je({},jt(O)),{request_event_type:q,request_bytes_loaded:R,request_start:Y,request_response_start:oe,request_response_end:se,request_type:"manifest",request_hostname:Z(w),request_response_headers:Ae(S),request_rendition_lists:{media:L,audio:C,video:{}}}))};a.on(n.Events.MANIFEST_LOADED,v);var g=function(q,b){var m=b.details,p=b.level,w=b.networkDetails,E=b.stats,S=He(E),O=S.bytesLoaded,L=S.requestStart,C=S.responseStart,N=S.responseEnd,R=m.fragments[m.fragments.length-1],Y=u(R)+s(R.duration);d("requestcompleted",{request_event_type:q,request_bytes_loaded:O,request_start:L,request_response_start:C,request_response_end:N,request_current_level:p,request_type:"manifest",request_hostname:Z(m.url),request_response_headers:Ae(w),video_holdback:m.holdBack&&s(m.holdBack),video_part_holdback:m.partHoldBack&&s(m.partHoldBack),video_part_target_duration:m.partTarget&&s(m.partTarget),video_target_duration:m.targetduration&&s(m.targetduration),video_source_is_live:m.live,player_manifest_newest_program_time:isNaN(Y)?void 0:Y})};a.on(n.Events.LEVEL_LOADED,g);var c=function(q,b){var m=b.details,p=b.networkDetails,w=b.stats,E=He(w),S=E.bytesLoaded,O=E.requestStart,L=E.responseStart,C=E.responseEnd;d("requestcompleted",{request_event_type:q,request_bytes_loaded:S,request_start:O,request_response_start:L,request_response_end:C,request_type:"manifest",request_hostname:Z(m.url),request_response_headers:Ae(p)})};a.on(n.Events.AUDIO_TRACK_LOADED,c);var l=function(q,b){var m=b.stats,p=b.networkDetails,w=b.frag;m=m||w.stats;var E=He(m),S=E.bytesLoaded,O=E.requestStart,L=E.responseStart,C=E.responseEnd,N=p?Ae(p):void 0,R={request_event_type:q,request_bytes_loaded:S,request_start:O,request_response_start:L,request_response_end:C,request_hostname:p?Z(p.responseURL):void 0,request_id:N?Ge(N):void 0,request_response_headers:N,request_media_duration:w.duration,request_url:p==null?void 0:p.responseURL};w.type==="main"?(R.request_type="media",R.request_current_level=w.level,R.request_video_width=(a.levels[w.level]||{}).width,R.request_video_height=(a.levels[w.level]||{}).height,R.request_labeled_bitrate=(a.levels[w.level]||{}).bitrate):R.request_type=w.type,d("requestcompleted",R)};a.on(n.Events.FRAG_LOADED,l);var _=function(q,b){var m=b.frag,p=m.start,w=u(m),E={currentFragmentPDT:w,currentFragmentStart:s(p)};d("fragmentchange",E)};a.on(n.Events.FRAG_CHANGED,_);var f=function(q,b){var m=b.type,p=b.details,w=b.response,E=b.fatal,S=b.frag,O=b.networkDetails,L=(S==null?void 0:S.url)||b.url||"",C=O?Ae(O):void 0;if((p===n.ErrorDetails.MANIFEST_LOAD_ERROR||p===n.ErrorDetails.MANIFEST_LOAD_TIMEOUT||p===n.ErrorDetails.FRAG_LOAD_ERROR||p===n.ErrorDetails.FRAG_LOAD_TIMEOUT||p===n.ErrorDetails.LEVEL_LOAD_ERROR||p===n.ErrorDetails.LEVEL_LOAD_TIMEOUT||p===n.ErrorDetails.AUDIO_TRACK_LOAD_ERROR||p===n.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT||p===n.ErrorDetails.SUBTITLE_LOAD_ERROR||p===n.ErrorDetails.SUBTITLE_LOAD_TIMEOUT||p===n.ErrorDetails.KEY_LOAD_ERROR||p===n.ErrorDetails.KEY_LOAD_TIMEOUT)&&d("requestfailed",{request_error:p,request_url:L,request_hostname:Z(L),request_id:C?Ge(C):void 0,request_type:p===n.ErrorDetails.FRAG_LOAD_ERROR||p===n.ErrorDetails.FRAG_LOAD_TIMEOUT?"media":p===n.ErrorDetails.AUDIO_TRACK_LOAD_ERROR||p===n.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT?"audio":p===n.ErrorDetails.SUBTITLE_LOAD_ERROR||p===n.ErrorDetails.SUBTITLE_LOAD_TIMEOUT?"subtitle":p===n.ErrorDetails.KEY_LOAD_ERROR||p===n.ErrorDetails.KEY_LOAD_TIMEOUT?"encryption":"manifest",request_error_code:w==null?void 0:w.code,request_error_text:w==null?void 0:w.text}),E){var N,R="".concat(L?"url: ".concat(L,"\n"):"")+"".concat(w&&(w.code||w.text)?"response: ".concat(w.code,", ").concat(w.text,"\n"):"")+"".concat(b.reason?"failure reason: ".concat(b.reason,"\n"):"")+"".concat(b.level?"level: ".concat(b.level,"\n"):"")+"".concat(b.parent?"parent stream controller: ".concat(b.parent,"\n"):"")+"".concat(b.buffer?"buffer length: ".concat(b.buffer,"\n"):"")+"".concat(b.error?"error: ".concat(b.error,"\n"):"")+"".concat(b.event?"event: ".concat(b.event,"\n"):"")+"".concat(b.err?"error message: ".concat((N=b.err)===null||N===void 0?void 0:N.message,"\n"):"");d("error",{player_error_code:m,player_error_message:p,player_error_context:R})}};a.on(n.Events.ERROR,f);var k=function(q,b){var m=b.frag,p=m&&m._url||"";d("requestcanceled",{request_event_type:q,request_url:p,request_type:"media",request_hostname:Z(p)})};a.on(n.Events.FRAG_LOAD_EMERGENCY_ABORTED,k);var x=function(q,b){var m=b.level,p=a.levels[m];if(p&&p.attrs&&p.attrs.BANDWIDTH){var w=p.attrs.BANDWIDTH,E,S=parseFloat(p.attrs["FRAME-RATE"]);isNaN(S)||(E=S),w?d("renditionchange",{video_source_fps:E,video_source_bitrate:w,video_source_width:p.width,video_source_height:p.height,video_source_rendition_name:p.name,video_source_codec:p==null?void 0:p.videoCodec}):o.warn("missing BANDWIDTH from HLS manifest parsed by HLS.js")}};a.on(n.Events.LEVEL_SWITCHED,x),a._stopMuxMonitor=function(){a.off(n.Events.MANIFEST_LOADED,v),a.off(n.Events.LEVEL_LOADED,g),a.off(n.Events.AUDIO_TRACK_LOADED,c),a.off(n.Events.FRAG_LOADED,l),a.off(n.Events.FRAG_CHANGED,_),a.off(n.Events.ERROR,f),a.off(n.Events.FRAG_LOAD_EMERGENCY_ABORTED,k),a.off(n.Events.LEVEL_SWITCHED,x),a.off(n.Events.DESTROYING,a._stopMuxMonitor),delete a._stopMuxMonitor},a.on(n.Events.DESTROYING,a._stopMuxMonitor)},sr=function(e){e&&typeof e._stopMuxMonitor=="function"&&e._stopMuxMonitor()},gt=function(e,t){if(!e||!e.requestEndDate)return{};var a=Z(e.url),r=e.url,n=e.bytesLoaded,o=new Date(e.requestStartDate).getTime(),s=new Date(e.firstByteDate).getTime(),u=new Date(e.requestEndDate).getTime(),d=isNaN(e.duration)?0:e.duration,v=typeof t.getMetricsFor=="function"?t.getMetricsFor(e.mediaType).HttpList:t.getDashMetrics().getHttpRequests(e.mediaType),g;v.length>0&&(g=st(v[v.length-1]._responseHeaders||""));var c=g?Ge(g):void 0;return{requestStart:o,requestResponseStart:s,requestResponseEnd:u,requestBytesLoaded:n,requestResponseHeaders:g,requestMediaDuration:d,requestHostname:a,requestUrl:r,requestId:c}},ur=function(e,t){var a=t.getQualityFor(e),r=t.getCurrentTrackFor(e).bitrateList;return r?{currentLevel:a,renditionWidth:r[a].width||null,renditionHeight:r[a].height||null,renditionBitrate:r[a].bandwidth}:{}},dr=function(e){var t;return(t=e.match(/.*codecs\*?="(.*)"/))===null||t===void 0?void 0:t[1]},lr=function(e){try{var t,a,r=(a=e.getVersion)===null||a===void 0||(t=a.call(e))===null||t===void 0?void 0:t.split(".").map(function(n){return parseInt(n)})[0];return r}catch(n){return!1}},cr=function(e,t,a){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},n=e.log;if(!a||!a.on){n.warn("Invalid dash.js player reference. Monitoring blocked.");return}var o=lr(a),s=function(m,p){return e.emit(t,m,p)},u=function(m){var p=m.type,w=m.data,E=(w||{}).url;s("requestcompleted",{request_event_type:p,request_start:0,request_response_start:0,request_response_end:0,request_bytes_loaded:-1,request_type:"manifest",request_hostname:Z(E),request_url:E})};a.on("manifestLoaded",u);var d={},v=function(m){if(typeof m.getRequests!="function")return null;var p=m.getRequests({state:"executed"});return p.length===0?null:p[p.length-1]},g=function(m){var p=m.type,w=m.fragmentModel,E=m.chunk,S=v(w);c({type:p,request:S,chunk:E})},c=function(m){var p=m.type,w=m.chunk,E=m.request,S=(w||{}).mediaInfo,O=S||{},L=O.type,C=O.bitrateList;C=C||[];var N={};C.forEach(function(ue,W){N[W]={},N[W].width=ue.width,N[W].height=ue.height,N[W].bitrate=ue.bandwidth,N[W].attrs={}}),L==="video"?d.video=N:L==="audio"?d.audio=N:d.media=N;var R=gt(E,a),Y=R.requestStart,oe=R.requestResponseStart,se=R.requestResponseEnd,V=R.requestResponseHeaders,te=R.requestMediaDuration,he=R.requestHostname,me=R.requestUrl,ye=R.requestId;s("requestcompleted",{request_event_type:p,request_start:Y,request_response_start:oe,request_response_end:se,request_bytes_loaded:-1,request_type:L+"_init",request_response_headers:V,request_hostname:he,request_id:ye,request_url:me,request_media_duration:te,request_rendition_lists:d})};o>=4?a.on("initFragmentLoaded",c):a.on("initFragmentLoaded",g);var l=function(m){var p=m.type,w=m.fragmentModel,E=m.chunk,S=v(w);_({type:p,request:S,chunk:E})},_=function(m){var p=m.type,w=m.chunk,E=m.request,S=w||{},O=S.mediaInfo,L=S.start,C=O||{},N=C.type,R=gt(E,a),Y=R.requestStart,oe=R.requestResponseStart,se=R.requestResponseEnd,V=R.requestBytesLoaded,te=R.requestResponseHeaders,he=R.requestMediaDuration,me=R.requestHostname,ye=R.requestUrl,ue=R.requestId,W=ur(N,a),Pe=W.currentLevel,Ne=W.renditionWidth,qe=W.renditionHeight,Le=W.renditionBitrate;s("requestcompleted",{request_event_type:p,request_start:Y,request_response_start:oe,request_response_end:se,request_bytes_loaded:V,request_type:N,request_response_headers:te,request_hostname:me,request_id:ue,request_url:ye,request_media_start_time:L,request_media_duration:he,request_current_level:Pe,request_labeled_bitrate:Le,request_video_width:Ne,request_video_height:qe})};o>=4?a.on("mediaFragmentLoaded",_):a.on("mediaFragmentLoaded",l);var f={video:void 0,audio:void 0,totalBitrate:void 0},k=function(){if(f.video&&typeof f.video.bitrate=="number"){if(!(f.video.width&&f.video.height)){n.warn("have bitrate info for video but missing width/height");return}var m=f.video.bitrate;if(f.audio&&typeof f.audio.bitrate=="number"&&(m+=f.audio.bitrate),m!==f.totalBitrate)return f.totalBitrate=m,{video_source_bitrate:m,video_source_height:f.video.height,video_source_width:f.video.width,video_source_codec:dr(f.video.codec)}}},x=function(m,p,w){if(typeof m.newQuality!="number"){n.warn("missing evt.newQuality in qualityChangeRendered event",m);return}var E=m.mediaType;if(E==="audio"||E==="video"){var S=a.getBitrateInfoListFor(E).find(function(L){var C=L.qualityIndex;return C===m.newQuality});if(!(S&&typeof S.bitrate=="number")){n.warn("missing bitrate info for ".concat(E));return}f[E]=ot(Je({},S),{codec:a.getCurrentTrackFor(E).codec});var O=k();O&&s("renditionchange",O)}};a.on("qualityChangeRendered",x);var T=function(m){var p=m.request,w=m.mediaType;p=p||{},s("requestcanceled",{request_event_type:p.type+"_"+p.action,request_url:p.url,request_type:w,request_hostname:Z(p.url)})};a.on("fragmentLoadingAbandoned",T);var q=function(m){var p=m.error,w,E,S=(p==null||(w=p.data)===null||w===void 0?void 0:w.request)||{},O=(p==null||(E=p.data)===null||E===void 0?void 0:E.response)||{};(p==null?void 0:p.code)===27&&s("requestfailed",{request_error:S.type+"_"+S.action,request_url:S.url,request_hostname:Z(S.url),request_type:S.mediaType,request_error_code:O.status,request_error_text:O.statusText});var L="".concat(S!=null&&S.url?"url: ".concat(S.url,"\n"):"")+"".concat(O!=null&&O.status||O!=null&&O.statusText?"response: ".concat(O==null?void 0:O.status,", ").concat(O==null?void 0:O.statusText,"\n"):"");s("error",{player_error_code:p==null?void 0:p.code,player_error_message:p==null?void 0:p.message,player_error_context:L})};a.on("error",q),a._stopMuxMonitor=function(){a.off("manifestLoaded",u),a.off("initFragmentLoaded",c),a.off("mediaFragmentLoaded",_),a.off("qualityChangeRendered",x),a.off("error",q),a.off("fragmentLoadingAbandoned",T),delete a._stopMuxMonitor}},vr=function(e){e&&typeof e._stopMuxMonitor=="function"&&e._stopMuxMonitor()},bt=0,_r=function(){"use strict";function i(){F(this,i),y(this,"_listeners",void 0)}return ae(i,[{key:"on",value:function(t,a,r){return a._eventEmitterGuid=a._eventEmitterGuid||++bt,this._listeners=this._listeners||{},this._listeners[t]=this._listeners[t]||[],r&&(a=a.bind(r)),this._listeners[t].push(a),a}},{key:"off",value:function(t,a){var r=this._listeners&&this._listeners[t];r&&r.forEach(function(n,o){n._eventEmitterGuid===a._eventEmitterGuid&&r.splice(o,1)})}},{key:"one",value:function(t,a,r){var n=this;a._eventEmitterGuid=a._eventEmitterGuid||++bt;var o=function(){n.off(t,o),a.apply(r||this,arguments)};o._eventEmitterGuid=a._eventEmitterGuid,this.on(t,o)}},{key:"emit",value:function(t,a){var r=this;if(this._listeners){a=a||{};var n=this._listeners["before*"]||[],o=this._listeners[t]||[],s=this._listeners["after"+t]||[],u=function(v,g){v=v.slice(),v.forEach(function(c){c.call(r,{type:t},g)})};u(n,a),u(o,a),u(s,a)}}}]),i}(),pr=_r,Ze=z(ee()),fr=function(){"use strict";function i(e){var t=this;F(this,i),y(this,"_playbackHeartbeatInterval",void 0),y(this,"_playheadShouldBeProgressing",void 0),y(this,"pm",void 0),this.pm=e,this._playbackHeartbeatInterval=null,this._playheadShouldBeProgressing=!1,e.on("playing",function(){t._playheadShouldBeProgressing=!0}),e.on("play",this._startPlaybackHeartbeatInterval.bind(this)),e.on("playing",this._startPlaybackHeartbeatInterval.bind(this)),e.on("adbreakstart",this._startPlaybackHeartbeatInterval.bind(this)),e.on("adplay",this._startPlaybackHeartbeatInterval.bind(this)),e.on("adplaying",this._startPlaybackHeartbeatInterval.bind(this)),e.on("devicewake",this._startPlaybackHeartbeatInterval.bind(this)),e.on("viewstart",this._startPlaybackHeartbeatInterval.bind(this)),e.on("rebufferstart",this._startPlaybackHeartbeatInterval.bind(this)),e.on("pause",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("ended",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("viewend",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("error",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("aderror",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("adpause",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("adended",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("adbreakend",this._stopPlaybackHeartbeatInterval.bind(this)),e.on("seeked",function(){e.data.player_is_paused?t._stopPlaybackHeartbeatInterval():t._startPlaybackHeartbeatInterval()}),e.on("timeupdate",function(){t._playbackHeartbeatInterval!==null&&e.emit("playbackheartbeat")}),e.on("devicesleep",function(a,r){t._playbackHeartbeatInterval!==null&&(Ze.default.clearInterval(t._playbackHeartbeatInterval),e.emit("playbackheartbeatend",{viewer_time:r.viewer_time}),t._playbackHeartbeatInterval=null)})}return ae(i,[{key:"_startPlaybackHeartbeatInterval",value:function(){var t=this;this._playbackHeartbeatInterval===null&&(this.pm.emit("playbackheartbeat"),this._playbackHeartbeatInterval=Ze.default.setInterval(function(){t.pm.emit("playbackheartbeat")},this.pm.playbackHeartbeatTime))}},{key:"_stopPlaybackHeartbeatInterval",value:function(){this._playheadShouldBeProgressing=!1,this._playbackHeartbeatInterval!==null&&(Ze.default.clearInterval(this._playbackHeartbeatInterval),this.pm.emit("playbackheartbeatend"),this._playbackHeartbeatInterval=null)}}]),i}(),hr=fr,mr=function i(e){"use strict";var t=this;F(this,i),y(this,"viewErrored",void 0),e.on("viewinit",function(){t.viewErrored=!1}),e.on("error",function(a,r){try{var n=e.errorTranslator({player_error_code:r.player_error_code,player_error_message:r.player_error_message,player_error_context:r.player_error_context,player_error_severity:r.player_error_severity,player_error_business_exception:r.player_error_business_exception});n&&(e.data.player_error_code=n.player_error_code||r.player_error_code,e.data.player_error_message=n.player_error_message||r.player_error_message,e.data.player_error_context=n.player_error_context||r.player_error_context,e.data.player_error_severity=n.player_error_severity||r.player_error_severity,e.data.player_error_business_exception=n.player_error_business_exception||r.player_error_business_exception,t.viewErrored=!0)}catch(o){e.mux.log.warn("Exception in error translator callback.",o),t.viewErrored=!0}}),e.on("aftererror",function(){var a,r,n,o,s;(a=e.data)===null||a===void 0||delete a.player_error_code,(r=e.data)===null||r===void 0||delete r.player_error_message,(n=e.data)===null||n===void 0||delete n.player_error_context,(o=e.data)===null||o===void 0||delete o.player_error_severity,(s=e.data)===null||s===void 0||delete s.player_error_business_exception})},yr=mr,gr=function(){"use strict";function i(e){F(this,i),y(this,"_watchTimeTrackerLastCheckedTime",void 0),y(this,"pm",void 0),this.pm=e,this._watchTimeTrackerLastCheckedTime=null,e.on("playbackheartbeat",this._updateWatchTime.bind(this)),e.on("playbackheartbeatend",this._clearWatchTimeState.bind(this))}return ae(i,[{key:"_updateWatchTime",value:function(t,a){var r=a.viewer_time;this._watchTimeTrackerLastCheckedTime===null&&(this._watchTimeTrackerLastCheckedTime=r),U(this.pm.data,"view_watch_time",r-this._watchTimeTrackerLastCheckedTime),this._watchTimeTrackerLastCheckedTime=r}},{key:"_clearWatchTimeState",value:function(t,a){this._updateWatchTime(t,a),this._watchTimeTrackerLastCheckedTime=null}}]),i}(),br=gr,Tr=function(){"use strict";function i(e){var t=this;F(this,i),y(this,"_playbackTimeTrackerLastPlayheadPosition",void 0),y(this,"_lastTime",void 0),y(this,"_isAdPlaying",void 0),y(this,"_callbackUpdatePlaybackTime",void 0),y(this,"pm",void 0),this.pm=e,this._playbackTimeTrackerLastPlayheadPosition=-1,this._lastTime=B.now(),this._isAdPlaying=!1,this._callbackUpdatePlaybackTime=null;var a=this._startPlaybackTimeTracking.bind(this);e.on("playing",a),e.on("adplaying",a),e.on("seeked",a);var r=this._stopPlaybackTimeTracking.bind(this);e.on("playbackheartbeatend",r),e.on("seeking",r),e.on("adplaying",function(){t._isAdPlaying=!0}),e.on("adended",function(){t._isAdPlaying=!1}),e.on("adpause",function(){t._isAdPlaying=!1}),e.on("adbreakstart",function(){t._isAdPlaying=!1}),e.on("adbreakend",function(){t._isAdPlaying=!1}),e.on("adplay",function(){t._isAdPlaying=!1}),e.on("viewinit",function(){t._playbackTimeTrackerLastPlayheadPosition=-1,t._lastTime=B.now(),t._isAdPlaying=!1,t._callbackUpdatePlaybackTime=null})}return ae(i,[{key:"_startPlaybackTimeTracking",value:function(){this._callbackUpdatePlaybackTime===null&&(this._callbackUpdatePlaybackTime=this._updatePlaybackTime.bind(this),this._playbackTimeTrackerLastPlayheadPosition=this.pm.data.player_playhead_time,this.pm.on("playbackheartbeat",this._callbackUpdatePlaybackTime))}},{key:"_stopPlaybackTimeTracking",value:function(){this._callbackUpdatePlaybackTime&&(this._updatePlaybackTime(),this.pm.off("playbackheartbeat",this._callbackUpdatePlaybackTime),this._callbackUpdatePlaybackTime=null,this._playbackTimeTrackerLastPlayheadPosition=-1)}},{key:"_updatePlaybackTime",value:function(){var t=this.pm.data.player_playhead_time,a=B.now(),r=-1;this._playbackTimeTrackerLastPlayheadPosition>=0&&t>this._playbackTimeTrackerLastPlayheadPosition?r=t-this._playbackTimeTrackerLastPlayheadPosition:this._isAdPlaying&&(r=a-this._lastTime),r>0&&r<=1e3&&U(this.pm.data,"view_content_playback_time",r),this._playbackTimeTrackerLastPlayheadPosition=t,this._lastTime=a}}]),i}(),Er=Tr,wr=function(){"use strict";function i(e){F(this,i),y(this,"pm",void 0),this.pm=e;var t=this._updatePlayheadTime.bind(this);e.on("playbackheartbeat",t),e.on("playbackheartbeatend",t),e.on("timeupdate",t),e.on("destroy",function(){e.off("timeupdate",t)})}return ae(i,[{key:"_updateMaxPlayheadPosition",value:function(){this.pm.data.view_max_playhead_position=typeof this.pm.data.view_max_playhead_position=="undefined"?this.pm.data.player_playhead_time:Math.max(this.pm.data.view_max_playhead_position,this.pm.data.player_playhead_time)}},{key:"_updatePlayheadTime",value:function(t,a){var r=this,n=function(){r.pm.currentFragmentPDT&&r.pm.currentFragmentStart&&(r.pm.data.player_program_time=r.pm.currentFragmentPDT+r.pm.data.player_playhead_time-r.pm.currentFragmentStart)};if(a&&a.player_playhead_time)this.pm.data.player_playhead_time=a.player_playhead_time,n(),this._updateMaxPlayheadPosition();else if(this.pm.getPlayheadTime){var o=this.pm.getPlayheadTime();typeof o!="undefined"&&(this.pm.data.player_playhead_time=o,n(),this._updateMaxPlayheadPosition())}}}]),i}(),Dr=wr,Tt=5*60*1e3,Ar=function i(e){"use strict";if(F(this,i),!e.disableRebufferTracking){var t,a=function(o,s){r(s),t=void 0},r=function(o){if(t){var s=o.viewer_time-t;U(e.data,"view_rebuffer_duration",s),t=o.viewer_time,e.data.view_rebuffer_duration>Tt&&(e.emit("viewend"),e.send("viewend"),e.mux.log.warn("Ending view after rebuffering for longer than ".concat(Tt,"ms, future events will be ignored unless a programchange or videochange occurs.")))}e.data.view_watch_time>=0&&e.data.view_rebuffer_count>0&&(e.data.view_rebuffer_frequency=e.data.view_rebuffer_count/e.data.view_watch_time,e.data.view_rebuffer_percentage=e.data.view_rebuffer_duration/e.data.view_watch_time)};e.on("playbackheartbeat",function(n,o){return r(o)}),e.on("rebufferstart",function(n,o){t||(U(e.data,"view_rebuffer_count",1),t=o.viewer_time,e.one("rebufferend",a))}),e.on("viewinit",function(){t=void 0,e.off("rebufferend",a)})}},kr=Ar,xr=function(){"use strict";function i(e){var t=this;F(this,i),y(this,"_lastCheckedTime",void 0),y(this,"_lastPlayheadTime",void 0),y(this,"_lastPlayheadTimeUpdatedTime",void 0),y(this,"_rebuffering",void 0),y(this,"pm",void 0),this.pm=e,!(e.disableRebufferTracking||e.disablePlayheadRebufferTracking)&&(this._lastCheckedTime=null,this._lastPlayheadTime=null,this._lastPlayheadTimeUpdatedTime=null,e.on("playbackheartbeat",this._checkIfRebuffering.bind(this)),e.on("playbackheartbeatend",this._cleanupRebufferTracker.bind(this)),e.on("seeking",function(){t._cleanupRebufferTracker(null,{viewer_time:B.now()})}))}return ae(i,[{key:"_checkIfRebuffering",value:function(t,a){if(this.pm.seekingTracker.isSeeking||this.pm.adTracker.isAdBreak||!this.pm.playbackHeartbeat._playheadShouldBeProgressing){this._cleanupRebufferTracker(t,a);return}if(this._lastCheckedTime===null){this._prepareRebufferTrackerState(a.viewer_time);return}if(this._lastPlayheadTime!==this.pm.data.player_playhead_time){this._cleanupRebufferTracker(t,a,!0);return}var r=a.viewer_time-this._lastPlayheadTimeUpdatedTime;typeof this.pm.sustainedRebufferThreshold=="number"&&r>=this.pm.sustainedRebufferThreshold&&(this._rebuffering||(this._rebuffering=!0,this.pm.emit("rebufferstart",{viewer_time:this._lastPlayheadTimeUpdatedTime}))),this._lastCheckedTime=a.viewer_time}},{key:"_clearRebufferTrackerState",value:function(){this._lastCheckedTime=null,this._lastPlayheadTime=null,this._lastPlayheadTimeUpdatedTime=null}},{key:"_prepareRebufferTrackerState",value:function(t){this._lastCheckedTime=t,this._lastPlayheadTime=this.pm.data.player_playhead_time,this._lastPlayheadTimeUpdatedTime=t}},{key:"_cleanupRebufferTracker",value:function(t,a){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this._rebuffering)this._rebuffering=!1,this.pm.emit("rebufferend",{viewer_time:a.viewer_time});else{if(this._lastCheckedTime===null)return;var n=this.pm.data.player_playhead_time-this._lastPlayheadTime,o=a.viewer_time-this._lastPlayheadTimeUpdatedTime;typeof this.pm.minimumRebufferDuration=="number"&&n>0&&o-n>this.pm.minimumRebufferDuration&&(this._lastCheckedTime=null,this.pm.emit("rebufferstart",{viewer_time:this._lastPlayheadTimeUpdatedTime}),this.pm.emit("rebufferend",{viewer_time:this._lastPlayheadTimeUpdatedTime+o-n}))}r?this._prepareRebufferTrackerState(a.viewer_time):this._clearRebufferTrackerState()}}]),i}(),Sr=xr,Rr=function(){"use strict";function i(e){var t=this;F(this,i),y(this,"NAVIGATION_START",void 0),y(this,"pm",void 0),this.pm=e,e.on("viewinit",function(){var a=e.data,r=a.view_id;if(!a.view_program_changed){var n=function(s,u){var d=u.viewer_time;(s.type==="playing"&&typeof e.data.view_time_to_first_frame=="undefined"||s.type==="adplaying"&&(typeof e.data.view_time_to_first_frame=="undefined"||t._inPrerollPosition()))&&t.calculateTimeToFirstFrame(d||B.now(),r)};e.one("playing",n),e.one("adplaying",n),e.one("viewend",function(){e.off("playing",n),e.off("adplaying",n)})}})}return ae(i,[{key:"_inPrerollPosition",value:function(){return typeof this.pm.data.view_content_playback_time=="undefined"||this.pm.data.view_content_playback_time<=1e3}},{key:"calculateTimeToFirstFrame",value:function(t,a){a===this.pm.data.view_id&&(this.pm.watchTimeTracker._updateWatchTime(null,{viewer_time:t}),this.pm.data.view_time_to_first_frame=this.pm.data.view_watch_time,(this.pm.data.player_autoplay_on||this.pm.data.video_is_autoplay)&&this.NAVIGATION_START&&(this.pm.data.view_aggregate_startup_time=this.pm.data.view_start+this.pm.data.view_watch_time-this.NAVIGATION_START))}}]),i}(),Or=Rr,Pr=function i(e){"use strict";var t=this;F(this,i),y(this,"_lastPlayerHeight",void 0),y(this,"_lastPlayerWidth",void 0),y(this,"_lastPlayheadPosition",void 0),y(this,"_lastSourceHeight",void 0),y(this,"_lastSourceWidth",void 0),e.on("viewinit",function(){t._lastPlayheadPosition=-1});var a=["pause","rebufferstart","seeking","error","adbreakstart","hb","renditionchange","orientationchange","viewend"],r=["playing","hb","renditionchange","orientationchange"];a.forEach(function(n){e.on(n,function(){if(t._lastPlayheadPosition>=0&&e.data.player_playhead_time>=0&&t._lastPlayerWidth>=0&&t._lastSourceWidth>0&&t._lastPlayerHeight>=0&&t._lastSourceHeight>0){var o=e.data.player_playhead_time-t._lastPlayheadPosition;if(o<0){t._lastPlayheadPosition=-1;return}var s=Math.min(t._lastPlayerWidth/t._lastSourceWidth,t._lastPlayerHeight/t._lastSourceHeight),u=Math.max(0,s-1),d=Math.max(0,1-s);e.data.view_max_upscale_percentage=Math.max(e.data.view_max_upscale_percentage||0,u),e.data.view_max_downscale_percentage=Math.max(e.data.view_max_downscale_percentage||0,d),U(e.data,"view_total_content_playback_time",o),U(e.data,"view_total_upscaling",u*o),U(e.data,"view_total_downscaling",d*o)}t._lastPlayheadPosition=-1})}),r.forEach(function(n){e.on(n,function(){t._lastPlayheadPosition=e.data.player_playhead_time,t._lastPlayerWidth=e.data.player_width,t._lastPlayerHeight=e.data.player_height,t._lastSourceWidth=e.data.video_source_width,t._lastSourceHeight=e.data.video_source_height})})},Nr=Pr,qr=2e3,Lr=function i(e){"use strict";var t=this;F(this,i),y(this,"isSeeking",void 0),this.isSeeking=!1;var a=-1,r=function(){var o=B.now(),s=(e.data.viewer_time||o)-(a||o);U(e.data,"view_seek_duration",s),e.data.view_max_seek_time=Math.max(e.data.view_max_seek_time||0,s),t.isSeeking=!1,a=-1};e.on("seeking",function(n,o){if(Object.assign(e.data,o),t.isSeeking&&o.viewer_time-a<=qr){a=o.viewer_time;return}t.isSeeking&&r(),t.isSeeking=!0,a=o.viewer_time,U(e.data,"view_seek_count",1),e.send("seeking")}),e.on("seeked",function(){r()}),e.on("viewend",function(){t.isSeeking&&(r(),e.send("seeked")),t.isSeeking=!1,a=-1})},Ir=Lr,Et=function(e,t){e.push(t),e.sort(function(a,r){return a.viewer_time-r.viewer_time})},Cr=["adbreakstart","adrequest","adresponse","adplay","adplaying","adpause","adended","adbreakend","aderror","adclicked","adskipped"],Mr=function(){"use strict";function i(e){var t=this;F(this,i),y(this,"_adHasPlayed",void 0),y(this,"_adRequests",void 0),y(this,"_adResponses",void 0),y(this,"_currentAdRequestNumber",void 0),y(this,"_currentAdResponseNumber",void 0),y(this,"_prerollPlayTime",void 0),y(this,"_wouldBeNewAdPlay",void 0),y(this,"isAdBreak",void 0),y(this,"pm",void 0),this.pm=e,e.on("viewinit",function(){t.isAdBreak=!1,t._currentAdRequestNumber=0,t._currentAdResponseNumber=0,t._adRequests=[],t._adResponses=[],t._adHasPlayed=!1,t._wouldBeNewAdPlay=!0,t._prerollPlayTime=void 0}),Cr.forEach(function(r){return e.on(r,t._updateAdData.bind(t))});var a=function(){t.isAdBreak=!1};e.on("adbreakstart",function(){t.isAdBreak=!0}),e.on("play",a),e.on("playing",a),e.on("viewend",a),e.on("adrequest",function(r,n){n=Object.assign({ad_request_id:"generatedAdRequestId"+t._currentAdRequestNumber++},n),Et(t._adRequests,n),U(e.data,"view_ad_request_count"),t.inPrerollPosition()&&(e.data.view_preroll_requested=!0,t._adHasPlayed||U(e.data,"view_preroll_request_count"))}),e.on("adresponse",function(r,n){n=Object.assign({ad_request_id:"generatedAdRequestId"+t._currentAdResponseNumber++},n),Et(t._adResponses,n);var o=t.findAdRequest(n.ad_request_id);o&&U(e.data,"view_ad_request_time",Math.max(0,n.viewer_time-o.viewer_time))}),e.on("adplay",function(r,n){t._adHasPlayed=!0,t._wouldBeNewAdPlay&&(t._wouldBeNewAdPlay=!1,U(e.data,"view_ad_played_count")),t.inPrerollPosition()&&!e.data.view_preroll_played&&(e.data.view_preroll_played=!0,t._adRequests.length>0&&(e.data.view_preroll_request_time=Math.max(0,n.viewer_time-t._adRequests[0].viewer_time)),e.data.view_start&&(e.data.view_startup_preroll_request_time=Math.max(0,n.viewer_time-e.data.view_start)),t._prerollPlayTime=n.viewer_time)}),e.on("adplaying",function(r,n){t.inPrerollPosition()&&typeof e.data.view_preroll_load_time=="undefined"&&typeof t._prerollPlayTime!="undefined"&&(e.data.view_preroll_load_time=n.viewer_time-t._prerollPlayTime,e.data.view_startup_preroll_load_time=n.viewer_time-t._prerollPlayTime)}),e.on("adclicked",function(r,n){t._wouldBeNewAdPlay||U(e.data,"view_ad_clicked_count")}),e.on("adskipped",function(r,n){t._wouldBeNewAdPlay||U(e.data,"view_ad_skipped_count")}),e.on("adended",function(){t._wouldBeNewAdPlay=!0}),e.on("aderror",function(){t._wouldBeNewAdPlay=!0})}return ae(i,[{key:"inPrerollPosition",value:function(){return typeof this.pm.data.view_content_playback_time=="undefined"||this.pm.data.view_content_playback_time<=1e3}},{key:"findAdRequest",value:function(t){for(var a=0;a<this._adRequests.length;a++)if(this._adRequests[a].ad_request_id===t)return this._adRequests[a]}},{key:"_updateAdData",value:function(t,a){if(this.inPrerollPosition()){if(!this.pm.data.view_preroll_ad_tag_hostname&&a.ad_tag_url){var r=re(Oe(a.ad_tag_url),2),n=r[0],o=r[1];this.pm.data.view_preroll_ad_tag_domain=o,this.pm.data.view_preroll_ad_tag_hostname=n}if(!this.pm.data.view_preroll_ad_asset_hostname&&a.ad_asset_url){var s=re(Oe(a.ad_asset_url),2),u=s[0],d=s[1];this.pm.data.view_preroll_ad_asset_domain=d,this.pm.data.view_preroll_ad_asset_hostname=u}}this.pm.data.ad_asset_url=a==null?void 0:a.ad_asset_url,this.pm.data.ad_tag_url=a==null?void 0:a.ad_tag_url,this.pm.data.ad_creative_id=a==null?void 0:a.ad_creative_id,this.pm.data.ad_id=a==null?void 0:a.ad_id,this.pm.data.ad_universal_id=a==null?void 0:a.ad_universal_id}}]),i}(),jr=Mr,wt=z(ee()),Hr=function i(e){"use strict";F(this,i);var t,a,r=function(){e.disableRebufferTracking||(U(e.data,"view_waiting_rebuffer_count",1),t=B.now(),a=wt.default.setInterval(function(){if(t){var v=B.now();U(e.data,"view_waiting_rebuffer_duration",v-t),t=v}},250))},n=function(){e.disableRebufferTracking||t&&(U(e.data,"view_waiting_rebuffer_duration",B.now()-t),t=!1,wt.default.clearInterval(a))},o=!1,s=function(){o=!0},u=function(){o=!1,n()};e.on("waiting",function(){o&&r()}),e.on("playing",function(){n(),s()}),e.on("pause",u),e.on("seeking",u)},Ur=Hr,Br=function i(e){"use strict";var t=this;F(this,i),y(this,"lastWallClockTime",void 0);var a=function(){t.lastWallClockTime=B.now(),e.on("before*",r)},r=function(o){var s=B.now(),u=t.lastWallClockTime;t.lastWallClockTime=s,s-u>3e4&&(e.emit("devicesleep",{viewer_time:u}),Object.assign(e.data,{viewer_time:u}),e.send("devicesleep"),e.emit("devicewake",{viewer_time:s}),Object.assign(e.data,{viewer_time:s}),e.send("devicewake"))};e.one("playbackheartbeat",a),e.on("playbackheartbeatend",function(){e.off("before*",r),e.one("playbackheartbeat",a)})},Fr=Br,et=z(ee()),Ht=function(i){return i()}(function(){var i=function(){for(var a=0,r={};a<arguments.length;a++){var n=arguments[a];for(var o in n)r[o]=n[o]}return r};function e(t){function a(r,n,o){var s;if(typeof document!="undefined"){if(arguments.length>1){if(o=i({path:"/"},a.defaults,o),typeof o.expires=="number"){var u=new Date;u.setMilliseconds(u.getMilliseconds()+o.expires*864e5),o.expires=u}try{s=JSON.stringify(n),/^[\{\[]/.test(s)&&(n=s)}catch(f){}return t.write?n=t.write(n,r):n=encodeURIComponent(String(n)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),r=encodeURIComponent(String(r)),r=r.replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent),r=r.replace(/[\(\)]/g,escape),document.cookie=[r,"=",n,o.expires?"; expires="+o.expires.toUTCString():"",o.path?"; path="+o.path:"",o.domain?"; domain="+o.domain:"",o.secure?"; secure":""].join("")}r||(s={});for(var d=document.cookie?document.cookie.split("; "):[],v=/(%[0-9A-Z]{2})+/g,g=0;g<d.length;g++){var c=d[g].split("="),l=c.slice(1).join("=");l.charAt(0)==='"'&&(l=l.slice(1,-1));try{var _=c[0].replace(v,decodeURIComponent);if(l=t.read?t.read(l,_):t(l,_)||l.replace(v,decodeURIComponent),this.json)try{l=JSON.parse(l)}catch(f){}if(r===_){s=l;break}r||(s[_]=l)}catch(f){}}return s}}return a.set=a,a.get=function(r){return a.call(a,r)},a.getJSON=function(){return a.apply({json:!0},[].slice.call(arguments))},a.defaults={},a.remove=function(r,n){a(r,"",i(n,{expires:-1}))},a.withConverter=e,a}return e(function(){})}),Ut="muxData",Jr=function(e){return Object.entries(e).map(function(t){var a=re(t,2),r=a[0],n=a[1];return"".concat(r,"=").concat(n)}).join("&")},Gr=function(e){return e.split("&").reduce(function(t,a){var r=re(a.split("="),2),n=r[0],o=r[1],s=+o,u=o&&s==o?s:o;return t[n]=u,t},{})},Bt=function(){var e;try{e=Gr(Ht.get(Ut)||"")}catch(t){e={}}return e},Ft=function(e){try{Ht.set(Ut,Jr(e),{expires:365})}catch(t){}},Vr=function(){var e=Bt();return e.mux_viewer_id=e.mux_viewer_id||Re(),e.msn=e.msn||Math.random(),Ft(e),{mux_viewer_id:e.mux_viewer_id,mux_sample_number:e.msn}},Qr=function(){var e=Bt(),t=B.now();return e.session_start&&(e.sst=e.session_start,delete e.session_start),e.session_id&&(e.sid=e.session_id,delete e.session_id),e.session_expires&&(e.sex=e.session_expires,delete e.session_expires),(!e.sex||e.sex<t)&&(e.sid=Re(),e.sst=t),e.sex=t+25*60*1e3,Ft(e),{session_id:e.sid,session_start:e.sst,session_expires:e.sex}};function Wr(i,e){var t=e.beaconCollectionDomain,a=e.beaconDomain;if(t)return"https://"+t;i=i||"inferred";var r=a||"litix.io";return i.match(/^[a-z0-9]+$/)?"https://"+i+"."+r:"https://img.litix.io/a.gif"}var zr=z(ee()),Jt=function(){var e;switch(Gt()){case"cellular":e="cellular";break;case"ethernet":e="wired";break;case"wifi":e="wifi";break;case void 0:break;default:e="other"}return e},Gt=function(){var e=zr.default.navigator,t=e&&(e.connection||e.mozConnection||e.webkitConnection);return t&&t.type};Jt.getConnectionFromAPI=Gt;var Kr=Jt,Yr={a:"env",b:"beacon",c:"custom",d:"ad",e:"event",f:"experiment",i:"internal",m:"mux",n:"response",p:"player",q:"request",r:"retry",s:"session",t:"timestamp",u:"viewer",v:"video",w:"page",x:"view",y:"sub"},Xr=Vt(Yr),$r={ad:"ad",af:"affiliate",ag:"aggregate",ap:"api",al:"application",ao:"audio",ar:"architecture",as:"asset",au:"autoplay",av:"average",bi:"bitrate",bn:"brand",br:"break",bw:"browser",by:"bytes",bz:"business",ca:"cached",cb:"cancel",cc:"codec",cd:"code",cg:"category",ch:"changed",ci:"client",ck:"clicked",cl:"canceled",cn:"config",co:"count",ce:"counter",cp:"complete",cq:"creator",cr:"creative",cs:"captions",ct:"content",cu:"current",cx:"connection",cz:"context",dg:"downscaling",dm:"domain",dn:"cdn",do:"downscale",dr:"drm",dp:"dropped",du:"duration",dv:"device",dy:"dynamic",eb:"enabled",ec:"encoding",ed:"edge",en:"end",eg:"engine",em:"embed",er:"error",ep:"experiments",es:"errorcode",et:"errortext",ee:"event",ev:"events",ex:"expires",ez:"exception",fa:"failed",fi:"first",fm:"family",ft:"format",fp:"fps",fq:"frequency",fr:"frame",fs:"fullscreen",ha:"has",hb:"holdback",he:"headers",ho:"host",hn:"hostname",ht:"height",id:"id",ii:"init",in:"instance",ip:"ip",is:"is",ke:"key",la:"language",lb:"labeled",le:"level",li:"live",ld:"loaded",lo:"load",ls:"lists",lt:"latency",ma:"max",md:"media",me:"message",mf:"manifest",mi:"mime",ml:"midroll",mm:"min",mn:"manufacturer",mo:"model",mx:"mux",ne:"newest",nm:"name",no:"number",on:"on",or:"origin",os:"os",pa:"paused",pb:"playback",pd:"producer",pe:"percentage",pf:"played",pg:"program",ph:"playhead",pi:"plugin",pl:"preroll",pn:"playing",po:"poster",pp:"pip",pr:"preload",ps:"position",pt:"part",py:"property",px:"pop",pz:"plan",ra:"rate",rd:"requested",re:"rebuffer",rf:"rendition",rg:"range",rm:"remote",ro:"ratio",rp:"response",rq:"request",rs:"requests",sa:"sample",sd:"skipped",se:"session",sh:"shift",sk:"seek",sm:"stream",so:"source",sq:"sequence",sr:"series",ss:"status",st:"start",su:"startup",sv:"server",sw:"software",sy:"severity",ta:"tag",tc:"tech",te:"text",tg:"target",th:"throughput",ti:"time",tl:"total",to:"to",tt:"title",ty:"type",ug:"upscaling",un:"universal",up:"upscale",ur:"url",us:"user",va:"variant",vd:"viewed",vi:"video",ve:"version",vw:"view",vr:"viewer",wd:"width",wa:"watch",wt:"waiting"},Dt=Vt($r);function Vt(i){var e={};for(var t in i)i.hasOwnProperty(t)&&(e[i[t]]=t);return e}function nt(i){var e={},t={};return Object.keys(i).forEach(function(a){var r=!1;if(i.hasOwnProperty(a)&&i[a]!==void 0){var n=a.split("_"),o=n[0],s=Xr[o];s||(I.info("Data key word `"+n[0]+"` not expected in "+a),s=o+"_"),n.splice(1).forEach(function(u){u==="url"&&(r=!0),Dt[u]?s+=Dt[u]:Number.isInteger(Number(u))?s+=u:(I.info("Data key word `"+u+"` not expected in "+a),s+="_"+u+"_")}),r?t[s]=i[a]:e[s]=i[a]}}),Object.assign(e,t)}var _e=z(ee()),Zr=z(Lt()),ei={maxBeaconSize:300,maxQueueLength:3600,baseTimeBetweenBeacons:1e4,maxPayloadKBSize:500},ti=56*1024,ai=["hb","requestcompleted","requestfailed","requestcanceled"],ri="https://img.litix.io",ie=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this._beaconUrl=e||ri,this._eventQueue=[],this._postInFlight=!1,this._resendAfterPost=!1,this._failureCount=0,this._sendTimeout=!1,this._options=Object.assign({},ei,t)};ie.prototype.queueEvent=function(i,e){var t=Object.assign({},e);return this._eventQueue.length<=this._options.maxQueueLength||i==="eventrateexceeded"?(this._eventQueue.push(t),this._sendTimeout||this._startBeaconSending(),this._eventQueue.length<=this._options.maxQueueLength):!1};ie.prototype.flushEvents=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(i&&this._eventQueue.length===1){this._eventQueue.pop();return}this._eventQueue.length&&this._sendBeaconQueue(),this._startBeaconSending()};ie.prototype.destroy=function(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;this.destroyed=!0,i?this._clearBeaconQueue():this.flushEvents(),_e.default.clearTimeout(this._sendTimeout)};ie.prototype._clearBeaconQueue=function(){var i=this._eventQueue.length>this._options.maxBeaconSize?this._eventQueue.length-this._options.maxBeaconSize:0,e=this._eventQueue.slice(i);i>0&&Object.assign(e[e.length-1],nt({mux_view_message:"event queue truncated"}));var t=this._createPayload(e);Qt(this._beaconUrl,t,!0,function(){})};ie.prototype._sendBeaconQueue=function(){var i=this;if(this._postInFlight){this._resendAfterPost=!0;return}var e=this._eventQueue.slice(0,this._options.maxBeaconSize);this._eventQueue=this._eventQueue.slice(this._options.maxBeaconSize),this._postInFlight=!0;var t=this._createPayload(e),a=B.now();Qt(this._beaconUrl,t,!1,function(r,n){n?(i._eventQueue=e.concat(i._eventQueue),i._failureCount+=1,I.info("Error sending beacon: "+n)):i._failureCount=0,i._roundTripTime=B.now()-a,i._postInFlight=!1,i._resendAfterPost&&(i._resendAfterPost=!1,i._eventQueue.length>0&&i._sendBeaconQueue())})};ie.prototype._getNextBeaconTime=function(){if(!this._failureCount)return this._options.baseTimeBetweenBeacons;var i=Math.pow(2,this._failureCount-1);return i=i*Math.random(),(1+i)*this._options.baseTimeBetweenBeacons};ie.prototype._startBeaconSending=function(){var i=this;_e.default.clearTimeout(this._sendTimeout),!this.destroyed&&(this._sendTimeout=_e.default.setTimeout(function(){i._eventQueue.length&&i._sendBeaconQueue(),i._startBeaconSending()},this._getNextBeaconTime()))};ie.prototype._createPayload=function(i){var e=this,t={transmission_timestamp:Math.round(B.now())};this._roundTripTime&&(t.rtt_ms=Math.round(this._roundTripTime));var a,r,n,o=function(){a=JSON.stringify({metadata:t,events:r||i}),n=a.length/1024},s=function(){return n<=e._options.maxPayloadKBSize};return o(),s()||(I.info("Payload size is too big ("+n+" kb). Removing unnecessary events."),r=i.filter(function(u){return ai.indexOf(u.e)===-1}),o()),s()||(I.info("Payload size still too big ("+n+" kb). Cropping fields.."),r.forEach(function(u){for(var d in u){var v=u[d],g=50*1024;typeof v=="string"&&v.length>g&&(u[d]=v.substring(0,g))}}),o()),a};var ii=typeof Zr.default.exitPictureInPicture=="function"?function(e){return e.length<=ti}:function(i){return!1},Qt=function(e,t,a,r){if(a&&navigator&&navigator.sendBeacon&&navigator.sendBeacon(e,t)){r();return}if(_e.default.fetch){_e.default.fetch(e,{method:"POST",body:t,headers:{"Content-Type":"text/plain"},keepalive:ii(t)}).then(function(o){return r(null,o.ok?null:"Error")}).catch(function(o){return r(null,o)});return}if(_e.default.XMLHttpRequest){var n=new _e.default.XMLHttpRequest;n.onreadystatechange=function(){if(n.readyState===4)return r(null,n.status!==200?"error":void 0)},n.open("POST",e),n.setRequestHeader("Content-Type","text/plain"),n.send(t);return}r()},ni=ie,oi=["env_key","view_id","view_sequence_number","player_sequence_number","beacon_domain","player_playhead_time","viewer_time","mux_api_version","event","video_id","player_instance_id","player_error_code","player_error_message","player_error_context","player_error_severity","player_error_business_exception"],si=["adplay","adplaying","adpause","adfirstquartile","admidpoint","adthirdquartile","adended","adresponse","adrequest"],ui=["ad_id","ad_creative_id","ad_universal_id"],di=["viewstart","error","ended","viewend"],li=10*60*1e3,ci=function(){"use strict";function i(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};F(this,i);var r,n,o,s,u,d,v,g,c,l,_,f;y(this,"mux",void 0),y(this,"envKey",void 0),y(this,"options",void 0),y(this,"eventQueue",void 0),y(this,"sampleRate",void 0),y(this,"disableCookies",void 0),y(this,"respectDoNotTrack",void 0),y(this,"previousBeaconData",void 0),y(this,"lastEventTime",void 0),y(this,"rateLimited",void 0),y(this,"pageLevelData",void 0),y(this,"viewerData",void 0),this.mux=e,this.envKey=t,this.options=a,this.previousBeaconData=null,this.lastEventTime=0,this.rateLimited=!1,this.eventQueue=new ni(Wr(this.envKey,this.options));var k;this.sampleRate=(k=this.options.sampleRate)!==null&&k!==void 0?k:1;var x;this.disableCookies=(x=this.options.disableCookies)!==null&&x!==void 0?x:!1;var T;this.respectDoNotTrack=(T=this.options.respectDoNotTrack)!==null&&T!==void 0?T:!1,this.previousBeaconData=null,this.lastEventTime=0,this.rateLimited=!1,this.pageLevelData={mux_api_version:this.mux.API_VERSION,mux_embed:this.mux.NAME,mux_embed_version:this.mux.VERSION,viewer_application_name:(r=this.options.platform)===null||r===void 0?void 0:r.name,viewer_application_version:(n=this.options.platform)===null||n===void 0?void 0:n.version,viewer_application_engine:(o=this.options.platform)===null||o===void 0?void 0:o.layout,viewer_device_name:(s=this.options.platform)===null||s===void 0?void 0:s.product,viewer_device_category:"",viewer_device_manufacturer:(u=this.options.platform)===null||u===void 0?void 0:u.manufacturer,viewer_os_family:(v=this.options.platform)===null||v===void 0||(d=v.os)===null||d===void 0?void 0:d.family,viewer_os_architecture:(c=this.options.platform)===null||c===void 0||(g=c.os)===null||g===void 0?void 0:g.architecture,viewer_os_version:(_=this.options.platform)===null||_===void 0||(l=_.os)===null||l===void 0?void 0:l.version,viewer_connection_type:Kr(),page_url:et.default===null||et.default===void 0||(f=et.default.location)===null||f===void 0?void 0:f.href},this.viewerData=this.disableCookies?{}:Vr()}return ae(i,[{key:"send",value:function(t,a){if(!(!t||!(a!=null&&a.view_id))){if(this.respectDoNotTrack&&rt())return I.info("Not sending `"+t+"` because Do Not Track is enabled");if(!a||typeof a!="object")return I.error("A data object was expected in send() but was not provided");var r=this.disableCookies?{}:Qr(),n=ot(Je({},this.pageLevelData,a,r,this.viewerData),{event:t,env_key:this.envKey});n.user_id&&(n.viewer_user_id=n.user_id,delete n.user_id);var o,s=((o=n.mux_sample_number)!==null&&o!==void 0?o:0)>=this.sampleRate,u=this._deduplicateBeaconData(t,n),d=nt(u);if(this.lastEventTime=this.mux.utils.now(),s)return I.info("Not sending event due to sample rate restriction",t,n,d);if(this.envKey||I.info("Missing environment key (envKey) - beacons will be dropped if the video source is not a valid mux video URL",t,n,d),!this.rateLimited){if(I.info("Sending event",t,n,d),this.rateLimited=!this.eventQueue.queueEvent(t,d),this.mux.WINDOW_UNLOADING&&t==="viewend")this.eventQueue.destroy(!0);else if(this.mux.WINDOW_HIDDEN&&t==="hb"?this.eventQueue.flushEvents(!0):di.indexOf(t)>=0&&this.eventQueue.flushEvents(),this.rateLimited)return n.event="eventrateexceeded",d=nt(n),this.eventQueue.queueEvent(n.event,d),I.error("Beaconing disabled due to rate limit.")}}}},{key:"destroy",value:function(){this.eventQueue.destroy(!1)}},{key:"_deduplicateBeaconData",value:function(t,a){var r=this,n={},o=a.view_id;if(o==="-1"||t==="viewstart"||t==="viewend"||!this.previousBeaconData||this.mux.utils.now()-this.lastEventTime>=li)n=Je({},a),o&&(this.previousBeaconData=n),o&&t==="viewend"&&(this.previousBeaconData=null);else{var s=t.indexOf("request")===0;Object.entries(a).forEach(function(u){var d=re(u,2),v=d[0],g=d[1];r.previousBeaconData&&(g!==r.previousBeaconData[v]||oi.indexOf(v)>-1||r.objectHasChanged(s,v,g,r.previousBeaconData[v])||r.eventRequiresKey(t,v))&&(n[v]=g,r.previousBeaconData[v]=g)})}return n}},{key:"objectHasChanged",value:function(t,a,r,n){return!t||a.indexOf("request_")!==0?!1:a==="request_response_headers"||typeof r!="object"||typeof n!="object"?!0:Object.keys(r||{}).length!==Object.keys(n||{}).length}},{key:"eventRequiresKey",value:function(t,a){return!!(t==="renditionchange"&&a.indexOf("video_source_")===0||ui.includes(a)&&si.includes(t))}}]),i}(),vi=function i(e){"use strict";F(this,i);var t=0,a=0,r=0,n=0,o=0,s=0,u=0,d=function(l,_){var f=_.request_start,k=_.request_response_start,x=_.request_response_end,T=_.request_bytes_loaded;n++;var q,b;if(k?(q=k-(f!=null?f:0),b=(x!=null?x:0)-k):b=(x!=null?x:0)-(f!=null?f:0),b>0&&T&&T>0){var m=T/b*8e3;o++,a+=T,r+=b,e.data.view_min_request_throughput=Math.min(e.data.view_min_request_throughput||1/0,m),e.data.view_average_request_throughput=a/r*8e3,e.data.view_request_count=n,q>0&&(t+=q,e.data.view_max_request_latency=Math.max(e.data.view_max_request_latency||0,q),e.data.view_average_request_latency=t/o)}},v=function(l,_){n++,s++,e.data.view_request_count=n,e.data.view_request_failed_count=s},g=function(l,_){n++,u++,e.data.view_request_count=n,e.data.view_request_canceled_count=u};e.on("requestcompleted",d),e.on("requestfailed",v),e.on("requestcanceled",g)},_i=vi,pi=60*60*1e3,fi=function i(e){"use strict";var t=this;F(this,i),y(this,"_lastEventTime",void 0),e.on("before*",function(a,r){var n=r.viewer_time,o=B.now(),s=t._lastEventTime;if(t._lastEventTime=o,s&&o-s>pi){var u=Object.keys(e.data).reduce(function(v,g){return g.indexOf("video_")===0?Object.assign(v,y({},g,e.data[g])):v},{});e.mux.log.info("Received event after at least an hour inactivity, creating a new view");var d=e.playbackHeartbeat._playheadShouldBeProgressing;e._resetView(Object.assign({viewer_time:n},u)),e.playbackHeartbeat._playheadShouldBeProgressing=d,e.playbackHeartbeat._playheadShouldBeProgressing&&a.type!=="play"&&a.type!=="adbreakstart"&&(e.emit("play",{viewer_time:n}),a.type!=="playing"&&e.emit("playing",{viewer_time:n}))}})},hi=fi,mi=["viewstart","ended","loadstart","pause","play","playing","ratechange","waiting","adplay","adpause","adended","aderror","adplaying","adrequest","adresponse","adbreakstart","adbreakend","adfirstquartile","admidpoint","adthirdquartile","rebufferstart","rebufferend","seeked","error","hb","requestcompleted","requestfailed","requestcanceled","renditionchange"],yi=new Set(["requestcompleted","requestfailed","requestcanceled"]),gi=function(i){"use strict";Xa(t,i);var e=er(t);function t(a,r,n){F(this,t);var o;o=e.call(this),y(D(o),"DOM_CONTENT_LOADED_EVENT_END",void 0),y(D(o),"NAVIGATION_START",void 0),y(D(o),"_destroyed",void 0),y(D(o),"_heartBeatTimeout",void 0),y(D(o),"adTracker",void 0),y(D(o),"dashjs",void 0),y(D(o),"data",void 0),y(D(o),"disablePlayheadRebufferTracking",void 0),y(D(o),"disableRebufferTracking",void 0),y(D(o),"errorTracker",void 0),y(D(o),"errorTranslator",void 0),y(D(o),"emitTranslator",void 0),y(D(o),"getAdData",void 0),y(D(o),"getPlayheadTime",void 0),y(D(o),"getStateData",void 0),y(D(o),"stateDataTranslator",void 0),y(D(o),"hlsjs",void 0),y(D(o),"id",void 0),y(D(o),"longResumeTracker",void 0),y(D(o),"minimumRebufferDuration",void 0),y(D(o),"mux",void 0),y(D(o),"playbackEventDispatcher",void 0),y(D(o),"playbackHeartbeat",void 0),y(D(o),"playbackHeartbeatTime",void 0),y(D(o),"playheadTime",void 0),y(D(o),"seekingTracker",void 0),y(D(o),"sustainedRebufferThreshold",void 0),y(D(o),"watchTimeTracker",void 0),y(D(o),"currentFragmentPDT",void 0),y(D(o),"currentFragmentStart",void 0),o.DOM_CONTENT_LOADED_EVENT_END=Fe.domContentLoadedEventEnd(),o.NAVIGATION_START=Fe.navigationStart();var s={debug:!1,minimumRebufferDuration:250,sustainedRebufferThreshold:1e3,playbackHeartbeatTime:25,beaconDomain:"litix.io",sampleRate:1,disableCookies:!1,respectDoNotTrack:!1,disableRebufferTracking:!1,disablePlayheadRebufferTracking:!1,errorTranslator:function(l){return l},emitTranslator:function(){for(var l=arguments.length,_=new Array(l),f=0;f<l;f++)_[f]=arguments[f];return _},stateDataTranslator:function(l){return l}};o.mux=a,o.id=r,n!=null&&n.beaconDomain&&o.mux.log.warn("The `beaconDomain` setting has been deprecated in favor of `beaconCollectionDomain`. Please change your integration to use `beaconCollectionDomain` instead of `beaconDomain`."),n=Object.assign(s,n),n.data=n.data||{},n.data.property_key&&(n.data.env_key=n.data.property_key,delete n.data.property_key),I.level=n.debug?ve.DEBUG:ve.WARN,o.getPlayheadTime=n.getPlayheadTime,o.getStateData=n.getStateData||function(){return{}},o.getAdData=n.getAdData||function(){},o.minimumRebufferDuration=n.minimumRebufferDuration,o.sustainedRebufferThreshold=n.sustainedRebufferThreshold,o.playbackHeartbeatTime=n.playbackHeartbeatTime,o.disableRebufferTracking=n.disableRebufferTracking,o.disableRebufferTracking&&o.mux.log.warn("Disabling rebuffer tracking. This should only be used in specific circumstances as a last resort when your player is known to unreliably track rebuffering."),o.disablePlayheadRebufferTracking=n.disablePlayheadRebufferTracking,o.errorTranslator=n.errorTranslator,o.emitTranslator=n.emitTranslator,o.stateDataTranslator=n.stateDataTranslator,o.playbackEventDispatcher=new ci(a,n.data.env_key,n),o.data={player_instance_id:Re(),mux_sample_rate:n.sampleRate,beacon_domain:n.beaconCollectionDomain||n.beaconDomain},o.data.view_sequence_number=1,o.data.player_sequence_number=1;var u=function(){typeof this.data.view_start=="undefined"&&(this.data.view_start=this.mux.utils.now(),this.emit("viewstart"))}.bind(D(o));if(o.on("viewinit",function(c,l){this._resetVideoData(),this._resetViewData(),this._resetErrorData(),this._updateStateData(),Object.assign(this.data,l),this._initializeViewData(),this.one("play",u),this.one("adbreakstart",u)}),o.on("videochange",function(c,l){this._resetView(l)}),o.on("programchange",function(c,l){this.data.player_is_paused&&this.mux.log.warn("The `programchange` event is intended to be used when the content changes mid playback without the video source changing, however the video is not currently playing. If the video source is changing please use the videochange event otherwise you will lose startup time information."),this._resetView(Object.assign(l,{view_program_changed:!0})),u(),this.emit("play"),this.emit("playing")}),o.on("fragmentchange",function(c,l){this.currentFragmentPDT=l.currentFragmentPDT,this.currentFragmentStart=l.currentFragmentStart}),o.on("destroy",o.destroy),typeof window!="undefined"&&typeof window.addEventListener=="function"&&typeof window.removeEventListener=="function"){var d=function(){var l=typeof o.data.view_start!="undefined";o.mux.WINDOW_HIDDEN=document.visibilityState==="hidden",l&&o.mux.WINDOW_HIDDEN&&(o.data.player_is_paused||o.emit("hb"))};window.addEventListener("visibilitychange",d,!1);var v=function(l){l.persisted||o.destroy()};window.addEventListener("pagehide",v,!1),o.on("destroy",function(){window.removeEventListener("visibilitychange",d),window.removeEventListener("pagehide",v)})}o.on("playerready",function(c,l){Object.assign(this.data,l)}),mi.forEach(function(c){o.on(c,function(l,_){c.indexOf("ad")!==0&&this._updateStateData(),Object.assign(this.data,_),this._sanitizeData()}),o.on("after"+c,function(){(c!=="error"||this.errorTracker.viewErrored)&&this.send(c)})}),o.on("viewend",function(c,l){Object.assign(o.data,l)});var g=function(l){var _=this.mux.utils.now();this.data.player_init_time&&(this.data.player_startup_time=_-this.data.player_init_time),!this.mux.PLAYER_TRACKED&&this.NAVIGATION_START&&(this.mux.PLAYER_TRACKED=!0,(this.data.player_init_time||this.DOM_CONTENT_LOADED_EVENT_END)&&(this.data.page_load_time=Math.min(this.data.player_init_time||1/0,this.DOM_CONTENT_LOADED_EVENT_END||1/0)-this.NAVIGATION_START)),this.send("playerready"),delete this.data.player_startup_time,delete this.data.page_load_time};return o.one("playerready",g),o.longResumeTracker=new hi(D(o)),o.errorTracker=new yr(D(o)),new Fr(D(o)),o.seekingTracker=new Ir(D(o)),o.playheadTime=new Dr(D(o)),o.playbackHeartbeat=new hr(D(o)),new Nr(D(o)),o.watchTimeTracker=new br(D(o)),new Er(D(o)),o.adTracker=new jr(D(o)),new Sr(D(o)),new kr(D(o)),new Or(D(o)),new Ur(D(o)),new _i(D(o)),n.hlsjs&&o.addHLSJS(n),n.dashjs&&o.addDashJS(n),o.emit("viewinit",n.data),o}return ae(t,[{key:"emit",value:function(r,n){var o,s=Object.assign({viewer_time:this.mux.utils.now()},n),u=[r,s];if(this.emitTranslator)try{u=this.emitTranslator(r,s)}catch(d){this.mux.log.warn("Exception in emit translator callback.",d)}u!=null&&u.length&&(o=Ue(be(t.prototype),"emit",this)).call.apply(o,[this].concat($(u)))}},{key:"destroy",value:function(){this._destroyed||(this._destroyed=!0,typeof this.data.view_start!="undefined"&&(this.emit("viewend"),this.send("viewend")),this.playbackEventDispatcher.destroy(),this.removeHLSJS(),this.removeDashJS(),window.clearTimeout(this._heartBeatTimeout))}},{key:"send",value:function(r){if(this.data.view_id){var n=Object.assign({},this.data),o=["player_program_time","player_manifest_newest_program_time","player_live_edge_program_time","player_program_time","video_holdback","video_part_holdback","video_target_duration","video_part_target_duration"];if(n.video_source_is_live===void 0&&(n.player_source_duration===1/0||n.video_source_duration===1/0?n.video_source_is_live=!0:(n.player_source_duration>0||n.video_source_duration>0)&&(n.video_source_is_live=!1)),n.video_source_is_live||o.forEach(function(v){n[v]=void 0}),n.video_source_url=n.video_source_url||n.player_source_url,n.video_source_url){var s=re(Oe(n.video_source_url),2),u=s[0],d=s[1];n.video_source_domain=d,n.video_source_hostname=u}delete n.ad_request_id,this.playbackEventDispatcher.send(r,n),this.data.view_sequence_number++,this.data.player_sequence_number++,yi.has(r)||this._restartHeartBeat(),r==="viewend"&&delete this.data.view_id}}},{key:"_resetView",value:function(r){this.emit("viewend"),this.send("viewend"),this.emit("viewinit",r)}},{key:"_updateStateData",value:function(){var r=this.getStateData();if(typeof this.stateDataTranslator=="function")try{r=this.stateDataTranslator(r)}catch(n){this.mux.log.warn("Exception in stateDataTranslator translator callback.",n)}Object.assign(this.data,r),this.playheadTime._updatePlayheadTime(),this._sanitizeData()}},{key:"_sanitizeData",value:function(){var r=this,n=["player_width","player_height","video_source_width","video_source_height","player_playhead_time","video_source_bitrate"];n.forEach(function(s){var u=parseInt(r.data[s],10);r.data[s]=isNaN(u)?void 0:u});var o=["player_source_url","video_source_url"];o.forEach(function(s){if(r.data[s]){var u=r.data[s].toLowerCase();(u.indexOf("data:")===0||u.indexOf("blob:")===0)&&(r.data[s]="MSE style URL")}})}},{key:"_resetVideoData",value:function(){var r=this;Object.keys(this.data).forEach(function(n){n.indexOf("video_")===0&&delete r.data[n]})}},{key:"_resetViewData",value:function(){var r=this;Object.keys(this.data).forEach(function(n){n.indexOf("view_")===0&&delete r.data[n]}),this.data.view_sequence_number=1}},{key:"_resetErrorData",value:function(){delete this.data.player_error_code,delete this.data.player_error_message,delete this.data.player_error_context,delete this.data.player_error_severity,delete this.data.player_error_business_exception}},{key:"_initializeViewData",value:function(){var r=this,n=this.data.view_id=Re(),o=function(){n===r.data.view_id&&U(r.data,"player_view_count",1)};this.data.player_is_paused?this.one("play",o):o()}},{key:"_restartHeartBeat",value:function(){var r=this;window.clearTimeout(this._heartBeatTimeout),this._heartBeatTimeout=window.setTimeout(function(){r.data.player_is_paused||r.emit("hb")},1e4)}},{key:"addHLSJS",value:function(r){if(!r.hlsjs){this.mux.log.warn("You must pass a valid hlsjs instance in order to track it.");return}if(this.hlsjs){this.mux.log.warn("An instance of HLS.js is already being monitored for this player.");return}this.hlsjs=r.hlsjs,or(this.mux,this.id,r.hlsjs,{},r.Hls||window.Hls)}},{key:"removeHLSJS",value:function(){this.hlsjs&&(sr(this.hlsjs),this.hlsjs=void 0)}},{key:"addDashJS",value:function(r){if(!r.dashjs){this.mux.log.warn("You must pass a valid dashjs instance in order to track it.");return}if(this.dashjs){this.mux.log.warn("An instance of Dash.js is already being monitored for this player.");return}this.dashjs=r.dashjs,cr(this.mux,this.id,r.dashjs)}},{key:"removeDashJS",value:function(){this.dashjs&&(vr(this.dashjs),this.dashjs=void 0)}}]),t}(pr),bi=gi,ke=z(Lt());function Ti(){return ke.default&&!!(ke.default.fullscreenElement||ke.default.webkitFullscreenElement||ke.default.mozFullScreenElement||ke.default.msFullscreenElement)}var Ei=["loadstart","pause","play","playing","seeking","seeked","timeupdate","ratechange","stalled","waiting","error","ended"],wi={1:"MEDIA_ERR_ABORTED",2:"MEDIA_ERR_NETWORK",3:"MEDIA_ERR_DECODE",4:"MEDIA_ERR_SRC_NOT_SUPPORTED"};function Di(i,e,t){var a=re(Be(e),3),r=a[0],n=a[1],o=a[2],s=i.log,u=i.utils.getComputedStyle,d=i.utils.secondsToMs,v={automaticErrorTracking:!0};if(r){if(o!=="video"&&o!=="audio")return s.error("The element of `"+n+"` was not a media element.")}else return s.error("No element was found with the `"+n+"` query selector.");r.mux&&(r.mux.destroy(),delete r.mux,s.warn("Already monitoring this video element, replacing existing event listeners"));var g={getPlayheadTime:function(){return d(r.currentTime)},getStateData:function(){var _,f,k,x=((_=(f=this).getPlayheadTime)===null||_===void 0?void 0:_.call(f))||d(r.currentTime),T=this.hlsjs&&this.hlsjs.url,q=this.dashjs&&typeof this.dashjs.getSource=="function"&&this.dashjs.getSource(),b={player_is_paused:r.paused,player_width:parseInt(u(r,"width")),player_height:parseInt(u(r,"height")),player_autoplay_on:r.autoplay,player_preload_on:r.preload,player_language_code:r.lang,player_is_fullscreen:Ti(),video_poster_url:r.poster,video_source_url:T||q||r.currentSrc,video_source_duration:d(r.duration),video_source_height:r.videoHeight,video_source_width:r.videoWidth,view_dropped_frame_count:r==null||(k=r.getVideoPlaybackQuality)===null||k===void 0?void 0:k.call(r).droppedVideoFrames};if(r.getStartDate&&x>0){var m=r.getStartDate();if(m&&typeof m.getTime=="function"&&m.getTime()){var p=m.getTime();if(b.player_program_time=p+x,r.seekable.length>0){var w=p+r.seekable.end(r.seekable.length-1);b.player_live_edge_program_time=w}}}return b}};t=Object.assign(v,t,g),t.data=Object.assign({player_software:"HTML5 Video Element",player_mux_plugin_name:"VideoElementMonitor",player_mux_plugin_version:i.VERSION},t.data),r.mux=r.mux||{},r.mux.deleted=!1,r.mux.emit=function(l,_){i.emit(n,l,_)},r.mux.updateData=function(l){r.mux.emit("hb",l)};var c=function(){s.error("The monitor for this video element has already been destroyed.")};r.mux.destroy=function(){Object.keys(r.mux.listeners).forEach(function(l){r.removeEventListener(l,r.mux.listeners[l],!1)}),delete r.mux.listeners,r.mux.destroy=c,r.mux.swapElement=c,r.mux.emit=c,r.mux.addHLSJS=c,r.mux.addDashJS=c,r.mux.removeHLSJS=c,r.mux.removeDashJS=c,r.mux.updateData=c,r.mux.setEmitTranslator=c,r.mux.setStateDataTranslator=c,r.mux.setGetPlayheadTime=c,r.mux.deleted=!0,i.emit(n,"destroy")},r.mux.swapElement=function(l){var _=re(Be(l),3),f=_[0],k=_[1],x=_[2];if(f){if(x!=="video"&&x!=="audio")return i.log.error("The element of `"+k+"` was not a media element.")}else return i.log.error("No element was found with the `"+k+"` query selector.");f.muxId=r.muxId,delete r.muxId,f.mux=f.mux||{},f.mux.listeners=Object.assign({},r.mux.listeners),delete r.mux.listeners,Object.keys(f.mux.listeners).forEach(function(T){r.removeEventListener(T,f.mux.listeners[T],!1),f.addEventListener(T,f.mux.listeners[T],!1)}),f.mux.swapElement=r.mux.swapElement,f.mux.destroy=r.mux.destroy,delete r.mux,r=f},r.mux.addHLSJS=function(l){i.addHLSJS(n,l)},r.mux.addDashJS=function(l){i.addDashJS(n,l)},r.mux.removeHLSJS=function(){i.removeHLSJS(n)},r.mux.removeDashJS=function(){i.removeDashJS(n)},r.mux.setEmitTranslator=function(l){i.setEmitTranslator(n,l)},r.mux.setStateDataTranslator=function(l){i.setStateDataTranslator(n,l)},r.mux.setGetPlayheadTime=function(l){l||(l=t.getPlayheadTime),i.setGetPlayheadTime(n,l)},i.init(n,t),i.emit(n,"playerready"),r.paused||(i.emit(n,"play"),r.readyState>2&&i.emit(n,"playing")),r.mux.listeners={},Ei.forEach(function(l){l==="error"&&!t.automaticErrorTracking||(r.mux.listeners[l]=function(){var _={};if(l==="error"){if(!r.error||r.error.code===1)return;_.player_error_code=r.error.code,_.player_error_message=wi[r.error.code]||r.error.message}i.emit(n,l,_)},r.addEventListener(l,r.mux.listeners[l],!1))})}function Ai(i,e,t,a){var r=a;if(i&&typeof i[e]=="function")try{r=i[e].apply(i,t)}catch(n){I.info("safeCall error",n)}return r}var Se=z(ee()),ge;Se.default&&Se.default.WeakMap&&(ge=new WeakMap);function ki(i,e){if(!i||!e||!Se.default||typeof Se.default.getComputedStyle!="function")return"";var t;return ge&&ge.has(i)&&(t=ge.get(i)),t||(t=Se.default.getComputedStyle(i,null),ge&&ge.set(i,t)),t.getPropertyValue(e)}function xi(i){return Math.floor(i*1e3)}var ce={TARGET_DURATION:"#EXT-X-TARGETDURATION",PART_INF:"#EXT-X-PART-INF",SERVER_CONTROL:"#EXT-X-SERVER-CONTROL",INF:"#EXTINF",PROGRAM_DATE_TIME:"#EXT-X-PROGRAM-DATE-TIME",VERSION:"#EXT-X-VERSION",SESSION_DATA:"#EXT-X-SESSION-DATA"},Ve=function(e){return this.buffer="",this.manifest={segments:[],serverControl:{},sessionData:{}},this.currentUri={},this.process(e),this.manifest};Ve.prototype.process=function(i){var e;for(this.buffer+=i,e=this.buffer.indexOf("\n");e>-1;e=this.buffer.indexOf("\n"))this.processLine(this.buffer.substring(0,e)),this.buffer=this.buffer.substring(e+1)};Ve.prototype.processLine=function(i){var e=i.indexOf(":"),t=Pi(i,e),a=t[0],r=t.length===2?ut(t[1]):void 0;if(a[0]!=="#")this.currentUri.uri=a,this.manifest.segments.push(this.currentUri),this.manifest.targetDuration&&!("duration"in this.currentUri)&&(this.currentUri.duration=this.manifest.targetDuration),this.currentUri={};else switch(a){case ce.TARGET_DURATION:{if(!isFinite(r)||r<0)return;this.manifest.targetDuration=r,this.setHoldBack();break}case ce.PART_INF:{tt(this.manifest,t),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),this.setHoldBack();break}case ce.SERVER_CONTROL:{tt(this.manifest,t),this.setHoldBack();break}case ce.INF:{r===0?this.currentUri.duration=.01:r>0&&(this.currentUri.duration=r);break}case ce.PROGRAM_DATE_TIME:{var n=r,o=new Date(n);this.manifest.dateTimeString||(this.manifest.dateTimeString=n,this.manifest.dateTimeObject=o),this.currentUri.dateTimeString=n,this.currentUri.dateTimeObject=o;break}case ce.VERSION:{tt(this.manifest,t);break}case ce.SESSION_DATA:{var s=Ni(t[1]),u=jt(s);Object.assign(this.manifest.sessionData,u)}}};Ve.prototype.setHoldBack=function(){var i=this.manifest,e=i.serverControl,t=i.targetDuration,a=i.partTargetDuration;if(e){var r="holdBack",n="partHoldBack",o=t&&t*3,s=a&&a*2;t&&!e.hasOwnProperty(r)&&(e[r]=o),o&&e[r]<o&&(e[r]=o),a&&!e.hasOwnProperty(n)&&(e[n]=a*3),a&&e[n]<s&&(e[n]=s)}};var tt=function(e,t){var a=Wt(t[0].replace("#EXT-X-","")),r;Oi(t[1])?(r={},r=Object.assign(Ri(t[1]),r)):r=ut(t[1]),e[a]=r},Wt=function(e){return e.toLowerCase().replace(/-(\w)/g,function(t){return t[1].toUpperCase()})},ut=function(e){if(e.toLowerCase()==="yes"||e.toLowerCase()==="no")return e.toLowerCase()==="yes";var t=e.indexOf(":")!==-1?e:parseFloat(e);return isNaN(t)?e:t},Si=function(e){var t={},a=e.split("=");if(a.length>1){var r=Wt(a[0]);t[r]=ut(a[1])}return t},Ri=function(e){for(var t=e.split(","),a={},r=0;t.length>r;r++){var n=t[r],o=Si(n);a=Object.assign(o,a)}return a},Oi=function(e){return e.indexOf("=")>-1},Pi=function(e,t){return t===-1?[e]:[e.substring(0,t),e.substring(t+1)]},Ni=function(e){var t={};if(e){var a=e.search(","),r=e.slice(0,a),n=e.slice(a+1),o=[r,n];return o.forEach(function(s,u){for(var d=s.replace(/['"]+/g,"").split("="),v=0;v<d.length;v++)d[v]==="DATA-ID"&&(t["DATA-ID"]=d[1-v]),d[v]==="VALUE"&&(t.VALUE=d[1-v])}),{data:t}}},qi=Ve,Li={safeCall:Ai,safeIncrement:U,getComputedStyle:ki,secondsToMs:xi,assign:Object.assign,headersStringToObject:st,cdnHeadersToRequestId:Ge,extractHostnameAndDomain:Oe,extractHostname:Z,manifestParser:qi,generateShortID:Ct,generateUUID:Re,now:B.now,findMediaElement:Be},Ii=Li,Ci={PLAYER_READY:"playerready",VIEW_INIT:"viewinit",VIDEO_CHANGE:"videochange",PLAY:"play",PAUSE:"pause",PLAYING:"playing",TIME_UPDATE:"timeupdate",SEEKING:"seeking",SEEKED:"seeked",REBUFFER_START:"rebufferstart",REBUFFER_END:"rebufferend",ERROR:"error",ENDED:"ended",RENDITION_CHANGE:"renditionchange",ORIENTATION_CHANGE:"orientationchange",AD_REQUEST:"adrequest",AD_RESPONSE:"adresponse",AD_BREAK_START:"adbreakstart",AD_PLAY:"adplay",AD_PLAYING:"adplaying",AD_PAUSE:"adpause",AD_FIRST_QUARTILE:"adfirstquartile",AD_MID_POINT:"admidpoint",AD_THIRD_QUARTILE:"adthirdquartile",AD_ENDED:"adended",AD_BREAK_END:"adbreakend",AD_ERROR:"aderror",REQUEST_COMPLETED:"requestcompleted",REQUEST_FAILED:"requestfailed",REQUEST_CANCELLED:"requestcanceled",HEARTBEAT:"hb",DESTROY:"destroy"},Mi=Ci,ji="mux-embed",Hi="5.9.0",Ui="2.1",H={},de=function(e){var t=arguments;typeof e=="string"?de.hasOwnProperty(e)?xe.default.setTimeout(function(){t=Array.prototype.splice.call(t,1),de[e].apply(null,t)},0):I.warn("`"+e+"` is an unknown task"):typeof e=="function"?xe.default.setTimeout(function(){e(de)},0):I.warn("`"+e+"` is invalid.")},Bi={loaded:B.now(),NAME:ji,VERSION:Hi,API_VERSION:Ui,PLAYER_TRACKED:!1,monitor:function(e,t){return Di(de,e,t)},destroyMonitor:function(e){var t=re(Be(e),1),a=t[0];a&&a.mux&&typeof a.mux.destroy=="function"?a.mux.destroy():I.error("A video element monitor for `"+e+"` has not been initialized via `mux.monitor`.")},addHLSJS:function(e,t){var a=X(e);H[a]?H[a].addHLSJS(t):I.error("A monitor for `"+a+"` has not been initialized.")},addDashJS:function(e,t){var a=X(e);H[a]?H[a].addDashJS(t):I.error("A monitor for `"+a+"` has not been initialized.")},removeHLSJS:function(e){var t=X(e);H[t]?H[t].removeHLSJS():I.error("A monitor for `"+t+"` has not been initialized.")},removeDashJS:function(e){var t=X(e);H[t]?H[t].removeDashJS():I.error("A monitor for `"+t+"` has not been initialized.")},init:function(e,t){rt()&&t&&t.respectDoNotTrack&&I.info("The browser's Do Not Track flag is enabled - Mux beaconing is disabled.");var a=X(e);H[a]=new bi(de,a,t)},emit:function(e,t,a){var r=X(e);H[r]?(H[r].emit(t,a),t==="destroy"&&delete H[r]):I.error("A monitor for `"+r+"` has not been initialized.")},updateData:function(e,t){var a=X(e);H[a]?H[a].emit("hb",t):I.error("A monitor for `"+a+"` has not been initialized.")},setEmitTranslator:function(e,t){var a=X(e);H[a]?H[a].emitTranslator=t:I.error("A monitor for `"+a+"` has not been initialized.")},setStateDataTranslator:function(e,t){var a=X(e);H[a]?H[a].stateDataTranslator=t:I.error("A monitor for `"+a+"` has not been initialized.")},setGetPlayheadTime:function(e,t){var a=X(e);H[a]?H[a].getPlayheadTime=t:I.error("A monitor for `"+a+"` has not been initialized.")},checkDoNotTrack:rt,log:I,utils:Ii,events:Mi,WINDOW_HIDDEN:!1,WINDOW_UNLOADING:!1};Object.assign(de,Bi);typeof xe.default!="undefined"&&typeof xe.default.addEventListener=="function"&&xe.default.addEventListener("pagehide",function(i){i.persisted||(de.WINDOW_UNLOADING=!0)},!1);var ne=de;function zt(i,e,t){return e in i?Object.defineProperty(i,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):i[e]=t,i}function Qe(i){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{},a=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(t).filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),a.forEach(function(r){zt(i,r,t[r])})}return i}function Fi(i,e){var t=Object.keys(i);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);e&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(i,r).enumerable})),t.push.apply(t,a)}return t}function Kt(i,e){return e=e!=null?e:{},Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):Fi(Object(e)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(e,t))}),i}var Xt=ne.utils,Ji=Xt.findMediaElement,We=Xt.secondsToMs,P=ne.events,ze=ne.log,Yt=[P.LOADSTART,P.PAUSE,P.PLAY,P.PLAYING,P.SEEKING,P.SEEKED,P.TIME_UPDATE,P.RATECHANGE,P.STALLED,P.WAITING,P.ENDED],Te=function(i){var e,t;return(t=(e=i.currentSrc)!==null&&e!==void 0?e:i.src)!==null&&t!==void 0?t:void 0},dt=function(i,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{getSrc:Te},a=t.getSrc,r=a===void 0?Te:a,n,o=le(Ji(i),1),s=o[0],u=function(){return s==null?void 0:s.mux};if(!u()){ze.error("Missing mux data instance.");return}if(!(!((n=google)===null||n===void 0)&&n.ima)){ze.error("Missing google.ima SDK. Make sure you include it via a script tag.");return}var d,v=!1,g=!1,c,l,_=!1,f,k=function(){for(var h=arguments.length,A=new Array(h),j=0;j<h;j++)A[j]=arguments[j];var G=le(A,2),J=G[0],M=G[1];if(v){if(c&&s.src===c){if(Yt.includes(J))return}else Yt.includes(J)&&ze.warn("Unexpected event during adbreak:",J);if(J===P.RENDITION_CHANGE){f=M;return}}else if(J===P.PLAY){if(_)return;_=!0}else if(J===P.PAUSE){if(!_)return;_=!1}return A};u().setEmitTranslator(k);var x,T,q=function(){return v&&c&&s.src===c&&T!=null?T:We(s.currentTime)},b=function(){x={player_is_paused:!0,video_source_url:r(s),video_source_duration:We(s.duration),video_source_height:s.videoHeight,video_source_width:s.videoWidth},T=We(s.currentTime)},m=function(){x=void 0,T=void 0},p=function(){return x!=null?x:{}},w=function(h){if(v){var A=g,j=c&&s.src===c?p():{};return Qe(Kt(Qe({},h),{player_is_fullscreen:A}),j)}return h};u().setStateDataTranslator(w),u().setGetPlayheadTime(q);var E=function(){var h=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},A,j,G,J,M=(J=(A=h.getAdData)===null||A===void 0?void 0:A.call(h))!==null&&J!==void 0?J:{},K,Ce=(K=(j=h.getAd)===null||j===void 0?void 0:j.call(h))!==null&&K!==void 0?K:{},_a=l?{ad_tag_url:l}:{},Ke,Me=(Ke=(G=Ce.getMediaUrl)===null||G===void 0?void 0:G.call(Ce))!==null&&Ke!==void 0?Ke:c,pa=Me?{ad_asset_url:Me}:{},fa=M!=null&&M.adId?{ad_id:M==null?void 0:M.adId}:{},ha=M!=null&&M.creativeId?{ad_creative_id:M==null?void 0:M.creativeId}:{},ma=M!=null&&M.universalAdIdValue?{ad_universal_id:M==null?void 0:M.universalAdIdValue}:{},Ee;if(Me){if(Ee=Me,typeof Ce.getAdPodInfo=="function"){var lt=Ce.getAdPodInfo(),ya=lt.getPodIndex(),ga=lt.getAdPosition();Ee+=":".concat(ya,":").concat(ga)}}else Ee=l;var ba=Ee?{ad_request_id:Ee}:{},Ta=v?{player_is_fullscreen:g}:{};return Qe({},_a,pa,fa,ha,ma,ba,Ta)};u().triggerAdRequest=function(){u().emit(P.AD_REQUEST,E())};var S=e.requestAds,O=S.bind(e);e.requestAds=function(){for(var h=arguments.length,A=new Array(h),j=0;j<h;j++)A[j]=arguments[j];var G,J,M=le(A,1),K=M[0];return l=K==null?void 0:K.adTagUrl,(G=(J=u()).triggerAdRequest)===null||G===void 0||G.call(J),O==null?void 0:O.apply(void 0,De(A))};var L,C,N,R,Y=function(h){d=h.getAdsManager(s),L=d.resize,C=L.bind(d),d.resize=function(A,j,G){return g=G===google.ima.ViewMode.FULLSCREEN,C==null?void 0:C(A,j,G)},N=d.init,R=N.bind(d),d.init=function(A,j,G){for(var J=arguments.length,M=new Array(J>3?J-3:0),K=3;K<J;K++)M[K-3]=arguments[K];return g=G===google.ima.ViewMode.FULLSCREEN,R==null?void 0:R.apply(void 0,[A,j,G].concat(De(M)))},la(d)},oe=function(h){b(),_&&u().emit(P.PAUSE),v=!0;var A=E(h);c=A.ad_asset_url,u().emit(P.AD_BREAK_START),u().emit(P.AD_PLAY,A)},se=function(h){var A=v;c=void 0,A&&(v=!1,u().emit(P.AD_BREAK_END)),f&&(u().emit(P.RENDITION_CHANGE,f),f=void 0),m()},V=function(h){var A=E(h);c=A.ad_asset_url,u().emit(P.AD_RESPONSE,A)},te=function(h){var A=E(h);u().emit(P.AD_PLAYING,A)},he=function(h){var A=E(h);u().emit(P.AD_PLAY,A),u().emit(P.AD_PLAYING,A)},me=function(h){var A=E(h);u().emit(P.AD_PAUSE,A)},ye=function(h){var A=E(h);u().emit(P.AD_FIRST_QUARTILE,A)},ue=function(h){var A=E(h);u().emit(P.AD_MID_POINT,A)},W=function(h){var A=E(h);u().emit(P.AD_THIRD_QUARTILE,A)},Pe=function(h){var A=E(h);u().emit(P.AD_ENDED,A)},Ne=function(h){var A=E(h);u().emit(P.AD_ENDED,A)},qe=function(h){v=!1,c=void 0,l=void 0,f=void 0,m()},Le=function(h){var A=E(h);u().emit(P.AD_RESPONSE,A)},Ie=function(h){var A=h.getError(),j=A.getType();j===google.ima.AdError.Type.AD_LOAD&&u().emit(P.AD_RESPONSE,E()),u().emit(P.AD_ERROR)},ua=function(h){h.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,Y),h.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,Ie)},da=function(h){h.removeEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,Y),h.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,Ie)},la=function(h){h.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,oe),h.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,se),h.addEventListener(google.ima.AdEvent.Type.LOADED,V),h.addEventListener(google.ima.AdEvent.Type.STARTED,te),h.addEventListener(google.ima.AdEvent.Type.RESUMED,he),h.addEventListener(google.ima.AdEvent.Type.PAUSED,me),h.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,ye),h.addEventListener(google.ima.AdEvent.Type.MIDPOINT,ue),h.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,W),h.addEventListener(google.ima.AdEvent.Type.COMPLETE,Pe),h.addEventListener(google.ima.AdEvent.Type.SKIPPED,Ne),h.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,qe),h.addEventListener(google.ima.AdEvent.Type.AD_METADATA,Le),h.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,Ie)},ca=function(h){h.removeEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,oe),h.removeEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,se),h.removeEventListener(google.ima.AdEvent.Type.LOADED,V),h.removeEventListener(google.ima.AdEvent.Type.STARTED,te),h.removeEventListener(google.ima.AdEvent.Type.RESUMED,he),h.removeEventListener(google.ima.AdEvent.Type.PAUSED,me),h.removeEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,ye),h.removeEventListener(google.ima.AdEvent.Type.MIDPOINT,ue),h.removeEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,W),h.removeEventListener(google.ima.AdEvent.Type.COMPLETE,Pe),h.removeEventListener(google.ima.AdEvent.Type.SKIPPED,Ne),h.removeEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,qe),h.removeEventListener(google.ima.AdEvent.Type.AD_METADATA,Le),h.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,Ie)};u().swapElement=function(h){ze.error("swapElement() is not supported when using google-ima.")};var va=u().destroy;u().destroy=function(){var h=u();da(e),S&&(e.requestAds=S,S=O=void 0),d&&(ca(d),L&&(d.resize=L,L=C=void 0),N&&(d.init=N,N=R=void 0),d=void 0),h.swapElement=function(){},h.triggerAdRequest=function(){},h.setEmitTranslator(void 0),h.setStateDataTranslator(void 0),h.setGetPlayheadTime(void 0),va()},ua(e)};var $t=ne.utils.findMediaElement,Gi=ne.monitor,Zt=function(i){return i.dashjs!=null},ea=function(i){return i.hlsjs!=null&&i.Hls!=null},ta=function(i,e){var t=e.hlsjs,a=e.Hls,r=function(s){var u;return(u=t==null?void 0:t.url)!==null&&u!==void 0?u:Te(s)},n=function(s){t=s.hlsjs,a=s.Hls},o=function(){t=void 0,a=void 0};return{getSrc:r,addHLSJS:n,removeHLSJS:o}},aa=function(i,e){var t=e.dashjs,a=function(o){var s;return(s=t==null?void 0:t.getSource())!==null&&s!==void 0?s:Te(o)},r=function(o){t=o.dashjs},n=function(){t=void 0};return{getSrc:a,addDashJS:r,removeDashJS:n}},ra=function(i,e){return ea(e)?ta(i,e):Zt(e)?aa(i,e):{getSrc:Te}},ia=function(i,e){var t=function(){var o=le($t(i),1),s=o[0];return s==null?void 0:s.mux},a=t(),r=a.addHLSJS,n=a.removeHLSJS;a.addHLSJS=function(o){e.addHLSJS(o),r(o)},a.removeHLSJS=function(){e.removeHLSJS(),n()}},na=function(i,e){var t=function(){var o=le($t(i),1),s=o[0];return s==null?void 0:s.mux},a=t(),r=a.addDashJS,n=a.removeDashJS;a.addDashJS=function(o){e.addDashJS(o),r(o)},a.removeDashJS=function(){e.removeDashJS(),n()}},oa=function(i,e){"addHLSJS"in e&&ia(i,e),"addDashJS"in e&&na(i,e)},Vi=function(e,t){var a=t.imaAdsLoader;if(Gi(e,t),!!a){var r=ra(e,t);dt(e,a,{getSrc:r.getSrc}),oa(e,r)}},Qi={initializeIma:dt,monitor:Vi},sa=function(){for(var i=arguments.length,e=new Array(i),t=0;t<i;t++)e[t]=arguments[t];return ne.apply(void 0,De(e))};Object.assign(sa,ne,Qi);var Wi=sa;
/*!
* JavaScript Cookie v2.1.3
* https://github.com/js-cookie/js-cookie
*
* Copyright 2006, 2015 Klaus Hartl & Fagner Brack
* Released under the MIT license
*/
