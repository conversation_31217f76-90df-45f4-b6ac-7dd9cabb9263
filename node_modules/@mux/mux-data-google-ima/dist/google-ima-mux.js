/*!
 * @mux/mux-data-google-ima
 * @version 0.2.8
 * @copyright 2025 Mux, Inc
 */
!function(){var e,t;e=this,t=function(){return function(){var e={254:function(e,t,r){e.exports=r(277).default},277:function(e,t,r){"use strict";r.d(t,{default:function(){return Er}});var a=Object.create,i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,d=function(e,t){return function(){return e&&(t=e(e=0)),t}},l=function(e,t){return function(){return t||e((t={exports:{}}).exports,t),t.exports}},c=function(e,t,r){return r=null!=e?a(s(e)):{},function(e,t,r,a){if(t&&"object"==typeof t||"function"==typeof t)for(var s,d=o(t),l=0,c=d.length;l<c;l++)s=d[l],!u.call(e,s)&&s!==r&&i(e,s,{get:function(e){return t[e]}.bind(null,s),enumerable:!(a=n(t,s))||a.enumerable});return e}(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)},_=l((function(e,t){var r;r="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t.exports=r}));function p(e,t){return null!=t&&"undefined"!=typeof Symbol&&t[Symbol.hasInstance]?!!t[Symbol.hasInstance](e):p(e,t)}var h=d((function(){h()}));function v(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}var f=d((function(){})),m=l((function(e,t){var r=Array.prototype.slice;t.exports=function(e,t){for(("length"in e||(e=[e]),e=r.call(e));e.length;){var a=e.shift(),i=t(a);if(i)return i;a.childNodes&&a.childNodes.length&&(e=r.call(a.childNodes).concat(e))}}})),y=l((function(e,t){function r(e,t){if(!p(this,r))return new r(e,t);this.data=e,this.nodeValue=e,this.length=e.length,this.ownerDocument=t||null}h(),t.exports=r,r.prototype.nodeType=8,r.prototype.nodeName="#comment",r.prototype.toString=function(){return"[object Comment]"}})),g=l((function(e,t){function r(e,t){if(!p(this,r))return new r(e);this.data=e||"",this.length=this.data.length,this.ownerDocument=t||null}h(),t.exports=r,r.prototype.type="DOMTextNode",r.prototype.nodeType=3,r.prototype.nodeName="#text",r.prototype.toString=function(){return this.data},r.prototype.replaceData=function(e,t,r){var a=this.data,i=a.substring(0,e),n=a.substring(e+t,a.length);this.data=i+r+n,this.length=this.data.length}})),b=l((function(e,t){t.exports=function(e){var t=this,r=e.type;e.target||(e.target=t),t.listeners||(t.listeners={});var a=t.listeners[r];if(a)return a.forEach((function(r){e.currentTarget=t,"function"==typeof r?r(e):r.handleEvent(e)}));t.parentNode&&t.parentNode.dispatchEvent(e)}})),T=l((function(e,t){t.exports=function(e,t){var r=this;r.listeners||(r.listeners={}),r.listeners[e]||(r.listeners[e]=[]),-1===r.listeners[e].indexOf(t)&&r.listeners[e].push(t)}})),E=l((function(e,t){t.exports=function(e,t){var r=this;if(r.listeners&&r.listeners[e]){var a=r.listeners[e],i=a.indexOf(t);-1!==i&&a.splice(i,1)}}})),w=l((function(e,t){f(),t.exports=a;var r=["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr"];function a(e){switch(e.nodeType){case 3:return o(e.data);case 8:return"\x3c!--"+e.data+"--\x3e";default:return function(e){var t=[],s=e.tagName;return"http://www.w3.org/1999/xhtml"===e.namespaceURI&&(s=s.toLowerCase()),t.push("<"+s+function(e){var t=[];for(var r in e)i(e,r)&&t.push({name:r,value:e[r]});for(var a in e._attributes)for(var o in e._attributes[a]){var s=e._attributes[a][o],u=(s.prefix?s.prefix+":":"")+o;t.push({name:u,value:s.value})}return e.className&&t.push({name:"class",value:e.className}),t.length?n(t):""}(e)+function(e){var t=e.dataset,r=[];for(var a in t)r.push({name:"data-"+a,value:t[a]});return r.length?n(r):""}(e)),r.indexOf(s)>-1?t.push(" />"):(t.push(">"),e.childNodes.length?t.push.apply(t,e.childNodes.map(a)):e.textContent||e.innerText?t.push(o(e.textContent||e.innerText)):e.innerHTML&&t.push(e.innerHTML),t.push("</"+s+">")),t.join("")}(e)}}function i(e,t){var r=v(e[t]);return"style"===t&&Object.keys(e.style).length>0||e.hasOwnProperty(t)&&("string"===r||"boolean"===r||"number"===r)&&"nodeName"!==t&&"className"!==t&&"tagName"!==t&&"textContent"!==t&&"innerText"!==t&&"namespaceURI"!==t&&"innerHTML"!==t}function n(e){var t=[];return e.forEach((function(e){var r=e.name,a=e.value;"style"===r&&(a=function(e){if("string"==typeof e)return e;var t="";return Object.keys(e).forEach((function(r){var a=e[r];r=r.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()})),t+=r+":"+a+";"})),t}(a)),t.push(r+'="'+function(e){return o(e).replace(/"/g,"&quot;")}(a)+'"')})),t.length?" "+t.join(" "):""}function o(e){var t="";return"string"==typeof e?t=e:e&&(t=e.toString()),t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}})),D=l((function(e,t){h();var r=m(),a=b(),i=T(),n=E(),o=w(),s="http://www.w3.org/1999/xhtml";function u(e,t,r){if(!p(this,u))return new u(e);var a=void 0===r?s:r||null;this.tagName=a===s?String(e).toUpperCase():e,this.nodeName=this.tagName,this.className="",this.dataset={},this.childNodes=[],this.parentNode=null,this.style={},this.ownerDocument=t||null,this.namespaceURI=a,this._attributes={},"INPUT"===this.tagName&&(this.type="text")}t.exports=u,u.prototype.type="DOMElement",u.prototype.nodeType=1,u.prototype.appendChild=function(e){return e.parentNode&&e.parentNode.removeChild(e),this.childNodes.push(e),e.parentNode=this,e},u.prototype.replaceChild=function(e,t){e.parentNode&&e.parentNode.removeChild(e);var r=this.childNodes.indexOf(t);return t.parentNode=null,this.childNodes[r]=e,e.parentNode=this,t},u.prototype.removeChild=function(e){var t=this.childNodes.indexOf(e);return this.childNodes.splice(t,1),e.parentNode=null,e},u.prototype.insertBefore=function(e,t){e.parentNode&&e.parentNode.removeChild(e);var r=null==t?-1:this.childNodes.indexOf(t);return r>-1?this.childNodes.splice(r,0,e):this.childNodes.push(e),e.parentNode=this,e},u.prototype.setAttributeNS=function(e,t,r){var a=null,i=t,n=t.indexOf(":");n>-1&&(a=t.substr(0,n),i=t.substr(n+1)),"INPUT"===this.tagName&&"type"===t?this.type=r:(this._attributes[e]||(this._attributes[e]={}))[i]={value:r,prefix:a}},u.prototype.getAttributeNS=function(e,t){var r=this._attributes[e],a=r&&r[t]&&r[t].value;return"INPUT"===this.tagName&&"type"===t?this.type:"string"!=typeof a?null:a},u.prototype.removeAttributeNS=function(e,t){var r=this._attributes[e];r&&delete r[t]},u.prototype.hasAttributeNS=function(e,t){var r=this._attributes[e];return!!r&&t in r},u.prototype.setAttribute=function(e,t){return this.setAttributeNS(null,e,t)},u.prototype.getAttribute=function(e){return this.getAttributeNS(null,e)},u.prototype.removeAttribute=function(e){return this.removeAttributeNS(null,e)},u.prototype.hasAttribute=function(e){return this.hasAttributeNS(null,e)},u.prototype.removeEventListener=n,u.prototype.addEventListener=i,u.prototype.dispatchEvent=a,u.prototype.focus=function(){},u.prototype.toString=function(){return o(this)},u.prototype.getElementsByClassName=function(e){var t=e.split(" "),a=[];return r(this,(function(e){if(1===e.nodeType){var r=(e.className||"").split(" ");t.every((function(e){return-1!==r.indexOf(e)}))&&a.push(e)}})),a},u.prototype.getElementsByTagName=function(e){e=e.toLowerCase();var t=[];return r(this.childNodes,(function(r){1===r.nodeType&&("*"===e||r.tagName.toLowerCase()===e)&&t.push(r)})),t},u.prototype.contains=function(e){return r(this,(function(t){return e===t}))||!1}})),k=l((function(e,t){h();var r=D();function a(e){if(!p(this,a))return new a;this.childNodes=[],this.parentNode=null,this.ownerDocument=e||null}t.exports=a,a.prototype.type="DocumentFragment",a.prototype.nodeType=11,a.prototype.nodeName="#document-fragment",a.prototype.appendChild=r.prototype.appendChild,a.prototype.replaceChild=r.prototype.replaceChild,a.prototype.removeChild=r.prototype.removeChild,a.prototype.toString=function(){return this.childNodes.map((function(e){return String(e)})).join("")}})),A=l((function(e,t){function r(e){}t.exports=r,r.prototype.initEvent=function(e,t,r){this.type=e,this.bubbles=t,this.cancelable=r},r.prototype.preventDefault=function(){}})),x=l((function(e,t){h();var r=m(),a=y(),i=g(),n=D(),o=k(),s=A(),u=b(),d=T(),l=E();function c(){if(!p(this,c))return new c;this.head=this.createElement("head"),this.body=this.createElement("body"),this.documentElement=this.createElement("html"),this.documentElement.appendChild(this.head),this.documentElement.appendChild(this.body),this.childNodes=[this.documentElement],this.nodeType=9}t.exports=c;var _=c.prototype;_.createTextNode=function(e){return new i(e,this)},_.createElementNS=function(e,t){var r=null===e?null:String(e);return new n(t,this,r)},_.createElement=function(e){return new n(e,this)},_.createDocumentFragment=function(){return new o(this)},_.createEvent=function(e){return new s(e)},_.createComment=function(e){return new a(e,this)},_.getElementById=function(e){return e=String(e),r(this.childNodes,(function(t){if(String(t.id)===e)return t}))||null},_.getElementsByClassName=n.prototype.getElementsByClassName,_.getElementsByTagName=n.prototype.getElementsByTagName,_.contains=n.prototype.contains,_.removeEventListener=l,_.addEventListener=d,_.dispatchEvent=u})),S=l((function(e,t){var r=x();t.exports=new r})),O=l((function(e,t){var r,a="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},i=S();"undefined"!=typeof document?r=document:(r=a["__GLOBAL_DOCUMENT_CACHE@4"])||(r=a["__GLOBAL_DOCUMENT_CACHE@4"]=i),t.exports=r}));function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}function P(e,t){if(e){if("string"==typeof e)return R(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(r);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(e,t)}}function q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n=[],o=!0,s=!1;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!t||n.length!==t);o=!0);}catch(e){s=!0,i=e}finally{try{!o&&null!=r.return&&r.return()}finally{if(s)throw i}}return n}}(e,t)||P(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var L=c(_()),N=c(_()),I=c(_()),C={now:function(){var e=I.default.performance,t=e&&e.timing,r=t&&t.navigationStart,a="number"==typeof r&&"function"==typeof e.now?r+e.now():Date.now();return Math.round(a)}},j=function(){var e,t,r;if("function"==typeof(null===(e=N.default.crypto)||void 0===e?void 0:e.getRandomValues)){r=new Uint8Array(32),N.default.crypto.getRandomValues(r);for(var a=0;a<32;a++)r[a]=r[a]%16}else{r=[];for(var i=0;i<32;i++)r[i]=16*Math.random()|0}var n=0;t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t="x"===e?r[n]:3&r[n]|8;return n++,t.toString(16)}));var o=C.now(),s=null==o?void 0:o.toString(16).substring(3);return s?t.substring(0,28)+s:t},H=function(){return("000000"+(Math.random()*Math.pow(36,6)<<0).toString(36)).slice(-6)},M=function(e){if(e&&void 0!==e.nodeName)return e.muxId||(e.muxId=H()),e.muxId;var t;try{t=document.querySelector(e)}catch(e){}return t&&!t.muxId&&(t.muxId=e),(null==t?void 0:t.muxId)||e},U=function(e){var t;e&&void 0!==e.nodeName?e=M(t=e):t=document.querySelector(e);var r=t&&t.nodeName?t.nodeName.toLowerCase():"";return[t,e,r]};function B(e){return function(e){if(Array.isArray(e))return R(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||P(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var F=0,G=1,Q=2,V=3,J=4,W=function(e){var t,r,a,i,n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,s=e?[console,e]:[console],u=(t=console.trace).bind.apply(t,B(s)),d=(r=console.info).bind.apply(r,B(s)),l=(a=console.debug).bind.apply(a,B(s)),c=(i=console.warn).bind.apply(i,B(s)),_=(n=console.error).bind.apply(n,B(s)),p=o;return{trace:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!(p>F))return u.apply(void 0,B(t))},debug:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!(p>G))return l.apply(void 0,B(t))},info:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!(p>Q))return d.apply(void 0,B(t))},warn:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!(p>V))return c.apply(void 0,B(t))},error:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!(p>J))return _.apply(void 0,B(t))},get level(){return p},set level(e){e!==this.level&&(p=null!=e?e:o)}}}("[mux]"),z=c(_());function K(){return"1"===(z.default.doNotTrack||z.default.navigator&&z.default.navigator.doNotTrack)}function Y(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function X(e,t){if(!p(e,t))throw new TypeError("Cannot call a class as a function")}function $(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}function Z(e,t,r){return t&&$(e.prototype,t),r&&$(e,r),e}function ee(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function te(e){return(te=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function re(e,t,r){return re="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var a=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=te(e)););return e}(e,t);if(a){var i=Object.getOwnPropertyDescriptor(a,t);return i.get?i.get.call(r||e):i.value}},re(e,t,r||e)}function ae(e,t){return(ae=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function ie(e,t){return!t||"object"!==v(t)&&"function"!=typeof t?Y(e):t}function ne(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,a=te(e);if(t){var i=te(this).constructor;r=Reflect.construct(a,arguments,i)}else r=a.apply(this,arguments);return ie(this,r)}}h(),f();var oe=function(e){return se(e)[0]},se=function(e){if("string"!=typeof e||""===e)return["localhost"];var t,r=(e.match(/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/)||[])[4];return r&&(t=(r.match(/[^\.]+\.[^\.]+$/)||[])[0]),[r,t]},ue=c(_()),de={exists:function(){var e=ue.default.performance;return void 0!==(e&&e.timing)},domContentLoadedEventEnd:function(){var e=ue.default.performance,t=e&&e.timing;return t&&t.domContentLoadedEventEnd},navigationStart:function(){var e=ue.default.performance,t=e&&e.timing;return t&&t.navigationStart}};function le(e,t,r){r=void 0===r?1:r,e[t]=e[t]||0,e[t]+=r}function ce(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},a=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),a.forEach((function(t){ee(e,t,r[t])}))}return e}function _e(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}(Object(t)).forEach((function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})),e}var pe=["x-request-id","cf-ray","x-amz-cf-id","x-akamai-request-id"],he=["x-cdn","content-type"].concat(pe);function ve(e){var t={};return(e=e||"").trim().split(/[\r\n]+/).forEach((function(e){if(e){var r=e.split(": "),a=r.shift();a&&(he.indexOf(a.toLowerCase())>=0||0===a.toLowerCase().indexOf("x-litix-"))&&(t[a]=r.join(": "))}})),t}function fe(e){if(e){var t=pe.find((function(t){return void 0!==e[t]}));return t?e[t]:void 0}}var me=function(e){var t={};for(var r in e){var a=e[r];-1!==a["DATA-ID"].search("io.litix.data.")&&(t[a["DATA-ID"].replace("io.litix.data.","")]=a.VALUE)}return t},ye=function(e){if(!e)return{};var t=de.navigationStart(),r=e.loading,a=r?r.start:e.trequest,i=r?r.first:e.tfirst,n=r?r.end:e.tload;return{bytesLoaded:e.total,requestStart:Math.round(t+a),responseStart:Math.round(t+i),responseEnd:Math.round(t+n)}},ge=function(e){if(e&&"function"==typeof e.getAllResponseHeaders)return ve(e.getAllResponseHeaders())},be=function(e,t){if(!e||!e.requestEndDate)return{};var r,a=oe(e.url),i=e.url,n=e.bytesLoaded,o=new Date(e.requestStartDate).getTime(),s=new Date(e.firstByteDate).getTime(),u=new Date(e.requestEndDate).getTime(),d=isNaN(e.duration)?0:e.duration,l="function"==typeof t.getMetricsFor?t.getMetricsFor(e.mediaType).HttpList:t.getDashMetrics().getHttpRequests(e.mediaType);return l.length>0&&(r=ve(l[l.length-1]._responseHeaders||"")),{requestStart:o,requestResponseStart:s,requestResponseEnd:u,requestBytesLoaded:n,requestResponseHeaders:r,requestMediaDuration:d,requestHostname:a,requestUrl:i,requestId:r?fe(r):void 0}},Te=function(e,t){var r=t.getQualityFor(e),a=t.getCurrentTrackFor(e).bitrateList;return a?{currentLevel:r,renditionWidth:a[r].width||null,renditionHeight:a[r].height||null,renditionBitrate:a[r].bandwidth}:{}},Ee=function(e){var t;return null===(t=e.match(/.*codecs\*?="(.*)"/))||void 0===t?void 0:t[1]},we=function(e){try{var t,r;return null===(r=e.getVersion)||void 0===r||null===(t=r.call(e))||void 0===t?void 0:t.split(".").map((function(e){return parseInt(e)}))[0]}catch(e){return!1}},De=0,ke=function(){function e(){X(this,e),ee(this,"_listeners",void 0)}return Z(e,[{key:"on",value:function(e,t,r){return t._eventEmitterGuid=t._eventEmitterGuid||++De,this._listeners=this._listeners||{},this._listeners[e]=this._listeners[e]||[],r&&(t=t.bind(r)),this._listeners[e].push(t),t}},{key:"off",value:function(e,t){var r=this._listeners&&this._listeners[e];r&&r.forEach((function(e,a){e._eventEmitterGuid===t._eventEmitterGuid&&r.splice(a,1)}))}},{key:"one",value:function(e,t,r){var a=this;t._eventEmitterGuid=t._eventEmitterGuid||++De;var i=function(){a.off(e,i),t.apply(r||this,arguments)};i._eventEmitterGuid=t._eventEmitterGuid,this.on(e,i)}},{key:"emit",value:function(e,t){var r=this;if(this._listeners){t=t||{};var a=this._listeners["before*"]||[],i=this._listeners[e]||[],n=this._listeners["after"+e]||[],o=function(t,a){(t=t.slice()).forEach((function(t){t.call(r,{type:e},a)}))};o(a,t),o(i,t),o(n,t)}}}]),e}(),Ae=ke,xe=c(_()),Se=function(){function e(t){var r=this;X(this,e),ee(this,"_playbackHeartbeatInterval",void 0),ee(this,"_playheadShouldBeProgressing",void 0),ee(this,"pm",void 0),this.pm=t,this._playbackHeartbeatInterval=null,this._playheadShouldBeProgressing=!1,t.on("playing",(function(){r._playheadShouldBeProgressing=!0})),t.on("play",this._startPlaybackHeartbeatInterval.bind(this)),t.on("playing",this._startPlaybackHeartbeatInterval.bind(this)),t.on("adbreakstart",this._startPlaybackHeartbeatInterval.bind(this)),t.on("adplay",this._startPlaybackHeartbeatInterval.bind(this)),t.on("adplaying",this._startPlaybackHeartbeatInterval.bind(this)),t.on("devicewake",this._startPlaybackHeartbeatInterval.bind(this)),t.on("viewstart",this._startPlaybackHeartbeatInterval.bind(this)),t.on("rebufferstart",this._startPlaybackHeartbeatInterval.bind(this)),t.on("pause",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("ended",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("viewend",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("error",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("aderror",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("adpause",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("adended",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("adbreakend",this._stopPlaybackHeartbeatInterval.bind(this)),t.on("seeked",(function(){t.data.player_is_paused?r._stopPlaybackHeartbeatInterval():r._startPlaybackHeartbeatInterval()})),t.on("timeupdate",(function(){null!==r._playbackHeartbeatInterval&&t.emit("playbackheartbeat")})),t.on("devicesleep",(function(e,a){null!==r._playbackHeartbeatInterval&&(xe.default.clearInterval(r._playbackHeartbeatInterval),t.emit("playbackheartbeatend",{viewer_time:a.viewer_time}),r._playbackHeartbeatInterval=null)}))}return Z(e,[{key:"_startPlaybackHeartbeatInterval",value:function(){var e=this;null===this._playbackHeartbeatInterval&&(this.pm.emit("playbackheartbeat"),this._playbackHeartbeatInterval=xe.default.setInterval((function(){e.pm.emit("playbackheartbeat")}),this.pm.playbackHeartbeatTime))}},{key:"_stopPlaybackHeartbeatInterval",value:function(){this._playheadShouldBeProgressing=!1,null!==this._playbackHeartbeatInterval&&(xe.default.clearInterval(this._playbackHeartbeatInterval),this.pm.emit("playbackheartbeatend"),this._playbackHeartbeatInterval=null)}}]),e}(),Oe=function e(t){var r=this;X(this,e),ee(this,"viewErrored",void 0),t.on("viewinit",(function(){r.viewErrored=!1})),t.on("error",(function(e,a){try{var i=t.errorTranslator({player_error_code:a.player_error_code,player_error_message:a.player_error_message,player_error_context:a.player_error_context,player_error_severity:a.player_error_severity,player_error_business_exception:a.player_error_business_exception});i&&(t.data.player_error_code=i.player_error_code||a.player_error_code,t.data.player_error_message=i.player_error_message||a.player_error_message,t.data.player_error_context=i.player_error_context||a.player_error_context,t.data.player_error_severity=i.player_error_severity||a.player_error_severity,t.data.player_error_business_exception=i.player_error_business_exception||a.player_error_business_exception,r.viewErrored=!0)}catch(e){t.mux.log.warn("Exception in error translator callback.",e),r.viewErrored=!0}})),t.on("aftererror",(function(){var e,r,a,i,n;null===(e=t.data)||void 0===e||delete e.player_error_code,null===(r=t.data)||void 0===r||delete r.player_error_message,null===(a=t.data)||void 0===a||delete a.player_error_context,null===(i=t.data)||void 0===i||delete i.player_error_severity,null===(n=t.data)||void 0===n||delete n.player_error_business_exception}))},Re=function(){function e(t){X(this,e),ee(this,"_watchTimeTrackerLastCheckedTime",void 0),ee(this,"pm",void 0),this.pm=t,this._watchTimeTrackerLastCheckedTime=null,t.on("playbackheartbeat",this._updateWatchTime.bind(this)),t.on("playbackheartbeatend",this._clearWatchTimeState.bind(this))}return Z(e,[{key:"_updateWatchTime",value:function(e,t){var r=t.viewer_time;null===this._watchTimeTrackerLastCheckedTime&&(this._watchTimeTrackerLastCheckedTime=r),le(this.pm.data,"view_watch_time",r-this._watchTimeTrackerLastCheckedTime),this._watchTimeTrackerLastCheckedTime=r}},{key:"_clearWatchTimeState",value:function(e,t){this._updateWatchTime(e,t),this._watchTimeTrackerLastCheckedTime=null}}]),e}(),Pe=function(){function e(t){var r=this;X(this,e),ee(this,"_playbackTimeTrackerLastPlayheadPosition",void 0),ee(this,"_lastTime",void 0),ee(this,"_isAdPlaying",void 0),ee(this,"_callbackUpdatePlaybackTime",void 0),ee(this,"pm",void 0),this.pm=t,this._playbackTimeTrackerLastPlayheadPosition=-1,this._lastTime=C.now(),this._isAdPlaying=!1,this._callbackUpdatePlaybackTime=null;var a=this._startPlaybackTimeTracking.bind(this);t.on("playing",a),t.on("adplaying",a),t.on("seeked",a);var i=this._stopPlaybackTimeTracking.bind(this);t.on("playbackheartbeatend",i),t.on("seeking",i),t.on("adplaying",(function(){r._isAdPlaying=!0})),t.on("adended",(function(){r._isAdPlaying=!1})),t.on("adpause",(function(){r._isAdPlaying=!1})),t.on("adbreakstart",(function(){r._isAdPlaying=!1})),t.on("adbreakend",(function(){r._isAdPlaying=!1})),t.on("adplay",(function(){r._isAdPlaying=!1})),t.on("viewinit",(function(){r._playbackTimeTrackerLastPlayheadPosition=-1,r._lastTime=C.now(),r._isAdPlaying=!1,r._callbackUpdatePlaybackTime=null}))}return Z(e,[{key:"_startPlaybackTimeTracking",value:function(){null===this._callbackUpdatePlaybackTime&&(this._callbackUpdatePlaybackTime=this._updatePlaybackTime.bind(this),this._playbackTimeTrackerLastPlayheadPosition=this.pm.data.player_playhead_time,this.pm.on("playbackheartbeat",this._callbackUpdatePlaybackTime))}},{key:"_stopPlaybackTimeTracking",value:function(){this._callbackUpdatePlaybackTime&&(this._updatePlaybackTime(),this.pm.off("playbackheartbeat",this._callbackUpdatePlaybackTime),this._callbackUpdatePlaybackTime=null,this._playbackTimeTrackerLastPlayheadPosition=-1)}},{key:"_updatePlaybackTime",value:function(){var e=this.pm.data.player_playhead_time,t=C.now(),r=-1;this._playbackTimeTrackerLastPlayheadPosition>=0&&e>this._playbackTimeTrackerLastPlayheadPosition?r=e-this._playbackTimeTrackerLastPlayheadPosition:this._isAdPlaying&&(r=t-this._lastTime),r>0&&r<=1e3&&le(this.pm.data,"view_content_playback_time",r),this._playbackTimeTrackerLastPlayheadPosition=e,this._lastTime=t}}]),e}(),qe=function(){function e(t){X(this,e),ee(this,"pm",void 0),this.pm=t;var r=this._updatePlayheadTime.bind(this);t.on("playbackheartbeat",r),t.on("playbackheartbeatend",r),t.on("timeupdate",r),t.on("destroy",(function(){t.off("timeupdate",r)}))}return Z(e,[{key:"_updateMaxPlayheadPosition",value:function(){this.pm.data.view_max_playhead_position=void 0===this.pm.data.view_max_playhead_position?this.pm.data.player_playhead_time:Math.max(this.pm.data.view_max_playhead_position,this.pm.data.player_playhead_time)}},{key:"_updatePlayheadTime",value:function(e,t){var r=this,a=function(){r.pm.currentFragmentPDT&&r.pm.currentFragmentStart&&(r.pm.data.player_program_time=r.pm.currentFragmentPDT+r.pm.data.player_playhead_time-r.pm.currentFragmentStart)};if(t&&t.player_playhead_time)this.pm.data.player_playhead_time=t.player_playhead_time,a(),this._updateMaxPlayheadPosition();else if(this.pm.getPlayheadTime){var i=this.pm.getPlayheadTime();void 0!==i&&(this.pm.data.player_playhead_time=i,a(),this._updateMaxPlayheadPosition())}}}]),e}(),Le=3e5,Ne=function e(t){if(X(this,e),!t.disableRebufferTracking){var r,a=function(e,t){i(t),r=void 0},i=function(e){if(r){var a=e.viewer_time-r;le(t.data,"view_rebuffer_duration",a),r=e.viewer_time,t.data.view_rebuffer_duration>Le&&(t.emit("viewend"),t.send("viewend"),t.mux.log.warn("Ending view after rebuffering for longer than ".concat(Le,"ms, future events will be ignored unless a programchange or videochange occurs.")))}t.data.view_watch_time>=0&&t.data.view_rebuffer_count>0&&(t.data.view_rebuffer_frequency=t.data.view_rebuffer_count/t.data.view_watch_time,t.data.view_rebuffer_percentage=t.data.view_rebuffer_duration/t.data.view_watch_time)};t.on("playbackheartbeat",(function(e,t){return i(t)})),t.on("rebufferstart",(function(e,i){r||(le(t.data,"view_rebuffer_count",1),r=i.viewer_time,t.one("rebufferend",a))})),t.on("viewinit",(function(){r=void 0,t.off("rebufferend",a)}))}},Ie=function(){function e(t){var r=this;X(this,e),ee(this,"_lastCheckedTime",void 0),ee(this,"_lastPlayheadTime",void 0),ee(this,"_lastPlayheadTimeUpdatedTime",void 0),ee(this,"_rebuffering",void 0),ee(this,"pm",void 0),this.pm=t,!t.disableRebufferTracking&&!t.disablePlayheadRebufferTracking&&(this._lastCheckedTime=null,this._lastPlayheadTime=null,this._lastPlayheadTimeUpdatedTime=null,t.on("playbackheartbeat",this._checkIfRebuffering.bind(this)),t.on("playbackheartbeatend",this._cleanupRebufferTracker.bind(this)),t.on("seeking",(function(){r._cleanupRebufferTracker(null,{viewer_time:C.now()})})))}return Z(e,[{key:"_checkIfRebuffering",value:function(e,t){if(this.pm.seekingTracker.isSeeking||this.pm.adTracker.isAdBreak||!this.pm.playbackHeartbeat._playheadShouldBeProgressing)this._cleanupRebufferTracker(e,t);else if(null!==this._lastCheckedTime)if(this._lastPlayheadTime===this.pm.data.player_playhead_time){var r=t.viewer_time-this._lastPlayheadTimeUpdatedTime;"number"==typeof this.pm.sustainedRebufferThreshold&&r>=this.pm.sustainedRebufferThreshold&&(this._rebuffering||(this._rebuffering=!0,this.pm.emit("rebufferstart",{viewer_time:this._lastPlayheadTimeUpdatedTime}))),this._lastCheckedTime=t.viewer_time}else this._cleanupRebufferTracker(e,t,!0);else this._prepareRebufferTrackerState(t.viewer_time)}},{key:"_clearRebufferTrackerState",value:function(){this._lastCheckedTime=null,this._lastPlayheadTime=null,this._lastPlayheadTimeUpdatedTime=null}},{key:"_prepareRebufferTrackerState",value:function(e){this._lastCheckedTime=e,this._lastPlayheadTime=this.pm.data.player_playhead_time,this._lastPlayheadTimeUpdatedTime=e}},{key:"_cleanupRebufferTracker",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(this._rebuffering)this._rebuffering=!1,this.pm.emit("rebufferend",{viewer_time:t.viewer_time});else{if(null===this._lastCheckedTime)return;var a=this.pm.data.player_playhead_time-this._lastPlayheadTime,i=t.viewer_time-this._lastPlayheadTimeUpdatedTime;"number"==typeof this.pm.minimumRebufferDuration&&a>0&&i-a>this.pm.minimumRebufferDuration&&(this._lastCheckedTime=null,this.pm.emit("rebufferstart",{viewer_time:this._lastPlayheadTimeUpdatedTime}),this.pm.emit("rebufferend",{viewer_time:this._lastPlayheadTimeUpdatedTime+i-a}))}r?this._prepareRebufferTrackerState(t.viewer_time):this._clearRebufferTrackerState()}}]),e}(),Ce=Ie,je=function(){function e(t){var r=this;X(this,e),ee(this,"NAVIGATION_START",void 0),ee(this,"pm",void 0),this.pm=t,t.on("viewinit",(function(){var e=t.data,a=e.view_id;if(!e.view_program_changed){var i=function(e,i){var n=i.viewer_time;("playing"===e.type&&void 0===t.data.view_time_to_first_frame||"adplaying"===e.type&&(void 0===t.data.view_time_to_first_frame||r._inPrerollPosition()))&&r.calculateTimeToFirstFrame(n||C.now(),a)};t.one("playing",i),t.one("adplaying",i),t.one("viewend",(function(){t.off("playing",i),t.off("adplaying",i)}))}}))}return Z(e,[{key:"_inPrerollPosition",value:function(){return void 0===this.pm.data.view_content_playback_time||this.pm.data.view_content_playback_time<=1e3}},{key:"calculateTimeToFirstFrame",value:function(e,t){t===this.pm.data.view_id&&(this.pm.watchTimeTracker._updateWatchTime(null,{viewer_time:e}),this.pm.data.view_time_to_first_frame=this.pm.data.view_watch_time,(this.pm.data.player_autoplay_on||this.pm.data.video_is_autoplay)&&this.NAVIGATION_START&&(this.pm.data.view_aggregate_startup_time=this.pm.data.view_start+this.pm.data.view_watch_time-this.NAVIGATION_START))}}]),e}(),He=function e(t){var r=this;X(this,e),ee(this,"_lastPlayerHeight",void 0),ee(this,"_lastPlayerWidth",void 0),ee(this,"_lastPlayheadPosition",void 0),ee(this,"_lastSourceHeight",void 0),ee(this,"_lastSourceWidth",void 0),t.on("viewinit",(function(){r._lastPlayheadPosition=-1})),["pause","rebufferstart","seeking","error","adbreakstart","hb","renditionchange","orientationchange","viewend"].forEach((function(e){t.on(e,(function(){if(r._lastPlayheadPosition>=0&&t.data.player_playhead_time>=0&&r._lastPlayerWidth>=0&&r._lastSourceWidth>0&&r._lastPlayerHeight>=0&&r._lastSourceHeight>0){var e=t.data.player_playhead_time-r._lastPlayheadPosition;if(e<0)return void(r._lastPlayheadPosition=-1);var a=Math.min(r._lastPlayerWidth/r._lastSourceWidth,r._lastPlayerHeight/r._lastSourceHeight),i=Math.max(0,a-1),n=Math.max(0,1-a);t.data.view_max_upscale_percentage=Math.max(t.data.view_max_upscale_percentage||0,i),t.data.view_max_downscale_percentage=Math.max(t.data.view_max_downscale_percentage||0,n),le(t.data,"view_total_content_playback_time",e),le(t.data,"view_total_upscaling",i*e),le(t.data,"view_total_downscaling",n*e)}r._lastPlayheadPosition=-1}))})),["playing","hb","renditionchange","orientationchange"].forEach((function(e){t.on(e,(function(){r._lastPlayheadPosition=t.data.player_playhead_time,r._lastPlayerWidth=t.data.player_width,r._lastPlayerHeight=t.data.player_height,r._lastSourceWidth=t.data.video_source_width,r._lastSourceHeight=t.data.video_source_height}))}))},Me=function e(t){var r=this;X(this,e),ee(this,"isSeeking",void 0),this.isSeeking=!1;var a=-1,i=function(){var e=C.now(),i=(t.data.viewer_time||e)-(a||e);le(t.data,"view_seek_duration",i),t.data.view_max_seek_time=Math.max(t.data.view_max_seek_time||0,i),r.isSeeking=!1,a=-1};t.on("seeking",(function(e,n){Object.assign(t.data,n),r.isSeeking&&n.viewer_time-a<=2e3?a=n.viewer_time:(r.isSeeking&&i(),r.isSeeking=!0,a=n.viewer_time,le(t.data,"view_seek_count",1),t.send("seeking"))})),t.on("seeked",(function(){i()})),t.on("viewend",(function(){r.isSeeking&&(i(),t.send("seeked")),r.isSeeking=!1,a=-1}))},Ue=function(e,t){e.push(t),e.sort((function(e,t){return e.viewer_time-t.viewer_time}))},Be=["adbreakstart","adrequest","adresponse","adplay","adplaying","adpause","adended","adbreakend","aderror","adclicked","adskipped"],Fe=function(){function e(t){var r=this;X(this,e),ee(this,"_adHasPlayed",void 0),ee(this,"_adRequests",void 0),ee(this,"_adResponses",void 0),ee(this,"_currentAdRequestNumber",void 0),ee(this,"_currentAdResponseNumber",void 0),ee(this,"_prerollPlayTime",void 0),ee(this,"_wouldBeNewAdPlay",void 0),ee(this,"isAdBreak",void 0),ee(this,"pm",void 0),this.pm=t,t.on("viewinit",(function(){r.isAdBreak=!1,r._currentAdRequestNumber=0,r._currentAdResponseNumber=0,r._adRequests=[],r._adResponses=[],r._adHasPlayed=!1,r._wouldBeNewAdPlay=!0,r._prerollPlayTime=void 0})),Be.forEach((function(e){return t.on(e,r._updateAdData.bind(r))}));var a=function(){r.isAdBreak=!1};t.on("adbreakstart",(function(){r.isAdBreak=!0})),t.on("play",a),t.on("playing",a),t.on("viewend",a),t.on("adrequest",(function(e,a){a=Object.assign({ad_request_id:"generatedAdRequestId"+r._currentAdRequestNumber++},a),Ue(r._adRequests,a),le(t.data,"view_ad_request_count"),r.inPrerollPosition()&&(t.data.view_preroll_requested=!0,r._adHasPlayed||le(t.data,"view_preroll_request_count"))})),t.on("adresponse",(function(e,a){a=Object.assign({ad_request_id:"generatedAdRequestId"+r._currentAdResponseNumber++},a),Ue(r._adResponses,a);var i=r.findAdRequest(a.ad_request_id);i&&le(t.data,"view_ad_request_time",Math.max(0,a.viewer_time-i.viewer_time))})),t.on("adplay",(function(e,a){r._adHasPlayed=!0,r._wouldBeNewAdPlay&&(r._wouldBeNewAdPlay=!1,le(t.data,"view_ad_played_count")),r.inPrerollPosition()&&!t.data.view_preroll_played&&(t.data.view_preroll_played=!0,r._adRequests.length>0&&(t.data.view_preroll_request_time=Math.max(0,a.viewer_time-r._adRequests[0].viewer_time)),t.data.view_start&&(t.data.view_startup_preroll_request_time=Math.max(0,a.viewer_time-t.data.view_start)),r._prerollPlayTime=a.viewer_time)})),t.on("adplaying",(function(e,a){r.inPrerollPosition()&&void 0===t.data.view_preroll_load_time&&void 0!==r._prerollPlayTime&&(t.data.view_preroll_load_time=a.viewer_time-r._prerollPlayTime,t.data.view_startup_preroll_load_time=a.viewer_time-r._prerollPlayTime)})),t.on("adclicked",(function(e,a){r._wouldBeNewAdPlay||le(t.data,"view_ad_clicked_count")})),t.on("adskipped",(function(e,a){r._wouldBeNewAdPlay||le(t.data,"view_ad_skipped_count")})),t.on("adended",(function(){r._wouldBeNewAdPlay=!0})),t.on("aderror",(function(){r._wouldBeNewAdPlay=!0}))}return Z(e,[{key:"inPrerollPosition",value:function(){return void 0===this.pm.data.view_content_playback_time||this.pm.data.view_content_playback_time<=1e3}},{key:"findAdRequest",value:function(e){for(var t=0;t<this._adRequests.length;t++)if(this._adRequests[t].ad_request_id===e)return this._adRequests[t]}},{key:"_updateAdData",value:function(e,t){if(this.inPrerollPosition()){if(!this.pm.data.view_preroll_ad_tag_hostname&&t.ad_tag_url){var r=q(se(t.ad_tag_url),2),a=r[0],i=r[1];this.pm.data.view_preroll_ad_tag_domain=i,this.pm.data.view_preroll_ad_tag_hostname=a}if(!this.pm.data.view_preroll_ad_asset_hostname&&t.ad_asset_url){var n=q(se(t.ad_asset_url),2),o=n[0],s=n[1];this.pm.data.view_preroll_ad_asset_domain=s,this.pm.data.view_preroll_ad_asset_hostname=o}}this.pm.data.ad_asset_url=null==t?void 0:t.ad_asset_url,this.pm.data.ad_tag_url=null==t?void 0:t.ad_tag_url,this.pm.data.ad_creative_id=null==t?void 0:t.ad_creative_id,this.pm.data.ad_id=null==t?void 0:t.ad_id,this.pm.data.ad_universal_id=null==t?void 0:t.ad_universal_id}}]),e}(),Ge=c(_()),Qe=function e(t){X(this,e);var r,a,i=function(){t.disableRebufferTracking||r&&(le(t.data,"view_waiting_rebuffer_duration",C.now()-r),r=!1,Ge.default.clearInterval(a))},n=!1,o=function(){n=!1,i()};t.on("waiting",(function(){n&&(t.disableRebufferTracking||(le(t.data,"view_waiting_rebuffer_count",1),r=C.now(),a=Ge.default.setInterval((function(){if(r){var e=C.now();le(t.data,"view_waiting_rebuffer_duration",e-r),r=e}}),250)))})),t.on("playing",(function(){i(),n=!0})),t.on("pause",o),t.on("seeking",o)},Ve=function e(t){var r=this;X(this,e),ee(this,"lastWallClockTime",void 0);var a=function(){r.lastWallClockTime=C.now(),t.on("before*",i)},i=function(e){var a=C.now(),i=r.lastWallClockTime;r.lastWallClockTime=a,a-i>3e4&&(t.emit("devicesleep",{viewer_time:i}),Object.assign(t.data,{viewer_time:i}),t.send("devicesleep"),t.emit("devicewake",{viewer_time:a}),Object.assign(t.data,{viewer_time:a}),t.send("devicewake"))};t.one("playbackheartbeat",a),t.on("playbackheartbeatend",(function(){t.off("before*",i),t.one("playbackheartbeat",a)}))},Je=c(_()),We=function(){var e=function(){for(var e=0,t={};e<arguments.length;e++){var r=arguments[e];for(var a in r)t[a]=r[a]}return t};function t(r){function a(t,i,n){var o;if("undefined"!=typeof document){if(arguments.length>1){if("number"==typeof(n=e({path:"/"},a.defaults,n)).expires){var s=new Date;s.setMilliseconds(s.getMilliseconds()+864e5*n.expires),n.expires=s}try{o=JSON.stringify(i),/^[\{\[]/.test(o)&&(i=o)}catch(e){}return i=r.write?r.write(i,t):encodeURIComponent(String(i)).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent),t=(t=(t=encodeURIComponent(String(t))).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent)).replace(/[\(\)]/g,escape),document.cookie=[t,"=",i,n.expires?"; expires="+n.expires.toUTCString():"",n.path?"; path="+n.path:"",n.domain?"; domain="+n.domain:"",n.secure?"; secure":""].join("")}t||(o={});for(var u=document.cookie?document.cookie.split("; "):[],d=/(%[0-9A-Z]{2})+/g,l=0;l<u.length;l++){var c=u[l].split("="),_=c.slice(1).join("=");'"'===_.charAt(0)&&(_=_.slice(1,-1));try{var p=c[0].replace(d,decodeURIComponent);if(_=r.read?r.read(_,p):r(_,p)||_.replace(d,decodeURIComponent),this.json)try{_=JSON.parse(_)}catch(e){}if(t===p){o=_;break}t||(o[p]=_)}catch(e){}}return o}}return a.set=a,a.get=function(e){return a.call(a,e)},a.getJSON=function(){return a.apply({json:!0},[].slice.call(arguments))},a.defaults={},a.remove=function(t,r){a(t,"",e(r,{expires:-1}))},a.withConverter=t,a}return t((function(){}))}(),ze="muxData",Ke=function(){var e;try{e=(We.get(ze)||"").split("&").reduce((function(e,t){var r=q(t.split("="),2),a=r[0],i=r[1],n=+i,o=i&&n==i?n:i;return e[a]=o,e}),{})}catch(t){e={}}return e},Ye=function(e){try{We.set(ze,(t=e,Object.entries(t).map((function(e){var t=q(e,2),r=t[0],a=t[1];return"".concat(r,"=").concat(a)})).join("&")),{expires:365})}catch(e){}var t},Xe=function(){var e=Ke();return e.mux_viewer_id=e.mux_viewer_id||j(),e.msn=e.msn||Math.random(),Ye(e),{mux_viewer_id:e.mux_viewer_id,mux_sample_number:e.msn}};function $e(e,t){var r=t.beaconCollectionDomain,a=t.beaconDomain;if(r)return"https://"+r;var i=a||"litix.io";return(e=e||"inferred").match(/^[a-z0-9]+$/)?"https://"+e+"."+i:"https://img.litix.io/a.gif"}var Ze=c(_()),et=function(){var e;switch(tt()){case"cellular":e="cellular";break;case"ethernet":e="wired";break;case"wifi":e="wifi";break;case void 0:break;default:e="other"}return e},tt=function(){var e=Ze.default.navigator,t=e&&(e.connection||e.mozConnection||e.webkitConnection);return t&&t.type};et.getConnectionFromAPI=tt;var rt=et,at=nt({a:"env",b:"beacon",c:"custom",d:"ad",e:"event",f:"experiment",i:"internal",m:"mux",n:"response",p:"player",q:"request",r:"retry",s:"session",t:"timestamp",u:"viewer",v:"video",w:"page",x:"view",y:"sub"}),it=nt({ad:"ad",af:"affiliate",ag:"aggregate",ap:"api",al:"application",ao:"audio",ar:"architecture",as:"asset",au:"autoplay",av:"average",bi:"bitrate",bn:"brand",br:"break",bw:"browser",by:"bytes",bz:"business",ca:"cached",cb:"cancel",cc:"codec",cd:"code",cg:"category",ch:"changed",ci:"client",ck:"clicked",cl:"canceled",cn:"config",co:"count",ce:"counter",cp:"complete",cq:"creator",cr:"creative",cs:"captions",ct:"content",cu:"current",cx:"connection",cz:"context",dg:"downscaling",dm:"domain",dn:"cdn",do:"downscale",dr:"drm",dp:"dropped",du:"duration",dv:"device",dy:"dynamic",eb:"enabled",ec:"encoding",ed:"edge",en:"end",eg:"engine",em:"embed",er:"error",ep:"experiments",es:"errorcode",et:"errortext",ee:"event",ev:"events",ex:"expires",ez:"exception",fa:"failed",fi:"first",fm:"family",ft:"format",fp:"fps",fq:"frequency",fr:"frame",fs:"fullscreen",ha:"has",hb:"holdback",he:"headers",ho:"host",hn:"hostname",ht:"height",id:"id",ii:"init",in:"instance",ip:"ip",is:"is",ke:"key",la:"language",lb:"labeled",le:"level",li:"live",ld:"loaded",lo:"load",ls:"lists",lt:"latency",ma:"max",md:"media",me:"message",mf:"manifest",mi:"mime",ml:"midroll",mm:"min",mn:"manufacturer",mo:"model",mx:"mux",ne:"newest",nm:"name",no:"number",on:"on",or:"origin",os:"os",pa:"paused",pb:"playback",pd:"producer",pe:"percentage",pf:"played",pg:"program",ph:"playhead",pi:"plugin",pl:"preroll",pn:"playing",po:"poster",pp:"pip",pr:"preload",ps:"position",pt:"part",py:"property",px:"pop",pz:"plan",ra:"rate",rd:"requested",re:"rebuffer",rf:"rendition",rg:"range",rm:"remote",ro:"ratio",rp:"response",rq:"request",rs:"requests",sa:"sample",sd:"skipped",se:"session",sh:"shift",sk:"seek",sm:"stream",so:"source",sq:"sequence",sr:"series",ss:"status",st:"start",su:"startup",sv:"server",sw:"software",sy:"severity",ta:"tag",tc:"tech",te:"text",tg:"target",th:"throughput",ti:"time",tl:"total",to:"to",tt:"title",ty:"type",ug:"upscaling",un:"universal",up:"upscale",ur:"url",us:"user",va:"variant",vd:"viewed",vi:"video",ve:"version",vw:"view",vr:"viewer",wd:"width",wa:"watch",wt:"waiting"});function nt(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}function ot(e){var t={},r={};return Object.keys(e).forEach((function(a){var i=!1;if(e.hasOwnProperty(a)&&void 0!==e[a]){var n=a.split("_"),o=n[0],s=at[o];s||(W.info("Data key word `"+n[0]+"` not expected in "+a),s=o+"_"),n.splice(1).forEach((function(e){"url"===e&&(i=!0),it[e]?s+=it[e]:Number.isInteger(Number(e))?s+=e:(W.info("Data key word `"+e+"` not expected in "+a),s+="_"+e+"_")})),i?r[s]=e[a]:t[s]=e[a]}})),Object.assign(t,r)}var st=c(_()),ut=c(O()),dt={maxBeaconSize:300,maxQueueLength:3600,baseTimeBetweenBeacons:1e4,maxPayloadKBSize:500},lt=["hb","requestcompleted","requestfailed","requestcanceled"],ct="https://img.litix.io",_t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this._beaconUrl=e||ct,this._eventQueue=[],this._postInFlight=!1,this._resendAfterPost=!1,this._failureCount=0,this._sendTimeout=!1,this._options=Object.assign({},dt,t)};_t.prototype.queueEvent=function(e,t){var r=Object.assign({},t);return(this._eventQueue.length<=this._options.maxQueueLength||"eventrateexceeded"===e)&&(this._eventQueue.push(r),this._sendTimeout||this._startBeaconSending(),this._eventQueue.length<=this._options.maxQueueLength)},_t.prototype.flushEvents=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];e&&1===this._eventQueue.length?this._eventQueue.pop():(this._eventQueue.length&&this._sendBeaconQueue(),this._startBeaconSending())},_t.prototype.destroy=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.destroyed=!0,e?this._clearBeaconQueue():this.flushEvents(),st.default.clearTimeout(this._sendTimeout)},_t.prototype._clearBeaconQueue=function(){var e=this._eventQueue.length>this._options.maxBeaconSize?this._eventQueue.length-this._options.maxBeaconSize:0,t=this._eventQueue.slice(e);e>0&&Object.assign(t[t.length-1],ot({mux_view_message:"event queue truncated"}));var r=this._createPayload(t);vt(this._beaconUrl,r,!0,(function(){}))},_t.prototype._sendBeaconQueue=function(){var e=this;if(this._postInFlight)this._resendAfterPost=!0;else{var t=this._eventQueue.slice(0,this._options.maxBeaconSize);this._eventQueue=this._eventQueue.slice(this._options.maxBeaconSize),this._postInFlight=!0;var r=this._createPayload(t),a=C.now();vt(this._beaconUrl,r,!1,(function(r,i){i?(e._eventQueue=t.concat(e._eventQueue),e._failureCount+=1,W.info("Error sending beacon: "+i)):e._failureCount=0,e._roundTripTime=C.now()-a,e._postInFlight=!1,e._resendAfterPost&&(e._resendAfterPost=!1,e._eventQueue.length>0&&e._sendBeaconQueue())}))}},_t.prototype._getNextBeaconTime=function(){if(!this._failureCount)return this._options.baseTimeBetweenBeacons;var e=Math.pow(2,this._failureCount-1);return(1+(e*=Math.random()))*this._options.baseTimeBetweenBeacons},_t.prototype._startBeaconSending=function(){var e=this;st.default.clearTimeout(this._sendTimeout),!this.destroyed&&(this._sendTimeout=st.default.setTimeout((function(){e._eventQueue.length&&e._sendBeaconQueue(),e._startBeaconSending()}),this._getNextBeaconTime()))},_t.prototype._createPayload=function(e){var t=this,r={transmission_timestamp:Math.round(C.now())};this._roundTripTime&&(r.rtt_ms=Math.round(this._roundTripTime));var a,i,n,o=function(){a=JSON.stringify({metadata:r,events:i||e}),n=a.length/1024},s=function(){return n<=t._options.maxPayloadKBSize};return o(),s()||(W.info("Payload size is too big ("+n+" kb). Removing unnecessary events."),i=e.filter((function(e){return-1===lt.indexOf(e.e)})),o()),s()||(W.info("Payload size still too big ("+n+" kb). Cropping fields.."),i.forEach((function(e){for(var t in e){var r=e[t];"string"==typeof r&&r.length>51200&&(e[t]=r.substring(0,51200))}})),o()),a};var pt,ht="function"==typeof ut.default.exitPictureInPicture?function(e){return e.length<=57344}:function(e){return!1},vt=function(e,t,r,a){if(r&&navigator&&navigator.sendBeacon&&navigator.sendBeacon(e,t))a();else if(st.default.fetch)st.default.fetch(e,{method:"POST",body:t,headers:{"Content-Type":"text/plain"},keepalive:ht(t)}).then((function(e){return a(null,e.ok?null:"Error")})).catch((function(e){return a(null,e)}));else{if(st.default.XMLHttpRequest){var i=new st.default.XMLHttpRequest;return i.onreadystatechange=function(){if(4===i.readyState)return a(null,200!==i.status?"error":void 0)},i.open("POST",e),i.setRequestHeader("Content-Type","text/plain"),void i.send(t)}a()}},ft=_t,mt=["env_key","view_id","view_sequence_number","player_sequence_number","beacon_domain","player_playhead_time","viewer_time","mux_api_version","event","video_id","player_instance_id","player_error_code","player_error_message","player_error_context","player_error_severity","player_error_business_exception"],yt=["adplay","adplaying","adpause","adfirstquartile","admidpoint","adthirdquartile","adended","adresponse","adrequest"],gt=["ad_id","ad_creative_id","ad_universal_id"],bt=["viewstart","error","ended","viewend"],Tt=function(){function e(t,r){var a,i,n,o,s,u,d,l,c,_,p,h,v,f,m,y=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};X(this,e),ee(this,"mux",void 0),ee(this,"envKey",void 0),ee(this,"options",void 0),ee(this,"eventQueue",void 0),ee(this,"sampleRate",void 0),ee(this,"disableCookies",void 0),ee(this,"respectDoNotTrack",void 0),ee(this,"previousBeaconData",void 0),ee(this,"lastEventTime",void 0),ee(this,"rateLimited",void 0),ee(this,"pageLevelData",void 0),ee(this,"viewerData",void 0),this.mux=t,this.envKey=r,this.options=y,this.previousBeaconData=null,this.lastEventTime=0,this.rateLimited=!1,this.eventQueue=new ft($e(this.envKey,this.options)),this.sampleRate=null!==(v=this.options.sampleRate)&&void 0!==v?v:1,this.disableCookies=null!==(f=this.options.disableCookies)&&void 0!==f&&f,this.respectDoNotTrack=null!==(m=this.options.respectDoNotTrack)&&void 0!==m&&m,this.previousBeaconData=null,this.lastEventTime=0,this.rateLimited=!1,this.pageLevelData={mux_api_version:this.mux.API_VERSION,mux_embed:this.mux.NAME,mux_embed_version:this.mux.VERSION,viewer_application_name:null===(a=this.options.platform)||void 0===a?void 0:a.name,viewer_application_version:null===(i=this.options.platform)||void 0===i?void 0:i.version,viewer_application_engine:null===(n=this.options.platform)||void 0===n?void 0:n.layout,viewer_device_name:null===(o=this.options.platform)||void 0===o?void 0:o.product,viewer_device_category:"",viewer_device_manufacturer:null===(s=this.options.platform)||void 0===s?void 0:s.manufacturer,viewer_os_family:null===(d=this.options.platform)||void 0===d||null===(u=d.os)||void 0===u?void 0:u.family,viewer_os_architecture:null===(c=this.options.platform)||void 0===c||null===(l=c.os)||void 0===l?void 0:l.architecture,viewer_os_version:null===(p=this.options.platform)||void 0===p||null===(_=p.os)||void 0===_?void 0:_.version,viewer_connection_type:rt(),page_url:null===Je.default||void 0===Je.default||null===(h=Je.default.location)||void 0===h?void 0:h.href},this.viewerData=this.disableCookies?{}:Xe()}return Z(e,[{key:"send",value:function(e,t){if(e&&null!=t&&t.view_id){if(this.respectDoNotTrack&&K())return W.info("Not sending `"+e+"` because Do Not Track is enabled");if(!t||"object"!=typeof t)return W.error("A data object was expected in send() but was not provided");var r=this.disableCookies?{}:function(){var e=Ke(),t=C.now();return e.session_start&&(e.sst=e.session_start,delete e.session_start),e.session_id&&(e.sid=e.session_id,delete e.session_id),e.session_expires&&(e.sex=e.session_expires,delete e.session_expires),(!e.sex||e.sex<t)&&(e.sid=j(),e.sst=t),e.sex=t+15e5,Ye(e),{session_id:e.sid,session_start:e.sst,session_expires:e.sex}}(),a=_e(ce({},this.pageLevelData,t,r,this.viewerData),{event:e,env_key:this.envKey});a.user_id&&(a.viewer_user_id=a.user_id,delete a.user_id);var i,n=(null!==(i=a.mux_sample_number)&&void 0!==i?i:0)>=this.sampleRate,o=ot(this._deduplicateBeaconData(e,a));if(this.lastEventTime=this.mux.utils.now(),n)return W.info("Not sending event due to sample rate restriction",e,a,o);if(this.envKey||W.info("Missing environment key (envKey) - beacons will be dropped if the video source is not a valid mux video URL",e,a,o),!this.rateLimited)if(W.info("Sending event",e,a,o),this.rateLimited=!this.eventQueue.queueEvent(e,o),this.mux.WINDOW_UNLOADING&&"viewend"===e)this.eventQueue.destroy(!0);else if(this.mux.WINDOW_HIDDEN&&"hb"===e?this.eventQueue.flushEvents(!0):bt.indexOf(e)>=0&&this.eventQueue.flushEvents(),this.rateLimited)return a.event="eventrateexceeded",o=ot(a),this.eventQueue.queueEvent(a.event,o),W.error("Beaconing disabled due to rate limit.")}}},{key:"destroy",value:function(){this.eventQueue.destroy(!1)}},{key:"_deduplicateBeaconData",value:function(e,t){var r=this,a={},i=t.view_id;if("-1"===i||"viewstart"===e||"viewend"===e||!this.previousBeaconData||this.mux.utils.now()-this.lastEventTime>=6e5)a=ce({},t),i&&(this.previousBeaconData=a),i&&"viewend"===e&&(this.previousBeaconData=null);else{var n=0===e.indexOf("request");Object.entries(t).forEach((function(t){var i=q(t,2),o=i[0],s=i[1];r.previousBeaconData&&(s!==r.previousBeaconData[o]||mt.indexOf(o)>-1||r.objectHasChanged(n,o,s,r.previousBeaconData[o])||r.eventRequiresKey(e,o))&&(a[o]=s,r.previousBeaconData[o]=s)}))}return a}},{key:"objectHasChanged",value:function(e,t,r,a){return!(!e||0!==t.indexOf("request_")||"request_response_headers"!==t&&"object"==typeof r&&"object"==typeof a&&Object.keys(r||{}).length===Object.keys(a||{}).length)}},{key:"eventRequiresKey",value:function(e,t){return!!("renditionchange"===e&&0===t.indexOf("video_source_")||gt.includes(t)&&yt.includes(e))}}]),e}(),Et=function e(t){X(this,e);var r=0,a=0,i=0,n=0,o=0,s=0,u=0;t.on("requestcompleted",(function(e,s){var u,d,l=s.request_start,c=s.request_response_start,_=s.request_response_end,p=s.request_bytes_loaded;if(n++,c?(u=c-(null!=l?l:0),d=(null!=_?_:0)-c):d=(null!=_?_:0)-(null!=l?l:0),d>0&&p&&p>0){var h=p/d*8e3;o++,a+=p,i+=d,t.data.view_min_request_throughput=Math.min(t.data.view_min_request_throughput||1/0,h),t.data.view_average_request_throughput=a/i*8e3,t.data.view_request_count=n,u>0&&(r+=u,t.data.view_max_request_latency=Math.max(t.data.view_max_request_latency||0,u),t.data.view_average_request_latency=r/o)}})),t.on("requestfailed",(function(e,r){n++,s++,t.data.view_request_count=n,t.data.view_request_failed_count=s})),t.on("requestcanceled",(function(e,r){n++,u++,t.data.view_request_count=n,t.data.view_request_canceled_count=u}))},wt=function e(t){var r=this;X(this,e),ee(this,"_lastEventTime",void 0),t.on("before*",(function(e,a){var i=a.viewer_time,n=C.now(),o=r._lastEventTime;if(r._lastEventTime=n,o&&n-o>36e5){var s=Object.keys(t.data).reduce((function(e,r){return 0===r.indexOf("video_")?Object.assign(e,ee({},r,t.data[r])):e}),{});t.mux.log.info("Received event after at least an hour inactivity, creating a new view");var u=t.playbackHeartbeat._playheadShouldBeProgressing;t._resetView(Object.assign({viewer_time:i},s)),t.playbackHeartbeat._playheadShouldBeProgressing=u,t.playbackHeartbeat._playheadShouldBeProgressing&&"play"!==e.type&&"adbreakstart"!==e.type&&(t.emit("play",{viewer_time:i}),"playing"!==e.type&&t.emit("playing",{viewer_time:i}))}}))},Dt=["viewstart","ended","loadstart","pause","play","playing","ratechange","waiting","adplay","adpause","adended","aderror","adplaying","adrequest","adresponse","adbreakstart","adbreakend","adfirstquartile","admidpoint","adthirdquartile","rebufferstart","rebufferend","seeked","error","hb","requestcompleted","requestfailed","requestcanceled","renditionchange"],kt=new Set(["requestcompleted","requestfailed","requestcanceled"]),At=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&ae(e,t)}(r,e);var t=ne(r);function r(e,a,i){var n;X(this,r),ee(Y(n=t.call(this)),"DOM_CONTENT_LOADED_EVENT_END",void 0),ee(Y(n),"NAVIGATION_START",void 0),ee(Y(n),"_destroyed",void 0),ee(Y(n),"_heartBeatTimeout",void 0),ee(Y(n),"adTracker",void 0),ee(Y(n),"dashjs",void 0),ee(Y(n),"data",void 0),ee(Y(n),"disablePlayheadRebufferTracking",void 0),ee(Y(n),"disableRebufferTracking",void 0),ee(Y(n),"errorTracker",void 0),ee(Y(n),"errorTranslator",void 0),ee(Y(n),"emitTranslator",void 0),ee(Y(n),"getAdData",void 0),ee(Y(n),"getPlayheadTime",void 0),ee(Y(n),"getStateData",void 0),ee(Y(n),"stateDataTranslator",void 0),ee(Y(n),"hlsjs",void 0),ee(Y(n),"id",void 0),ee(Y(n),"longResumeTracker",void 0),ee(Y(n),"minimumRebufferDuration",void 0),ee(Y(n),"mux",void 0),ee(Y(n),"playbackEventDispatcher",void 0),ee(Y(n),"playbackHeartbeat",void 0),ee(Y(n),"playbackHeartbeatTime",void 0),ee(Y(n),"playheadTime",void 0),ee(Y(n),"seekingTracker",void 0),ee(Y(n),"sustainedRebufferThreshold",void 0),ee(Y(n),"watchTimeTracker",void 0),ee(Y(n),"currentFragmentPDT",void 0),ee(Y(n),"currentFragmentStart",void 0),n.DOM_CONTENT_LOADED_EVENT_END=de.domContentLoadedEventEnd(),n.NAVIGATION_START=de.navigationStart();var o={debug:!1,minimumRebufferDuration:250,sustainedRebufferThreshold:1e3,playbackHeartbeatTime:25,beaconDomain:"litix.io",sampleRate:1,disableCookies:!1,respectDoNotTrack:!1,disableRebufferTracking:!1,disablePlayheadRebufferTracking:!1,errorTranslator:function(e){return e},emitTranslator:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return t},stateDataTranslator:function(e){return e}};n.mux=e,n.id=a,null!=i&&i.beaconDomain&&n.mux.log.warn("The `beaconDomain` setting has been deprecated in favor of `beaconCollectionDomain`. Please change your integration to use `beaconCollectionDomain` instead of `beaconDomain`."),(i=Object.assign(o,i)).data=i.data||{},i.data.property_key&&(i.data.env_key=i.data.property_key,delete i.data.property_key),W.level=i.debug?G:V,n.getPlayheadTime=i.getPlayheadTime,n.getStateData=i.getStateData||function(){return{}},n.getAdData=i.getAdData||function(){},n.minimumRebufferDuration=i.minimumRebufferDuration,n.sustainedRebufferThreshold=i.sustainedRebufferThreshold,n.playbackHeartbeatTime=i.playbackHeartbeatTime,n.disableRebufferTracking=i.disableRebufferTracking,n.disableRebufferTracking&&n.mux.log.warn("Disabling rebuffer tracking. This should only be used in specific circumstances as a last resort when your player is known to unreliably track rebuffering."),n.disablePlayheadRebufferTracking=i.disablePlayheadRebufferTracking,n.errorTranslator=i.errorTranslator,n.emitTranslator=i.emitTranslator,n.stateDataTranslator=i.stateDataTranslator,n.playbackEventDispatcher=new Tt(e,i.data.env_key,i),n.data={player_instance_id:j(),mux_sample_rate:i.sampleRate,beacon_domain:i.beaconCollectionDomain||i.beaconDomain},n.data.view_sequence_number=1,n.data.player_sequence_number=1;var s=function(){void 0===this.data.view_start&&(this.data.view_start=this.mux.utils.now(),this.emit("viewstart"))}.bind(Y(n));if(n.on("viewinit",(function(e,t){this._resetVideoData(),this._resetViewData(),this._resetErrorData(),this._updateStateData(),Object.assign(this.data,t),this._initializeViewData(),this.one("play",s),this.one("adbreakstart",s)})),n.on("videochange",(function(e,t){this._resetView(t)})),n.on("programchange",(function(e,t){this.data.player_is_paused&&this.mux.log.warn("The `programchange` event is intended to be used when the content changes mid playback without the video source changing, however the video is not currently playing. If the video source is changing please use the videochange event otherwise you will lose startup time information."),this._resetView(Object.assign(t,{view_program_changed:!0})),s(),this.emit("play"),this.emit("playing")})),n.on("fragmentchange",(function(e,t){this.currentFragmentPDT=t.currentFragmentPDT,this.currentFragmentStart=t.currentFragmentStart})),n.on("destroy",n.destroy),"undefined"!=typeof window&&"function"==typeof window.addEventListener&&"function"==typeof window.removeEventListener){var u=function(){var e=void 0!==n.data.view_start;n.mux.WINDOW_HIDDEN="hidden"===document.visibilityState,e&&n.mux.WINDOW_HIDDEN&&(n.data.player_is_paused||n.emit("hb"))};window.addEventListener("visibilitychange",u,!1);var d=function(e){e.persisted||n.destroy()};window.addEventListener("pagehide",d,!1),n.on("destroy",(function(){window.removeEventListener("visibilitychange",u),window.removeEventListener("pagehide",d)}))}return n.on("playerready",(function(e,t){Object.assign(this.data,t)})),Dt.forEach((function(e){n.on(e,(function(t,r){0!==e.indexOf("ad")&&this._updateStateData(),Object.assign(this.data,r),this._sanitizeData()})),n.on("after"+e,(function(){("error"!==e||this.errorTracker.viewErrored)&&this.send(e)}))})),n.on("viewend",(function(e,t){Object.assign(n.data,t)})),n.one("playerready",(function(e){var t=this.mux.utils.now();this.data.player_init_time&&(this.data.player_startup_time=t-this.data.player_init_time),!this.mux.PLAYER_TRACKED&&this.NAVIGATION_START&&(this.mux.PLAYER_TRACKED=!0,(this.data.player_init_time||this.DOM_CONTENT_LOADED_EVENT_END)&&(this.data.page_load_time=Math.min(this.data.player_init_time||1/0,this.DOM_CONTENT_LOADED_EVENT_END||1/0)-this.NAVIGATION_START)),this.send("playerready"),delete this.data.player_startup_time,delete this.data.page_load_time})),n.longResumeTracker=new wt(Y(n)),n.errorTracker=new Oe(Y(n)),new Ve(Y(n)),n.seekingTracker=new Me(Y(n)),n.playheadTime=new qe(Y(n)),n.playbackHeartbeat=new Se(Y(n)),new He(Y(n)),n.watchTimeTracker=new Re(Y(n)),new Pe(Y(n)),n.adTracker=new Fe(Y(n)),new Ce(Y(n)),new Ne(Y(n)),new je(Y(n)),new Qe(Y(n)),new Et(Y(n)),i.hlsjs&&n.addHLSJS(i),i.dashjs&&n.addDashJS(i),n.emit("viewinit",i.data),n}return Z(r,[{key:"emit",value:function(e,t){var a,i=Object.assign({viewer_time:this.mux.utils.now()},t),n=[e,i];if(this.emitTranslator)try{n=this.emitTranslator(e,i)}catch(e){this.mux.log.warn("Exception in emit translator callback.",e)}null!=n&&n.length&&(a=re(te(r.prototype),"emit",this)).call.apply(a,[this].concat(B(n)))}},{key:"destroy",value:function(){this._destroyed||(this._destroyed=!0,void 0!==this.data.view_start&&(this.emit("viewend"),this.send("viewend")),this.playbackEventDispatcher.destroy(),this.removeHLSJS(),this.removeDashJS(),window.clearTimeout(this._heartBeatTimeout))}},{key:"send",value:function(e){if(this.data.view_id){var t=Object.assign({},this.data);if(void 0===t.video_source_is_live&&(t.player_source_duration===1/0||t.video_source_duration===1/0?t.video_source_is_live=!0:(t.player_source_duration>0||t.video_source_duration>0)&&(t.video_source_is_live=!1)),t.video_source_is_live||["player_program_time","player_manifest_newest_program_time","player_live_edge_program_time","player_program_time","video_holdback","video_part_holdback","video_target_duration","video_part_target_duration"].forEach((function(e){t[e]=void 0})),t.video_source_url=t.video_source_url||t.player_source_url,t.video_source_url){var r=q(se(t.video_source_url),2),a=r[0],i=r[1];t.video_source_domain=i,t.video_source_hostname=a}delete t.ad_request_id,this.playbackEventDispatcher.send(e,t),this.data.view_sequence_number++,this.data.player_sequence_number++,kt.has(e)||this._restartHeartBeat(),"viewend"===e&&delete this.data.view_id}}},{key:"_resetView",value:function(e){this.emit("viewend"),this.send("viewend"),this.emit("viewinit",e)}},{key:"_updateStateData",value:function(){var e=this.getStateData();if("function"==typeof this.stateDataTranslator)try{e=this.stateDataTranslator(e)}catch(e){this.mux.log.warn("Exception in stateDataTranslator translator callback.",e)}Object.assign(this.data,e),this.playheadTime._updatePlayheadTime(),this._sanitizeData()}},{key:"_sanitizeData",value:function(){var e=this;["player_width","player_height","video_source_width","video_source_height","player_playhead_time","video_source_bitrate"].forEach((function(t){var r=parseInt(e.data[t],10);e.data[t]=isNaN(r)?void 0:r})),["player_source_url","video_source_url"].forEach((function(t){if(e.data[t]){var r=e.data[t].toLowerCase();(0===r.indexOf("data:")||0===r.indexOf("blob:"))&&(e.data[t]="MSE style URL")}}))}},{key:"_resetVideoData",value:function(){var e=this;Object.keys(this.data).forEach((function(t){0===t.indexOf("video_")&&delete e.data[t]}))}},{key:"_resetViewData",value:function(){var e=this;Object.keys(this.data).forEach((function(t){0===t.indexOf("view_")&&delete e.data[t]})),this.data.view_sequence_number=1}},{key:"_resetErrorData",value:function(){delete this.data.player_error_code,delete this.data.player_error_message,delete this.data.player_error_context,delete this.data.player_error_severity,delete this.data.player_error_business_exception}},{key:"_initializeViewData",value:function(){var e=this,t=this.data.view_id=j(),r=function(){t===e.data.view_id&&le(e.data,"player_view_count",1)};this.data.player_is_paused?this.one("play",r):r()}},{key:"_restartHeartBeat",value:function(){var e=this;window.clearTimeout(this._heartBeatTimeout),this._heartBeatTimeout=window.setTimeout((function(){e.data.player_is_paused||e.emit("hb")}),1e4)}},{key:"addHLSJS",value:function(e){e.hlsjs?this.hlsjs?this.mux.log.warn("An instance of HLS.js is already being monitored for this player."):(this.hlsjs=e.hlsjs,function(e,t,r){var a=arguments.length>4?arguments[4]:void 0,i=e.log,n=e.utils.secondsToMs,o=function(e){var t,r=parseInt(a.version);return 1===r&&null!==e.programDateTime&&(t=e.programDateTime),0===r&&null!==e.pdt&&(t=e.pdt),t};if(de.exists()){var s=function(r,a){return e.emit(t,r,a)},u=function(e,t){var r=t.levels,a=t.audioTracks,i=t.url,n=t.stats,o=t.networkDetails,u=t.sessionData,d={},l={};r.forEach((function(e,t){d[t]={width:e.width,height:e.height,bitrate:e.bitrate,attrs:e.attrs}})),a.forEach((function(e,t){l[t]={name:e.name,language:e.lang,bitrate:e.bitrate}}));var c=ye(n),_=c.bytesLoaded,p=c.requestStart,h=c.responseStart,v=c.responseEnd;s("requestcompleted",_e(ce({},me(u)),{request_event_type:e,request_bytes_loaded:_,request_start:p,request_response_start:h,request_response_end:v,request_type:"manifest",request_hostname:oe(i),request_response_headers:ge(o),request_rendition_lists:{media:d,audio:l,video:{}}}))};r.on(a.Events.MANIFEST_LOADED,u);var d=function(e,t){var r=t.details,a=t.level,i=t.networkDetails,u=t.stats,d=ye(u),l=d.bytesLoaded,c=d.requestStart,_=d.responseStart,p=d.responseEnd,h=r.fragments[r.fragments.length-1],v=o(h)+n(h.duration);s("requestcompleted",{request_event_type:e,request_bytes_loaded:l,request_start:c,request_response_start:_,request_response_end:p,request_current_level:a,request_type:"manifest",request_hostname:oe(r.url),request_response_headers:ge(i),video_holdback:r.holdBack&&n(r.holdBack),video_part_holdback:r.partHoldBack&&n(r.partHoldBack),video_part_target_duration:r.partTarget&&n(r.partTarget),video_target_duration:r.targetduration&&n(r.targetduration),video_source_is_live:r.live,player_manifest_newest_program_time:isNaN(v)?void 0:v})};r.on(a.Events.LEVEL_LOADED,d);var l=function(e,t){var r=t.details,a=t.networkDetails,i=t.stats,n=ye(i),o=n.bytesLoaded,u=n.requestStart,d=n.responseStart,l=n.responseEnd;s("requestcompleted",{request_event_type:e,request_bytes_loaded:o,request_start:u,request_response_start:d,request_response_end:l,request_type:"manifest",request_hostname:oe(r.url),request_response_headers:ge(a)})};r.on(a.Events.AUDIO_TRACK_LOADED,l);var c=function(e,t){var a=t.stats,i=t.networkDetails,n=t.frag;a=a||n.stats;var o=ye(a),u=o.bytesLoaded,d=o.requestStart,l=o.responseStart,c=o.responseEnd,_=i?ge(i):void 0,p={request_event_type:e,request_bytes_loaded:u,request_start:d,request_response_start:l,request_response_end:c,request_hostname:i?oe(i.responseURL):void 0,request_id:_?fe(_):void 0,request_response_headers:_,request_media_duration:n.duration,request_url:null==i?void 0:i.responseURL};"main"===n.type?(p.request_type="media",p.request_current_level=n.level,p.request_video_width=(r.levels[n.level]||{}).width,p.request_video_height=(r.levels[n.level]||{}).height,p.request_labeled_bitrate=(r.levels[n.level]||{}).bitrate):p.request_type=n.type,s("requestcompleted",p)};r.on(a.Events.FRAG_LOADED,c);var _=function(e,t){var r=t.frag,a=r.start,i={currentFragmentPDT:o(r),currentFragmentStart:n(a)};s("fragmentchange",i)};r.on(a.Events.FRAG_CHANGED,_);var p=function(e,t){var r=t.type,i=t.details,n=t.response,o=t.fatal,u=t.frag,d=t.networkDetails,l=(null==u?void 0:u.url)||t.url||"",c=d?ge(d):void 0;if((i===a.ErrorDetails.MANIFEST_LOAD_ERROR||i===a.ErrorDetails.MANIFEST_LOAD_TIMEOUT||i===a.ErrorDetails.FRAG_LOAD_ERROR||i===a.ErrorDetails.FRAG_LOAD_TIMEOUT||i===a.ErrorDetails.LEVEL_LOAD_ERROR||i===a.ErrorDetails.LEVEL_LOAD_TIMEOUT||i===a.ErrorDetails.AUDIO_TRACK_LOAD_ERROR||i===a.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT||i===a.ErrorDetails.SUBTITLE_LOAD_ERROR||i===a.ErrorDetails.SUBTITLE_LOAD_TIMEOUT||i===a.ErrorDetails.KEY_LOAD_ERROR||i===a.ErrorDetails.KEY_LOAD_TIMEOUT)&&s("requestfailed",{request_error:i,request_url:l,request_hostname:oe(l),request_id:c?fe(c):void 0,request_type:i===a.ErrorDetails.FRAG_LOAD_ERROR||i===a.ErrorDetails.FRAG_LOAD_TIMEOUT?"media":i===a.ErrorDetails.AUDIO_TRACK_LOAD_ERROR||i===a.ErrorDetails.AUDIO_TRACK_LOAD_TIMEOUT?"audio":i===a.ErrorDetails.SUBTITLE_LOAD_ERROR||i===a.ErrorDetails.SUBTITLE_LOAD_TIMEOUT?"subtitle":i===a.ErrorDetails.KEY_LOAD_ERROR||i===a.ErrorDetails.KEY_LOAD_TIMEOUT?"encryption":"manifest",request_error_code:null==n?void 0:n.code,request_error_text:null==n?void 0:n.text}),o){var _,p="".concat(l?"url: ".concat(l,"\n"):"")+"".concat(n&&(n.code||n.text)?"response: ".concat(n.code,", ").concat(n.text,"\n"):"")+"".concat(t.reason?"failure reason: ".concat(t.reason,"\n"):"")+"".concat(t.level?"level: ".concat(t.level,"\n"):"")+"".concat(t.parent?"parent stream controller: ".concat(t.parent,"\n"):"")+"".concat(t.buffer?"buffer length: ".concat(t.buffer,"\n"):"")+"".concat(t.error?"error: ".concat(t.error,"\n"):"")+"".concat(t.event?"event: ".concat(t.event,"\n"):"")+"".concat(t.err?"error message: ".concat(null===(_=t.err)||void 0===_?void 0:_.message,"\n"):"");s("error",{player_error_code:r,player_error_message:i,player_error_context:p})}};r.on(a.Events.ERROR,p);var h=function(e,t){var r=t.frag,a=r&&r._url||"";s("requestcanceled",{request_event_type:e,request_url:a,request_type:"media",request_hostname:oe(a)})};r.on(a.Events.FRAG_LOAD_EMERGENCY_ABORTED,h);var v=function(e,t){var a=t.level,n=r.levels[a];if(n&&n.attrs&&n.attrs.BANDWIDTH){var o,u=n.attrs.BANDWIDTH,d=parseFloat(n.attrs["FRAME-RATE"]);isNaN(d)||(o=d),u?s("renditionchange",{video_source_fps:o,video_source_bitrate:u,video_source_width:n.width,video_source_height:n.height,video_source_rendition_name:n.name,video_source_codec:null==n?void 0:n.videoCodec}):i.warn("missing BANDWIDTH from HLS manifest parsed by HLS.js")}};r.on(a.Events.LEVEL_SWITCHED,v),r._stopMuxMonitor=function(){r.off(a.Events.MANIFEST_LOADED,u),r.off(a.Events.LEVEL_LOADED,d),r.off(a.Events.AUDIO_TRACK_LOADED,l),r.off(a.Events.FRAG_LOADED,c),r.off(a.Events.FRAG_CHANGED,_),r.off(a.Events.ERROR,p),r.off(a.Events.FRAG_LOAD_EMERGENCY_ABORTED,h),r.off(a.Events.LEVEL_SWITCHED,v),r.off(a.Events.DESTROYING,r._stopMuxMonitor),delete r._stopMuxMonitor},r.on(a.Events.DESTROYING,r._stopMuxMonitor)}else i.warn("performance timing not supported. Not tracking HLS.js.")}(this.mux,this.id,e.hlsjs,{},e.Hls||window.Hls)):this.mux.log.warn("You must pass a valid hlsjs instance in order to track it.")}},{key:"removeHLSJS",value:function(){this.hlsjs&&(function(e){e&&"function"==typeof e._stopMuxMonitor&&e._stopMuxMonitor()}(this.hlsjs),this.hlsjs=void 0)}},{key:"addDashJS",value:function(e){e.dashjs?this.dashjs?this.mux.log.warn("An instance of Dash.js is already being monitored for this player."):(this.dashjs=e.dashjs,function(e,t,r){var a=e.log;if(r&&r.on){var i=we(r),n=function(r,a){return e.emit(t,r,a)},o=function(e){var t=e.type,r=(e.data||{}).url;n("requestcompleted",{request_event_type:t,request_start:0,request_response_start:0,request_response_end:0,request_bytes_loaded:-1,request_type:"manifest",request_hostname:oe(r),request_url:r})};r.on("manifestLoaded",o);var s={},u=function(e){if("function"!=typeof e.getRequests)return null;var t=e.getRequests({state:"executed"});return 0===t.length?null:t[t.length-1]},d=function(e){var t=e.type,r=e.fragmentModel,a=e.chunk,i=u(r);l({type:t,request:i,chunk:a})},l=function(e){var t=e.type,a=e.chunk,i=e.request,o=(a||{}).mediaInfo||{},u=o.type,d=o.bitrateList,l={};(d=d||[]).forEach((function(e,t){l[t]={},l[t].width=e.width,l[t].height=e.height,l[t].bitrate=e.bandwidth,l[t].attrs={}})),"video"===u?s.video=l:"audio"===u?s.audio=l:s.media=l;var c=be(i,r),_=c.requestStart,p=c.requestResponseStart,h=c.requestResponseEnd,v=c.requestResponseHeaders,f=c.requestMediaDuration,m=c.requestHostname,y=c.requestUrl,g=c.requestId;n("requestcompleted",{request_event_type:t,request_start:_,request_response_start:p,request_response_end:h,request_bytes_loaded:-1,request_type:u+"_init",request_response_headers:v,request_hostname:m,request_id:g,request_url:y,request_media_duration:f,request_rendition_lists:s})};i>=4?r.on("initFragmentLoaded",l):r.on("initFragmentLoaded",d);var c=function(e){var t=e.type,r=e.fragmentModel,a=e.chunk,i=u(r);_({type:t,request:i,chunk:a})},_=function(e){var t=e.type,a=e.chunk,i=e.request,o=a||{},s=o.mediaInfo,u=o.start,d=(s||{}).type,l=be(i,r),c=l.requestStart,_=l.requestResponseStart,p=l.requestResponseEnd,h=l.requestBytesLoaded,v=l.requestResponseHeaders,f=l.requestMediaDuration,m=l.requestHostname,y=l.requestUrl,g=l.requestId,b=Te(d,r),T=b.currentLevel,E=b.renditionWidth,w=b.renditionHeight,D=b.renditionBitrate;n("requestcompleted",{request_event_type:t,request_start:c,request_response_start:_,request_response_end:p,request_bytes_loaded:h,request_type:d,request_response_headers:v,request_hostname:m,request_id:g,request_url:y,request_media_start_time:u,request_media_duration:f,request_current_level:T,request_labeled_bitrate:D,request_video_width:E,request_video_height:w})};i>=4?r.on("mediaFragmentLoaded",_):r.on("mediaFragmentLoaded",c);var p={video:void 0,audio:void 0,totalBitrate:void 0},h=function(){if(p.video&&"number"==typeof p.video.bitrate){if(!p.video.width||!p.video.height)return void a.warn("have bitrate info for video but missing width/height");var e=p.video.bitrate;if(p.audio&&"number"==typeof p.audio.bitrate&&(e+=p.audio.bitrate),e!==p.totalBitrate)return p.totalBitrate=e,{video_source_bitrate:e,video_source_height:p.video.height,video_source_width:p.video.width,video_source_codec:Ee(p.video.codec)}}},v=function(e,t,i){if("number"==typeof e.newQuality){var o=e.mediaType;if("audio"===o||"video"===o){var s=r.getBitrateInfoListFor(o).find((function(t){return t.qualityIndex===e.newQuality}));if(!s||"number"!=typeof s.bitrate)return void a.warn("missing bitrate info for ".concat(o));p[o]=_e(ce({},s),{codec:r.getCurrentTrackFor(o).codec});var u=h();u&&n("renditionchange",u)}}else a.warn("missing evt.newQuality in qualityChangeRendered event",e)};r.on("qualityChangeRendered",v);var f=function(e){var t=e.request,r=e.mediaType;n("requestcanceled",{request_event_type:(t=t||{}).type+"_"+t.action,request_url:t.url,request_type:r,request_hostname:oe(t.url)})};r.on("fragmentLoadingAbandoned",f);var m=function(e){var t,r,a=e.error,i=(null==a||null===(t=a.data)||void 0===t?void 0:t.request)||{},o=(null==a||null===(r=a.data)||void 0===r?void 0:r.response)||{};27===(null==a?void 0:a.code)&&n("requestfailed",{request_error:i.type+"_"+i.action,request_url:i.url,request_hostname:oe(i.url),request_type:i.mediaType,request_error_code:o.status,request_error_text:o.statusText});var s="".concat(null!=i&&i.url?"url: ".concat(i.url,"\n"):"")+"".concat(null!=o&&o.status||null!=o&&o.statusText?"response: ".concat(null==o?void 0:o.status,", ").concat(null==o?void 0:o.statusText,"\n"):"");n("error",{player_error_code:null==a?void 0:a.code,player_error_message:null==a?void 0:a.message,player_error_context:s})};r.on("error",m),r._stopMuxMonitor=function(){r.off("manifestLoaded",o),r.off("initFragmentLoaded",l),r.off("mediaFragmentLoaded",_),r.off("qualityChangeRendered",v),r.off("error",m),r.off("fragmentLoadingAbandoned",f),delete r._stopMuxMonitor}}else a.warn("Invalid dash.js player reference. Monitoring blocked.")}(this.mux,this.id,e.dashjs)):this.mux.log.warn("You must pass a valid dashjs instance in order to track it.")}},{key:"removeDashJS",value:function(){this.dashjs&&(function(e){e&&"function"==typeof e._stopMuxMonitor&&e._stopMuxMonitor()}(this.dashjs),this.dashjs=void 0)}}]),r}(Ae),xt=At,St=c(O()),Ot=["loadstart","pause","play","playing","seeking","seeked","timeupdate","ratechange","stalled","waiting","error","ended"],Rt={1:"MEDIA_ERR_ABORTED",2:"MEDIA_ERR_NETWORK",3:"MEDIA_ERR_DECODE",4:"MEDIA_ERR_SRC_NOT_SUPPORTED"},Pt=c(_());Pt.default&&Pt.default.WeakMap&&(pt=new WeakMap);var qt="#EXT-X-TARGETDURATION",Lt="#EXT-X-PART-INF",Nt="#EXT-X-SERVER-CONTROL",It="#EXTINF",Ct="#EXT-X-PROGRAM-DATE-TIME",jt="#EXT-X-VERSION",Ht="#EXT-X-SESSION-DATA",Mt=function(e){return this.buffer="",this.manifest={segments:[],serverControl:{},sessionData:{}},this.currentUri={},this.process(e),this.manifest};Mt.prototype.process=function(e){var t;for(this.buffer+=e,t=this.buffer.indexOf("\n");t>-1;t=this.buffer.indexOf("\n"))this.processLine(this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)},Mt.prototype.processLine=function(e){var t=e.indexOf(":"),r=Jt(e,t),a=r[0],i=2===r.length?Ft(r[1]):void 0;if("#"!==a[0])this.currentUri.uri=a,this.manifest.segments.push(this.currentUri),this.manifest.targetDuration&&!("duration"in this.currentUri)&&(this.currentUri.duration=this.manifest.targetDuration),this.currentUri={};else switch(a){case qt:if(!isFinite(i)||i<0)return;this.manifest.targetDuration=i,this.setHoldBack();break;case Lt:Ut(this.manifest,r),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),this.setHoldBack();break;case Nt:Ut(this.manifest,r),this.setHoldBack();break;case It:0===i?this.currentUri.duration=.01:i>0&&(this.currentUri.duration=i);break;case Ct:var n=i,o=new Date(n);this.manifest.dateTimeString||(this.manifest.dateTimeString=n,this.manifest.dateTimeObject=o),this.currentUri.dateTimeString=n,this.currentUri.dateTimeObject=o;break;case jt:Ut(this.manifest,r);break;case Ht:var s=Wt(r[1]),u=me(s);Object.assign(this.manifest.sessionData,u)}},Mt.prototype.setHoldBack=function(){var e=this.manifest,t=e.serverControl,r=e.targetDuration,a=e.partTargetDuration;if(t){var i="holdBack",n="partHoldBack",o=r&&3*r,s=a&&2*a;r&&!t.hasOwnProperty(i)&&(t[i]=o),o&&t[i]<o&&(t[i]=o),a&&!t.hasOwnProperty(n)&&(t[n]=3*a),a&&t[n]<s&&(t[n]=s)}};var Ut=function(e,t){var r,a=Bt(t[0].replace("#EXT-X-",""));Vt(t[1])?(r={},r=Object.assign(Qt(t[1]),r)):r=Ft(t[1]),e[a]=r},Bt=function(e){return e.toLowerCase().replace(/-(\w)/g,(function(e){return e[1].toUpperCase()}))},Ft=function(e){if("yes"===e.toLowerCase()||"no"===e.toLowerCase())return"yes"===e.toLowerCase();var t=-1!==e.indexOf(":")?e:parseFloat(e);return isNaN(t)?e:t},Gt=function(e){var t={},r=e.split("=");return r.length>1&&(t[Bt(r[0])]=Ft(r[1])),t},Qt=function(e){for(var t=e.split(","),r={},a=0;t.length>a;a++){var i=t[a],n=Gt(i);r=Object.assign(n,r)}return r},Vt=function(e){return e.indexOf("=")>-1},Jt=function(e,t){return-1===t?[e]:[e.substring(0,t),e.substring(t+1)]},Wt=function(e){var t={};if(e){var r=e.search(",");return[e.slice(0,r),e.slice(r+1)].forEach((function(e,r){for(var a=e.replace(/['"]+/g,"").split("="),i=0;i<a.length;i++)"DATA-ID"===a[i]&&(t["DATA-ID"]=a[1-i]),"VALUE"===a[i]&&(t.VALUE=a[1-i])})),{data:t}}},zt=Mt,Kt={safeCall:function(e,t,r,a){var i=a;if(e&&"function"==typeof e[t])try{i=e[t].apply(e,r)}catch(e){W.info("safeCall error",e)}return i},safeIncrement:le,getComputedStyle:function(e,t){return e&&t&&Pt.default&&"function"==typeof Pt.default.getComputedStyle?(pt&&pt.has(e)&&(r=pt.get(e)),r||(r=Pt.default.getComputedStyle(e,null),pt&&pt.set(e,r)),r.getPropertyValue(t)):"";var r},secondsToMs:function(e){return Math.floor(1e3*e)},assign:Object.assign,headersStringToObject:ve,cdnHeadersToRequestId:fe,extractHostnameAndDomain:se,extractHostname:oe,manifestParser:zt,generateShortID:H,generateUUID:j,now:C.now,findMediaElement:U},Yt={PLAYER_READY:"playerready",VIEW_INIT:"viewinit",VIDEO_CHANGE:"videochange",PLAY:"play",PAUSE:"pause",PLAYING:"playing",TIME_UPDATE:"timeupdate",SEEKING:"seeking",SEEKED:"seeked",REBUFFER_START:"rebufferstart",REBUFFER_END:"rebufferend",ERROR:"error",ENDED:"ended",RENDITION_CHANGE:"renditionchange",ORIENTATION_CHANGE:"orientationchange",AD_REQUEST:"adrequest",AD_RESPONSE:"adresponse",AD_BREAK_START:"adbreakstart",AD_PLAY:"adplay",AD_PLAYING:"adplaying",AD_PAUSE:"adpause",AD_FIRST_QUARTILE:"adfirstquartile",AD_MID_POINT:"admidpoint",AD_THIRD_QUARTILE:"adthirdquartile",AD_ENDED:"adended",AD_BREAK_END:"adbreakend",AD_ERROR:"aderror",REQUEST_COMPLETED:"requestcompleted",REQUEST_FAILED:"requestfailed",REQUEST_CANCELLED:"requestcanceled",HEARTBEAT:"hb",DESTROY:"destroy"},Xt={},$t=function(e){var t=arguments;"string"==typeof e?$t.hasOwnProperty(e)?L.default.setTimeout((function(){t=Array.prototype.splice.call(t,1),$t[e].apply(null,t)}),0):W.warn("`"+e+"` is an unknown task"):"function"==typeof e?L.default.setTimeout((function(){e($t)}),0):W.warn("`"+e+"` is invalid.")},Zt={loaded:C.now(),NAME:"mux-embed",VERSION:"5.9.0",API_VERSION:"2.1",PLAYER_TRACKED:!1,monitor:function(e,t){return function(e,t,r){var a=q(U(t),3),i=a[0],n=a[1],o=a[2],s=e.log,u=e.utils.getComputedStyle,d=e.utils.secondsToMs;if(!i)return s.error("No element was found with the `"+n+"` query selector.");if("video"!==o&&"audio"!==o)return s.error("The element of `"+n+"` was not a media element.");i.mux&&(i.mux.destroy(),delete i.mux,s.warn("Already monitoring this video element, replacing existing event listeners"));var l={getPlayheadTime:function(){return d(i.currentTime)},getStateData:function(){var e,t,r=(null===(e=this.getPlayheadTime)||void 0===e?void 0:e.call(this))||d(i.currentTime),a=this.hlsjs&&this.hlsjs.url,n=this.dashjs&&"function"==typeof this.dashjs.getSource&&this.dashjs.getSource(),o={player_is_paused:i.paused,player_width:parseInt(u(i,"width")),player_height:parseInt(u(i,"height")),player_autoplay_on:i.autoplay,player_preload_on:i.preload,player_language_code:i.lang,player_is_fullscreen:St.default&&!!(St.default.fullscreenElement||St.default.webkitFullscreenElement||St.default.mozFullScreenElement||St.default.msFullscreenElement),video_poster_url:i.poster,video_source_url:a||n||i.currentSrc,video_source_duration:d(i.duration),video_source_height:i.videoHeight,video_source_width:i.videoWidth,view_dropped_frame_count:null==i||null===(t=i.getVideoPlaybackQuality)||void 0===t?void 0:t.call(i).droppedVideoFrames};if(i.getStartDate&&r>0){var s=i.getStartDate();if(s&&"function"==typeof s.getTime&&s.getTime()){var l=s.getTime();if(o.player_program_time=l+r,i.seekable.length>0){var c=l+i.seekable.end(i.seekable.length-1);o.player_live_edge_program_time=c}}}return o}};(r=Object.assign({automaticErrorTracking:!0},r,l)).data=Object.assign({player_software:"HTML5 Video Element",player_mux_plugin_name:"VideoElementMonitor",player_mux_plugin_version:e.VERSION},r.data),i.mux=i.mux||{},i.mux.deleted=!1,i.mux.emit=function(t,r){e.emit(n,t,r)},i.mux.updateData=function(e){i.mux.emit("hb",e)};var c=function(){s.error("The monitor for this video element has already been destroyed.")};i.mux.destroy=function(){Object.keys(i.mux.listeners).forEach((function(e){i.removeEventListener(e,i.mux.listeners[e],!1)})),delete i.mux.listeners,i.mux.destroy=c,i.mux.swapElement=c,i.mux.emit=c,i.mux.addHLSJS=c,i.mux.addDashJS=c,i.mux.removeHLSJS=c,i.mux.removeDashJS=c,i.mux.updateData=c,i.mux.setEmitTranslator=c,i.mux.setStateDataTranslator=c,i.mux.setGetPlayheadTime=c,i.mux.deleted=!0,e.emit(n,"destroy")},i.mux.swapElement=function(t){var r=q(U(t),3),a=r[0],n=r[1],o=r[2];return a?"video"!==o&&"audio"!==o?e.log.error("The element of `"+n+"` was not a media element."):(a.muxId=i.muxId,delete i.muxId,a.mux=a.mux||{},a.mux.listeners=Object.assign({},i.mux.listeners),delete i.mux.listeners,Object.keys(a.mux.listeners).forEach((function(e){i.removeEventListener(e,a.mux.listeners[e],!1),a.addEventListener(e,a.mux.listeners[e],!1)})),a.mux.swapElement=i.mux.swapElement,a.mux.destroy=i.mux.destroy,delete i.mux,void(i=a)):e.log.error("No element was found with the `"+n+"` query selector.")},i.mux.addHLSJS=function(t){e.addHLSJS(n,t)},i.mux.addDashJS=function(t){e.addDashJS(n,t)},i.mux.removeHLSJS=function(){e.removeHLSJS(n)},i.mux.removeDashJS=function(){e.removeDashJS(n)},i.mux.setEmitTranslator=function(t){e.setEmitTranslator(n,t)},i.mux.setStateDataTranslator=function(t){e.setStateDataTranslator(n,t)},i.mux.setGetPlayheadTime=function(t){t||(t=r.getPlayheadTime),e.setGetPlayheadTime(n,t)},e.init(n,r),e.emit(n,"playerready"),i.paused||(e.emit(n,"play"),i.readyState>2&&e.emit(n,"playing")),i.mux.listeners={},Ot.forEach((function(t){"error"===t&&!r.automaticErrorTracking||(i.mux.listeners[t]=function(){var r={};if("error"===t){if(!i.error||1===i.error.code)return;r.player_error_code=i.error.code,r.player_error_message=Rt[i.error.code]||i.error.message}e.emit(n,t,r)},i.addEventListener(t,i.mux.listeners[t],!1))}))}($t,e,t)},destroyMonitor:function(e){var t=q(U(e),1)[0];t&&t.mux&&"function"==typeof t.mux.destroy?t.mux.destroy():W.error("A video element monitor for `"+e+"` has not been initialized via `mux.monitor`.")},addHLSJS:function(e,t){var r=M(e);Xt[r]?Xt[r].addHLSJS(t):W.error("A monitor for `"+r+"` has not been initialized.")},addDashJS:function(e,t){var r=M(e);Xt[r]?Xt[r].addDashJS(t):W.error("A monitor for `"+r+"` has not been initialized.")},removeHLSJS:function(e){var t=M(e);Xt[t]?Xt[t].removeHLSJS():W.error("A monitor for `"+t+"` has not been initialized.")},removeDashJS:function(e){var t=M(e);Xt[t]?Xt[t].removeDashJS():W.error("A monitor for `"+t+"` has not been initialized.")},init:function(e,t){K()&&t&&t.respectDoNotTrack&&W.info("The browser's Do Not Track flag is enabled - Mux beaconing is disabled.");var r=M(e);Xt[r]=new xt($t,r,t)},emit:function(e,t,r){var a=M(e);Xt[a]?(Xt[a].emit(t,r),"destroy"===t&&delete Xt[a]):W.error("A monitor for `"+a+"` has not been initialized.")},updateData:function(e,t){var r=M(e);Xt[r]?Xt[r].emit("hb",t):W.error("A monitor for `"+r+"` has not been initialized.")},setEmitTranslator:function(e,t){var r=M(e);Xt[r]?Xt[r].emitTranslator=t:W.error("A monitor for `"+r+"` has not been initialized.")},setStateDataTranslator:function(e,t){var r=M(e);Xt[r]?Xt[r].stateDataTranslator=t:W.error("A monitor for `"+r+"` has not been initialized.")},setGetPlayheadTime:function(e,t){var r=M(e);Xt[r]?Xt[r].getPlayheadTime=t:W.error("A monitor for `"+r+"` has not been initialized.")},checkDoNotTrack:K,log:W,utils:Kt,events:Yt,WINDOW_HIDDEN:!1,WINDOW_UNLOADING:!1};Object.assign($t,Zt),void 0!==L.default&&"function"==typeof L.default.addEventListener&&L.default.addEventListener("pagehide",(function(e){e.persisted||($t.WINDOW_UNLOADING=!0)}),!1);var er=$t;
/*!
* JavaScript Cookie v2.1.3
* https://github.com/js-cookie/js-cookie
*
* Copyright 2006, 2015 Klaus Hartl & Fagner Brack
* Released under the MIT license
*/function tr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,a)}return r}function rr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tr(Object(r),!0).forEach((function(t){ar(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ar(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ir(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n=[],o=!0,s=!1;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!t||n.length!==t);o=!0);}catch(e){s=!0,i=e}finally{try{o||null==r.return||r.return()}finally{if(s)throw i}}return n}}(e,t)||function(e,t){if(e){if("string"==typeof e)return nr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?nr(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}var or=er.utils,sr=or.findMediaElement,ur=or.secondsToMs,dr=er.events,lr=er.log,cr=[dr.LOADSTART,dr.PAUSE,dr.PLAY,dr.PLAYING,dr.SEEKING,dr.SEEKED,dr.TIME_UPDATE,dr.RATECHANGE,dr.STALLED,dr.WAITING,dr.ENDED],_r=function(e){var t,r;return null!==(t=null!==(r=e.currentSrc)&&void 0!==r?r:e.src)&&void 0!==t?t:void 0},pr=function(e,t){var r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{getSrc:_r},i=a.getSrc,n=void 0===i?_r:i,o=sr(e),s=ir(o,1),u=s[0],d=function(){return null==u?void 0:u.mux};if(d())if(null!==(r=google)&&void 0!==r&&r.ima){var l,c,_,p,h,v,f=!1,m=!1,y=!1,g=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t[0],i=t[1];if(f){if(c&&u.src===c){if(cr.includes(a))return}else cr.includes(a)&&lr.warn("Unexpected event during adbreak:",a);if(a===dr.RENDITION_CHANGE)return void(p=i)}else if(a===dr.PLAY){if(y)return;y=!0}else if(a===dr.PAUSE){if(!y)return;y=!1}return t};d().setEmitTranslator(g);var b=function(){var e;return f&&c&&u.src===c&&null!==(e=v)&&void 0!==e?e:ur(u.currentTime)},T=function(){h={player_is_paused:!0,video_source_url:n(u),video_source_duration:ur(u.duration),video_source_height:u.videoHeight,video_source_width:u.videoWidth},v=ur(u.currentTime)},E=function(){h=void 0,v=void 0},w=function(){var e;return null!==(e=h)&&void 0!==e?e:{}},D=function(e){if(f){var t=m,r=c&&u.src===c?w():{};return rr(rr({},e),{},{player_is_fullscreen:t},r)}return e};d().setStateDataTranslator(D),d().setGetPlayheadTime(b);var k=function(){var e,t,r,a,i,n,o,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=null!==(e=null===(t=s.getAdData)||void 0===t?void 0:t.call(s))&&void 0!==e?e:{},d=null!==(r=null===(a=s.getAd)||void 0===a?void 0:a.call(s))&&void 0!==r?r:{},l=_?{ad_tag_url:_}:{},p=null!==(i=null===(n=d.getMediaUrl)||void 0===n?void 0:n.call(d))&&void 0!==i?i:c,h=p?{ad_asset_url:p}:{},v=null!=u&&u.adId?{ad_id:null==u?void 0:u.adId}:{},y=null!=u&&u.creativeId?{ad_creative_id:null==u?void 0:u.creativeId}:{},g=null!=u&&u.universalAdIdValue?{ad_universal_id:null==u?void 0:u.universalAdIdValue}:{};if(p){if(o=p,"function"==typeof d.getAdPodInfo){var b=d.getAdPodInfo(),T=b.getPodIndex(),E=b.getAdPosition();o+=":".concat(T,":").concat(E)}}else o=_;var w=o?{ad_request_id:o}:{},D=f?{player_is_fullscreen:m}:{};return rr(rr(rr(rr(rr(rr(rr({},l),h),v),y),g),w),D)};d().triggerAdRequest=function(){d().emit(dr.AD_REQUEST,k())};var A,x,S,O,R=t.requestAds,P=R.bind(t);t.requestAds=function(){for(var e,t,r,a=arguments.length,i=new Array(a),n=0;n<a;n++)i[n]=arguments[n];var o=i[0];return _=null==o?void 0:o.adTagUrl,null===(e=(t=d()).triggerAdRequest)||void 0===e||e.call(t),null===(r=P)||void 0===r?void 0:r.apply(void 0,i)};var q=function(e){l=e.getAdsManager(u),A=l.resize,x=A.bind(l),l.resize=function(e,t,r){var a;return m=r===google.ima.ViewMode.FULLSCREEN,null===(a=x)||void 0===a?void 0:a(e,t,r)},S=l.init,O=S.bind(l),l.init=function(e,t,r){var a;m=r===google.ima.ViewMode.FULLSCREEN;for(var i=arguments.length,n=new Array(i>3?i-3:0),o=3;o<i;o++)n[o-3]=arguments[o];return null===(a=O)||void 0===a?void 0:a.apply(void 0,[e,t,r].concat(n))},K(l)},L=function(e){T(),y&&d().emit(dr.PAUSE),f=!0;var t=k(e);c=t.ad_asset_url,d().emit(dr.AD_BREAK_START),d().emit(dr.AD_PLAY,t)},N=function(e){c=void 0,f&&(f=!1,d().emit(dr.AD_BREAK_END)),p&&(d().emit(dr.RENDITION_CHANGE,p),p=void 0),E()},I=function(e){var t=k(e);c=t.ad_asset_url,d().emit(dr.AD_RESPONSE,t)},C=function(e){var t=k(e);d().emit(dr.AD_PLAYING,t)},j=function(e){var t=k(e);d().emit(dr.AD_PLAY,t),d().emit(dr.AD_PLAYING,t)},H=function(e){var t=k(e);d().emit(dr.AD_PAUSE,t)},M=function(e){var t=k(e);d().emit(dr.AD_FIRST_QUARTILE,t)},U=function(e){var t=k(e);d().emit(dr.AD_MID_POINT,t)},B=function(e){var t=k(e);d().emit(dr.AD_THIRD_QUARTILE,t)},F=function(e){var t=k(e);d().emit(dr.AD_ENDED,t)},G=function(e){var t=k(e);d().emit(dr.AD_ENDED,t)},Q=function(e){f=!1,c=void 0,_=void 0,p=void 0,E()},V=function(e){var t=k(e);d().emit(dr.AD_RESPONSE,t)},J=function(e){e.getError().getType()===google.ima.AdError.Type.AD_LOAD&&d().emit(dr.AD_RESPONSE,k()),d().emit(dr.AD_ERROR)},W=function(e){e.addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,q),e.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,J)},z=function(e){e.removeEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,q),e.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,J)},K=function(e){e.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,L),e.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,N),e.addEventListener(google.ima.AdEvent.Type.LOADED,I),e.addEventListener(google.ima.AdEvent.Type.STARTED,C),e.addEventListener(google.ima.AdEvent.Type.RESUMED,j),e.addEventListener(google.ima.AdEvent.Type.PAUSED,H),e.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,M),e.addEventListener(google.ima.AdEvent.Type.MIDPOINT,U),e.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,B),e.addEventListener(google.ima.AdEvent.Type.COMPLETE,F),e.addEventListener(google.ima.AdEvent.Type.SKIPPED,G),e.addEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,Q),e.addEventListener(google.ima.AdEvent.Type.AD_METADATA,V),e.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,J)},Y=function(e){e.removeEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,L),e.removeEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,N),e.removeEventListener(google.ima.AdEvent.Type.LOADED,I),e.removeEventListener(google.ima.AdEvent.Type.STARTED,C),e.removeEventListener(google.ima.AdEvent.Type.RESUMED,j),e.removeEventListener(google.ima.AdEvent.Type.PAUSED,H),e.removeEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,M),e.removeEventListener(google.ima.AdEvent.Type.MIDPOINT,U),e.removeEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,B),e.removeEventListener(google.ima.AdEvent.Type.COMPLETE,F),e.removeEventListener(google.ima.AdEvent.Type.SKIPPED,G),e.removeEventListener(google.ima.AdEvent.Type.ALL_ADS_COMPLETED,Q),e.removeEventListener(google.ima.AdEvent.Type.AD_METADATA,V),e.removeEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,J)};d().swapElement=function(e){lr.error("swapElement() is not supported when using google-ima.")};var X=d().destroy;d().destroy=function(){var e=d();z(t),R&&(t.requestAds=R,R=P=void 0),l&&(Y(l),A&&(l.resize=A,A=x=void 0),S&&(l.init=S,S=O=void 0),l=void 0),e.swapElement=function(){},e.triggerAdRequest=function(){},e.setEmitTranslator(void 0),e.setStateDataTranslator(void 0),e.setGetPlayheadTime(void 0),X()},W(t)}else lr.error("Missing google.ima SDK. Make sure you include it via a script tag.");else lr.error("Missing mux data instance.")};function hr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n=[],o=!0,s=!1;try{for(r=r.call(e);!(o=(a=r.next()).done)&&(n.push(a.value),!t||n.length!==t);o=!0);}catch(e){s=!0,i=e}finally{try{o||null==r.return||r.return()}finally{if(s)throw i}}return n}}(e,t)||function(e,t){if(e){if("string"==typeof e)return vr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?vr(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=new Array(t);r<t;r++)a[r]=e[r];return a}var fr=er.utils.findMediaElement,mr=er.monitor,yr=function(e,t){return function(e){return null!=e.hlsjs&&null!=e.Hls}(t)?function(e,t){var r=t.hlsjs;return t.Hls,{getSrc:function(e){var t,a;return null!==(t=null===(a=r)||void 0===a?void 0:a.url)&&void 0!==t?t:_r(e)},addHLSJS:function(e){r=e.hlsjs,e.Hls},removeHLSJS:function(){r=void 0}}}(0,t):function(e){return null!=e.dashjs}(t)?function(e,t){var r=t.dashjs;return{getSrc:function(e){var t,a;return null!==(t=null===(a=r)||void 0===a?void 0:a.getSource())&&void 0!==t?t:_r(e)},addDashJS:function(e){r=e.dashjs},removeDashJS:function(){r=void 0}}}(0,t):{getSrc:_r}},gr=function(e,t){"addHLSJS"in t&&function(e,t){var r,a=null==(r=hr(fr(e),1)[0])?void 0:r.mux,i=a.addHLSJS,n=a.removeHLSJS;a.addHLSJS=function(e){t.addHLSJS(e),i(e)},a.removeHLSJS=function(){t.removeHLSJS(),n()}}(e,t),"addDashJS"in t&&function(e,t){var r,a=null==(r=hr(fr(e),1)[0])?void 0:r.mux,i=a.addDashJS,n=a.removeDashJS;a.addDashJS=function(e){t.addDashJS(e),i(e)},a.removeDashJS=function(){t.removeDashJS(),n()}}(e,t)},br={initializeIma:pr,monitor:function(e,t){var r=t.imaAdsLoader;if(mr(e,t),r){var a=yr(0,t);pr(e,r,{getSrc:a.getSrc}),gr(e,a)}}},Tr=function(){return er.apply(void 0,arguments)};Object.assign(Tr,er,br);var Er=Tr}},t={};function r(a){var i=t[a];if(void 0!==i)return i.exports;var n=t[a]={exports:{}};return e[a](n,n.exports,r),n.exports}return r.d=function(e,t){for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r(254)}()},"object"==typeof exports&&"object"==typeof module?module.exports=t():"object"==typeof exports?exports.mux=t():e.mux=t()}();