"use strict";"use client";var y=Object.create;var l=Object.defineProperty;var x=Object.getOwnPropertyDescriptor;var P=Object.getOwnPropertyNames;var R=Object.getPrototypeOf,L=Object.prototype.hasOwnProperty;var T=(e,n)=>{for(var t in n)l(e,t,{get:n[t],enumerable:!0})},E=(e,n,t,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of P(n))!L.call(e,r)&&r!==t&&l(e,r,{get:()=>n[r],enumerable:!(o=x(n,r))||o.enumerable});return e};var u=(e,n,t)=>(t=e!=null?y(R(e)):{},E(n||!e||!e.__esModule?l(t,"default",{value:e,enumerable:!0}):t,e)),k=e=>E(l({},"__esModule",{value:!0}),e);var g={};T(g,{default:()=>b});module.exports=k(g);var s=u(require("react")),N=require("@mux/mux-player/ads"),M=u(require("@mux/mux-player-react"));var d=u(require("react"));function p(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function A(...e){return n=>{let t=!1,o=e.map(r=>{let a=p(r,n);return!t&&typeof a=="function"&&(t=!0),a});if(t)return()=>{for(let r=0;r<o.length;r++){let a=o[r];typeof a=="function"?a():p(e[r],null)}}}}function c(...e){return d.useCallback(A(...e),e)}var m=require("react"),v=(e,n,t)=>(0,m.useEffect)(()=>{let o=n==null?void 0:n.current;if(!o||!t)return;let r=e,a=t;return o.addEventListener(r,a),()=>{o.removeEventListener(r,a)}},[n==null?void 0:n.current,t,e]);var G=(0,s.forwardRef)((e,n)=>{let t=(0,s.useRef)(null),o={},r={};for(let[a,i]of Object.entries(e))a.startsWith("onAd")?o[a]=i:r[a]=i;for(let a in o){let i=o[a],f=a.slice(2).toLowerCase();v(f,t,i)}return s.default.createElement(M.default,{ref:c(t,n),...r})}),b=G;
//# sourceMappingURL=ads.cjs.js.map
