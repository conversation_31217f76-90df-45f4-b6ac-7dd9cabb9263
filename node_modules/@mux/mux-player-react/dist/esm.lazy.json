{"inputs": {"src/ConditionalSuspense.tsx": {"bytes": 449, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/useIsBrowser.ts": {"bytes": 286, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/useIsIntersecting.ts": {"bytes": 645, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/common/utils.ts": {"bytes": 2742, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/useComposedRefs.ts": {"bytes": 1822, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/useObjectPropEffect.ts": {"bytes": 1850, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/env.ts": {"bytes": 245, "imports": [], "format": "esm"}, "src/useEventCallbackEffect.ts": {"bytes": 950, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/types.ts": {"bytes": 4954, "imports": [], "format": "esm"}, "src/index.tsx": {"bytes": 6516, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "@mux/playback-core", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player", "kind": "import-statement", "external": true}, {"path": "src/common/utils.ts", "kind": "import-statement", "original": "./common/utils"}, {"path": "src/useComposedRefs.ts", "kind": "import-statement", "original": "./useComposedRefs"}, {"path": "src/useObjectPropEffect.ts", "kind": "import-statement", "original": "./useObjectPropEffect"}, {"path": "src/env.ts", "kind": "import-statement", "original": "./env"}, {"path": "src/useEventCallbackEffect.ts", "kind": "import-statement", "original": "./useEventCallbackEffect"}, {"path": "src/types.ts", "kind": "import-statement", "original": "./types"}], "format": "esm"}, "src/lazy.tsx": {"bytes": 4460, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "src/ConditionalSuspense.tsx", "kind": "import-statement", "original": "./ConditionalSuspense"}, {"path": "src/useIsBrowser.ts", "kind": "import-statement", "original": "./useIsBrowser"}, {"path": "src/useIsIntersecting.ts", "kind": "import-statement", "original": "./useIsIntersecting"}, {"path": "src/index.tsx", "kind": "dynamic-import", "original": "./index"}], "format": "esm"}}, "outputs": {"dist/lazy.mjs.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 8941}, "dist/lazy.mjs": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "dist/-W6F6BQLK.mjs", "kind": "dynamic-import"}], "exports": ["default"], "entryPoint": "src/lazy.tsx", "inputs": {"src/lazy.tsx": {"bytesInOutput": 1687}, "src/ConditionalSuspense.tsx": {"bytesInOutput": 166}, "src/useIsBrowser.ts": {"bytesInOutput": 134}, "src/useIsIntersecting.ts": {"bytesInOutput": 274}}, "bytes": 2330}, "dist/-W6F6BQLK.mjs.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 22260}, "dist/-W6F6BQLK.mjs": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "@mux/playback-core", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["MaxResolution", "MediaError", "MinResolution", "RenditionOrder", "default", "generatePlayerInitTime", "playerSoftwareName", "playerSoftwareVersion"], "entryPoint": "src/index.tsx", "inputs": {"src/index.tsx": {"bytesInOutput": 1948}, "src/common/utils.ts": {"bytesInOutput": 619}, "src/useComposedRefs.ts": {"bytesInOutput": 351}, "src/useObjectPropEffect.ts": {"bytesInOutput": 602}, "src/env.ts": {"bytesInOutput": 71}, "src/useEventCallbackEffect.ts": {"bytesInOutput": 215}}, "bytes": 4041}}}