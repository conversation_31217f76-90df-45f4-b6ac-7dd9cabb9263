"use client";import m,{forwardRef as v,useRef as M}from"react";import"@mux/mux-player/ads";import f from"@mux/mux-player-react";import*as l from"react";function i(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function d(...e){return t=>{let a=!1,o=e.map(r=>{let n=i(r,t);return!a&&typeof n=="function"&&(a=!0),n});if(a)return()=>{for(let r=0;r<o.length;r++){let n=o[r];typeof n=="function"?n():i(e[r],null)}}}}function u(...e){return l.useCallback(d(...e),e)}import{useEffect as c}from"react";var E=(e,t,a)=>c(()=>{let o=t==null?void 0:t.current;if(!o||!a)return;let r=e,n=a;return o.addEventListener(r,n),()=>{o.removeEventListener(r,n)}},[t==null?void 0:t.current,a,e]);var y=v((e,t)=>{let a=M(null),o={},r={};for(let[n,s]of Object.entries(e))n.startsWith("onAd")?o[n]=s:r[n]=s;for(let n in o){let s=o[n],p=n.slice(2).toLowerCase();E(p,a,s)}return m.createElement(f,{ref:u(a,t),...r})}),g=y;export{g as default};
//# sourceMappingURL=ads.mjs.map
