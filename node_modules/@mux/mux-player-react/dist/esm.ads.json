{"inputs": {"src/useComposedRefs.ts": {"bytes": 1822, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/useEventCallbackEffect.ts": {"bytes": 950, "imports": [{"path": "react", "kind": "import-statement", "external": true}], "format": "esm"}, "src/ads.tsx": {"bytes": 2980, "imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player/ads", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player-react", "kind": "import-statement", "external": true}, {"path": "src/useComposedRefs.ts", "kind": "import-statement", "original": "./useComposedRefs"}, {"path": "src/useEventCallbackEffect.ts", "kind": "import-statement", "original": "./useEventCallbackEffect"}], "format": "esm"}}, "outputs": {"dist/ads.mjs.map": {"imports": [], "exports": [], "inputs": {}, "bytes": 7980}, "dist/ads.mjs": {"imports": [{"path": "react", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player/ads", "kind": "import-statement", "external": true}, {"path": "@mux/mux-player-react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}, {"path": "react", "kind": "import-statement", "external": true}], "exports": ["default"], "entryPoint": "src/ads.tsx", "inputs": {"src/ads.tsx": {"bytesInOutput": 336}, "src/useComposedRefs.ts": {"bytesInOutput": 349}, "src/useEventCallbackEffect.ts": {"bytesInOutput": 213}}, "bytes": 966}}}