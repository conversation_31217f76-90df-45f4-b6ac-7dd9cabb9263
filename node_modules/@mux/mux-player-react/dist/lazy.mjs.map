{"version": 3, "sources": ["../src/lazy.tsx", "../src/ConditionalSuspense.tsx", "../src/useIsBrowser.ts", "../src/useIsIntersecting.ts"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState } from 'react';\nimport type { ValueOf } from '@mux/playback-core';\n\nimport ConditionalSuspense from './ConditionalSuspense';\nimport useIsBrowser from './useIsBrowser';\nimport useIsIntersecting from './useIsIntersecting';\n\nimport type { MuxPlayerProps, MuxPlayerRefAttributes } from './index';\nimport type MuxPlayerElement from '@mux/mux-player';\n\ninterface MuxPlayerElementReact extends Partial<Omit<MuxPlayerElement, 'style' | 'children'>> {\n  ref: React.MutableRefObject<MuxPlayerElement | null> | null | undefined;\n  style: React.CSSProperties;\n  children?: React.ReactNode;\n}\n\ndeclare global {\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  namespace JSX {\n    interface IntrinsicElements {\n      'mux-player': MuxPlayerElementReact;\n    }\n  }\n}\n\nconst MuxPlayerIndex = React.lazy(() => import('./index'));\n\ninterface FallbackProps extends MuxPlayerProps {\n  onIntersection?: () => void;\n}\nconst Fallback = (props: FallbackProps) => {\n  const { style, className, onIntersection, placeholder } = props;\n\n  const intersectionRef = React.useRef<MuxPlayerElement>(null);\n  const isIntersecting = useIsIntersecting(intersectionRef);\n\n  useEffect(() => {\n    if (isIntersecting && onIntersection) {\n      onIntersection();\n    }\n  }, [isIntersecting, onIntersection]);\n\n  return (\n    /* \n    Why do we have a mux-player element before the mux-player bundle is even loaded?\n    Before the bundle is loaded, this mux-player element just acts like a div.\n    However, by calling this placeholder \"mux-player\",\n    it now gets the same CSS applied to it that the eventual \"real\" mux-player element will. \n    */\n    <>\n      <mux-player\n        ref={intersectionRef}\n        data-mux-player-react-lazy-placeholder\n        placeholder={placeholder ?? ''}\n        style={\n          {\n            '--mux-player-react-lazy-placeholder': placeholder ? `url('${placeholder}');` : '',\n            ...style,\n          } as React.CSSProperties\n        }\n        className={className || ''}\n        // since there's a possibility that the bundle loads before Suspense clears this placeholder,\n        // we need to make sure that the placeholder isn't interactive and its player chrome in doesn't get rendered\n        nohotkeys\n        aria-hidden\n        tabIndex={-1}\n      >\n        <div data-mux-player-react-lazy-placeholder-overlay />\n      </mux-player>\n      <style>{\n        /* css */ `\n        mux-player[data-mux-player-react-lazy-placeholder] {\n          aspect-ratio: 16/9;\n          display: block;\n          background-color: var(--media-background-color, #000);\n          width: 100%;\n          position: relative;\n          background-image: var(--mux-player-react-lazy-placeholder);\n          background-repeat: no-repeat;\n          background-size: var(--media-object-fit, contain);\n          background-position: var(--media-object-position, 50% 50%);\n          --controls: none;\n          --controls-backdrop-color: rgba(0, 0, 0, 0.6);\n        }\n        mux-player [data-mux-player-react-lazy-placeholder-overlay] {\n          position: absolute;\n          inset: 0;\n          background-color: var(--controls-backdrop-color);\n        }\n      `\n      }</style>\n    </>\n  );\n};\n\ntype LoadingType = {\n  PAGE: 'page';\n  VIEWPORT: 'viewport';\n};\n\nconst LoadingType: LoadingType = {\n  PAGE: 'page',\n  VIEWPORT: 'viewport',\n};\n\ninterface MuxPlayerLazyProps extends MuxPlayerProps {\n  loading?: ValueOf<LoadingType>;\n}\nconst MuxPlayer = React.forwardRef<MuxPlayerRefAttributes, MuxPlayerLazyProps>((props, ref) => {\n  const { loading = LoadingType.VIEWPORT, ...playerProps } = props;\n\n  // We load mux player once two conditions are met:\n  // 1. We're in a browser (react.lazy doesn't work on the server in react 17)\n  const isBrowser = useIsBrowser();\n  // 2. The player has entered the viewport, according to the fallback (if enabled).\n  const [isIntersecting, setIsIntersecting] = useState(() => (loading === LoadingType.VIEWPORT ? false : true));\n\n  return (\n    <ConditionalSuspense\n      condition={isBrowser && isIntersecting}\n      fallback={\n        <Fallback\n          style={playerProps.style}\n          className={playerProps.className}\n          placeholder={playerProps.placeholder}\n          onIntersection={() => setIsIntersecting(true)}\n        />\n      }\n    >\n      <MuxPlayerIndex {...playerProps} ref={ref} />\n    </ConditionalSuspense>\n  );\n});\n\nexport default MuxPlayer;\n", "import React, { Suspense } from 'react';\n\ntype Props = {\n  fallback: React.ReactChild | React.ReactFragment | React.ReactPortal | null;\n  condition: boolean;\n  children: React.ReactNode;\n};\nconst ConditionalSuspense = ({ condition, fallback, children, ...rest }: Props) => {\n  return condition ? (\n    <Suspense fallback={fallback} {...rest}>\n      {children}\n    </Suspense>\n  ) : (\n    <>{fallback}</>\n  );\n};\n\nexport default ConditionalSuspense;\n", "import { useState, useEffect } from 'react';\n\nconst useIsBrowser = () => {\n  const [isBrowser, setIsBrowser] = useState(false);\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      setIsBrowser(true);\n    }\n  }, []);\n\n  return isBrowser;\n};\n\nexport default useIsBrowser;\n", "import { useState, useEffect } from 'react';\n\nconst useIsIntersecting = (ref: React.RefObject<HTMLElement>, options?: IntersectionObserverInit) => {\n  const [isIntersecting, setIsIntersecting] = useState(false);\n\n  useEffect(() => {\n    if (typeof IntersectionObserver === 'function') {\n      const observer = new IntersectionObserver(([entry]) => {\n        setIsIntersecting(entry.isIntersecting);\n      }, options);\n\n      if (ref.current) {\n        observer.observe(ref.current);\n      }\n\n      return () => {\n        observer.disconnect();\n      };\n    }\n  }, [ref, options]);\n\n  return isIntersecting;\n};\n\nexport default useIsIntersecting;\n"], "mappings": "aAEA,OAAOA,GAAS,aAAAC,EAAW,YAAAC,MAAgB,QCF3C,OAAOC,GAAS,YAAAC,MAAgB,QAOhC,IAAMC,EAAsB,CAAC,CAAE,UAAAC,EAAW,SAAAC,EAAU,SAAAC,EAAU,GAAGC,CAAK,IAC7DH,EACLH,EAAA,cAACC,EAAA,CAAS,SAAUG,EAAW,GAAGE,GAC/BD,CACH,EAEAL,EAAA,cAAAA,EAAA,cAAGI,CAAS,EAITG,EAAQL,ECjBf,OAAS,YAAAM,EAAU,aAAAC,MAAiB,QAEpC,IAAMC,EAAe,IAAM,CACzB,GAAM,CAACC,EAAWC,CAAY,EAAIJ,EAAS,EAAK,EAEhD,OAAAC,EAAU,IAAM,CACV,OAAO,QAAW,aACpBG,EAAa,EAAI,CAErB,EAAG,CAAC,CAAC,EAEED,CACT,EAEOE,EAAQH,ECdf,OAAS,YAAAI,EAAU,aAAAC,MAAiB,QAEpC,IAAMC,EAAoB,CAACC,EAAmCC,IAAuC,CACnG,GAAM,CAACC,EAAgBC,CAAiB,EAAIN,EAAS,EAAK,EAE1D,OAAAC,EAAU,IAAM,CACd,GAAI,OAAO,sBAAyB,WAAY,CAC9C,IAAMM,EAAW,IAAI,qBAAqB,CAAC,CAACC,CAAK,IAAM,CACrDF,EAAkBE,EAAM,cAAc,CACxC,EAAGJ,CAAO,EAEV,OAAID,EAAI,SACNI,EAAS,QAAQJ,EAAI,OAAO,EAGvB,IAAM,CACXI,EAAS,WAAW,CACtB,CACF,CACF,EAAG,CAACJ,EAAKC,CAAO,CAAC,EAEVC,CACT,EAEOI,EAAQP,EHGf,IAAMQ,EAAiBC,EAAM,KAAK,IAAM,OAAO,iBAAS,CAAC,EAKnDC,EAAYC,GAAyB,CACzC,GAAM,CAAE,MAAAC,EAAO,UAAAC,EAAW,eAAAC,EAAgB,YAAAC,CAAY,EAAIJ,EAEpDK,EAAkBP,EAAM,OAAyB,IAAI,EACrDQ,EAAiBC,EAAkBF,CAAe,EAExD,OAAAG,EAAU,IAAM,CACVF,GAAkBH,GACpBA,EAAe,CAEnB,EAAG,CAACG,EAAgBH,CAAc,CAAC,EASjCL,EAAA,cAAAA,EAAA,cACEA,EAAA,cAAC,cACC,IAAKO,EACL,yCAAsC,GACtC,YAAaD,GAAA,KAAAA,EAAe,GAC5B,MACE,CACE,sCAAuCA,EAAc,QAAQA,CAAW,MAAQ,GAChF,GAAGH,CACL,EAEF,UAAWC,GAAa,GAGxB,UAAS,GACT,cAAW,GACX,SAAU,IAEVJ,EAAA,cAAC,OAAI,iDAA8C,GAAC,CACtD,EACAA,EAAA,cAAC,aACW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAoBX,CACH,CAEJ,EAOMW,EAA2B,CAC/B,KAAM,OACN,SAAU,UACZ,EAKMC,EAAYZ,EAAM,WAAuD,CAACE,EAAOW,IAAQ,CAC7F,GAAM,CAAE,QAAAC,EAAUH,EAAY,SAAU,GAAGI,CAAY,EAAIb,EAIrDc,EAAYC,EAAa,EAEzB,CAACT,EAAgBU,CAAiB,EAAIC,EAAS,IAAOL,IAAYH,EAAY,QAAwB,EAE5G,OACEX,EAAA,cAACoB,EAAA,CACC,UAAWJ,GAAaR,EACxB,SACER,EAAA,cAACC,EAAA,CACC,MAAOc,EAAY,MACnB,UAAWA,EAAY,UACvB,YAAaA,EAAY,YACzB,eAAgB,IAAMG,EAAkB,EAAI,EAC9C,GAGFlB,EAAA,cAACD,EAAA,CAAgB,GAAGgB,EAAa,IAAKF,EAAK,CAC7C,CAEJ,CAAC,EAEMQ,EAAQT", "names": ["React", "useEffect", "useState", "React", "Suspense", "ConditionalSuspense", "condition", "fallback", "children", "rest", "ConditionalSuspense_default", "useState", "useEffect", "useIsBrowser", "<PERSON><PERSON><PERSON><PERSON>", "setIsBrowser", "useIsBrowser_default", "useState", "useEffect", "useIsIntersecting", "ref", "options", "isIntersecting", "setIsIntersecting", "observer", "entry", "useIsIntersecting_default", "MuxPlayerIndex", "React", "Fallback", "props", "style", "className", "onIntersection", "placeholder", "intersectionRef", "isIntersecting", "useIsIntersecting_default", "useEffect", "LoadingType", "MuxPlayer", "ref", "loading", "playerProps", "<PERSON><PERSON><PERSON><PERSON>", "useIsBrowser_default", "setIsIntersecting", "useState", "ConditionalSuspense_default", "lazy_default"]}