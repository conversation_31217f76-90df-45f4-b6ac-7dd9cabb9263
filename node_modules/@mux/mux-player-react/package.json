{"name": "@mux/mux-player-react", "version": "3.5.1", "description": "An open source Mux player for React that Just Works™", "homepage": "https://mux.com/player", "keywords": ["video", "mux", "player", "hls", "react"], "main": "./dist/index.cjs.js", "module": "./dist/index.mjs", "typesVersions": {"<4.3.5": {".": ["./dist/types-ts3.4/index.d.ts"], "themes/*": ["./dist/types-ts3.4/themes/*.d.ts"], "*": ["./dist/types-ts3.4/*.d.ts"]}, "*": {".": ["./dist/types/index.d.ts"], "themes/*": ["./dist/types/themes/*.d.ts"], "*": ["./dist/types/*.d.ts"]}}, "exports": {".": {"types@<4.3.5": "./dist/types-ts3.4/index.d.ts", "types": "./dist/types/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs.js", "default": "./dist/index.cjs.js"}, "./lazy": {"types@<4.3.5": "./dist/types-ts3.4/lazy.d.ts", "types": "./dist/types/lazy.d.ts", "import": "./dist/lazy.mjs", "default": "./dist/lazy.mjs"}, "./news": {"types@<4.3.5": "./dist/types-ts3.4/news/index.d.ts", "types": "./dist/types/news/index.d.ts", "import": "./dist/news/index.mjs", "require": "./dist/news/index.cjs.js", "default": "./dist/news/index.cjs.js"}, "./themes/*": {"types@<4.3.5": "./dist/types-ts3.4/themes/*.d.ts", "types": "./dist/types/themes/*.d.ts", "import": "./dist/themes/*.mjs", "require": "./dist/themes/*.cjs.js", "default": "./dist/themes/*.cjs.js"}, "./*": {"types@<4.3.5": "./dist/types-ts3.4/*.d.ts", "types": "./dist/types/*.d.ts", "import": "./dist/*.mjs", "require": "./dist/*.cjs.js", "default": "./dist/*.mjs"}}, "types": "./dist/types/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/muxinc/elements.git", "directory": "packages/mux-player-react"}, "author": "Mux, Inc.", "license": "MIT", "scripts": {"clean": "shx rm -rf dist/", "dev:types": "npm run build:types -- -w", "dev:esm": "npm-run-all --parallel 'build:esm:** -- --watch=forever'", "dev:cjs": "npm-run-all --parallel 'build:cjs:** -- --watch=forever'", "dev": "npm-run-all --parallel dev:types dev:esm dev:cjs", "build:cjs:index": "esbuild src/index.tsx src/themes/*.ts --target=es2019 --bundle --sourcemap --metafile=./dist/cjs.json --format=cjs --loader:.css=text --outdir=dist --out-extension:.js=.cjs.js --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:esm:index": "esbuild src/index.tsx src/themes/*.ts --target=es2019 --bundle --sourcemap --metafile=./dist/esm.json --format=esm --loader:.css=text --outdir=dist --out-extension:.js=.mjs --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:esm:news": "esbuild src/news/index.tsx --target=es2019 --bundle --sourcemap --metafile=./dist/news/esm.json --format=esm --loader:.css=text --outdir=dist/news --out-extension:.js=.mjs --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:cjs:news": "esbuild src/news/index.tsx --target=es2019 --bundle --sourcemap --metafile=./dist/news/cjs.json --format=cjs --loader:.css=text --outdir=dist/news --out-extension:.js=.cjs.js --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:esm:ads": "esbuild src/ads.tsx --target=es2019 --bundle --sourcemap --metafile=./dist/esm.ads.json --format=esm --loader:.css=text --outdir=dist --out-extension:.js=.mjs --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:cjs:ads": "esbuild src/ads.tsx --target=es2019 --bundle --sourcemap --metafile=./dist/cjs.ads.json --format=cjs --loader:.css=text --outdir=dist --out-extension:.js=.cjs.js --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:cjs:lazy": "echo 'esbuild cjs does not support code-splitting. See https://esbuild.github.io/api/#splitting for details'", "build:esm:lazy": "esbuild src/lazy.tsx --splitting --target=es2019 --bundle --sourcemap --metafile=./dist/esm.lazy.json --format=esm --loader:.css=text --outdir=dist --out-extension:.js=.mjs --external:react --external:@mux/* --external:prop-types --define:PLAYER_VERSION=\"'$npm_package_version'\"", "build:types": "tsc", "postbuild:types": "downlevel-dts ./dist/types ./dist/types-ts3.4", "build": "npm-run-all --parallel 'build:cjs:* -- --minify' 'build:esm:* -- --minify'"}, "peerDependencies": {"@types/react": "^17.0.0 || ^17.0.0-0 || ^18 || ^18.0.0-0 || ^19 || ^19.0.0-0", "react": "^17.0.2 || ^17.0.0-0 || ^18 || ^18.0.0-0 || ^19 || ^19.0.0-0", "react-dom": "^17.0.2 || ^17.0.2-0 || ^18 || ^18.0.0-0 || ^19 || ^19.0.0-0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "dependencies": {"@mux/mux-player": "3.5.1", "@mux/playback-core": "0.30.1", "prop-types": "^15.8.1"}, "devDependencies": {"@types/prop-types": "^15.7.14", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "downlevel-dts": "^0.11.0", "esbuild": "^0.25.1", "npm-run-all": "^4.1.5", "react": "^18.2.0", "react-dom": "^18.2.0", "shx": "^0.4.0", "typescript": "^5.8.2"}}