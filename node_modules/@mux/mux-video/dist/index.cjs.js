"use strict";var y=Object.defineProperty;var M=Object.getOwnPropertyDescriptor;var v=Object.getOwnPropertyNames;var R=Object.prototype.hasOwnProperty;var C=t=>{throw TypeError(t)};var w=(t,o)=>{for(var e in o)y(t,e,{get:o[e],enumerable:!0})},g=(t,o,e,a)=>{if(o&&typeof o=="object"||typeof o=="function")for(let r of v(o))!R.call(t,r)&&r!==e&&y(t,r,{get:()=>o[r],enumerable:!(a=M(o,r))||a.enumerable});return t},n=(t,o,e)=>(g(t,o,"default"),e&&g(e,o,"default"));var V=t=>g(y({},"__esModule",{value:!0}),t);var T=(t,o,e)=>o.has(t)||C("Cannot "+e);var c=(t,o,e)=>(T(t,o,"read from private field"),e?e.call(t):o.get(t)),p=(t,o,e)=>o.has(t)?C("Cannot add the same private member more than once"):o instanceof WeakSet?o.add(t):o.set(t,e),h=(t,o,e,a)=>(T(t,o,"write to private field"),a?a.call(t,e):o.set(t,e),e);var s={};w(s,{default:()=>O});module.exports=V(s);var i=class{addEventListener(){}removeEventListener(){}dispatchEvent(o){return!0}};if(typeof DocumentFragment=="undefined"){class t extends i{}globalThis.DocumentFragment=t}var l=class extends i{},b=class extends i{},F={get(t){},define(t,o,e){},getName(t){return null},upgrade(t){},whenDefined(t){return Promise.resolve(l)}},u,x=class{constructor(o,e={}){p(this,u);h(this,u,e==null?void 0:e.detail)}get detail(){return c(this,u)}initCustomEvent(){}};u=new WeakMap;function G(t,o){return new l}var E={document:{createElement:G},DocumentFragment,customElements:F,CustomEvent:x,EventTarget:i,HTMLElement:l,HTMLVideoElement:b},D=typeof window=="undefined"||typeof globalThis.customElements=="undefined",f=D?E:globalThis,N=D?E.document:globalThis.document;var _=require("@mux/mux-video/base"),k=require("castable-video/castable-mixin.js"),A=require("media-tracks");n(s,require("@mux/mux-video/base"),module.exports);var d,m=class extends(0,k.CastableMediaMixin)((0,A.MediaTracksMixin)(_.MuxVideoBaseElement)){constructor(){super(...arguments);p(this,d)}get autoplay(){let e=this.getAttribute("autoplay");return e===null?!1:e===""?!0:e}set autoplay(e){let a=this.autoplay;e!==a&&(e?this.setAttribute("autoplay",typeof e=="string"?e:""):this.removeAttribute("autoplay"))}get muxCastCustomData(){return{mux:{playbackId:this.playbackId,minResolution:this.minResolution,maxResolution:this.maxResolution,renditionOrder:this.renditionOrder,customDomain:this.customDomain,tokens:{drm:this.drmToken},envKey:this.envKey,metadata:this.metadata,disableCookies:this.disableCookies,disableTracking:this.disableTracking,beaconCollectionDomain:this.beaconCollectionDomain,startTime:this.startTime,preferCmcd:this.preferCmcd}}}get castCustomData(){var e;return(e=c(this,d))!=null?e:this.muxCastCustomData}set castCustomData(e){h(this,d,e)}};d=new WeakMap;f.customElements.get("mux-video")||(f.customElements.define("mux-video",m),f.MuxVideoElement=m);var O=m;
//# sourceMappingURL=index.cjs.js.map
