var f=e=>{throw TypeError(e)};var g=(e,o,t)=>o.has(e)||f("Cannot "+t);var u=(e,o,t)=>(g(e,o,"read from private field"),t?t.call(e):o.get(e)),m=(e,o,t)=>o.has(e)?f("Cannot add the same private member more than once"):o instanceof WeakSet?o.add(e):o.set(e,t),d=(e,o,t,l)=>(g(e,o,"write to private field"),l?l.call(e,t):o.set(e,t),t);var s=class{addEventListener(){}removeEventListener(){}dispatchEvent(o){return!0}};if(typeof DocumentFragment=="undefined"){class e extends s{}globalThis.DocumentFragment=e}var n=class extends s{},p=class extends s{},x={get(e){},define(e,o,t){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(n)}},a,h=class{constructor(o,t={}){m(this,a);d(this,a,t==null?void 0:t.detail)}get detail(){return u(this,a)}initCustomEvent(){}};a=new WeakMap;function C(e,o){return new n}var y={document:{createElement:C},DocumentFragment,customElements:x,CustomEvent:h,EventTarget:s,HTMLElement:n,HTMLVideoElement:p},b=typeof window=="undefined"||typeof globalThis.customElements=="undefined",c=b?y:globalThis,k=b?y.document:globalThis.document;import{MuxVideoBaseElement as T}from"@mux/mux-video/base";import{CastableMediaMixin as E}from"castable-video/castable-mixin.js";import{MediaTracksMixin as D}from"media-tracks";export*from"@mux/mux-video/base";var r,i=class extends E(D(T)){constructor(){super(...arguments);m(this,r)}get autoplay(){let t=this.getAttribute("autoplay");return t===null?!1:t===""?!0:t}set autoplay(t){let l=this.autoplay;t!==l&&(t?this.setAttribute("autoplay",typeof t=="string"?t:""):this.removeAttribute("autoplay"))}get muxCastCustomData(){return{mux:{playbackId:this.playbackId,minResolution:this.minResolution,maxResolution:this.maxResolution,renditionOrder:this.renditionOrder,customDomain:this.customDomain,tokens:{drm:this.drmToken},envKey:this.envKey,metadata:this.metadata,disableCookies:this.disableCookies,disableTracking:this.disableTracking,beaconCollectionDomain:this.beaconCollectionDomain,startTime:this.startTime,preferCmcd:this.preferCmcd}}}get castCustomData(){var t;return(t=u(this,r))!=null?t:this.muxCastCustomData}set castCustomData(t){d(this,r,t)}};r=new WeakMap;c.customElements.get("mux-video")||(c.customElements.define("mux-video",i),c.MuxVideoElement=i);var F=i;export{F as default};
//# sourceMappingURL=index.mjs.map
