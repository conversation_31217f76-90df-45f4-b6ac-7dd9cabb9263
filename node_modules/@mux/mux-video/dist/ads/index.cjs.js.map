{"version": 3, "sources": ["../../src/ads/index.ts", "../../src/polyfills/index.ts"], "sourcesContent": ["/* eslint @typescript-eslint/triple-slash-reference: \"off\" */\n/// <reference types=\"google_interactive_media_ads_types\" preserve=\"true\"/>\n/// <reference path=\"../../../../node_modules/mux-embed/dist/types/mux-embed.d.ts\" preserve=\"true\" />\n\n/** @TODO publish types for package to use here (CJP) */\n// @ts-ignore\nimport mux from '@mux/mux-data-google-ima';\nimport type { MuxDataSDK } from '@mux/playback-core';\nimport { Autoplay } from '@mux/playback-core';\nimport { MuxVideoBaseElement, Attributes as BaseAttributes, EventMap as BaseEventMap } from '@mux/mux-video/base';\nimport { CastableMediaMixin } from 'castable-video/castable-mixin.js';\nimport { MediaTracksMixin } from 'media-tracks';\nimport { globalThis } from '../polyfills';\nimport {\n  AdsVideoMixin,\n  Attributes as AdsAttributes,\n  Events as AdEvents,\n  Expand,\n  EventMap as AdEventMap,\n} from '@mux/mux-video/ads/mixin';\n\nexport * from '@mux/mux-video/base';\n\nexport type EventMap = Expand<BaseEventMap & AdEventMap>;\n\nexport const Attributes = {\n  ...BaseAttributes,\n  ...AdsAttributes,\n} as const;\n\n// castable-video should be mixed in last so that it can override load().\nclass MuxVideoElement extends CastableMediaMixin(MediaTracksMixin(AdsVideoMixin(MuxVideoBaseElement))) {\n  #muxDataKeepSession = false;\n\n  handleEvent(event: Event) {\n    super.handleEvent(event);\n\n    if (event.type === AdEvents.AD_BREAK_START) {\n      this.#handleAdBreakStart();\n    } else if (event.type === AdEvents.AD_BREAK_END) {\n      this.#handleAdBreakEnd();\n    }\n  }\n\n  #handleAdBreakStart() {\n    if (!this.ad?.isLinear()) return;\n\n    if (this.ad?.isCustomPlaybackUsed()) {\n      this.muxDataKeepSession = true;\n      this.unload();\n      this.muxDataKeepSession = false;\n    }\n  }\n\n  #handleAdBreakEnd() {\n    if (!this.ad?.isLinear()) return;\n\n    if (this.ad?.isCustomPlaybackUsed()) {\n      this.muxDataKeepSession = true;\n      this.load();\n      this.muxDataKeepSession = false;\n    }\n  }\n\n  get muxDataSDK() {\n    return mux as MuxDataSDK;\n  }\n\n  get muxDataSDKOptions() {\n    return {\n      imaAdsLoader: this.adsLoader,\n    };\n  }\n\n  set muxDataKeepSession(val) {\n    // Don't sprout attributes here, this setter is used internally.\n    this.#muxDataKeepSession = Boolean(val);\n  }\n\n  get muxDataKeepSession() {\n    return this.#muxDataKeepSession;\n  }\n\n  // Define autoplay in the most outer layer because mux-video accepts string | boolean\n  // which is not compatible the CustomVideoElement.autoplay boolean only type.\n  /** @ts-ignore */\n  get autoplay(): Autoplay {\n    const attr = this.getAttribute('autoplay');\n\n    if (attr === null) {\n      return false;\n    } else if (attr === '') {\n      return true;\n    } else {\n      return attr as Autoplay;\n    }\n  }\n\n  /** @ts-ignore */\n  set autoplay(val: Autoplay) {\n    const currentVal = this.autoplay;\n    if (val === currentVal) {\n      return;\n    }\n\n    if (val) {\n      this.setAttribute('autoplay', typeof val === 'string' ? val : '');\n    } else {\n      this.removeAttribute('autoplay');\n    }\n  }\n\n  // NOTE: CastableMediaMixin needs to be a subclass of whatever implements the load() method\n  // (i.e. MuxVideoBaseElement), but we're overriding castCustomData to provide mux-specific\n  // values by default, so it needs to be defined here (i.e. in the composed subclass of\n  // CastableMediaMixin). (CJP)\n  #castCustomData: Record<string, any> | undefined;\n\n  get muxCastCustomData() {\n    return {\n      mux: {\n        // Mux Video values\n        playbackId: this.playbackId,\n        minResolution: this.minResolution,\n        maxResolution: this.maxResolution,\n        renditionOrder: this.renditionOrder,\n        customDomain: this.customDomain,\n        /** @TODO Add this.tokens to MuxVideoElement (CJP) */\n        tokens: {\n          drm: this.drmToken,\n        },\n        // Mux Data values\n        envKey: this.envKey,\n        metadata: this.metadata,\n        disableCookies: this.disableCookies,\n        disableTracking: this.disableTracking,\n        beaconCollectionDomain: this.beaconCollectionDomain,\n        // Playback values\n        startTime: this.startTime,\n        // Other values\n        preferCmcd: this.preferCmcd,\n      },\n    } as const;\n  }\n\n  get castCustomData() {\n    return this.#castCustomData ?? this.muxCastCustomData;\n  }\n\n  set castCustomData(val: Record<string, any> | undefined) {\n    this.#castCustomData = val;\n  }\n}\n\nif (!globalThis.customElements.get('mux-video')) {\n  globalThis.customElements.define('mux-video', MuxVideoElement);\n}\n\nexport default MuxVideoElement;\n", "/* eslint @typescript-eslint/no-empty-function: \"off\" */\n\nclass EventTarget {\n  addEventListener() {}\n  removeEventListener() {}\n  dispatchEvent(_event: Event) {\n    return true;\n  }\n}\n\n// @github/template-parts requires DocumentFragment to be available on globalThis for SSR\nif (typeof DocumentFragment === 'undefined') {\n  class DocumentFragment extends EventTarget {}\n  // @ts-ignore\n  globalThis.DocumentFragment = DocumentFragment;\n}\n\nclass HTMLElement extends EventTarget {}\nclass HTMLVideoElement extends EventTarget {}\n\nconst customElements: CustomElementRegistry = {\n  get(_name: string) {\n    return undefined;\n  },\n  define(_name, _constructor, _options) {},\n  getName(_constructor) {\n    return null;\n  },\n  upgrade(_root) {},\n  whenDefined(_name) {\n    return Promise.resolve(HTMLElement as unknown as CustomElementConstructor);\n  },\n};\n\nclass CustomEvent {\n  #detail;\n  get detail() {\n    return this.#detail;\n  }\n  constructor(_typeArg: string, eventInitDict: CustomEventInit = {}) {\n    // super(typeArg, eventInitDict);\n    this.#detail = eventInitDict?.detail;\n  }\n  initCustomEvent() {}\n}\n\nfunction createElement(_tagName: string, _options?: ElementCreationOptions): HTMLElement {\n  return new HTMLElement();\n}\n\nconst globalThisShim = {\n  document: {\n    createElement,\n  },\n  DocumentFragment,\n  customElements,\n  CustomEvent,\n  EventTarget,\n  HTMLElement,\n  HTMLVideoElement,\n};\n\n// const isServer = typeof window === 'undefined' || typeof globalThis.customElements === 'undefined';\n// const GlobalThis = isServer ? globalThisShim : globalThis;\n// const Document = isServer ? globalThisShim.document : globalThis.document;\n//\n// export { GlobalThis as globalThis, Document as document };\nconst isServer = typeof window === 'undefined' || typeof globalThis.customElements === 'undefined';\ntype GlobalThis = typeof globalThis;\nconst internalGlobalThis: GlobalThis = (isServer ? globalThisShim : globalThis) as GlobalThis;\nconst internalDocument: Document = (isServer ? globalThisShim.document : globalThis.document) as Document;\n\nexport { internalGlobalThis as globalThis, internalDocument as document };\n"], "mappings": "i+BAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,gBAAAE,EAAA,YAAAC,IAAA,eAAAC,EAAAJ,GAMA,IAAAK,EAAgB,uCAGhBC,EAA4F,+BAC5FC,EAAmC,4CACnCC,EAAiC,wBCTjC,IAAMC,EAAN,KAAkB,CAChB,kBAAmB,CAAC,CACpB,qBAAsB,CAAC,CACvB,cAAcC,EAAe,CAC3B,MAAO,EACT,CACF,EAGA,GAAI,OAAO,kBAAqB,YAAa,CAC3C,MAAMC,UAAyBF,CAAY,CAAC,CAE5C,WAAW,iBAAmBE,CAChC,CAEA,IAAMC,EAAN,cAA0BH,CAAY,CAAC,EACjCI,EAAN,cAA+BJ,CAAY,CAAC,EAEtCK,EAAwC,CAC5C,IAAIC,EAAe,CAEnB,EACA,OAAOA,EAAOC,EAAcC,EAAU,CAAC,EACvC,QAAQD,EAAc,CACpB,OAAO,IACT,EACA,QAAQE,EAAO,CAAC,EAChB,YAAYH,EAAO,CACjB,OAAO,QAAQ,QAAQH,CAAkD,CAC3E,CACF,EAhCAO,EAkCMC,EAAN,KAAkB,CAKhB,YAAYC,EAAkBC,EAAiC,CAAC,EAAG,CAJnEC,EAAA,KAAAJ,GAMEK,EAAA,KAAKL,EAAUG,GAAA,YAAAA,EAAe,OAChC,CANA,IAAI,QAAS,CACX,OAAOG,EAAA,KAAKN,EACd,CAKA,iBAAkB,CAAC,CACrB,EATEA,EAAA,YAWF,SAASO,EAAcC,EAAkBV,EAAgD,CACvF,OAAO,IAAIL,CACb,CAEA,IAAMgB,EAAiB,CACrB,SAAU,CACR,cAAAF,CACF,EACA,iBACA,eAAAZ,EACA,YAAAM,EACA,YAAAX,EACA,YAAAG,EACA,iBAAAC,CACF,EAOMgB,EAAW,OAAO,QAAW,aAAe,OAAO,WAAW,gBAAmB,YAEjFC,EAAkCD,EAAWD,EAAiB,WAC9DG,EAA8BF,EAAWD,EAAe,SAAW,WAAW,SDzDpF,IAAAI,EAMO,oCAEPC,EAAAC,EAAc,+BArBd,gBAyBO,IAAMC,EAAa,CACxB,GAAG,EAAAC,WACH,GAAG,EAAAC,UACL,EA5BAC,EAAAC,EAAAC,EAAAC,EAAAC,EA+BMC,EAAN,gBAA8B,yBAAmB,uBAAiB,iBAAc,qBAAmB,CAAC,CAAC,CAAE,CAAvG,kCAAAC,EAAA,KAAAL,GACEK,EAAA,KAAAN,EAAsB,IAoFtBM,EAAA,KAAAF,GAlFA,YAAYG,EAAc,CACxB,MAAM,YAAYA,CAAK,EAEnBA,EAAM,OAAS,EAAAC,OAAS,eAC1BC,EAAA,KAAKR,EAAAC,GAAL,WACSK,EAAM,OAAS,EAAAC,OAAS,cACjCC,EAAA,KAAKR,EAAAE,GAAL,UAEJ,CAsBA,IAAI,YAAa,CACf,OAAO,EAAAO,OACT,CAEA,IAAI,mBAAoB,CACtB,MAAO,CACL,aAAc,KAAK,SACrB,CACF,CAEA,IAAI,mBAAmBC,EAAK,CAE1BC,EAAA,KAAKZ,EAAsB,EAAQW,EACrC,CAEA,IAAI,oBAAqB,CACvB,OAAOE,EAAA,KAAKb,EACd,CAKA,IAAI,UAAqB,CACvB,IAAMc,EAAO,KAAK,aAAa,UAAU,EAEzC,OAAIA,IAAS,KACJ,GACEA,IAAS,GACX,GAEAA,CAEX,CAGA,IAAI,SAASH,EAAe,CAC1B,IAAMI,EAAa,KAAK,SACpBJ,IAAQI,IAIRJ,EACF,KAAK,aAAa,WAAY,OAAOA,GAAQ,SAAWA,EAAM,EAAE,EAEhE,KAAK,gBAAgB,UAAU,EAEnC,CAQA,IAAI,mBAAoB,CACtB,MAAO,CACL,IAAK,CAEH,WAAY,KAAK,WACjB,cAAe,KAAK,cACpB,cAAe,KAAK,cACpB,eAAgB,KAAK,eACrB,aAAc,KAAK,aAEnB,OAAQ,CACN,IAAK,KAAK,QACZ,EAEA,OAAQ,KAAK,OACb,SAAU,KAAK,SACf,eAAgB,KAAK,eACrB,gBAAiB,KAAK,gBACtB,uBAAwB,KAAK,uBAE7B,UAAW,KAAK,UAEhB,WAAY,KAAK,UACnB,CACF,CACF,CAEA,IAAI,gBAAiB,CAjJvB,IAAAK,EAkJI,OAAOA,EAAAH,EAAA,KAAKT,KAAL,KAAAY,EAAwB,KAAK,iBACtC,CAEA,IAAI,eAAeL,EAAsC,CACvDC,EAAA,KAAKR,EAAkBO,EACzB,CACF,EAxHEX,EAAA,YADFC,EAAA,YAaEC,EAAmB,UAAG,CA5CxB,IAAAc,EAAAC,GA6CSD,EAAA,KAAK,KAAL,MAAAA,EAAS,aAEVC,EAAA,KAAK,KAAL,MAAAA,EAAS,yBACX,KAAK,mBAAqB,GAC1B,KAAK,OAAO,EACZ,KAAK,mBAAqB,GAE9B,EAEAd,EAAiB,UAAG,CAtDtB,IAAAa,EAAAC,GAuDSD,EAAA,KAAK,KAAL,MAAAA,EAAS,aAEVC,EAAA,KAAK,KAAL,MAAAA,EAAS,yBACX,KAAK,mBAAqB,GAC1B,KAAK,KAAK,EACV,KAAK,mBAAqB,GAE9B,EAsDAb,EAAA,YAsCGc,EAAW,eAAe,IAAI,WAAW,GAC5CA,EAAW,eAAe,OAAO,YAAab,CAAe,EAG/D,IAAOc,EAAQd", "names": ["index_exports", "__export", "Attributes", "index_default", "__toCommonJS", "import_mux_data_google_ima", "import_base", "import_castable_mixin", "import_media_tracks", "EventTarget", "_event", "DocumentFragment", "HTMLElement", "HTMLVideoElement", "customElements", "_name", "_constructor", "_options", "_root", "_detail", "CustomEvent", "_typeArg", "eventInitDict", "__privateAdd", "__privateSet", "__privateGet", "createElement", "_tagName", "globalThisShim", "isServer", "internalGlobalThis", "internalDocument", "import_mixin", "__reExport", "index_exports", "Attributes", "BaseAttributes", "AdsAttributes", "_muxDataKeepSession", "_MuxVideoElement_instances", "handleAdBreakStart_fn", "handleAdBreakEnd_fn", "_castCustomData", "MuxVideoElement", "__privateAdd", "event", "AdEvents", "__privateMethod", "mux", "val", "__privateSet", "__privateGet", "attr", "currentVal", "_a", "_b", "internalGlobalThis", "index_default"]}