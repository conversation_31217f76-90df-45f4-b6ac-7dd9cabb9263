var Le=Object.getPrototypeOf;var fe=Reflect.get;var ue=g=>{throw TypeError(g)};var X=(g,A,i)=>A.has(g)||ue("Cannot "+i);var e=(g,A,i)=>(X(g,A,"read from private field"),i?i.call(g):A.get(g)),u=(g,A,i)=>A.has(g)?ue("Cannot add the same private member more than once"):A instanceof WeakSet?A.add(g):A.set(g,i),o=(g,A,i,n)=>(X(g,A,"write to private field"),n?n.call(g,i):A.set(g,i),i),E=(g,A,i)=>(X(g,A,"access private method"),i);var Y=(g,A,i)=>fe(Le(g),i,A);var l=class extends Event{},h={AD_REQUEST:"adrequest",AD_RESPONSE:"adresponse",AD_BREAK_START:"adbreakstart",AD_FIRST_QUARTILE:"adfirstquartile",AD_MIDPOINT:"admidpoint",AD_THIRD_QUARTILE:"adthirdquartile",AD_ENDED:"adended",AD_BREAK_END:"adbreakend",AD_ERROR:"aderror",AD_PLAY:"adplay",AD_PLAYING:"adplaying",AD_PAUSE:"adpause",AD_IMPRESSION:"adimpression",AD_CLICK:"adclick",AD_SKIP:"adskip",AD_CLOSE:"adclose",PLAY:"play",PLAYING:"playing",PAUSE:"pause",VOLUME_CHANGE:"volumechange",TIME_UPDATE:"timeupdate",DURATION_CHANGE:"durationchange",WAITING:"waiting"};var N,B,W=class{constructor(A,i){u(this,N);u(this,B);o(this,N,A),o(this,B,i)}isLinear(){return e(this,N).isLinear()}isCustomPlaybackUsed(){return e(this,B).isCustomPlaybackUsed()}};N=new WeakMap,B=new WeakMap;var w,p,D,_,R,c,d,C,x,S,K,b,I,f,k,T,ge,H,V,z,q,G,j,Z,U=class extends EventTarget{constructor(i){var n;super();u(this,T);u(this,w);u(this,p);u(this,D);u(this,_);u(this,R);u(this,c);u(this,d);u(this,C);u(this,x);u(this,S);u(this,K,!1);u(this,b,!1);u(this,I,!1);u(this,f,!1);u(this,k,0);u(this,H,()=>{var i;o(this,I,!0),e(this,f)&&!((i=e(this,d))!=null&&i.isCustomPlaybackUsed())&&(console.warn("Video play prevented during ad break"),e(this,p).pause())});u(this,V,()=>{var i;e(this,f)&&!((i=e(this,d))!=null&&i.isCustomPlaybackUsed())&&e(this,p).currentTime!==e(this,k)&&(console.warn("Seek prevented during ad break"),e(this,p).currentTime=e(this,k),e(this,p).dispatchEvent(new Event("timeupdate")))});u(this,z,()=>{var i;(i=e(this,c))==null||i.contentComplete()});u(this,q,i=>{var n;console.error("Ad error",(n=i.getError())==null?void 0:n.getMessage()),this.dispatchEvent(new l(h.AD_ERROR)),E(this,T,G).call(this)});u(this,j,async i=>{var s,m,r,O,$,ee,te,ie,se,ne,ae,re,de,oe,L,he,J,t,a,v,le;let n=new google.ima.AdsRenderingSettings;o(this,d,i.getAdsManager(e(this,p),n)),(s=e(this,d))==null||s.addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,e(this,q)),(m=e(this,d))==null||m.addEventListener(google.ima.AdEvent.Type.LOADED,()=>{this.dispatchEvent(new l(h.AD_RESPONSE))}),(r=e(this,d))==null||r.addEventListener(google.ima.AdEvent.Type.CONTENT_PAUSE_REQUESTED,y=>{if(o(this,C,y.getAd()),!e(this,C)||!e(this,d)){console.warn("Google IMA ad is undefined");return}o(this,f,!0),o(this,k,e(this,p).currentTime||0),o(this,x,new W(e(this,C),e(this,d))),this.dispatchEvent(new l(h.AD_BREAK_START))}),(O=e(this,d))==null||O.addEventListener(google.ima.AdEvent.Type.CLICK,()=>{this.dispatchEvent(new l(h.AD_CLICK))}),($=e(this,d))==null||$.addEventListener(google.ima.AdEvent.Type.IMPRESSION,()=>{this.dispatchEvent(new l(h.AD_IMPRESSION))}),(ee=e(this,d))==null||ee.addEventListener(google.ima.AdEvent.Type.SKIPPED,()=>{this.dispatchEvent(new l(h.AD_SKIP))}),(te=e(this,d))==null||te.addEventListener(google.ima.AdEvent.Type.USER_CLOSE,()=>{this.dispatchEvent(new l(h.AD_CLOSE))}),(ie=e(this,d))==null||ie.addEventListener(google.ima.AdEvent.Type.FIRST_QUARTILE,()=>{this.dispatchEvent(new l(h.AD_FIRST_QUARTILE))}),(se=e(this,d))==null||se.addEventListener(google.ima.AdEvent.Type.MIDPOINT,()=>{this.dispatchEvent(new l(h.AD_MIDPOINT))}),(ne=e(this,d))==null||ne.addEventListener(google.ima.AdEvent.Type.THIRD_QUARTILE,()=>{this.dispatchEvent(new l(h.AD_THIRD_QUARTILE))}),(ae=e(this,d))==null||ae.addEventListener(google.ima.AdEvent.Type.COMPLETE,y=>{E(this,T,G).call(this,y)}),(re=e(this,d))==null||re.addEventListener(google.ima.AdEvent.Type.SKIPPED,y=>{E(this,T,G).call(this,y)}),(de=e(this,d))==null||de.addEventListener(google.ima.AdEvent.Type.CONTENT_RESUME_REQUESTED,()=>{o(this,f,!1),this.dispatchEvent(new l(h.AD_BREAK_END))}),(oe=e(this,d))==null||oe.addEventListener(google.ima.AdEvent.Type.STARTED,()=>{this.dispatchEvent(new l(h.AD_PLAYING)),this.dispatchEvent(new l(h.PLAYING))}),(L=e(this,d))==null||L.addEventListener(google.ima.AdEvent.Type.PAUSED,()=>{o(this,b,!0),this.dispatchEvent(new l(h.AD_PAUSE)),this.dispatchEvent(new l(h.PAUSE))}),(he=e(this,d))==null||he.addEventListener(google.ima.AdEvent.Type.RESUMED,()=>{o(this,b,!1),this.dispatchEvent(new l(h.AD_PLAY)),this.dispatchEvent(new l(h.PLAY)),this.dispatchEvent(new l(h.AD_PLAYING)),this.dispatchEvent(new l(h.PLAYING))}),(J=e(this,d))==null||J.addEventListener(google.ima.AdEvent.Type.AD_BUFFERING,()=>{this.dispatchEvent(new l(h.WAITING))}),(t=e(this,d))==null||t.addEventListener(google.ima.AdEvent.Type.AD_PROGRESS,y=>{o(this,S,y.getAdData()),this.dispatchEvent(new l(h.TIME_UPDATE))}),(a=e(this,d))==null||a.addEventListener(google.ima.AdEvent.Type.DURATION_CHANGE,()=>{this.dispatchEvent(new l(h.DURATION_CHANGE))}),(v=e(this,d))==null||v.addEventListener(google.ima.AdEvent.Type.VOLUME_CHANGED,()=>{this.dispatchEvent(new l(h.VOLUME_CHANGE))}),(le=e(this,d))==null||le.addEventListener(google.ima.AdEvent.Type.VOLUME_MUTED,()=>{this.dispatchEvent(new l(h.VOLUME_CHANGE))});try{e(this,I)?E(this,T,Z).call(this):e(this,p).addEventListener("play",()=>{o(this,I,!0),E(this,T,Z).call(this)},{once:!0})}catch{E(this,T,G).call(this)}});o(this,w,i.adContainer),o(this,p,i.videoElement),o(this,D,i.originalSize),o(this,I,!e(this,p).paused),e(this,p).addEventListener("play",e(this,H)),e(this,p).addEventListener("seeking",e(this,V)),e(this,p).addEventListener("ended",e(this,z)),o(this,R,new google.ima.AdDisplayContainer(e(this,w),e(this,p))),o(this,c,new google.ima.AdsLoader(e(this,R))),e(this,c).addEventListener(google.ima.AdErrorEvent.Type.AD_ERROR,e(this,q)),e(this,c).addEventListener(google.ima.AdsManagerLoadedEvent.Type.ADS_MANAGER_LOADED,e(this,j)),o(this,_,new ResizeObserver(s=>{for(let m of s){let{width:r,height:O}=m.contentRect;r>0&&O>0&&E(this,T,ge).call(this,r,O)}})),(n=e(this,_))==null||n.observe(e(this,w))}static isSDKAvailable(){return"google"in globalThis&&"ima"in globalThis.google?!0:(console.error("Missing google.ima SDK. Make sure you include it via a script tag."),!1)}destroy(){var i,n,s,m,r;e(this,p).removeEventListener("play",e(this,H)),e(this,p).removeEventListener("seeking",e(this,V)),e(this,p).removeEventListener("ended",e(this,z)),(i=e(this,_))==null||i.disconnect(),o(this,_,void 0),(n=e(this,d))==null||n.stop(),(s=e(this,d))==null||s.destroy(),(m=e(this,R))==null||m.destroy(),(r=e(this,c))==null||r.destroy()}unload(){var i;(i=e(this,d))==null||i.stop(),setTimeout(()=>{var n;(n=e(this,d))==null||n.destroy()},0)}get adsLoader(){return e(this,c)}get ad(){return e(this,x)}get adBreak(){return e(this,f)}get paused(){return e(this,b)}get duration(){var i,n,s,m;return(m=(s=(i=e(this,S))==null?void 0:i.duration)!=null?s:(n=e(this,C))==null?void 0:n.getDuration())!=null?m:NaN}get currentTime(){var i,n;return(n=(i=e(this,S))==null?void 0:i.currentTime)!=null?n:0}get volume(){var i,n;return(n=(i=e(this,d))==null?void 0:i.getVolume())!=null?n:1}set volume(i){var n;(n=e(this,d))==null||n.setVolume(i)}play(){var i;return(i=e(this,d))==null||i.resume(),Promise.resolve()}pause(){var i;(i=e(this,d))==null||i.pause()}initializeAdDisplayContainer(){var i;e(this,K)||(o(this,K,!0),(i=e(this,R))==null||i.initialize())}requestAds(i){var s;e(this,d)&&e(this,d).destroy(),e(this,c)&&e(this,c).contentComplete();let n=new google.ima.AdsRequest;n.adTagUrl=i,(s=e(this,c))==null||s.requestAds(n),this.dispatchEvent(new l(h.AD_REQUEST))}};w=new WeakMap,p=new WeakMap,D=new WeakMap,_=new WeakMap,R=new WeakMap,c=new WeakMap,d=new WeakMap,C=new WeakMap,x=new WeakMap,S=new WeakMap,K=new WeakMap,b=new WeakMap,I=new WeakMap,f=new WeakMap,k=new WeakMap,T=new WeakSet,ge=function(i,n){var s;o(this,D,{...e(this,D),width:i,height:n}),(s=e(this,d))==null||s.resize(e(this,D).width,e(this,D).height)},H=new WeakMap,V=new WeakMap,z=new WeakMap,q=new WeakMap,G=function(i){this.dispatchEvent(new l(h.AD_ENDED))},j=new WeakMap,Z=function(){var i,n;o(this,f,!0),e(this,p).pause(),(i=e(this,d))==null||i.init(e(this,D).width,e(this,D).height),(n=e(this,d))==null||n.start()};var P={AD_TAG_URL:"ad-tag-url",ALLOW_AD_BLOCKER:"allow-ad-blocker"};function Ue(g){var i,n,s,m,r,Ee,Ae,F,pe,me,Q,ve,ce,M,Te,De;let L=class L extends g{constructor(){super(...arguments);u(this,r);u(this,i,!1);u(this,n);u(this,s);u(this,m)}static get observedAttributes(){return[...super.observedAttributes,"src",P.AD_TAG_URL]}static get Events(){var t;return[...new Set([...(t=super.Events)!=null?t:[],...Object.values(h)])]}connectedCallback(){if(super.connectedCallback(),!U.isSDKAvailable()){console.error("Missing google.ima SDK. Make sure you include it via a script tag."),this.allowAdBlocker||E(this,r,ve).call(this);return}if(!e(this,s)){o(this,s,new U({adContainer:e(this,r,Q),videoElement:this.nativeEl,originalSize:this.getBoundingClientRect()}));for(let t of Object.values(h))e(this,s).addEventListener(t,this)}}attributeChangedCallback(t,a,v){super.attributeChangedCallback(t,a,v),t==="src"&&v!==a&&(o(this,n,void 0),o(this,m,void 0),o(this,i,!1)),t===P.AD_TAG_URL&&E(this,r,F).call(this)}handleEvent(t){var a;if(t instanceof l){E(this,r,ce).call(this,t);return}(a=e(this,s))!=null&&a.adBreak||(t.type==="loadedmetadata"?E(this,r,Ee).call(this):t.type==="play"&&E(this,r,Ae).call(this),super.handleEvent(t))}play(){var t;return!U.isSDKAvailable()&&!this.allowAdBlocker?Promise.reject(new Error("Playback failed: Ad experience not available")):(t=e(this,s))!=null&&t.adBreak?e(this,s).play():super.play()}pause(){var t,a;(t=e(this,s))!=null&&t.adBreak&&((a=e(this,s))==null||a.pause()),super.pause()}get ad(){var t;return(t=e(this,s))==null?void 0:t.ad}get adsLoader(){var t;return e(this,s)||console.warn("adsLoader not available yet"),(t=e(this,s))==null?void 0:t.adsLoader}get adTagUrl(){var t;return(t=this.getAttribute(P.AD_TAG_URL))!=null?t:void 0}set adTagUrl(t){t!=this.adTagUrl&&(t==null?this.removeAttribute(P.AD_TAG_URL):this.setAttribute(P.AD_TAG_URL,t))}get allowAdBlocker(){return this.hasAttribute(P.ALLOW_AD_BLOCKER)}set allowAdBlocker(t){this.toggleAttribute(P.ALLOW_AD_BLOCKER,!!t)}get paused(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.paused)!=null?v:!1:super.paused}get duration(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.duration)!=null?v:0:super.duration}get currentTime(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.currentTime)!=null?v:0:super.currentTime}set currentTime(t){var a;(a=e(this,s))!=null&&a.adBreak||(super.currentTime=t)}get volume(){var t,a,v;return(t=e(this,s))!=null&&t.adBreak?(v=(a=e(this,s))==null?void 0:a.volume)!=null?v:0:super.volume}set volume(t){var a;(a=e(this,s))!=null&&a.adBreak&&e(this,s)&&(e(this,s).volume=t),super.volume=t}get muted(){var t,a;return(t=e(this,s))!=null&&t.adBreak?!((a=e(this,s))!=null&&a.volume):super.muted}set muted(t){var a;(a=e(this,s))!=null&&a.adBreak&&e(this,s)&&(e(this,s).volume=t?0:this.volume),super.muted=t}get readyState(){var t;return(t=e(this,s))!=null&&t.adBreak?4:super.readyState}async requestPictureInPicture(){var t;if((t=e(this,s))!=null&&t.adBreak)throw new Error("Cannot use PiP while ads are playing!");return super.requestPictureInPicture()}};i=new WeakMap,n=new WeakMap,s=new WeakMap,m=new WeakMap,r=new WeakSet,Ee=function(){o(this,i,!0),E(this,r,F).call(this)},Ae=function(){var t;E(this,r,F).call(this),(t=e(this,s))==null||t.initializeAdDisplayContainer()},F=function(){this.adTagUrl?E(this,r,pe).call(this):E(this,r,me).call(this)},pe=async function(){var t;!this.adTagUrl||!this.isConnected||e(this,i)&&this.adTagUrl!==e(this,n)&&(o(this,n,this.adTagUrl),(t=e(this,s))==null||t.requestAds(this.adTagUrl))},me=function(){var t;(t=e(this,s))==null||t.unload(),o(this,n,void 0)},Q=function(){var t;return(t=this.shadowRoot)==null?void 0:t.getElementById("ad-container")},ve=function(){var t,a;(t=this.shadowRoot)!=null&&t.querySelector("#ima-unavailable-message")||(a=e(this,r,Q))==null||a.insertAdjacentHTML("afterend",`
          <div id="ima-unavailable-message">
            <h4>Ad experience unavailable.</h4>
            <span>This may be due to a missing SDK, network issue, or ad blocker.</span>
          </div>
        `)},ce=function(t){if(t.type===h.AD_BREAK_START){E(this,r,Te).call(this),E(this,r,M).call(this,h.DURATION_CHANGE),E(this,r,M).call(this,t.type);return}if(t.type===h.AD_BREAK_END){E(this,r,De).call(this),E(this,r,M).call(this,h.DURATION_CHANGE),E(this,r,M).call(this,t.type);return}E(this,r,M).call(this,t.type)},M=function(t){this.dispatchEvent(new l(t,{composed:!0}))},Te=function(){var t,a;(t=e(this,r,Q))==null||t.classList.toggle("ad-break",!0),(a=this.ad)!=null&&a.isLinear()&&(Y(L.prototype,this,"pause").call(this),o(this,m,{currentTime:Y(L.prototype,this,"currentTime")}))},De=function(){var t,a;(t=e(this,r,Q))==null||t.classList.toggle("ad-break",!1),(a=e(this,m))!=null&&a.currentTime&&(this.currentTime=e(this,m).currentTime),o(this,m,void 0),setTimeout(()=>{if(!Y(L.prototype,this,"ended"))try{this.play()}catch{}},100)},L.getTemplateHTML=t=>g.getTemplateHTML(t)+`
          <style>
            :host {
              position: relative;
            }

            #ad-container {
              position: absolute;
              top: 0px;
              left: 0px;
              bottom: 0px;
              right: 0px;
              z-index: -1;
              width: 100%;
              height: 100%;
            }

            #ad-container.ad-break {
              z-index: 0;
            }

            #ima-unavailable-message {
              position: absolute;
              inset: 0;
              z-index: 10;
              background: rgba(0, 0, 0, 0.75);
              color: white;
              font-size: 0.9em;
              text-align: center;
              line-height: 1.4;
              align-items: center;
              align-content: center;
              cursor: not-allowed;
            }

            #ima-unavailable-message h4 {
              font-size: 1rem;
              margin: 0;
            }
          </style>
          <div id="ad-container"></div>
        `;let A=L;return A}export{l as AdEvent,Ue as AdsVideoMixin,P as Attributes,h as Events};
//# sourceMappingURL=index.mjs.map
