"use strict";var L=Object.create;var b=Object.defineProperty;var F=Object.getOwnPropertyDescriptor;var G=Object.getOwnPropertyNames;var O=Object.getPrototypeOf,N=Object.prototype.hasOwnProperty;var k=e=>{throw TypeError(e)};var P=(e,s)=>{for(var t in s)b(e,t,{get:s[t],enumerable:!0})},x=(e,s,t,i)=>{if(s&&typeof s=="object"||typeof s=="function")for(let l of G(s))!N.call(e,l)&&l!==t&&b(e,l,{get:()=>s[l],enumerable:!(i=F(s,l))||i.enumerable});return e},n=(e,s,t)=>(x(e,s,"default"),t&&x(t,s,"default")),U=(e,s,t)=>(t=e!=null?L(O(e)):{},x(s||!e||!e.__esModule?b(t,"default",{value:e,enumerable:!0}):t,e)),V=e=>x(b({},"__esModule",{value:!0}),e);var E=(e,s,t)=>s.has(e)||k("Cannot "+t);var d=(e,s,t)=>(E(e,s,"read from private field"),t?t.call(e):s.get(e)),r=(e,s,t)=>s.has(e)?k("Cannot add the same private member more than once"):s instanceof WeakSet?s.add(e):s.set(e,t),p=(e,s,t,i)=>(E(e,s,"write to private field"),i?i.call(e,t):s.set(e,t),t),A=(e,s,t)=>(E(e,s,"access private method"),t);var a={};P(a,{Attributes:()=>z,default:()=>J});module.exports=V(a);var _=U(require("@mux/mux-data-google-ima")),D=require("@mux/mux-video/base"),M=require("castable-video/castable-mixin.js"),R=require("media-tracks");var u=class{addEventListener(){}removeEventListener(){}dispatchEvent(s){return!0}};if(typeof DocumentFragment=="undefined"){class e extends u{}globalThis.DocumentFragment=e}var c=class extends u{},C=class extends u{},j={get(e){},define(e,s,t){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(c)}},h,T=class{constructor(s,t={}){r(this,h);p(this,h,t==null?void 0:t.detail)}get detail(){return d(this,h)}initCustomEvent(){}};h=new WeakMap;function q(e,s){return new c}var K={document:{createElement:q},DocumentFragment,customElements:j,CustomEvent:T,EventTarget:u,HTMLElement:c,HTMLVideoElement:C},S=typeof window=="undefined"||typeof globalThis.customElements=="undefined",v=S?K:globalThis,W=S?K.document:globalThis.document;var o=require("@mux/mux-video/ads/mixin");n(a,require("@mux/mux-video/base"),module.exports);var z={...D.Attributes,...o.Attributes},f,m,B,w,g,y=class extends(0,M.CastableMediaMixin)((0,R.MediaTracksMixin)((0,o.AdsVideoMixin)(D.MuxVideoBaseElement))){constructor(){super(...arguments);r(this,m);r(this,f,!1);r(this,g)}handleEvent(t){super.handleEvent(t),t.type===o.Events.AD_BREAK_START?A(this,m,B).call(this):t.type===o.Events.AD_BREAK_END&&A(this,m,w).call(this)}get muxDataSDK(){return _.default}get muxDataSDKOptions(){return{imaAdsLoader:this.adsLoader}}set muxDataKeepSession(t){p(this,f,!!t)}get muxDataKeepSession(){return d(this,f)}get autoplay(){let t=this.getAttribute("autoplay");return t===null?!1:t===""?!0:t}set autoplay(t){let i=this.autoplay;t!==i&&(t?this.setAttribute("autoplay",typeof t=="string"?t:""):this.removeAttribute("autoplay"))}get muxCastCustomData(){return{mux:{playbackId:this.playbackId,minResolution:this.minResolution,maxResolution:this.maxResolution,renditionOrder:this.renditionOrder,customDomain:this.customDomain,tokens:{drm:this.drmToken},envKey:this.envKey,metadata:this.metadata,disableCookies:this.disableCookies,disableTracking:this.disableTracking,beaconCollectionDomain:this.beaconCollectionDomain,startTime:this.startTime,preferCmcd:this.preferCmcd}}}get castCustomData(){var t;return(t=d(this,g))!=null?t:this.muxCastCustomData}set castCustomData(t){p(this,g,t)}};f=new WeakMap,m=new WeakSet,B=function(){var t,i;(t=this.ad)!=null&&t.isLinear()&&(i=this.ad)!=null&&i.isCustomPlaybackUsed()&&(this.muxDataKeepSession=!0,this.unload(),this.muxDataKeepSession=!1)},w=function(){var t,i;(t=this.ad)!=null&&t.isLinear()&&(i=this.ad)!=null&&i.isCustomPlaybackUsed()&&(this.muxDataKeepSession=!0,this.load(),this.muxDataKeepSession=!1)},g=new WeakMap;v.customElements.get("mux-video")||v.customElements.define("mux-video",y);var J=y;
//# sourceMappingURL=index.cjs.js.map
