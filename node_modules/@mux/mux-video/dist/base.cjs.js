"use strict";var S=Object.defineProperty;var Y=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var G=Object.prototype.hasOwnProperty;var v=n=>{throw TypeError(n)};var V=(n,a)=>{for(var t in a)S(n,t,{get:a[t],enumerable:!0})},F=(n,a,t,i)=>{if(a&&typeof a=="object"||typeof a=="function")for(let s of B(a))!G.call(n,s)&&s!==t&&S(n,s,{get:()=>a[s],enumerable:!(i=Y(a,s))||i.enumerable});return n};var U=n=>F(S({},"__esModule",{value:!0}),n);var L=(n,a,t)=>a.has(n)||v("Cannot "+t);var o=(n,a,t)=>(L(n,a,"read from private field"),t?t.call(n):a.get(n)),d=(n,a,t)=>a.has(n)?v("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(n):a.set(n,t),u=(n,a,t,i)=>(L(n,a,"write to private field"),i?i.call(n,t):a.set(n,t),t),I=(n,a,t)=>(L(n,a,"access private method"),t);var $={};V($,{Attributes:()=>e,Events:()=>f.Events,MediaError:()=>r.MediaError,MuxVideoBaseElement:()=>k,generatePlayerInitTime:()=>r.generatePlayerInitTime,playerSoftwareName:()=>P,playerSoftwareVersion:()=>C});module.exports=U($);var r=require("@mux/playback-core");var W=()=>{try{return"0.26.1"}catch{}return"UNKNOWN"},w=W(),x=()=>w;var f=require("custom-media-element");var K=`
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" part="logo" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2" viewBox="0 0 1600 500"><g fill="#fff"><path d="M994.287 93.486c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m0-93.486c-34.509 0-62.484 27.976-62.484 62.486v187.511c0 68.943-56.09 125.033-125.032 125.033s-125.03-56.09-125.03-125.033V62.486C681.741 27.976 653.765 0 619.256 0s-62.484 27.976-62.484 62.486v187.511C556.772 387.85 668.921 500 806.771 500c137.851 0 250.001-112.15 250.001-250.003V62.486c0-34.51-27.976-62.486-62.485-62.486M1537.51 468.511c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m-275.883-218.509-143.33 143.329c-24.402 24.402-24.402 63.966 0 88.368 24.402 24.402 63.967 24.402 88.369 0l143.33-143.329 143.328 143.329c24.402 24.4 63.967 24.402 88.369 0 24.403-24.402 24.403-63.966.001-88.368l-143.33-143.329.001-.004 143.329-143.329c24.402-24.402 24.402-63.965 0-88.367s-63.967-24.402-88.369 0L1349.996 161.63 1206.667 18.302c-24.402-24.401-63.967-24.402-88.369 0s-24.402 63.965 0 88.367l143.329 143.329v.004ZM437.511 468.521c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31M461.426 4.759C438.078-4.913 411.2.432 393.33 18.303L249.999 161.632 106.669 18.303C88.798.432 61.922-4.913 38.573 4.759 15.224 14.43-.001 37.214-.001 62.488v375.026c0 34.51 27.977 62.486 62.487 62.486 34.51 0 62.486-27.976 62.486-62.486V213.341l80.843 80.844c24.404 24.402 63.965 24.402 88.369 0l80.843-80.844v224.173c0 34.51 27.976 62.486 62.486 62.486s62.486-27.976 62.486-62.486V62.488c0-25.274-15.224-48.058-38.573-57.729" style="fill-rule:nonzero"/></g></svg>`;var e={BEACON_COLLECTION_DOMAIN:"beacon-collection-domain",CUSTOM_DOMAIN:"custom-domain",DEBUG:"debug",DISABLE_TRACKING:"disable-tracking",DISABLE_COOKIES:"disable-cookies",DRM_TOKEN:"drm-token",PLAYBACK_TOKEN:"playback-token",ENV_KEY:"env-key",MAX_RESOLUTION:"max-resolution",MIN_RESOLUTION:"min-resolution",RENDITION_ORDER:"rendition-order",PROGRAM_START_TIME:"program-start-time",PROGRAM_END_TIME:"program-end-time",ASSET_START_TIME:"asset-start-time",ASSET_END_TIME:"asset-end-time",METADATA_URL:"metadata-url",PLAYBACK_ID:"playback-id",PLAYER_SOFTWARE_NAME:"player-software-name",PLAYER_SOFTWARE_VERSION:"player-software-version",PLAYER_INIT_TIME:"player-init-time",PREFER_CMCD:"prefer-cmcd",PREFER_PLAYBACK:"prefer-playback",START_TIME:"start-time",STREAM_TYPE:"stream-type",TARGET_LIVE_WINDOW:"target-live-window",LIVE_EDGE_OFFSET:"live-edge-offset",TYPE:"type",LOGO:"logo"},H=Object.values(e),C=x(),P="mux-video",h,g,T,m,p,_,O,M,R,c,b,y,k=class extends f.CustomVideoElement{constructor(){super();d(this,b);d(this,h);d(this,g);d(this,T);d(this,m,{});d(this,p,{});d(this,_);d(this,O);d(this,M);d(this,R);d(this,c,"");u(this,T,(0,r.generatePlayerInitTime)()),this.nativeEl.addEventListener("muxmetadata",t=>{var l;let i=(0,r.getMetadata)(this.nativeEl),s=(l=this.metadata)!=null?l:{};this.metadata={...i,...s},(i==null?void 0:i["com.mux.video.branding"])==="mux-free-plan"&&(u(this,c,"default"),this.updateLogo())})}static get NAME(){return P}static get VERSION(){return C}static get observedAttributes(){var t;return[...H,...(t=f.CustomVideoElement.observedAttributes)!=null?t:[]]}static getLogoHTML(t){return!t||t==="false"?"":t==="default"?K:`<img part="logo" src="${t}" />`}static getTemplateHTML(t={}){var i;return`
      ${f.CustomVideoElement.getTemplateHTML(t)}
      <style>
        :host {
          position: relative;
        }
        slot[name="logo"] {
          display: flex;
          justify-content: end;
          position: absolute;
          top: 1rem;
          right: 1rem;
          opacity: 0;
          transition: opacity 0.25s ease-in-out;
          z-index: 1;
        }
        slot[name="logo"]:has([part="logo"]) {
          opacity: 1;
        }
        slot[name="logo"] [part="logo"] {
          width: 5rem;
          pointer-events: none;
          user-select: none;
        }
      </style>
      <slot name="logo">
        ${this.getLogoHTML((i=t[e.LOGO])!=null?i:"")}
      </slot>
    `}get preferCmcd(){var t;return(t=this.getAttribute(e.PREFER_CMCD))!=null?t:void 0}set preferCmcd(t){t!==this.preferCmcd&&(t?r.CmcdTypeValues.includes(t)?this.setAttribute(e.PREFER_CMCD,t):console.warn(`Invalid value for preferCmcd. Must be one of ${r.CmcdTypeValues.join()}`):this.removeAttribute(e.PREFER_CMCD))}get playerInitTime(){return this.hasAttribute(e.PLAYER_INIT_TIME)?+this.getAttribute(e.PLAYER_INIT_TIME):o(this,T)}set playerInitTime(t){t!=this.playerInitTime&&(t==null?this.removeAttribute(e.PLAYER_INIT_TIME):this.setAttribute(e.PLAYER_INIT_TIME,`${+t}`))}get playerSoftwareName(){var t;return(t=o(this,M))!=null?t:P}set playerSoftwareName(t){u(this,M,t)}get playerSoftwareVersion(){var t;return(t=o(this,O))!=null?t:C}set playerSoftwareVersion(t){u(this,O,t)}get _hls(){var t;return(t=o(this,h))==null?void 0:t.engine}get mux(){var t;return(t=this.nativeEl)==null?void 0:t.mux}get error(){var t;return(t=(0,r.getError)(this.nativeEl))!=null?t:null}get errorTranslator(){return o(this,R)}set errorTranslator(t){u(this,R,t)}get src(){return this.getAttribute("src")}set src(t){t!==this.src&&(t==null?this.removeAttribute("src"):this.setAttribute("src",t))}get type(){var t;return(t=this.getAttribute(e.TYPE))!=null?t:void 0}set type(t){t!==this.type&&(t?this.setAttribute(e.TYPE,t):this.removeAttribute(e.TYPE))}get preload(){let t=this.getAttribute("preload");return t===""?"auto":["none","metadata","auto"].includes(t)?t:super.preload}set preload(t){t!=this.getAttribute("preload")&&(["","none","metadata","auto"].includes(t)?this.setAttribute("preload",t):this.removeAttribute("preload"))}get debug(){return this.getAttribute(e.DEBUG)!=null}set debug(t){t!==this.debug&&(t?this.setAttribute(e.DEBUG,""):this.removeAttribute(e.DEBUG))}get disableTracking(){return this.hasAttribute(e.DISABLE_TRACKING)}set disableTracking(t){t!==this.disableTracking&&this.toggleAttribute(e.DISABLE_TRACKING,!!t)}get disableCookies(){return this.hasAttribute(e.DISABLE_COOKIES)}set disableCookies(t){t!==this.disableCookies&&(t?this.setAttribute(e.DISABLE_COOKIES,""):this.removeAttribute(e.DISABLE_COOKIES))}get startTime(){let t=this.getAttribute(e.START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set startTime(t){t!==this.startTime&&(t==null?this.removeAttribute(e.START_TIME):this.setAttribute(e.START_TIME,`${t}`))}get playbackId(){var t;return this.hasAttribute(e.PLAYBACK_ID)?this.getAttribute(e.PLAYBACK_ID):(t=(0,r.toPlaybackIdFromSrc)(this.src))!=null?t:void 0}set playbackId(t){t!==this.playbackId&&(t?this.setAttribute(e.PLAYBACK_ID,t):this.removeAttribute(e.PLAYBACK_ID))}get maxResolution(){var t;return(t=this.getAttribute(e.MAX_RESOLUTION))!=null?t:void 0}set maxResolution(t){t!==this.maxResolution&&(t?this.setAttribute(e.MAX_RESOLUTION,t):this.removeAttribute(e.MAX_RESOLUTION))}get minResolution(){var t;return(t=this.getAttribute(e.MIN_RESOLUTION))!=null?t:void 0}set minResolution(t){t!==this.minResolution&&(t?this.setAttribute(e.MIN_RESOLUTION,t):this.removeAttribute(e.MIN_RESOLUTION))}get renditionOrder(){var t;return(t=this.getAttribute(e.RENDITION_ORDER))!=null?t:void 0}set renditionOrder(t){t!==this.renditionOrder&&(t?this.setAttribute(e.RENDITION_ORDER,t):this.removeAttribute(e.RENDITION_ORDER))}get programStartTime(){let t=this.getAttribute(e.PROGRAM_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programStartTime(t){t==null?this.removeAttribute(e.PROGRAM_START_TIME):this.setAttribute(e.PROGRAM_START_TIME,`${t}`)}get programEndTime(){let t=this.getAttribute(e.PROGRAM_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programEndTime(t){t==null?this.removeAttribute(e.PROGRAM_END_TIME):this.setAttribute(e.PROGRAM_END_TIME,`${t}`)}get assetStartTime(){let t=this.getAttribute(e.ASSET_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetStartTime(t){t==null?this.removeAttribute(e.ASSET_START_TIME):this.setAttribute(e.ASSET_START_TIME,`${t}`)}get assetEndTime(){let t=this.getAttribute(e.ASSET_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetEndTime(t){t==null?this.removeAttribute(e.ASSET_END_TIME):this.setAttribute(e.ASSET_END_TIME,`${t}`)}get customDomain(){var t;return(t=this.getAttribute(e.CUSTOM_DOMAIN))!=null?t:void 0}set customDomain(t){t!==this.customDomain&&(t?this.setAttribute(e.CUSTOM_DOMAIN,t):this.removeAttribute(e.CUSTOM_DOMAIN))}get drmToken(){var t;return(t=this.getAttribute(e.DRM_TOKEN))!=null?t:void 0}set drmToken(t){t!==this.drmToken&&(t?this.setAttribute(e.DRM_TOKEN,t):this.removeAttribute(e.DRM_TOKEN))}get playbackToken(){var t,i,s,l;if(this.hasAttribute(e.PLAYBACK_TOKEN))return(t=this.getAttribute(e.PLAYBACK_TOKEN))!=null?t:void 0;if(this.hasAttribute(e.PLAYBACK_ID)){let[,A]=(0,r.toPlaybackIdParts)((i=this.playbackId)!=null?i:"");return(s=new URLSearchParams(A).get("token"))!=null?s:void 0}if(this.src)return(l=new URLSearchParams(this.src).get("token"))!=null?l:void 0}set playbackToken(t){t!==this.playbackToken&&(t?this.setAttribute(e.PLAYBACK_TOKEN,t):this.removeAttribute(e.PLAYBACK_TOKEN))}get tokens(){let t=this.getAttribute(e.PLAYBACK_TOKEN),i=this.getAttribute(e.DRM_TOKEN);return{...o(this,p),...t!=null?{playback:t}:{},...i!=null?{drm:i}:{}}}set tokens(t){u(this,p,t!=null?t:{})}get ended(){return(0,r.getEnded)(this.nativeEl,this._hls)}get envKey(){var t;return(t=this.getAttribute(e.ENV_KEY))!=null?t:void 0}set envKey(t){t!==this.envKey&&(t?this.setAttribute(e.ENV_KEY,t):this.removeAttribute(e.ENV_KEY))}get beaconCollectionDomain(){var t;return(t=this.getAttribute(e.BEACON_COLLECTION_DOMAIN))!=null?t:void 0}set beaconCollectionDomain(t){t!==this.beaconCollectionDomain&&(t?this.setAttribute(e.BEACON_COLLECTION_DOMAIN,t):this.removeAttribute(e.BEACON_COLLECTION_DOMAIN))}get streamType(){var t;return(t=this.getAttribute(e.STREAM_TYPE))!=null?t:(0,r.getStreamType)(this.nativeEl)}set streamType(t){t!==this.streamType&&(t?this.setAttribute(e.STREAM_TYPE,t):this.removeAttribute(e.STREAM_TYPE))}get targetLiveWindow(){return this.hasAttribute(e.TARGET_LIVE_WINDOW)?+this.getAttribute(e.TARGET_LIVE_WINDOW):(0,r.getTargetLiveWindow)(this.nativeEl)}set targetLiveWindow(t){t!=this.targetLiveWindow&&(t==null?this.removeAttribute(e.TARGET_LIVE_WINDOW):this.setAttribute(e.TARGET_LIVE_WINDOW,`${+t}`))}get liveEdgeStart(){var t,i;if(this.hasAttribute(e.LIVE_EDGE_OFFSET)){let{liveEdgeOffset:s}=this,l=(t=this.nativeEl.seekable.end(0))!=null?t:0,A=(i=this.nativeEl.seekable.start(0))!=null?i:0;return Math.max(A,l-s)}return(0,r.getLiveEdgeStart)(this.nativeEl)}get liveEdgeOffset(){if(this.hasAttribute(e.LIVE_EDGE_OFFSET))return+this.getAttribute(e.LIVE_EDGE_OFFSET)}set liveEdgeOffset(t){t!=this.liveEdgeOffset&&(t==null?this.removeAttribute(e.LIVE_EDGE_OFFSET):this.setAttribute(e.LIVE_EDGE_OFFSET,`${+t}`))}get seekable(){return(0,r.getSeekable)(this.nativeEl)}async addCuePoints(t){return(0,r.addCuePoints)(this.nativeEl,t)}get activeCuePoint(){return(0,r.getActiveCuePoint)(this.nativeEl)}get cuePoints(){return(0,r.getCuePoints)(this.nativeEl)}async addChapters(t){return(0,r.addChapters)(this.nativeEl,t)}get activeChapter(){return(0,r.getActiveChapter)(this.nativeEl)}get chapters(){return(0,r.getChapters)(this.nativeEl)}getStartDate(){return(0,r.getStartDate)(this.nativeEl,this._hls)}get currentPdt(){return(0,r.getCurrentPdt)(this.nativeEl,this._hls)}get preferPlayback(){let t=this.getAttribute(e.PREFER_PLAYBACK);if(t===r.PlaybackTypes.MSE||t===r.PlaybackTypes.NATIVE)return t}set preferPlayback(t){t!==this.preferPlayback&&(t===r.PlaybackTypes.MSE||t===r.PlaybackTypes.NATIVE?this.setAttribute(e.PREFER_PLAYBACK,t):this.removeAttribute(e.PREFER_PLAYBACK))}get metadata(){return{...this.getAttributeNames().filter(i=>i.startsWith("metadata-")&&![e.METADATA_URL].includes(i)).reduce((i,s)=>{let l=this.getAttribute(s);return l!=null&&(i[s.replace(/^metadata-/,"").replace(/-/g,"_")]=l),i},{}),...o(this,m)}}set metadata(t){u(this,m,t!=null?t:{}),this.mux&&this.mux.emit("hb",o(this,m))}get _hlsConfig(){return o(this,_)}set _hlsConfig(t){u(this,_,t)}get logo(){var t;return(t=this.getAttribute(e.LOGO))!=null?t:o(this,c)}set logo(t){t?this.setAttribute(e.LOGO,t):this.removeAttribute(e.LOGO)}load(){u(this,h,(0,r.initialize)(this,this.nativeEl,o(this,h)))}unload(){(0,r.teardown)(this.nativeEl,o(this,h),this),u(this,h,void 0)}attributeChangedCallback(t,i,s){var A,D;switch(f.CustomVideoElement.observedAttributes.includes(t)&&!["src","autoplay","preload"].includes(t)&&super.attributeChangedCallback(t,i,s),t){case e.PLAYER_SOFTWARE_NAME:this.playerSoftwareName=s!=null?s:void 0;break;case e.PLAYER_SOFTWARE_VERSION:this.playerSoftwareVersion=s!=null?s:void 0;break;case"src":{let E=!!i,N=!!s;!E&&N?I(this,b,y).call(this):E&&!N?this.unload():E&&N&&(this.unload(),I(this,b,y).call(this));break}case"autoplay":if(s===i)break;(A=o(this,h))==null||A.setAutoplay(this.autoplay);break;case"preload":if(s===i)break;(D=o(this,h))==null||D.setPreload(s);break;case e.PLAYBACK_ID:this.src=(0,r.toMuxVideoURL)(this);break;case e.DEBUG:{let E=this.debug;this.mux&&console.info("Cannot toggle debug mode of mux data after initialization. Make sure you set all metadata to override before setting the src."),this._hls&&(this._hls.config.debug=E);break}case e.METADATA_URL:s&&fetch(s).then(E=>E.json()).then(E=>this.metadata=E).catch(()=>console.error(`Unable to load or parse metadata JSON from metadata-url ${s}!`));break;case e.STREAM_TYPE:(s==null||s!==i)&&this.dispatchEvent(new CustomEvent("streamtypechange",{composed:!0,bubbles:!0}));break;case e.TARGET_LIVE_WINDOW:(s==null||s!==i)&&this.dispatchEvent(new CustomEvent("targetlivewindowchange",{composed:!0,bubbles:!0,detail:this.targetLiveWindow}));break;case e.LOGO:(s==null||s!==i)&&this.updateLogo();break}}updateLogo(){if(!this.shadowRoot)return;let t=this.shadowRoot.querySelector('slot[name="logo"]');if(!t)return;let i=this.constructor.getLogoHTML(o(this,c)||this.logo);t.innerHTML=i}connectedCallback(){var t;(t=super.connectedCallback)==null||t.call(this),this.nativeEl&&this.src&&!o(this,h)&&I(this,b,y).call(this)}disconnectedCallback(){this.unload()}handleEvent(t){t.target===this.nativeEl&&this.dispatchEvent(new CustomEvent(t.type,{composed:!0,detail:t.detail}))}};h=new WeakMap,g=new WeakMap,T=new WeakMap,m=new WeakMap,p=new WeakMap,_=new WeakMap,O=new WeakMap,M=new WeakMap,R=new WeakMap,c=new WeakMap,b=new WeakSet,y=async function(){o(this,g)||(await u(this,g,Promise.resolve()),u(this,g,null),this.load())};
//# sourceMappingURL=base.cjs.js.map
