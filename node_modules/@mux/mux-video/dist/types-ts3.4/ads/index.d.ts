/// <reference path="../../../../../node_modules/mux-embed/dist/types/mux-embed.d.ts" preserve="true" />
/// <reference types="google_interactive_media_ads_types" preserve="true" />
import { MuxDataSDK } from '@mux/playback-core';
import { Autoplay } from '@mux/playback-core';
import { MuxVideoBaseElement, EventMap as BaseEventMap } from '@mux/mux-video/base';
import { Expand, EventMap as AdEventMap } from '@mux/mux-video/ads/mixin';
export * from '@mux/mux-video/base';
export type EventMap = Expand<BaseEventMap & AdEventMap>;
export declare const Attributes: {
    readonly AD_TAG_URL: "ad-tag-url";
    readonly ALLOW_AD_BLOCKER: "allow-ad-blocker";
    readonly BEACON_COLLECTION_DOMAIN: "beacon-collection-domain";
    readonly CUSTOM_DOMAIN: "custom-domain";
    readonly DEBUG: "debug";
    readonly DISABLE_TRACKING: "disable-tracking";
    readonly DISABLE_COOKIES: "disable-cookies";
    readonly DRM_TOKEN: "drm-token";
    readonly PLAYBACK_TOKEN: "playback-token";
    readonly ENV_KEY: "env-key";
    readonly MAX_RESOLUTION: "max-resolution";
    readonly MIN_RESOLUTION: "min-resolution";
    readonly RENDITION_ORDER: "rendition-order";
    readonly PROGRAM_START_TIME: "program-start-time";
    readonly PROGRAM_END_TIME: "program-end-time";
    readonly ASSET_START_TIME: "asset-start-time";
    readonly ASSET_END_TIME: "asset-end-time";
    readonly METADATA_URL: "metadata-url";
    readonly PLAYBACK_ID: "playback-id";
    readonly PLAYER_SOFTWARE_NAME: "player-software-name";
    readonly PLAYER_SOFTWARE_VERSION: "player-software-version";
    readonly PLAYER_INIT_TIME: "player-init-time";
    readonly PREFER_CMCD: "prefer-cmcd";
    readonly PREFER_PLAYBACK: "prefer-playback";
    readonly START_TIME: "start-time";
    readonly STREAM_TYPE: "stream-type";
    readonly TARGET_LIVE_WINDOW: "target-live-window";
    readonly LIVE_EDGE_OFFSET: "live-edge-offset";
    readonly TYPE: "type";
    readonly LOGO: "logo";
};
declare const MuxVideoElement_base: import("media-tracks").WithMediaTracks<import("@mux/mux-video/ads/mixin").Constructor<import("@mux/mux-video/ads/mixin").IAdsVideo> & typeof MuxVideoBaseElement>;
declare class MuxVideoElement extends MuxVideoElement_base {
    private "MuxVideoElement.#private";
    handleEvent(event: Event): void;
    readonly muxDataSDK: MuxDataSDK;
    readonly muxDataSDKOptions: {
        imaAdsLoader: google.ima.AdsLoader | undefined;
    };
    muxDataKeepSession: boolean;
    /*@ts-ignore
    @ts-ignore */
    autoplay: Autoplay;
    readonly muxCastCustomData: {
        readonly mux: {
            readonly playbackId: string | undefined;
            readonly minResolution: import("@mux/playback-core").MinResolutionValue | undefined;
            readonly maxResolution: import("@mux/playback-core").MaxResolutionValue | undefined;
            readonly renditionOrder: "desc" | undefined;
            readonly customDomain: string | undefined;
            /** @TODO Add this.tokens to MuxVideoElement (CJP) */
            readonly tokens: {
                readonly drm: string | undefined;
            };
            readonly envKey: string | undefined;
            readonly metadata: Readonly<Partial<import("mux-embed").Metadata>> | undefined;
            readonly disableCookies: boolean;
            readonly disableTracking: boolean;
            readonly beaconCollectionDomain: string | undefined;
            readonly startTime: number | undefined;
            readonly preferCmcd: import("@mux/playback-core").ValueOf<import("@mux/playback-core").CmcdTypes> | undefined;
        };
    };
    castCustomData: Record<string, any> | undefined;
}
export default MuxVideoElement;
