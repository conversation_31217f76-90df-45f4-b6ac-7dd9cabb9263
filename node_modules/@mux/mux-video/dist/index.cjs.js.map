{"version": 3, "sources": ["../src/index.ts", "../src/polyfills/index.ts"], "sourcesContent": ["import { globalThis } from './polyfills';\nimport { Autoplay } from '@mux/playback-core';\nimport { MuxVideoBaseElement } from '@mux/mux-video/base';\nimport { CastableMediaMixin } from 'castable-video/castable-mixin.js';\nimport { MediaTracksMixin } from 'media-tracks';\n\nexport * from '@mux/mux-video/base';\n\n// castable-video should be mixed in last so that it can override load().\nclass MuxVideoElement extends CastableMediaMixin(MediaTracksMixin(MuxVideoBaseElement)) {\n  // Define autoplay in the most outer layer because mux-video accepts string | boolean\n  // which is not compatible the CustomVideoElement.autoplay boolean only type.\n  /** @ts-ignore */\n  get autoplay(): Autoplay {\n    const attr = this.getAttribute('autoplay');\n\n    if (attr === null) {\n      return false;\n    } else if (attr === '') {\n      return true;\n    } else {\n      return attr as Autoplay;\n    }\n  }\n\n  /** @ts-ignore */\n  set autoplay(val: Autoplay) {\n    const currentVal = this.autoplay;\n    if (val === currentVal) {\n      return;\n    }\n\n    if (val) {\n      this.setAttribute('autoplay', typeof val === 'string' ? val : '');\n    } else {\n      this.removeAttribute('autoplay');\n    }\n  }\n\n  // NOTE: CastableMediaMixin needs to be a subclass of whatever implements the load() method\n  // (i.e. MuxVideoBaseElement), but we're overriding castCustomData to provide mux-specific\n  // values by default, so it needs to be defined here (i.e. in the composed subclass of\n  // CastableMediaMixin). (CJP)\n  #castCustomData: Record<string, any> | undefined;\n\n  get muxCastCustomData() {\n    return {\n      mux: {\n        // Mux Video values\n        playbackId: this.playbackId,\n        minResolution: this.minResolution,\n        maxResolution: this.maxResolution,\n        renditionOrder: this.renditionOrder,\n        customDomain: this.customDomain,\n        /** @TODO Add this.tokens to MuxVideoElement (CJP) */\n        tokens: {\n          drm: this.drmToken,\n        },\n        // Mux Data values\n        envKey: this.envKey,\n        metadata: this.metadata,\n        disableCookies: this.disableCookies,\n        disableTracking: this.disableTracking,\n        beaconCollectionDomain: this.beaconCollectionDomain,\n        // Playback values\n        startTime: this.startTime,\n        // Other values\n        preferCmcd: this.preferCmcd,\n      },\n    } as const;\n  }\n\n  get castCustomData() {\n    return this.#castCustomData ?? this.muxCastCustomData;\n  }\n\n  set castCustomData(val: Record<string, any> | undefined) {\n    this.#castCustomData = val;\n  }\n}\n\ntype MuxVideoElementType = typeof MuxVideoElement;\ndeclare global {\n  var MuxVideoElement: MuxVideoElementType; // eslint-disable-line\n}\n\nif (!globalThis.customElements.get('mux-video')) {\n  globalThis.customElements.define('mux-video', MuxVideoElement);\n  globalThis.MuxVideoElement = MuxVideoElement;\n}\n\nexport default MuxVideoElement;\n", "/* eslint @typescript-eslint/no-empty-function: \"off\" */\n\nclass EventTarget {\n  addEventListener() {}\n  removeEventListener() {}\n  dispatchEvent(_event: Event) {\n    return true;\n  }\n}\n\n// @github/template-parts requires DocumentFragment to be available on globalThis for SSR\nif (typeof DocumentFragment === 'undefined') {\n  class DocumentFragment extends EventTarget {}\n  // @ts-ignore\n  globalThis.DocumentFragment = DocumentFragment;\n}\n\nclass HTMLElement extends EventTarget {}\nclass HTMLVideoElement extends EventTarget {}\n\nconst customElements: CustomElementRegistry = {\n  get(_name: string) {\n    return undefined;\n  },\n  define(_name, _constructor, _options) {},\n  getName(_constructor) {\n    return null;\n  },\n  upgrade(_root) {},\n  whenDefined(_name) {\n    return Promise.resolve(HTMLElement as unknown as CustomElementConstructor);\n  },\n};\n\nclass CustomEvent {\n  #detail;\n  get detail() {\n    return this.#detail;\n  }\n  constructor(_typeArg: string, eventInitDict: CustomEventInit = {}) {\n    // super(typeArg, eventInitDict);\n    this.#detail = eventInitDict?.detail;\n  }\n  initCustomEvent() {}\n}\n\nfunction createElement(_tagName: string, _options?: ElementCreationOptions): HTMLElement {\n  return new HTMLElement();\n}\n\nconst globalThisShim = {\n  document: {\n    createElement,\n  },\n  DocumentFragment,\n  customElements,\n  CustomEvent,\n  EventTarget,\n  HTMLElement,\n  HTMLVideoElement,\n};\n\n// const isServer = typeof window === 'undefined' || typeof globalThis.customElements === 'undefined';\n// const GlobalThis = isServer ? globalThisShim : globalThis;\n// const Document = isServer ? globalThisShim.document : globalThis.document;\n//\n// export { GlobalThis as globalThis, Document as document };\nconst isServer = typeof window === 'undefined' || typeof globalThis.customElements === 'undefined';\ntype GlobalThis = typeof globalThis;\nconst internalGlobalThis: GlobalThis = (isServer ? globalThisShim : globalThis) as GlobalThis;\nconst internalDocument: Document = (isServer ? globalThisShim.document : globalThis.document) as Document;\n\nexport { internalGlobalThis as globalThis, internalDocument as document };\n"], "mappings": "syBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,IAAA,eAAAC,EAAAH,GCEA,IAAMI,EAAN,KAAkB,CAChB,kBAAmB,CAAC,CACpB,qBAAsB,CAAC,CACvB,cAAcC,EAAe,CAC3B,MAAO,EACT,CACF,EAGA,GAAI,OAAO,kBAAqB,YAAa,CAC3C,MAAMC,UAAyBF,CAAY,CAAC,CAE5C,WAAW,iBAAmBE,CAChC,CAEA,IAAMC,EAAN,cAA0BH,CAAY,CAAC,EACjCI,EAAN,cAA+BJ,CAAY,CAAC,EAEtCK,EAAwC,CAC5C,IAAIC,EAAe,CAEnB,EACA,OAAOA,EAAOC,EAAcC,EAAU,CAAC,EACvC,QAAQD,EAAc,CACpB,OAAO,IACT,EACA,QAAQE,EAAO,CAAC,EAChB,YAAYH,EAAO,CACjB,OAAO,QAAQ,QAAQH,CAAkD,CAC3E,CACF,EAhCAO,EAkCMC,EAAN,KAAkB,CAKhB,YAAYC,EAAkBC,EAAiC,CAAC,EAAG,CAJnEC,EAAA,KAAAJ,GAMEK,EAAA,KAAKL,EAAUG,GAAA,YAAAA,EAAe,OAChC,CANA,IAAI,QAAS,CACX,OAAOG,EAAA,KAAKN,EACd,CAKA,iBAAkB,CAAC,CACrB,EATEA,EAAA,YAWF,SAASO,EAAcC,EAAkBV,EAAgD,CACvF,OAAO,IAAIL,CACb,CAEA,IAAMgB,EAAiB,CACrB,SAAU,CACR,cAAAF,CACF,EACA,iBACA,eAAAZ,EACA,YAAAM,EACA,YAAAX,EACA,YAAAG,EACA,iBAAAC,CACF,EAOMgB,EAAW,OAAO,QAAW,aAAe,OAAO,WAAW,gBAAmB,YAEjFC,EAAkCD,EAAWD,EAAiB,WAC9DG,EAA8BF,EAAWD,EAAe,SAAW,WAAW,SDpEpF,IAAAI,EAAoC,+BACpCC,EAAmC,4CACnCC,EAAiC,wBAEjCC,EAAAC,EAAc,+BANd,gBAAA,IAAAC,EASMC,EAAN,gBAA8B,yBAAmB,oBAAiB,qBAAmB,CAAC,CAAE,CAAxF,kCAkCEC,EAAA,KAAAF,GA9BA,IAAI,UAAqB,CACvB,IAAMG,EAAO,KAAK,aAAa,UAAU,EAEzC,OAAIA,IAAS,KACJ,GACEA,IAAS,GACX,GAEAA,CAEX,CAGA,IAAI,SAASC,EAAe,CAC1B,IAAMC,EAAa,KAAK,SACpBD,IAAQC,IAIRD,EACF,KAAK,aAAa,WAAY,OAAOA,GAAQ,SAAWA,EAAM,EAAE,EAEhE,KAAK,gBAAgB,UAAU,EAEnC,CAQA,IAAI,mBAAoB,CACtB,MAAO,CACL,IAAK,CAEH,WAAY,KAAK,WACjB,cAAe,KAAK,cACpB,cAAe,KAAK,cACpB,eAAgB,KAAK,eACrB,aAAc,KAAK,aAEnB,OAAQ,CACN,IAAK,KAAK,QACZ,EAEA,OAAQ,KAAK,OACb,SAAU,KAAK,SACf,eAAgB,KAAK,eACrB,gBAAiB,KAAK,gBACtB,uBAAwB,KAAK,uBAE7B,UAAW,KAAK,UAEhB,WAAY,KAAK,UACnB,CACF,CACF,CAEA,IAAI,gBAAiB,CAxEvB,IAAAE,EAyEI,OAAOA,EAAAC,EAAA,KAAKP,KAAL,KAAAM,EAAwB,KAAK,iBACtC,CAEA,IAAI,eAAeF,EAAsC,CACvDI,EAAA,KAAKR,EAAkBI,EACzB,CACF,EApCEJ,EAAA,YA2CGS,EAAW,eAAe,IAAI,WAAW,IAC5CA,EAAW,eAAe,OAAO,YAAaR,CAAe,EAC7DQ,EAAW,gBAAkBR,GAG/B,IAAOS,EAAQT", "names": ["index_exports", "__export", "index_default", "__toCommonJS", "EventTarget", "_event", "DocumentFragment", "HTMLElement", "HTMLVideoElement", "customElements", "_name", "_constructor", "_options", "_root", "_detail", "CustomEvent", "_typeArg", "eventInitDict", "__privateAdd", "__privateSet", "__privateGet", "createElement", "_tagName", "globalThisShim", "isServer", "internalGlobalThis", "internalDocument", "import_base", "import_castable_mixin", "import_media_tracks", "__reExport", "index_exports", "_castCustomData", "MuxVideoElement", "__privateAdd", "attr", "val", "currentVal", "_a", "__privateGet", "__privateSet", "internalGlobalThis", "index_default"]}