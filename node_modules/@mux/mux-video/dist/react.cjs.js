"use strict";"use client";var x=Object.create;var y=Object.defineProperty;var F=Object.getOwnPropertyDescriptor;var j=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,Z=Object.prototype.hasOwnProperty;var $=(t,n)=>{for(var o in n)y(t,o,{get:n[o],enumerable:!0})},A=(t,n,o,s)=>{if(n&&typeof n=="object"||typeof n=="function")for(let c of j(n))!Z.call(t,c)&&c!==o&&y(t,c,{get:()=>n[c],enumerable:!(s=F(n,c))||s.enumerable});return t};var k=(t,n,o)=>(o=t!=null?x(D(t)):{},A(n||!t||!t.__esModule?y(o,"default",{value:t,enumerable:!0}):o,t)),q=t=>A(y({},"__esModule",{value:!0}),t);var Q={};$(Q,{default:()=>J});module.exports=q(Q);var S=k(require("@mux/mux-video")),O=k(require("react"));var z=new Set(["style","children","ref","key","suppressContentEditableWarning","suppressHydrationWarning","dangerouslySetInnerHTML"]),B={className:"class",htmlFor:"for"};function G(t){return t.toLowerCase()}function H(t){if(typeof t=="boolean")return t?"":void 0;if(typeof t!="function"&&!(typeof t=="object"&&t!==null))return t}function I({react:t,tagName:n,elementClass:o,events:s,displayName:c,toAttributeName:R=G,toAttributeValue:W=H}){let g=Number.parseInt(t.version)>=19,w=t.forwardRef((T,l)=>{var E,v,L,P,M;let u=t.useRef(null),b=t.useRef(new Map),m={},h={},f={},p={};for(let[e,r]of Object.entries(T)){if(z.has(e)){f[e]=r;continue}let i=R((E=B[e])!=null?E:e);if(e in o.prototype&&!(e in((L=(v=globalThis.HTMLElement)==null?void 0:v.prototype)!=null?L:{}))&&!((P=o.observedAttributes)!=null&&P.some(d=>d===i))){p[e]=r;continue}if(e.startsWith("on")){m[e]=r;continue}let a=W(r);if(i&&a!=null&&(h[i]=String(a),g||(f[i]=a)),i&&g){let d=H(r);a!==d?f[i]=a:f[i]=r}}if(typeof window!="undefined"){for(let e in m){let r=m[e],i=e.endsWith("Capture"),a=((M=s==null?void 0:s[e])!=null?M:e.slice(2).toLowerCase()).slice(0,i?-7:void 0);t.useLayoutEffect(()=>{let d=u==null?void 0:u.current;if(!(!d||typeof r!="function"))return d.addEventListener(a,r,i),()=>{d.removeEventListener(a,r,i)}},[u==null?void 0:u.current,r])}t.useLayoutEffect(()=>{if(u.current===null)return;let e=new Map;for(let r in p)_(u.current,r,p[r]),b.current.delete(r),e.set(r,p[r]);for(let[r,i]of b.current)_(u.current,r,void 0);b.current=e})}if(typeof window=="undefined"&&(o!=null&&o.getTemplateHTML)&&(o!=null&&o.shadowRootOptions)){let{mode:e,delegatesFocus:r}=o.shadowRootOptions,i=t.createElement("template",{shadowrootmode:e,shadowrootdelegatesfocus:r,dangerouslySetInnerHTML:{__html:o.getTemplateHTML(h,T)}});f.children=[i,f.children]}return t.createElement(n,{...f,ref:t.useCallback(e=>{u.current=e,typeof l=="function"?l(e):l!==null&&(l.current=e)},[l])})});return w.displayName=c!=null?c:o.name,w}function _(t,n,o){var s,c;t[n]=o,o==null&&n in((c=(s=globalThis.HTMLElement)==null?void 0:s.prototype)!=null?c:{})&&t.removeAttribute(n)}var J=I({react:O.default,tagName:"mux-video",elementClass:S.default,toAttributeName:K}),N={autoPlay:"autoplay",controlsList:"controlslist",crossOrigin:"crossorigin",playsInline:"playsinline",disablePictureInPicture:"disablepictureinpicture",disableRemotePlayback:"disableremoteplayback"};function K(t){return N[t]?N[t]:t.replace(/([A-Z])/g,"-$1").toLowerCase()}
/*! Bundled license information:

ce-la-react/dist/ce-la-react.js:
  (**
   * @license
   * Copyright 2018 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *
   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.
   *)
*/
