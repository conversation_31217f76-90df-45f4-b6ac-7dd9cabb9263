"use strict";var Zn=Object.defineProperty;var Rm=Object.getOwnPropertyDescriptor;var xm=Object.getOwnPropertyNames;var Dm=Object.prototype.hasOwnProperty;var Cd=t=>{throw TypeError(t)};var Om=(t,e)=>{for(var i in e)Zn(t,i,{get:e[i],enumerable:!0})},Nm=(t,e,i,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of xm(e))!Dm.call(t,r)&&r!==i&&Zn(t,r,{get:()=>e[r],enumerable:!(a=Rm(e,r))||a.enumerable});return t};var Pm=t=>Nm(Zn({},"__esModule",{value:!0}),t);var zn=(t,e,i)=>e.has(t)||Cd("Cannot "+i);var U=(t,e,i)=>(zn(t,e,"read from private field"),i?i.call(t):e.get(t)),he=(t,e,i)=>e.has(t)?Cd("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),Te=(t,e,i,a)=>(zn(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),X=(t,e,i)=>(zn(t,e,"access private method"),i);var kv={};Om(kv,{MediaError:()=>b.MediaError,default:()=>yv,generatePlayerInitTime:()=>w.generatePlayerInitTime,getVideoAttribute:()=>Ht,playerSoftwareName:()=>od,playerSoftwareVersion:()=>rd});module.exports=Pm(kv);var gi=class{addEventListener(){}removeEventListener(){}dispatchEvent(e){return!0}};if(typeof DocumentFragment=="undefined"){class t extends gi{}globalThis.DocumentFragment=t}var ka=class extends gi{},Qn=class extends gi{},Um={get(t){},define(t,e,i){},getName(t){return null},upgrade(t){},whenDefined(t){return Promise.resolve(ka)}},Sa,Xn=class{constructor(e,i={}){he(this,Sa);Te(this,Sa,i==null?void 0:i.detail)}get detail(){return U(this,Sa)}initCustomEvent(){}};Sa=new WeakMap;function Hm(t,e){return new ka}var Ld={document:{createElement:Hm},DocumentFragment,customElements:Um,CustomEvent:Xn,EventTarget:gi,HTMLElement:ka,HTMLVideoElement:Qn},wd=typeof window=="undefined"||typeof globalThis.customElements=="undefined",ye=wd?Ld:globalThis,_i=wd?Ld.document:globalThis.document;var p={MEDIA_PLAY_REQUEST:"mediaplayrequest",MEDIA_PAUSE_REQUEST:"mediapauserequest",MEDIA_MUTE_REQUEST:"mediamuterequest",MEDIA_UNMUTE_REQUEST:"mediaunmuterequest",MEDIA_VOLUME_REQUEST:"mediavolumerequest",MEDIA_SEEK_REQUEST:"mediaseekrequest",MEDIA_AIRPLAY_REQUEST:"mediaairplayrequest",MEDIA_ENTER_FULLSCREEN_REQUEST:"mediaenterfullscreenrequest",MEDIA_EXIT_FULLSCREEN_REQUEST:"mediaexitfullscreenrequest",MEDIA_PREVIEW_REQUEST:"mediapreviewrequest",MEDIA_ENTER_PIP_REQUEST:"mediaenterpiprequest",MEDIA_EXIT_PIP_REQUEST:"mediaexitpiprequest",MEDIA_ENTER_CAST_REQUEST:"mediaentercastrequest",MEDIA_EXIT_CAST_REQUEST:"mediaexitcastrequest",MEDIA_SHOW_TEXT_TRACKS_REQUEST:"mediashowtexttracksrequest",MEDIA_HIDE_TEXT_TRACKS_REQUEST:"mediahidetexttracksrequest",MEDIA_SHOW_SUBTITLES_REQUEST:"mediashowsubtitlesrequest",MEDIA_DISABLE_SUBTITLES_REQUEST:"mediadisablesubtitlesrequest",MEDIA_TOGGLE_SUBTITLES_REQUEST:"mediatogglesubtitlesrequest",MEDIA_PLAYBACK_RATE_REQUEST:"mediaplaybackraterequest",MEDIA_RENDITION_REQUEST:"mediarenditionrequest",MEDIA_AUDIO_TRACK_REQUEST:"mediaaudiotrackrequest",MEDIA_SEEK_TO_LIVE_REQUEST:"mediaseektoliverequest",REGISTER_MEDIA_STATE_RECEIVER:"registermediastatereceiver",UNREGISTER_MEDIA_STATE_RECEIVER:"unregistermediastatereceiver"},L={MEDIA_CHROME_ATTRIBUTES:"mediachromeattributes",MEDIA_CONTROLLER:"mediacontroller"},Jn={MEDIA_AIRPLAY_UNAVAILABLE:"mediaAirplayUnavailable",MEDIA_AUDIO_TRACK_ENABLED:"mediaAudioTrackEnabled",MEDIA_AUDIO_TRACK_LIST:"mediaAudioTrackList",MEDIA_AUDIO_TRACK_UNAVAILABLE:"mediaAudioTrackUnavailable",MEDIA_BUFFERED:"mediaBuffered",MEDIA_CAST_UNAVAILABLE:"mediaCastUnavailable",MEDIA_CHAPTERS_CUES:"mediaChaptersCues",MEDIA_CURRENT_TIME:"mediaCurrentTime",MEDIA_DURATION:"mediaDuration",MEDIA_ENDED:"mediaEnded",MEDIA_ERROR:"mediaError",MEDIA_ERROR_CODE:"mediaErrorCode",MEDIA_ERROR_MESSAGE:"mediaErrorMessage",MEDIA_FULLSCREEN_UNAVAILABLE:"mediaFullscreenUnavailable",MEDIA_HAS_PLAYED:"mediaHasPlayed",MEDIA_HEIGHT:"mediaHeight",MEDIA_IS_AIRPLAYING:"mediaIsAirplaying",MEDIA_IS_CASTING:"mediaIsCasting",MEDIA_IS_FULLSCREEN:"mediaIsFullscreen",MEDIA_IS_PIP:"mediaIsPip",MEDIA_LOADING:"mediaLoading",MEDIA_MUTED:"mediaMuted",MEDIA_PAUSED:"mediaPaused",MEDIA_PIP_UNAVAILABLE:"mediaPipUnavailable",MEDIA_PLAYBACK_RATE:"mediaPlaybackRate",MEDIA_PREVIEW_CHAPTER:"mediaPreviewChapter",MEDIA_PREVIEW_COORDS:"mediaPreviewCoords",MEDIA_PREVIEW_IMAGE:"mediaPreviewImage",MEDIA_PREVIEW_TIME:"mediaPreviewTime",MEDIA_RENDITION_LIST:"mediaRenditionList",MEDIA_RENDITION_SELECTED:"mediaRenditionSelected",MEDIA_RENDITION_UNAVAILABLE:"mediaRenditionUnavailable",MEDIA_SEEKABLE:"mediaSeekable",MEDIA_STREAM_TYPE:"mediaStreamType",MEDIA_SUBTITLES_LIST:"mediaSubtitlesList",MEDIA_SUBTITLES_SHOWING:"mediaSubtitlesShowing",MEDIA_TARGET_LIVE_WINDOW:"mediaTargetLiveWindow",MEDIA_TIME_IS_LIVE:"mediaTimeIsLive",MEDIA_VOLUME:"mediaVolume",MEDIA_VOLUME_LEVEL:"mediaVolumeLevel",MEDIA_VOLUME_UNAVAILABLE:"mediaVolumeUnavailable",MEDIA_WIDTH:"mediaWidth"},Rd=Object.entries(Jn),n=Rd.reduce((t,[e,i])=>(t[e]=i.toLowerCase(),t),{}),Bm={USER_INACTIVE_CHANGE:"userinactivechange",BREAKPOINTS_CHANGE:"breakpointchange",BREAKPOINTS_COMPUTED:"breakpointscomputed"},Ce=Rd.reduce((t,[e,i])=>(t[e]=i.toLowerCase(),t),{...Bm}),Lv=Object.entries(Ce).reduce((t,[e,i])=>{let a=n[e];return a&&(t[i]=a),t},{userinactivechange:"userinactive"}),xd=Object.entries(n).reduce((t,[e,i])=>{let a=Ce[e];return a&&(t[i]=a),t},{userinactive:"userinactivechange"}),pe={SUBTITLES:"subtitles",CAPTIONS:"captions",DESCRIPTIONS:"descriptions",CHAPTERS:"chapters",METADATA:"metadata"},gt={DISABLED:"disabled",HIDDEN:"hidden",SHOWING:"showing"};var jn={MOUSE:"mouse",PEN:"pen",TOUCH:"touch"},ke={UNAVAILABLE:"unavailable",UNSUPPORTED:"unsupported"},xe={LIVE:"live",ON_DEMAND:"on-demand",UNKNOWN:"unknown"};var Dd={INLINE:"inline",FULLSCREEN:"fullscreen",PICTURE_IN_PICTURE:"picture-in-picture"};function Od(t){return t==null?void 0:t.map(Wm).join(" ")}function Nd(t){return t==null?void 0:t.split(/\s+/).map(Fm)}function Wm(t){if(t){let{id:e,width:i,height:a}=t;return[e,i,a].filter(r=>r!=null).join(":")}}function Fm(t){if(t){let[e,i,a]=t.split(":");return{id:e,width:+i,height:+a}}}function Pd(t){return t==null?void 0:t.map(Vm).join(" ")}function Ud(t){return t==null?void 0:t.split(/\s+/).map(Km)}function Vm(t){if(t){let{id:e,kind:i,language:a,label:r}=t;return[e,i,a,r].filter(o=>o!=null).join(":")}}function Km(t){if(t){let[e,i,a,r]=t.split(":");return{id:e,kind:i,language:a,label:r}}}function Hd(t){return t.replace(/[-_]([a-z])/g,(e,i)=>i.toUpperCase())}function Ai(t){return typeof t=="number"&&!Number.isNaN(t)&&Number.isFinite(t)}function Wr(t){return typeof t!="string"?!1:!isNaN(t)&&!isNaN(parseFloat(t))}var Fr=t=>new Promise(e=>setTimeout(e,t));var Bd=[{singular:"hour",plural:"hours"},{singular:"minute",plural:"minutes"},{singular:"second",plural:"seconds"}],Gm=(t,e)=>{let i=t===1?Bd[e].singular:Bd[e].plural;return`${t} ${i}`},$t=t=>{if(!Ai(t))return"";let e=Math.abs(t),i=e!==t,a=new Date(0,0,0,0,0,e,0);return`${[a.getHours(),a.getMinutes(),a.getSeconds()].map((l,u)=>l&&Gm(l,u)).filter(l=>l).join(", ")}${i?" remaining":""}`};function De(t,e){let i=!1;t<0&&(i=!0,t=0-t),t=t<0?0:t;let a=Math.floor(t%60),r=Math.floor(t/60%60),o=Math.floor(t/3600),s=Math.floor(e/60%60),l=Math.floor(e/3600);return(isNaN(t)||t===1/0)&&(o=r=a="0"),o=o>0||l>0?o+":":"",r=((o||s>=10)&&r<10?"0"+r:r)+":",a=a<10?"0"+a:a,(i?"-":"")+o+r+a}var xv=Object.freeze({length:0,start(t){let e=t>>>0;if(e>=this.length)throw new DOMException(`Failed to execute 'start' on 'TimeRanges': The index provided (${e}) is greater than or equal to the maximum bound (${this.length}).`);return 0},end(t){let e=t>>>0;if(e>=this.length)throw new DOMException(`Failed to execute 'end' on 'TimeRanges': The index provided (${e}) is greater than or equal to the maximum bound (${this.length}).`);return 0}});var $d={"Start airplay":"Start airplay","Stop airplay":"Stop airplay",Audio:"Audio",Captions:"Captions","Enable captions":"Enable captions","Disable captions":"Disable captions","Start casting":"Start casting","Stop casting":"Stop casting","Enter fullscreen mode":"Enter fullscreen mode","Exit fullscreen mode":"Exit fullscreen mode",Mute:"Mute",Unmute:"Unmute","Enter picture in picture mode":"Enter picture in picture mode","Exit picture in picture mode":"Exit picture in picture mode",Play:"Play",Pause:"Pause","Playback rate":"Playback rate","Playback rate {playbackRate}":"Playback rate {playbackRate}",Quality:"Quality","Seek backward":"Seek backward","Seek forward":"Seek forward",Settings:"Settings",Auto:"Auto","audio player":"audio player","video player":"video player",volume:"volume",seek:"seek","closed captions":"closed captions","current playback rate":"current playback rate","playback time":"playback time","media loading":"media loading",settings:"settings","audio tracks":"audio tracks",quality:"quality",play:"play",pause:"pause",mute:"mute",unmute:"unmute",live:"live",Off:"Off","start airplay":"start airplay","stop airplay":"stop airplay","start casting":"start casting","stop casting":"stop casting","enter fullscreen mode":"enter fullscreen mode","exit fullscreen mode":"exit fullscreen mode","enter picture in picture mode":"enter picture in picture mode","exit picture in picture mode":"exit picture in picture mode","seek to live":"seek to live","playing live":"playing live","seek back {seekOffset} seconds":"seek back {seekOffset} seconds","seek forward {seekOffset} seconds":"seek forward {seekOffset} seconds","Network Error":"Network Error","Decode Error":"Decode Error","Source Not Supported":"Source Not Supported","Encryption Error":"Encryption Error","A network error caused the media download to fail.":"A network error caused the media download to fail.","A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format.":"A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format.","An unsupported error occurred. The server or network failed, or your browser does not support this format.":"An unsupported error occurred. The server or network failed, or your browser does not support this format.","The media is encrypted and there are no keys to decrypt it.":"The media is encrypted and there are no keys to decrypt it."};var Wd,es={en:$d},ts=((Wd=globalThis.navigator)==null?void 0:Wd.language)||"en",Fd=t=>{ts=t};var qm=t=>{var e,i,a;let[r]=ts.split("-");return((e=es[ts])==null?void 0:e[t])||((i=es[r])==null?void 0:i[t])||((a=es.en)==null?void 0:a[t])||t},h=(t,e={})=>qm(t).replace(/\{(\w+)\}/g,(i,a)=>a in e?String(e[a]):`{${a}}`);var Vr=class{addEventListener(){}removeEventListener(){}dispatchEvent(){return!0}},Kr=class extends Vr{},Gr=class extends Kr{constructor(){super(...arguments),this.role=null}},is=class{observe(){}unobserve(){}disconnect(){}},Vd={createElement:function(){return new Ia.HTMLElement},createElementNS:function(){return new Ia.HTMLElement},addEventListener(){},removeEventListener(){},dispatchEvent(t){return!1}},Ia={ResizeObserver:is,document:Vd,Node:Kr,Element:Gr,HTMLElement:class extends Gr{constructor(){super(...arguments),this.innerHTML=""}get content(){return new Ia.DocumentFragment}},DocumentFragment:class extends Vr{},customElements:{get:function(){},define:function(){},whenDefined:function(){}},localStorage:{getItem(t){return null},setItem(t,e){},removeItem(t){}},CustomEvent:function(){},getComputedStyle:function(){},navigator:{languages:[],get userAgent(){return""}},matchMedia(t){return{matches:!1,media:t}},DOMParser:class{parseFromString(e,i){return{body:{textContent:e}}}}},Kd=typeof window=="undefined"||typeof window.customElements=="undefined",Gd=Object.keys(Ia).every(t=>t in globalThis),d=Kd&&!Gd?Ia:globalThis,V=Kd&&!Gd?Vd:globalThis.document;var Yd=new WeakMap,as=t=>{let e=Yd.get(t);return e||Yd.set(t,e=new Set),e},qd=new d.ResizeObserver(t=>{for(let e of t)for(let i of as(e.target))i(e)});function st(t,e){as(t).add(e),qd.observe(t)}function lt(t,e){let i=as(t);i.delete(e),i.size||qd.unobserve(t)}function Y(t){let e={};for(let i of t)e[i.name]=i.value;return e}function Z(t){var e;return(e=Yr(t))!=null?e:He(t,"media-controller")}function Yr(t){var e;let{MEDIA_CONTROLLER:i}=L,a=t.getAttribute(i);if(a)return(e=Wt(t))==null?void 0:e.getElementById(a)}var qr=(t,e,i=".value")=>{let a=t.querySelector(i);a&&(a.textContent=e)},Zm=(t,e)=>{let i=`slot[name="${e}"]`,a=t.shadowRoot.querySelector(i);return a?a.children:[]},Zr=(t,e)=>Zm(t,e)[0],de=(t,e)=>!t||!e?!1:t!=null&&t.contains(e)?!0:de(t,e.getRootNode().host),He=(t,e)=>{if(!t)return null;let i=t.closest(e);return i||He(t.getRootNode().host,e)};function Ma(t=document){var e;let i=t==null?void 0:t.activeElement;return i?(e=Ma(i.shadowRoot))!=null?e:i:null}function Wt(t){var e;let i=(e=t==null?void 0:t.getRootNode)==null?void 0:e.call(t);return i instanceof ShadowRoot||i instanceof Document?i:null}function zr(t,{depth:e=3,checkOpacity:i=!0,checkVisibilityCSS:a=!0}={}){if(t.checkVisibility)return t.checkVisibility({checkOpacity:i,checkVisibilityCSS:a});let r=t;for(;r&&e>0;){let o=getComputedStyle(r);if(i&&o.opacity==="0"||a&&o.visibility==="hidden"||o.display==="none")return!1;r=r.parentElement,e--}return!0}function Zd(t,e,i,a){let r=a.x-i.x,o=a.y-i.y,s=r*r+o*o;if(s===0)return 0;let l=((t-i.x)*r+(e-i.y)*o)/s;return Math.max(0,Math.min(1,l))}function W(t,e){let i=zm(t,a=>a===e);return i||rs(t,e)}function zm(t,e){var i,a;let r;for(r of(i=t.querySelectorAll("style:not([media])"))!=null?i:[]){let o;try{o=(a=r.sheet)==null?void 0:a.cssRules}catch{continue}for(let s of o!=null?o:[])if(e(s.selectorText))return s}}function rs(t,e){var i,a;let r=(i=t.querySelectorAll("style:not([media])"))!=null?i:[],o=r==null?void 0:r[r.length-1];return o!=null&&o.sheet?(o==null||o.sheet.insertRule(`${e}{}`,o.sheet.cssRules.length),(a=o.sheet.cssRules)==null?void 0:a[o.sheet.cssRules.length-1]):(console.warn("Media Chrome: No style sheet found on style tag of",t),{style:{setProperty:()=>{},removeProperty:()=>"",getPropertyValue:()=>""}})}function R(t,e,i=Number.NaN){let a=t.getAttribute(e);return a!=null?+a:i}function O(t,e,i){let a=+i;if(i==null||Number.isNaN(a)){t.hasAttribute(e)&&t.removeAttribute(e);return}R(t,e,void 0)!==a&&t.setAttribute(e,`${a}`)}function k(t,e){return t.hasAttribute(e)}function S(t,e,i){if(i==null){t.hasAttribute(e)&&t.removeAttribute(e);return}k(t,e)!=i&&t.toggleAttribute(e,i)}function I(t,e,i=null){var a;return(a=t.getAttribute(e))!=null?a:i}function M(t,e,i){if(i==null){t.hasAttribute(e)&&t.removeAttribute(e);return}let a=`${i}`;I(t,e,void 0)!==a&&t.setAttribute(e,a)}var zd=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},_t=(t,e,i)=>(zd(t,e,"read from private field"),i?i.call(t):e.get(t)),Qm=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Qr=(t,e,i,a)=>(zd(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),fe;function Xm(t){return`
    <style>
      :host {
        display: var(--media-control-display, var(--media-gesture-receiver-display, inline-block));
        box-sizing: border-box;
      }
    </style>
  `}var Ti=class extends d.HTMLElement{constructor(){if(super(),Qm(this,fe,void 0),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}}static get observedAttributes(){return[L.MEDIA_CONTROLLER,n.MEDIA_PAUSED]}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===L.MEDIA_CONTROLLER&&(i&&((o=(r=_t(this,fe))==null?void 0:r.unassociateElement)==null||o.call(r,this),Qr(this,fe,null)),a&&this.isConnected&&(Qr(this,fe,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=_t(this,fe))==null?void 0:l.associateElement)==null||u.call(l,this)))}connectedCallback(){var e,i,a,r;this.tabIndex=-1,this.setAttribute("aria-hidden","true"),Qr(this,fe,Jm(this)),this.getAttribute(L.MEDIA_CONTROLLER)&&((i=(e=_t(this,fe))==null?void 0:e.associateElement)==null||i.call(e,this)),(a=_t(this,fe))==null||a.addEventListener("pointerdown",this),(r=_t(this,fe))==null||r.addEventListener("click",this)}disconnectedCallback(){var e,i,a,r;this.getAttribute(L.MEDIA_CONTROLLER)&&((i=(e=_t(this,fe))==null?void 0:e.unassociateElement)==null||i.call(e,this)),(a=_t(this,fe))==null||a.removeEventListener("pointerdown",this),(r=_t(this,fe))==null||r.removeEventListener("click",this),Qr(this,fe,null)}handleEvent(e){var i;let a=(i=e.composedPath())==null?void 0:i[0];if(["video","media-controller"].includes(a==null?void 0:a.localName)){if(e.type==="pointerdown")this._pointerType=e.pointerType;else if(e.type==="click"){let{clientX:o,clientY:s}=e,{left:l,top:u,width:m,height:_}=this.getBoundingClientRect(),g=o-l,f=s-u;if(g<0||f<0||g>m||f>_||m===0&&_===0)return;let{pointerType:v=this._pointerType}=e;if(this._pointerType=void 0,v===jn.TOUCH){this.handleTap(e);return}else if(v===jn.MOUSE){this.handleMouseClick(e);return}}}}get mediaPaused(){return k(this,n.MEDIA_PAUSED)}set mediaPaused(e){S(this,n.MEDIA_PAUSED,e)}handleTap(e){}handleMouseClick(e){let i=this.mediaPaused?p.MEDIA_PLAY_REQUEST:p.MEDIA_PAUSE_REQUEST;this.dispatchEvent(new d.CustomEvent(i,{composed:!0,bubbles:!0}))}};fe=new WeakMap;Ti.shadowRootOptions={mode:"open"};Ti.getTemplateHTML=Xm;function Jm(t){var e;let i=t.getAttribute(L.MEDIA_CONTROLLER);return i?(e=t.getRootNode())==null?void 0:e.getElementById(i):He(t,"media-controller")}d.customElements.get("media-gesture-receiver")||d.customElements.define("media-gesture-receiver",Ti);var Xr=Ti;var ls=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Ie=(t,e,i)=>(ls(t,e,"read from private field"),i?i.call(t):e.get(t)),Se=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Ft=(t,e,i,a)=>(ls(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Le=(t,e,i)=>(ls(t,e,"access private method"),i),eo,yi,La,ki,Jr,os,Qd,Ca,jr,ns,Xd,ss,Jd,wa,to,io,ds,Si,Ra,y={AUDIO:"audio",AUTOHIDE:"autohide",BREAKPOINTS:"breakpoints",GESTURES_DISABLED:"gesturesdisabled",KEYBOARD_CONTROL:"keyboardcontrol",NO_AUTOHIDE:"noautohide",USER_INACTIVE:"userinactive",AUTOHIDE_OVER_CONTROLS:"autohideovercontrols"};function jm(t){return`
    <style>
      
      :host([${n.MEDIA_IS_FULLSCREEN}]) ::slotted([slot=media]) {
        outline: none;
      }

      :host {
        box-sizing: border-box;
        position: relative;
        display: inline-block;
        line-height: 0;
        background-color: var(--media-background-color, #000);
      }

      :host(:not([${y.AUDIO}])) [part~=layer]:not([part~=media-layer]) {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        flex-flow: column nowrap;
        align-items: start;
        pointer-events: none;
        background: none;
      }

      slot[name=media] {
        display: var(--media-slot-display, contents);
      }

      
      :host([${y.AUDIO}]) slot[name=media] {
        display: var(--media-slot-display, none);
      }

      
      :host([${y.AUDIO}]) [part~=layer][part~=gesture-layer] {
        height: 0;
        display: block;
      }

      
      :host(:not([${y.AUDIO}])[${y.GESTURES_DISABLED}]) ::slotted([slot=gestures-chrome]),
          :host(:not([${y.AUDIO}])[${y.GESTURES_DISABLED}]) media-gesture-receiver[slot=gestures-chrome] {
        display: none;
      }

      
      ::slotted(:not([slot=media]):not([slot=poster]):not(media-loading-indicator):not([role=dialog]):not([hidden])) {
        pointer-events: auto;
      }

      :host(:not([${y.AUDIO}])) *[part~=layer][part~=centered-layer] {
        align-items: center;
        justify-content: center;
      }

      :host(:not([${y.AUDIO}])) ::slotted(media-gesture-receiver[slot=gestures-chrome]),
      :host(:not([${y.AUDIO}])) media-gesture-receiver[slot=gestures-chrome] {
        align-self: stretch;
        flex-grow: 1;
      }

      slot[name=middle-chrome] {
        display: inline;
        flex-grow: 1;
        pointer-events: none;
        background: none;
      }

      
      ::slotted([slot=media]),
      ::slotted([slot=poster]) {
        width: 100%;
        height: 100%;
      }

      
      :host(:not([${y.AUDIO}])) .spacer {
        flex-grow: 1;
      }

      
      :host(:-webkit-full-screen) {
        
        width: 100% !important;
        height: 100% !important;
      }

      
      ::slotted(:not([slot=media]):not([slot=poster]):not([${y.NO_AUTOHIDE}]):not([hidden]):not([role=dialog])) {
        opacity: 1;
        transition: var(--media-control-transition-in, opacity 0.25s);
      }

      
      :host([${y.USER_INACTIVE}]:not([${n.MEDIA_PAUSED}]):not([${n.MEDIA_IS_AIRPLAYING}]):not([${n.MEDIA_IS_CASTING}]):not([${y.AUDIO}])) ::slotted(:not([slot=media]):not([slot=poster]):not([${y.NO_AUTOHIDE}]):not([role=dialog])) {
        opacity: 0;
        transition: var(--media-control-transition-out, opacity 1s);
      }

      :host([${y.USER_INACTIVE}]:not([${y.NO_AUTOHIDE}]):not([${n.MEDIA_PAUSED}]):not([${n.MEDIA_IS_CASTING}]):not([${y.AUDIO}])) ::slotted([slot=media]) {
        cursor: none;
      }

      :host([${y.USER_INACTIVE}][${y.AUTOHIDE_OVER_CONTROLS}]:not([${y.NO_AUTOHIDE}]):not([${n.MEDIA_PAUSED}]):not([${n.MEDIA_IS_CASTING}]):not([${y.AUDIO}])) * {
        --media-cursor: none;
        cursor: none;
      }


      ::slotted(media-control-bar)  {
        align-self: stretch;
      }

      
      :host(:not([${y.AUDIO}])[${n.MEDIA_HAS_PLAYED}]) slot[name=poster] {
        display: none;
      }

      ::slotted([role=dialog]) {
        width: 100%;
        height: 100%;
        align-self: center;
      }

      ::slotted([role=menu]) {
        align-self: end;
      }
    </style>

    <slot name="media" part="layer media-layer"></slot>
    <slot name="poster" part="layer poster-layer"></slot>
    <slot name="gestures-chrome" part="layer gesture-layer">
      <media-gesture-receiver slot="gestures-chrome">
        <template shadowrootmode="${Xr.shadowRootOptions.mode}">
          ${Xr.getTemplateHTML({})}
        </template>
      </media-gesture-receiver>
    </slot>
    <span part="layer vertical-layer">
      <slot name="top-chrome" part="top chrome"></slot>
      <slot name="middle-chrome" part="middle chrome"></slot>
      <slot name="centered-chrome" part="layer centered-layer center centered chrome"></slot>
      
      <slot part="bottom chrome"></slot>
    </span>
    <slot name="dialog" part="layer dialog-layer"></slot>
  `}var eh=Object.values(n),th="sm:384 md:576 lg:768 xl:960";function ih(t){jd(t.target,t.contentRect.width)}function jd(t,e){var i;if(!t.isConnected)return;let a=(i=t.getAttribute(y.BREAKPOINTS))!=null?i:th,r=ah(a),o=rh(r,e),s=!1;if(Object.keys(r).forEach(l=>{if(o.includes(l)){t.hasAttribute(`breakpoint${l}`)||(t.setAttribute(`breakpoint${l}`,""),s=!0);return}t.hasAttribute(`breakpoint${l}`)&&(t.removeAttribute(`breakpoint${l}`),s=!0)}),s){let l=new CustomEvent(Ce.BREAKPOINTS_CHANGE,{detail:o});t.dispatchEvent(l)}t.breakpointsComputed||(t.breakpointsComputed=!0,t.dispatchEvent(new CustomEvent(Ce.BREAKPOINTS_COMPUTED,{bubbles:!0,composed:!0})))}function ah(t){let e=t.split(/\s+/);return Object.fromEntries(e.map(i=>i.split(":")))}function rh(t,e){return Object.keys(t).filter(i=>e>=parseInt(t[i]))}var Vt=class extends d.HTMLElement{constructor(){if(super(),Se(this,os),Se(this,ns),Se(this,ss),Se(this,wa),Se(this,io),Se(this,Si),Se(this,eo,0),Se(this,yi,null),Se(this,La,null),Se(this,ki,void 0),this.breakpointsComputed=!1,Se(this,Jr,new MutationObserver(Le(this,os,Qd).bind(this))),Se(this,Ca,!1),Se(this,jr,i=>{Ie(this,Ca)||(setTimeout(()=>{ih(i),Ft(this,Ca,!1)},0),Ft(this,Ca,!0))}),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let i=Y(this.attributes),a=this.constructor.getTemplateHTML(i);this.shadowRoot.setHTMLUnsafe?this.shadowRoot.setHTMLUnsafe(a):this.shadowRoot.innerHTML=a}let e=this.querySelector(":scope > slot[slot=media]");e&&e.addEventListener("slotchange",()=>{if(!e.assignedElements({flatten:!0}).length){Ie(this,yi)&&this.mediaUnsetCallback(Ie(this,yi));return}this.handleMediaUpdated(this.media)})}static get observedAttributes(){return[y.AUTOHIDE,y.GESTURES_DISABLED].concat(eh).filter(e=>![n.MEDIA_RENDITION_LIST,n.MEDIA_AUDIO_TRACK_LIST,n.MEDIA_CHAPTERS_CUES,n.MEDIA_WIDTH,n.MEDIA_HEIGHT,n.MEDIA_ERROR,n.MEDIA_ERROR_MESSAGE].includes(e))}attributeChangedCallback(e,i,a){e.toLowerCase()==y.AUTOHIDE&&(this.autohide=a)}get media(){let e=this.querySelector(":scope > [slot=media]");return(e==null?void 0:e.nodeName)=="SLOT"&&(e=e.assignedElements({flatten:!0})[0]),e}async handleMediaUpdated(e){e&&(Ft(this,yi,e),e.localName.includes("-")&&await d.customElements.whenDefined(e.localName),this.mediaSetCallback(e))}connectedCallback(){var e;Ie(this,Jr).observe(this,{childList:!0,subtree:!0}),st(this,Ie(this,jr));let a=this.getAttribute(y.AUDIO)!=null?h("audio player"):h("video player");this.setAttribute("role","region"),this.setAttribute("aria-label",a),this.handleMediaUpdated(this.media),this.setAttribute(y.USER_INACTIVE,""),jd(this,this.getBoundingClientRect().width),this.addEventListener("pointerdown",this),this.addEventListener("pointermove",this),this.addEventListener("pointerup",this),this.addEventListener("mouseleave",this),this.addEventListener("keyup",this),(e=d.window)==null||e.addEventListener("mouseup",this)}disconnectedCallback(){var e;Ie(this,Jr).disconnect(),lt(this,Ie(this,jr)),this.media&&this.mediaUnsetCallback(this.media),(e=d.window)==null||e.removeEventListener("mouseup",this)}mediaSetCallback(e){}mediaUnsetCallback(e){Ft(this,yi,null)}handleEvent(e){switch(e.type){case"pointerdown":Ft(this,eo,e.timeStamp);break;case"pointermove":Le(this,ns,Xd).call(this,e);break;case"pointerup":Le(this,ss,Jd).call(this,e);break;case"mouseleave":Le(this,wa,to).call(this);break;case"mouseup":this.removeAttribute(y.KEYBOARD_CONTROL);break;case"keyup":Le(this,Si,Ra).call(this),this.setAttribute(y.KEYBOARD_CONTROL,"");break}}set autohide(e){let i=Number(e);Ft(this,ki,isNaN(i)?0:i)}get autohide(){return(Ie(this,ki)===void 0?2:Ie(this,ki)).toString()}get breakpoints(){return I(this,y.BREAKPOINTS)}set breakpoints(e){M(this,y.BREAKPOINTS,e)}get audio(){return k(this,y.AUDIO)}set audio(e){S(this,y.AUDIO,e)}get gesturesDisabled(){return k(this,y.GESTURES_DISABLED)}set gesturesDisabled(e){S(this,y.GESTURES_DISABLED,e)}get keyboardControl(){return k(this,y.KEYBOARD_CONTROL)}set keyboardControl(e){S(this,y.KEYBOARD_CONTROL,e)}get noAutohide(){return k(this,y.NO_AUTOHIDE)}set noAutohide(e){S(this,y.NO_AUTOHIDE,e)}get autohideOverControls(){return k(this,y.AUTOHIDE_OVER_CONTROLS)}set autohideOverControls(e){S(this,y.AUTOHIDE_OVER_CONTROLS,e)}get userInteractive(){return k(this,y.USER_INACTIVE)}set userInteractive(e){S(this,y.USER_INACTIVE,e)}};eo=new WeakMap;yi=new WeakMap;La=new WeakMap;ki=new WeakMap;Jr=new WeakMap;os=new WeakSet;Qd=function(t){let e=this.media;for(let i of t){if(i.type!=="childList")continue;let a=i.removedNodes;for(let r of a){if(r.slot!="media"||i.target!=this)continue;let o=i.previousSibling&&i.previousSibling.previousElementSibling;if(!o||!e)this.mediaUnsetCallback(r);else{let s=o.slot!=="media";for(;(o=o.previousSibling)!==null;)o.slot=="media"&&(s=!1);s&&this.mediaUnsetCallback(r)}}if(e)for(let r of i.addedNodes)r===e&&this.handleMediaUpdated(e)}};Ca=new WeakMap;jr=new WeakMap;ns=new WeakSet;Xd=function(t){if(t.pointerType!=="mouse"&&t.timeStamp-Ie(this,eo)<250)return;Le(this,io,ds).call(this),clearTimeout(Ie(this,La));let e=this.hasAttribute(y.AUTOHIDE_OVER_CONTROLS);([this,this.media].includes(t.target)||e)&&Le(this,Si,Ra).call(this)};ss=new WeakSet;Jd=function(t){if(t.pointerType==="touch"){let e=!this.hasAttribute(y.USER_INACTIVE);[this,this.media].includes(t.target)&&e?Le(this,wa,to).call(this):Le(this,Si,Ra).call(this)}else t.composedPath().some(e=>["media-play-button","media-fullscreen-button"].includes(e==null?void 0:e.localName))&&Le(this,Si,Ra).call(this)};wa=new WeakSet;to=function(){if(Ie(this,ki)<0||this.hasAttribute(y.USER_INACTIVE))return;this.setAttribute(y.USER_INACTIVE,"");let t=new d.CustomEvent(Ce.USER_INACTIVE_CHANGE,{composed:!0,bubbles:!0,detail:!0});this.dispatchEvent(t)};io=new WeakSet;ds=function(){if(!this.hasAttribute(y.USER_INACTIVE))return;this.removeAttribute(y.USER_INACTIVE);let t=new d.CustomEvent(Ce.USER_INACTIVE_CHANGE,{composed:!0,bubbles:!0,detail:!1});this.dispatchEvent(t)};Si=new WeakSet;Ra=function(){Le(this,io,ds).call(this),clearTimeout(Ie(this,La));let t=parseInt(this.autohide);t<0||Ft(this,La,setTimeout(()=>{Le(this,wa,to).call(this)},t*1e3))};Vt.shadowRootOptions={mode:"open"};Vt.getTemplateHTML=jm;d.customElements.get("media-container")||d.customElements.define("media-container",Vt);var eu=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},ie=(t,e,i)=>(eu(t,e,"read from private field"),i?i.call(t):e.get(t)),xa=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},ao=(t,e,i,a)=>(eu(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Ii,Mi,ro,Kt,dt,At,Tt=class{constructor(e,i,{defaultValue:a}={defaultValue:void 0}){xa(this,dt),xa(this,Ii,void 0),xa(this,Mi,void 0),xa(this,ro,void 0),xa(this,Kt,new Set),ao(this,Ii,e),ao(this,Mi,i),ao(this,ro,new Set(a))}[Symbol.iterator](){return ie(this,dt,At).values()}get length(){return ie(this,dt,At).size}get value(){var e;return(e=[...ie(this,dt,At)].join(" "))!=null?e:""}set value(e){var i;e!==this.value&&(ao(this,Kt,new Set),this.add(...(i=e==null?void 0:e.split(" "))!=null?i:[]))}toString(){return this.value}item(e){return[...ie(this,dt,At)][e]}values(){return ie(this,dt,At).values()}forEach(e,i){ie(this,dt,At).forEach(e,i)}add(...e){var i,a;e.forEach(r=>ie(this,Kt).add(r)),!(this.value===""&&!((i=ie(this,Ii))!=null&&i.hasAttribute(`${ie(this,Mi)}`)))&&((a=ie(this,Ii))==null||a.setAttribute(`${ie(this,Mi)}`,`${this.value}`))}remove(...e){var i;e.forEach(a=>ie(this,Kt).delete(a)),(i=ie(this,Ii))==null||i.setAttribute(`${ie(this,Mi)}`,`${this.value}`)}contains(e){return ie(this,dt,At).has(e)}toggle(e,i){return typeof i!="undefined"?i?(this.add(e),!0):(this.remove(e),!1):this.contains(e)?(this.remove(e),!1):(this.add(e),!0)}replace(e,i){return this.remove(e),this.add(i),e===i}};Ii=new WeakMap;Mi=new WeakMap;ro=new WeakMap;Kt=new WeakMap;dt=new WeakSet;At=function(){return ie(this,Kt).size?ie(this,Kt):ie(this,ro)};var oh=(t="")=>t.split(/\s+/),tu=(t="")=>{let[e,i,a]=t.split(":"),r=a?decodeURIComponent(a):void 0;return{kind:e==="cc"?pe.CAPTIONS:pe.SUBTITLES,language:i,label:r}},Gt=(t="",e={})=>oh(t).map(i=>{let a=tu(i);return{...e,...a}}),us=t=>t?Array.isArray(t)?t.map(e=>typeof e=="string"?tu(e):e):typeof t=="string"?Gt(t):[t]:[],oo=({kind:t,label:e,language:i}={kind:"subtitles"})=>e?`${t==="captions"?"cc":"sb"}:${i}:${encodeURIComponent(e)}`:i,ut=(t=[])=>Array.prototype.map.call(t,oo).join(" "),nh=(t,e)=>i=>i[t]===e,iu=t=>{let e=Object.entries(t).map(([i,a])=>nh(i,a));return i=>e.every(a=>a(i))},Yt=(t,e=[],i=[])=>{let a=us(i).map(iu),r=o=>a.some(s=>s(o));Array.from(e).filter(r).forEach(o=>{o.mode=t})},qt=(t,e=()=>!0)=>{if(!(t!=null&&t.textTracks))return[];let i=typeof e=="function"?e:iu(e);return Array.from(t.textTracks).filter(i)},no=t=>{var e;return!!((e=t.mediaSubtitlesShowing)!=null&&e.length)||t.hasAttribute(n.MEDIA_SUBTITLES_SHOWING)};var ru=t=>{var e;let{media:i,fullscreenElement:a}=t;try{let r=a&&"requestFullscreen"in a?"requestFullscreen":a&&"webkitRequestFullScreen"in a?"webkitRequestFullScreen":void 0;if(r){let o=(e=a[r])==null?void 0:e.call(a);if(o instanceof Promise)return o.catch(()=>{})}else i!=null&&i.webkitEnterFullscreen?i.webkitEnterFullscreen():i!=null&&i.requestFullscreen&&i.requestFullscreen()}catch(r){console.error(r)}},au="exitFullscreen"in V?"exitFullscreen":"webkitExitFullscreen"in V?"webkitExitFullscreen":"webkitCancelFullScreen"in V?"webkitCancelFullScreen":void 0,ou=t=>{var e;let{documentElement:i}=t;if(au){let a=(e=i==null?void 0:i[au])==null?void 0:e.call(i);if(a instanceof Promise)return a.catch(()=>{})}},Da="fullscreenElement"in V?"fullscreenElement":"webkitFullscreenElement"in V?"webkitFullscreenElement":void 0,sh=t=>{let{documentElement:e,media:i}=t,a=e==null?void 0:e[Da];return!a&&"webkitDisplayingFullscreen"in i&&"webkitPresentationMode"in i&&i.webkitDisplayingFullscreen&&i.webkitPresentationMode===Dd.FULLSCREEN?i:a},nu=t=>{var e;let{media:i,documentElement:a,fullscreenElement:r=i}=t;if(!i||!a)return!1;let o=sh(t);if(!o)return!1;if(o===r||o===i)return!0;if(o.localName.includes("-")){let s=o.shadowRoot;if(!(Da in s))return de(o,r);for(;s!=null&&s[Da];){if(s[Da]===r)return!0;s=(e=s[Da])==null?void 0:e.shadowRoot}}return!1},lh="fullscreenEnabled"in V?"fullscreenEnabled":"webkitFullscreenEnabled"in V?"webkitFullscreenEnabled":void 0,su=t=>{let{documentElement:e,media:i}=t;return!!(e!=null&&e[lh])||i&&"webkitSupportsFullscreen"in i};var so,cs=()=>{var t,e;return so||(so=(e=(t=V)==null?void 0:t.createElement)==null?void 0:e.call(t,"video"),so)},lu=async(t=cs())=>{if(!t)return!1;let e=t.volume;t.volume=e/2+.1;let i=new AbortController,a=await Promise.race([dh(t,i.signal),uh(t,e)]);return i.abort(),a},dh=(t,e)=>new Promise(i=>{t.addEventListener("volumechange",()=>i(!0),{signal:e})}),uh=async(t,e)=>{for(let i=0;i<10;i++){if(t.volume===e)return!1;await Fr(10)}return t.volume!==e},ch=/.*Version\/.*Safari\/.*/.test(d.navigator.userAgent),ms=(t=cs())=>d.matchMedia("(display-mode: standalone)").matches&&ch?!1:typeof(t==null?void 0:t.requestPictureInPicture)=="function",hs=(t=cs())=>su({documentElement:V,media:t}),du=hs(),uu=ms(),cu=!!d.WebKitPlaybackTargetAvailabilityEvent,mu=!!d.chrome;var Ci=t=>qt(t.media,e=>[pe.SUBTITLES,pe.CAPTIONS].includes(e.kind)).sort((e,i)=>e.kind>=i.kind?1:-1),ps=t=>qt(t.media,e=>e.mode===gt.SHOWING&&[pe.SUBTITLES,pe.CAPTIONS].includes(e.kind)),lo=(t,e)=>{let i=Ci(t),a=ps(t),r=!!a.length;if(i.length){if(e===!1||r&&e!==!0)Yt(gt.DISABLED,i,a);else if(e===!0||!r&&e!==!1){let o=i[0],{options:s}=t;if(!(s!=null&&s.noSubtitlesLangPref)){let _=globalThis.localStorage.getItem("media-chrome-pref-subtitles-lang"),g=_?[_,...globalThis.navigator.languages]:globalThis.navigator.languages,f=i.filter(v=>g.some(N=>v.language.toLowerCase().startsWith(N.split("-")[0]))).sort((v,N)=>{let T=g.findIndex(P=>v.language.toLowerCase().startsWith(P.split("-")[0])),C=g.findIndex(P=>N.language.toLowerCase().startsWith(P.split("-")[0]));return T-C});f[0]&&(o=f[0])}let{language:l,label:u,kind:m}=o;Yt(gt.DISABLED,i,a),Yt(gt.SHOWING,i,[{language:l,label:u,kind:m}])}}},uo=(t,e)=>t===e?!0:t==null||e==null||typeof t!=typeof e?!1:typeof t=="number"&&Number.isNaN(t)&&Number.isNaN(e)?!0:typeof t!="object"?!1:Array.isArray(t)?mh(t,e):Object.entries(t).every(([i,a])=>i in e&&uo(a,e[i])),mh=(t,e)=>{let i=Array.isArray(t),a=Array.isArray(e);return i!==a?!1:i||a?t.length!==e.length?!1:t.every((r,o)=>uo(r,e[o])):!0};var hh=Object.values(xe),co,ph=lu().then(t=>(co=t,co)),hu=async(...t)=>{await Promise.all(t.filter(e=>e).map(async e=>{if(!("localName"in e&&e instanceof d.HTMLElement))return;let i=e.localName;if(!i.includes("-"))return;let a=d.customElements.get(i);a&&e instanceof a||(await d.customElements.whenDefined(i),d.customElements.upgrade(e))}))},fh=new d.DOMParser,vh=t=>t&&(fh.parseFromString(t,"text/html").body.textContent||t),Li={mediaError:{get(t,e){let{media:i}=t;if((e==null?void 0:e.type)!=="playing")return i==null?void 0:i.error},mediaEvents:["emptied","error","playing"]},mediaErrorCode:{get(t,e){var i;let{media:a}=t;if((e==null?void 0:e.type)!=="playing")return(i=a==null?void 0:a.error)==null?void 0:i.code},mediaEvents:["emptied","error","playing"]},mediaErrorMessage:{get(t,e){var i,a;let{media:r}=t;if((e==null?void 0:e.type)!=="playing")return(a=(i=r==null?void 0:r.error)==null?void 0:i.message)!=null?a:""},mediaEvents:["emptied","error","playing"]},mediaWidth:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.videoWidth)!=null?e:0},mediaEvents:["resize"]},mediaHeight:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.videoHeight)!=null?e:0},mediaEvents:["resize"]},mediaPaused:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.paused)!=null?e:!0},set(t,e){var i;let{media:a}=e;a&&(t?a.pause():(i=a.play())==null||i.catch(()=>{}))},mediaEvents:["play","playing","pause","emptied"]},mediaHasPlayed:{get(t,e){let{media:i}=t;return i?e?e.type==="playing":!i.paused:!1},mediaEvents:["playing","emptied"]},mediaEnded:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.ended)!=null?e:!1},mediaEvents:["seeked","ended","emptied"]},mediaPlaybackRate:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.playbackRate)!=null?e:1},set(t,e){let{media:i}=e;i&&Number.isFinite(+t)&&(i.playbackRate=+t)},mediaEvents:["ratechange","loadstart"]},mediaMuted:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.muted)!=null?e:!1},set(t,e){let{media:i}=e;if(i){try{d.localStorage.setItem("media-chrome-pref-muted",t?"true":"false")}catch(a){console.debug("Error setting muted pref",a)}i.muted=t}},mediaEvents:["volumechange"],stateOwnersUpdateHandlers:[(t,e)=>{let{options:{noMutedPref:i}}=e,{media:a}=e;if(!(!a||a.muted||i))try{let r=d.localStorage.getItem("media-chrome-pref-muted")==="true";Li.mediaMuted.set(r,e),t(r)}catch(r){console.debug("Error getting muted pref",r)}}]},mediaVolume:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.volume)!=null?e:1},set(t,e){let{media:i}=e;if(i){try{t==null?d.localStorage.removeItem("media-chrome-pref-volume"):d.localStorage.setItem("media-chrome-pref-volume",t.toString())}catch(a){console.debug("Error setting volume pref",a)}Number.isFinite(+t)&&(i.volume=+t)}},mediaEvents:["volumechange"],stateOwnersUpdateHandlers:[(t,e)=>{let{options:{noVolumePref:i}}=e;if(!i)try{let{media:a}=e;if(!a)return;let r=d.localStorage.getItem("media-chrome-pref-volume");if(r==null)return;Li.mediaVolume.set(+r,e),t(+r)}catch(a){console.debug("Error getting volume pref",a)}}]},mediaVolumeLevel:{get(t){let{media:e}=t;return typeof(e==null?void 0:e.volume)=="undefined"?"high":e.muted||e.volume===0?"off":e.volume<.5?"low":e.volume<.75?"medium":"high"},mediaEvents:["volumechange"]},mediaCurrentTime:{get(t){var e;let{media:i}=t;return(e=i==null?void 0:i.currentTime)!=null?e:0},set(t,e){let{media:i}=e;!i||!Ai(t)||(i.currentTime=t)},mediaEvents:["timeupdate","loadedmetadata"]},mediaDuration:{get(t){let{media:e,options:{defaultDuration:i}={}}=t;return i&&(!e||!e.duration||Number.isNaN(e.duration)||!Number.isFinite(e.duration))?i:Number.isFinite(e==null?void 0:e.duration)?e.duration:Number.NaN},mediaEvents:["durationchange","loadedmetadata","emptied"]},mediaLoading:{get(t){let{media:e}=t;return(e==null?void 0:e.readyState)<3},mediaEvents:["waiting","playing","emptied"]},mediaSeekable:{get(t){var e;let{media:i}=t;if(!((e=i==null?void 0:i.seekable)!=null&&e.length))return;let a=i.seekable.start(0),r=i.seekable.end(i.seekable.length-1);if(!(!a&&!r))return[Number(a.toFixed(3)),Number(r.toFixed(3))]},mediaEvents:["loadedmetadata","emptied","progress","seekablechange"]},mediaBuffered:{get(t){var e;let{media:i}=t,a=(e=i==null?void 0:i.buffered)!=null?e:[];return Array.from(a).map((r,o)=>[Number(a.start(o).toFixed(3)),Number(a.end(o).toFixed(3))])},mediaEvents:["progress","emptied"]},mediaStreamType:{get(t){let{media:e,options:{defaultStreamType:i}={}}=t,a=[xe.LIVE,xe.ON_DEMAND].includes(i)?i:void 0;if(!e)return a;let{streamType:r}=e;if(hh.includes(r))return r===xe.UNKNOWN?a:r;let o=e.duration;return o===1/0?xe.LIVE:Number.isFinite(o)?xe.ON_DEMAND:a},mediaEvents:["emptied","durationchange","loadedmetadata","streamtypechange"]},mediaTargetLiveWindow:{get(t){let{media:e}=t;if(!e)return Number.NaN;let{targetLiveWindow:i}=e,a=Li.mediaStreamType.get(t);return(i==null||Number.isNaN(i))&&a===xe.LIVE?0:i},mediaEvents:["emptied","durationchange","loadedmetadata","streamtypechange","targetlivewindowchange"]},mediaTimeIsLive:{get(t){let{media:e,options:{liveEdgeOffset:i=10}={}}=t;if(!e)return!1;if(typeof e.liveEdgeStart=="number")return Number.isNaN(e.liveEdgeStart)?!1:e.currentTime>=e.liveEdgeStart;if(!(Li.mediaStreamType.get(t)===xe.LIVE))return!1;let r=e.seekable;if(!r)return!0;if(!r.length)return!1;let o=r.end(r.length-1)-i;return e.currentTime>=o},mediaEvents:["playing","timeupdate","progress","waiting","emptied"]},mediaSubtitlesList:{get(t){return Ci(t).map(({kind:e,label:i,language:a})=>({kind:e,label:i,language:a}))},mediaEvents:["loadstart"],textTracksEvents:["addtrack","removetrack"]},mediaSubtitlesShowing:{get(t){return ps(t).map(({kind:e,label:i,language:a})=>({kind:e,label:i,language:a}))},mediaEvents:["loadstart"],textTracksEvents:["addtrack","removetrack","change"],stateOwnersUpdateHandlers:[(t,e)=>{var i,a;let{media:r,options:o}=e;if(!r)return;let s=l=>{var u;!o.defaultSubtitles||l&&![pe.CAPTIONS,pe.SUBTITLES].includes((u=l==null?void 0:l.track)==null?void 0:u.kind)||lo(e,!0)};return r.addEventListener("loadstart",s),(i=r.textTracks)==null||i.addEventListener("addtrack",s),(a=r.textTracks)==null||a.addEventListener("removetrack",s),()=>{var l,u;r.removeEventListener("loadstart",s),(l=r.textTracks)==null||l.removeEventListener("addtrack",s),(u=r.textTracks)==null||u.removeEventListener("removetrack",s)}}]},mediaChaptersCues:{get(t){var e;let{media:i}=t;if(!i)return[];let[a]=qt(i,{kind:pe.CHAPTERS});return Array.from((e=a==null?void 0:a.cues)!=null?e:[]).map(({text:r,startTime:o,endTime:s})=>({text:vh(r),startTime:o,endTime:s}))},mediaEvents:["loadstart","loadedmetadata"],textTracksEvents:["addtrack","removetrack","change"],stateOwnersUpdateHandlers:[(t,e)=>{var i;let{media:a}=e;if(!a)return;let r=a.querySelector('track[kind="chapters"][default][src]'),o=(i=a.shadowRoot)==null?void 0:i.querySelector(':is(video,audio) > track[kind="chapters"][default][src]');return r==null||r.addEventListener("load",t),o==null||o.addEventListener("load",t),()=>{r==null||r.removeEventListener("load",t),o==null||o.removeEventListener("load",t)}}]},mediaIsPip:{get(t){var e,i;let{media:a,documentElement:r}=t;if(!a||!r||!r.pictureInPictureElement)return!1;if(r.pictureInPictureElement===a)return!0;if(r.pictureInPictureElement instanceof HTMLMediaElement)return(e=a.localName)!=null&&e.includes("-")?de(a,r.pictureInPictureElement):!1;if(r.pictureInPictureElement.localName.includes("-")){let o=r.pictureInPictureElement.shadowRoot;for(;o!=null&&o.pictureInPictureElement;){if(o.pictureInPictureElement===a)return!0;o=(i=o.pictureInPictureElement)==null?void 0:i.shadowRoot}}return!1},set(t,e){let{media:i}=e;if(i)if(t){if(!V.pictureInPictureEnabled){console.warn("MediaChrome: Picture-in-picture is not enabled");return}if(!i.requestPictureInPicture){console.warn("MediaChrome: The current media does not support picture-in-picture");return}let a=()=>{console.warn("MediaChrome: The media is not ready for picture-in-picture. It must have a readyState > 0.")};i.requestPictureInPicture().catch(r=>{if(r.code===11){if(!i.src){console.warn("MediaChrome: The media is not ready for picture-in-picture. It must have a src set.");return}if(i.readyState===0&&i.preload==="none"){let o=()=>{i.removeEventListener("loadedmetadata",s),i.preload="none"},s=()=>{i.requestPictureInPicture().catch(a),o()};i.addEventListener("loadedmetadata",s),i.preload="metadata",setTimeout(()=>{i.readyState===0&&a(),o()},1e3)}else throw r}else throw r})}else V.pictureInPictureElement&&V.exitPictureInPicture()},mediaEvents:["enterpictureinpicture","leavepictureinpicture"]},mediaRenditionList:{get(t){var e;let{media:i}=t;return[...(e=i==null?void 0:i.videoRenditions)!=null?e:[]].map(a=>({...a}))},mediaEvents:["emptied","loadstart"],videoRenditionsEvents:["addrendition","removerendition"]},mediaRenditionSelected:{get(t){var e,i,a;let{media:r}=t;return(a=(i=r==null?void 0:r.videoRenditions)==null?void 0:i[(e=r.videoRenditions)==null?void 0:e.selectedIndex])==null?void 0:a.id},set(t,e){let{media:i}=e;if(!(i!=null&&i.videoRenditions)){console.warn("MediaController: Rendition selection not supported by this media.");return}let a=t,r=Array.prototype.findIndex.call(i.videoRenditions,o=>o.id==a);i.videoRenditions.selectedIndex!=r&&(i.videoRenditions.selectedIndex=r)},mediaEvents:["emptied"],videoRenditionsEvents:["addrendition","removerendition","change"]},mediaAudioTrackList:{get(t){var e;let{media:i}=t;return[...(e=i==null?void 0:i.audioTracks)!=null?e:[]]},mediaEvents:["emptied","loadstart"],audioTracksEvents:["addtrack","removetrack"]},mediaAudioTrackEnabled:{get(t){var e,i;let{media:a}=t;return(i=[...(e=a==null?void 0:a.audioTracks)!=null?e:[]].find(r=>r.enabled))==null?void 0:i.id},set(t,e){let{media:i}=e;if(!(i!=null&&i.audioTracks)){console.warn("MediaChrome: Audio track selection not supported by this media.");return}let a=t;for(let r of i.audioTracks)r.enabled=a==r.id},mediaEvents:["emptied"],audioTracksEvents:["addtrack","removetrack","change"]},mediaIsFullscreen:{get(t){return nu(t)},set(t,e){t?ru(e):ou(e)},rootEvents:["fullscreenchange","webkitfullscreenchange"],mediaEvents:["webkitbeginfullscreen","webkitendfullscreen","webkitpresentationmodechanged"]},mediaIsCasting:{get(t){var e;let{media:i}=t;return!(i!=null&&i.remote)||((e=i.remote)==null?void 0:e.state)==="disconnected"?!1:!!i.remote.state},set(t,e){var i,a;let{media:r}=e;if(r&&!(t&&((i=r.remote)==null?void 0:i.state)!=="disconnected")&&!(!t&&((a=r.remote)==null?void 0:a.state)!=="connected")){if(typeof r.remote.prompt!="function"){console.warn("MediaChrome: Casting is not supported in this environment");return}r.remote.prompt().catch(()=>{})}},remoteEvents:["connect","connecting","disconnect"]},mediaIsAirplaying:{get(){return!1},set(t,e){let{media:i}=e;if(i){if(!(i.webkitShowPlaybackTargetPicker&&d.WebKitPlaybackTargetAvailabilityEvent)){console.error("MediaChrome: received a request to select AirPlay but AirPlay is not supported in this environment");return}i.webkitShowPlaybackTargetPicker()}},mediaEvents:["webkitcurrentplaybacktargetiswirelesschanged"]},mediaFullscreenUnavailable:{get(t){let{media:e}=t;if(!du||!hs(e))return ke.UNSUPPORTED}},mediaPipUnavailable:{get(t){let{media:e}=t;if(!uu||!ms(e))return ke.UNSUPPORTED}},mediaVolumeUnavailable:{get(t){let{media:e}=t;if(co===!1||(e==null?void 0:e.volume)==null)return ke.UNSUPPORTED},stateOwnersUpdateHandlers:[t=>{co==null&&ph.then(e=>t(e?void 0:ke.UNSUPPORTED))}]},mediaCastUnavailable:{get(t,{availability:e="not-available"}={}){var i;let{media:a}=t;if(!mu||!((i=a==null?void 0:a.remote)!=null&&i.state))return ke.UNSUPPORTED;if(!(e==null||e==="available"))return ke.UNAVAILABLE},stateOwnersUpdateHandlers:[(t,e)=>{var i;let{media:a}=e;return a?(a.disableRemotePlayback||a.hasAttribute("disableremoteplayback")||(i=a==null?void 0:a.remote)==null||i.watchAvailability(o=>{t({availability:o?"available":"not-available"})}).catch(o=>{o.name==="NotSupportedError"?t({availability:null}):t({availability:"not-available"})}),()=>{var o;(o=a==null?void 0:a.remote)==null||o.cancelWatchAvailability().catch(()=>{})}):void 0}]},mediaAirplayUnavailable:{get(t,e){if(!cu)return ke.UNSUPPORTED;if((e==null?void 0:e.availability)==="not-available")return ke.UNAVAILABLE},mediaEvents:["webkitplaybacktargetavailabilitychanged"],stateOwnersUpdateHandlers:[(t,e)=>{var i;let{media:a}=e;return a?(a.disableRemotePlayback||a.hasAttribute("disableremoteplayback")||(i=a==null?void 0:a.remote)==null||i.watchAvailability(o=>{t({availability:o?"available":"not-available"})}).catch(o=>{o.name==="NotSupportedError"?t({availability:null}):t({availability:"not-available"})}),()=>{var o;(o=a==null?void 0:a.remote)==null||o.cancelWatchAvailability().catch(()=>{})}):void 0}]},mediaRenditionUnavailable:{get(t){var e;let{media:i}=t;if(!(i!=null&&i.videoRenditions))return ke.UNSUPPORTED;if(!((e=i.videoRenditions)!=null&&e.length))return ke.UNAVAILABLE},mediaEvents:["emptied","loadstart"],videoRenditionsEvents:["addrendition","removerendition"]},mediaAudioTrackUnavailable:{get(t){var e,i;let{media:a}=t;if(!(a!=null&&a.audioTracks))return ke.UNSUPPORTED;if(((i=(e=a.audioTracks)==null?void 0:e.length)!=null?i:0)<=1)return ke.UNAVAILABLE},mediaEvents:["emptied","loadstart"],audioTracksEvents:["addtrack","removetrack"]}};var pu={[p.MEDIA_PREVIEW_REQUEST](t,e,{detail:i}){var a,r,o;let{media:s}=e,l=i!=null?i:void 0,u,m;if(s&&l!=null){let[v]=qt(s,{kind:pe.METADATA,label:"thumbnails"}),N=Array.prototype.find.call((a=v==null?void 0:v.cues)!=null?a:[],(T,C,P)=>C===0?T.endTime>l:C===P.length-1?T.startTime<=l:T.startTime<=l&&T.endTime>l);if(N){let T=/'^(?:[a-z]+:)?\/\//i.test(N.text)||(r=s==null?void 0:s.querySelector('track[label="thumbnails"]'))==null?void 0:r.src,C=new URL(N.text,T);m=new URLSearchParams(C.hash).get("#xywh").split(",").map(j=>+j),u=C.href}}let _=t.mediaDuration.get(e),f=(o=t.mediaChaptersCues.get(e).find((v,N,T)=>N===T.length-1&&_===v.endTime?v.startTime<=l&&v.endTime>=l:v.startTime<=l&&v.endTime>l))==null?void 0:o.text;return i!=null&&f==null&&(f=""),{mediaPreviewTime:l,mediaPreviewImage:u,mediaPreviewCoords:m,mediaPreviewChapter:f}},[p.MEDIA_PAUSE_REQUEST](t,e){t["mediaPaused"].set(!0,e)},[p.MEDIA_PLAY_REQUEST](t,e){var i,a,r,o;let s="mediaPaused",u=t.mediaStreamType.get(e)===xe.LIVE,m=!((i=e.options)!=null&&i.noAutoSeekToLive),_=t.mediaTargetLiveWindow.get(e)>0;if(u&&m&&!_){let g=(a=t.mediaSeekable.get(e))==null?void 0:a[1];if(g){let f=(o=(r=e.options)==null?void 0:r.seekToLiveOffset)!=null?o:0,v=g-f;t.mediaCurrentTime.set(v,e)}}t[s].set(!1,e)},[p.MEDIA_PLAYBACK_RATE_REQUEST](t,e,{detail:i}){let a="mediaPlaybackRate",r=i;t[a].set(r,e)},[p.MEDIA_MUTE_REQUEST](t,e){t["mediaMuted"].set(!0,e)},[p.MEDIA_UNMUTE_REQUEST](t,e){let i="mediaMuted";t.mediaVolume.get(e)||t.mediaVolume.set(.25,e),t[i].set(!1,e)},[p.MEDIA_VOLUME_REQUEST](t,e,{detail:i}){let a="mediaVolume",r=i;r&&t.mediaMuted.get(e)&&t.mediaMuted.set(!1,e),t[a].set(r,e)},[p.MEDIA_SEEK_REQUEST](t,e,{detail:i}){let a="mediaCurrentTime",r=i;t[a].set(r,e)},[p.MEDIA_SEEK_TO_LIVE_REQUEST](t,e){var i,a,r;let o="mediaCurrentTime",s=(i=t.mediaSeekable.get(e))==null?void 0:i[1];if(Number.isNaN(Number(s)))return;let l=(r=(a=e.options)==null?void 0:a.seekToLiveOffset)!=null?r:0,u=s-l;t[o].set(u,e)},[p.MEDIA_SHOW_SUBTITLES_REQUEST](t,e,{detail:i}){var a;let{options:r}=e,o=Ci(e),s=us(i),l=(a=s[0])==null?void 0:a.language;l&&!r.noSubtitlesLangPref&&d.localStorage.setItem("media-chrome-pref-subtitles-lang",l),Yt(gt.SHOWING,o,s)},[p.MEDIA_DISABLE_SUBTITLES_REQUEST](t,e,{detail:i}){let a=Ci(e),r=i!=null?i:[];Yt(gt.DISABLED,a,r)},[p.MEDIA_TOGGLE_SUBTITLES_REQUEST](t,e,{detail:i}){lo(e,i)},[p.MEDIA_RENDITION_REQUEST](t,e,{detail:i}){let a="mediaRenditionSelected",r=i;t[a].set(r,e)},[p.MEDIA_AUDIO_TRACK_REQUEST](t,e,{detail:i}){let a="mediaAudioTrackEnabled",r=i;t[a].set(r,e)},[p.MEDIA_ENTER_PIP_REQUEST](t,e){let i="mediaIsPip";t.mediaIsFullscreen.get(e)&&t.mediaIsFullscreen.set(!1,e),t[i].set(!0,e)},[p.MEDIA_EXIT_PIP_REQUEST](t,e){t["mediaIsPip"].set(!1,e)},[p.MEDIA_ENTER_FULLSCREEN_REQUEST](t,e){let i="mediaIsFullscreen";t.mediaIsPip.get(e)&&t.mediaIsPip.set(!1,e),t[i].set(!0,e)},[p.MEDIA_EXIT_FULLSCREEN_REQUEST](t,e){t["mediaIsFullscreen"].set(!1,e)},[p.MEDIA_ENTER_CAST_REQUEST](t,e){let i="mediaIsCasting";t.mediaIsFullscreen.get(e)&&t.mediaIsFullscreen.set(!1,e),t[i].set(!0,e)},[p.MEDIA_EXIT_CAST_REQUEST](t,e){t["mediaIsCasting"].set(!1,e)},[p.MEDIA_AIRPLAY_REQUEST](t,e){t["mediaIsAirplaying"].set(!0,e)}};var fu=({media:t,fullscreenElement:e,documentElement:i,stateMediator:a=Li,requestMap:r=pu,options:o={},monitorStateOwnersOnlyWithSubscriptions:s=!0})=>{let l=[],u={options:{...o}},m=Object.freeze({mediaPreviewTime:void 0,mediaPreviewImage:void 0,mediaPreviewCoords:void 0,mediaPreviewChapter:void 0}),_=T=>{T!=null&&(uo(T,m)||(m=Object.freeze({...m,...T}),l.forEach(C=>C(m))))},g=()=>{let T=Object.entries(a).reduce((C,[P,{get:j}])=>(C[P]=j(u),C),{});_(T)},f={},v,N=async(T,C)=>{var P,j,ge,bt,ot,ca,ma,ha,pa,fa,va,Ea,ba,ga,_a,Aa;let $r=!!v;if(v={...u,...v!=null?v:{},...T},$r)return;await hu(...Object.values(T));let nt=l.length>0&&C===0&&s,Ta=u.media!==v.media,ld=((P=u.media)==null?void 0:P.textTracks)!==((j=v.media)==null?void 0:j.textTracks),dd=((ge=u.media)==null?void 0:ge.videoRenditions)!==((bt=v.media)==null?void 0:bt.videoRenditions),ud=((ot=u.media)==null?void 0:ot.audioTracks)!==((ca=v.media)==null?void 0:ca.audioTracks),cd=((ma=u.media)==null?void 0:ma.remote)!==((ha=v.media)==null?void 0:ha.remote),md=u.documentElement!==v.documentElement,hd=!!u.media&&(Ta||nt),pd=!!((pa=u.media)!=null&&pa.textTracks)&&(ld||nt),fd=!!((fa=u.media)!=null&&fa.videoRenditions)&&(dd||nt),vd=!!((va=u.media)!=null&&va.audioTracks)&&(ud||nt),Ed=!!((Ea=u.media)!=null&&Ea.remote)&&(cd||nt),bd=!!u.documentElement&&(md||nt),gd=hd||pd||fd||vd||Ed||bd,bi=l.length===0&&C===1&&s,_d=!!v.media&&(Ta||bi),Ad=!!((ba=v.media)!=null&&ba.textTracks)&&(ld||bi),Td=!!((ga=v.media)!=null&&ga.videoRenditions)&&(dd||bi),yd=!!((_a=v.media)!=null&&_a.audioTracks)&&(ud||bi),kd=!!((Aa=v.media)!=null&&Aa.remote)&&(cd||bi),Sd=!!v.documentElement&&(md||bi),Id=_d||Ad||Td||yd||kd||Sd;if(!(gd||Id)){Object.entries(v).forEach(([F,ya])=>{u[F]=ya}),g(),v=void 0;return}Object.entries(a).forEach(([F,{get:ya,mediaEvents:km=[],textTracksEvents:Sm=[],videoRenditionsEvents:Im=[],audioTracksEvents:Mm=[],remoteEvents:Cm=[],rootEvents:Lm=[],stateOwnersUpdateHandlers:wm=[]}])=>{f[F]||(f[F]={});let _e=Q=>{let Ae=ya(u,Q);_({[F]:Ae})},te;te=f[F].mediaEvents,km.forEach(Q=>{te&&hd&&(u.media.removeEventListener(Q,te),f[F].mediaEvents=void 0),_d&&(v.media.addEventListener(Q,_e),f[F].mediaEvents=_e)}),te=f[F].textTracksEvents,Sm.forEach(Q=>{var Ae,Re;te&&pd&&((Ae=u.media.textTracks)==null||Ae.removeEventListener(Q,te),f[F].textTracksEvents=void 0),Ad&&((Re=v.media.textTracks)==null||Re.addEventListener(Q,_e),f[F].textTracksEvents=_e)}),te=f[F].videoRenditionsEvents,Im.forEach(Q=>{var Ae,Re;te&&fd&&((Ae=u.media.videoRenditions)==null||Ae.removeEventListener(Q,te),f[F].videoRenditionsEvents=void 0),Td&&((Re=v.media.videoRenditions)==null||Re.addEventListener(Q,_e),f[F].videoRenditionsEvents=_e)}),te=f[F].audioTracksEvents,Mm.forEach(Q=>{var Ae,Re;te&&vd&&((Ae=u.media.audioTracks)==null||Ae.removeEventListener(Q,te),f[F].audioTracksEvents=void 0),yd&&((Re=v.media.audioTracks)==null||Re.addEventListener(Q,_e),f[F].audioTracksEvents=_e)}),te=f[F].remoteEvents,Cm.forEach(Q=>{var Ae,Re;te&&Ed&&((Ae=u.media.remote)==null||Ae.removeEventListener(Q,te),f[F].remoteEvents=void 0),kd&&((Re=v.media.remote)==null||Re.addEventListener(Q,_e),f[F].remoteEvents=_e)}),te=f[F].rootEvents,Lm.forEach(Q=>{te&&bd&&(u.documentElement.removeEventListener(Q,te),f[F].rootEvents=void 0),Sd&&(v.documentElement.addEventListener(Q,_e),f[F].rootEvents=_e)});let Md=f[F].stateOwnersUpdateHandlers;wm.forEach(Q=>{Md&&gd&&Md(),Id&&(f[F].stateOwnersUpdateHandlers=Q(_e,v))})}),Object.entries(v).forEach(([F,ya])=>{u[F]=ya}),g(),v=void 0};return N({media:t,fullscreenElement:e,documentElement:i,options:o}),{dispatch(T){let{type:C,detail:P}=T;if(r[C]&&m.mediaErrorCode==null){_(r[C](a,u,T));return}C==="mediaelementchangerequest"?N({media:P}):C==="fullscreenelementchangerequest"?N({fullscreenElement:P}):C==="documentelementchangerequest"?N({documentElement:P}):C==="optionschangerequest"&&Object.entries(P!=null?P:{}).forEach(([j,ge])=>{u.options[j]=ge})},getState(){return m},subscribe(T){return N({},l.length+1),l.push(T),T(m),()=>{let C=l.indexOf(T);C>=0&&(N({},l.length-1),l.splice(C,1))}}}};var bs=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},D=(t,e,i)=>(bs(t,e,"read from private field"),i?i.call(t):e.get(t)),ct=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},yt=(t,e,i,a)=>(bs(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),kt=(t,e,i)=>(bs(t,e,"access private method"),i),zt,Oa,K,Na,Be,mo,ho,fs,wi,Pa,po,vs,_u=["ArrowLeft","ArrowRight","Enter"," ","f","m","k","c"],vu=10,E={DEFAULT_SUBTITLES:"defaultsubtitles",DEFAULT_STREAM_TYPE:"defaultstreamtype",DEFAULT_DURATION:"defaultduration",FULLSCREEN_ELEMENT:"fullscreenelement",HOTKEYS:"hotkeys",KEYS_USED:"keysused",LIVE_EDGE_OFFSET:"liveedgeoffset",SEEK_TO_LIVE_OFFSET:"seektoliveoffset",NO_AUTO_SEEK_TO_LIVE:"noautoseektolive",NO_HOTKEYS:"nohotkeys",NO_VOLUME_PREF:"novolumepref",NO_SUBTITLES_LANG_PREF:"nosubtitleslangpref",NO_DEFAULT_STORE:"nodefaultstore",KEYBOARD_FORWARD_SEEK_OFFSET:"keyboardforwardseekoffset",KEYBOARD_BACKWARD_SEEK_OFFSET:"keyboardbackwardseekoffset",LANG:"lang"},fo=class extends Vt{constructor(){super(),ct(this,ho),ct(this,wi),ct(this,po),this.mediaStateReceivers=[],this.associatedElementSubscriptions=new Map,ct(this,zt,new Tt(this,E.HOTKEYS)),ct(this,Oa,void 0),ct(this,K,void 0),ct(this,Na,void 0),ct(this,Be,void 0),ct(this,mo,i=>{var a;(a=D(this,K))==null||a.dispatch(i)}),this.associateElement(this);let e={};yt(this,Na,i=>{Object.entries(i).forEach(([a,r])=>{if(a in e&&e[a]===r)return;this.propagateMediaState(a,r);let o=a.toLowerCase(),s=new d.CustomEvent(xd[o],{composed:!0,detail:r});this.dispatchEvent(s)}),e=i}),this.enableHotkeys()}static get observedAttributes(){return super.observedAttributes.concat(E.NO_HOTKEYS,E.HOTKEYS,E.DEFAULT_STREAM_TYPE,E.DEFAULT_SUBTITLES,E.DEFAULT_DURATION,E.LANG)}get mediaStore(){return D(this,K)}set mediaStore(e){var i,a;if(D(this,K)&&((i=D(this,Be))==null||i.call(this),yt(this,Be,void 0)),yt(this,K,e),!D(this,K)&&!this.hasAttribute(E.NO_DEFAULT_STORE)){kt(this,ho,fs).call(this);return}yt(this,Be,(a=D(this,K))==null?void 0:a.subscribe(D(this,Na)))}get fullscreenElement(){var e;return(e=D(this,Oa))!=null?e:this}set fullscreenElement(e){var i;this.hasAttribute(E.FULLSCREEN_ELEMENT)&&this.removeAttribute(E.FULLSCREEN_ELEMENT),yt(this,Oa,e),(i=D(this,K))==null||i.dispatch({type:"fullscreenelementchangerequest",detail:this.fullscreenElement})}get defaultSubtitles(){return k(this,E.DEFAULT_SUBTITLES)}set defaultSubtitles(e){S(this,E.DEFAULT_SUBTITLES,e)}get defaultStreamType(){return I(this,E.DEFAULT_STREAM_TYPE)}set defaultStreamType(e){M(this,E.DEFAULT_STREAM_TYPE,e)}get defaultDuration(){return R(this,E.DEFAULT_DURATION)}set defaultDuration(e){O(this,E.DEFAULT_DURATION,e)}get noHotkeys(){return k(this,E.NO_HOTKEYS)}set noHotkeys(e){S(this,E.NO_HOTKEYS,e)}get keysUsed(){return I(this,E.KEYS_USED)}set keysUsed(e){M(this,E.KEYS_USED,e)}get liveEdgeOffset(){return R(this,E.LIVE_EDGE_OFFSET)}set liveEdgeOffset(e){O(this,E.LIVE_EDGE_OFFSET,e)}get noAutoSeekToLive(){return k(this,E.NO_AUTO_SEEK_TO_LIVE)}set noAutoSeekToLive(e){S(this,E.NO_AUTO_SEEK_TO_LIVE,e)}get noVolumePref(){return k(this,E.NO_VOLUME_PREF)}set noVolumePref(e){S(this,E.NO_VOLUME_PREF,e)}get noSubtitlesLangPref(){return k(this,E.NO_SUBTITLES_LANG_PREF)}set noSubtitlesLangPref(e){S(this,E.NO_SUBTITLES_LANG_PREF,e)}get noDefaultStore(){return k(this,E.NO_DEFAULT_STORE)}set noDefaultStore(e){S(this,E.NO_DEFAULT_STORE,e)}attributeChangedCallback(e,i,a){var r,o,s,l,u,m,_,g;if(super.attributeChangedCallback(e,i,a),e===E.NO_HOTKEYS)a!==i&&a===""?(this.hasAttribute(E.HOTKEYS)&&console.warn("Media Chrome: Both `hotkeys` and `nohotkeys` have been set. All hotkeys will be disabled."),this.disableHotkeys()):a!==i&&a===null&&this.enableHotkeys();else if(e===E.HOTKEYS)D(this,zt).value=a;else if(e===E.DEFAULT_SUBTITLES&&a!==i)(r=D(this,K))==null||r.dispatch({type:"optionschangerequest",detail:{defaultSubtitles:this.hasAttribute(E.DEFAULT_SUBTITLES)}});else if(e===E.DEFAULT_STREAM_TYPE)(s=D(this,K))==null||s.dispatch({type:"optionschangerequest",detail:{defaultStreamType:(o=this.getAttribute(E.DEFAULT_STREAM_TYPE))!=null?o:void 0}});else if(e===E.LIVE_EDGE_OFFSET)(l=D(this,K))==null||l.dispatch({type:"optionschangerequest",detail:{liveEdgeOffset:this.hasAttribute(E.LIVE_EDGE_OFFSET)?+this.getAttribute(E.LIVE_EDGE_OFFSET):void 0,seekToLiveOffset:this.hasAttribute(E.SEEK_TO_LIVE_OFFSET)?void 0:+this.getAttribute(E.LIVE_EDGE_OFFSET)}});else if(e===E.SEEK_TO_LIVE_OFFSET)(u=D(this,K))==null||u.dispatch({type:"optionschangerequest",detail:{seekToLiveOffset:this.hasAttribute(E.SEEK_TO_LIVE_OFFSET)?+this.getAttribute(E.SEEK_TO_LIVE_OFFSET):void 0}});else if(e===E.NO_AUTO_SEEK_TO_LIVE)(m=D(this,K))==null||m.dispatch({type:"optionschangerequest",detail:{noAutoSeekToLive:this.hasAttribute(E.NO_AUTO_SEEK_TO_LIVE)}});else if(e===E.FULLSCREEN_ELEMENT){let f=a?(_=this.getRootNode())==null?void 0:_.getElementById(a):void 0;yt(this,Oa,f),(g=D(this,K))==null||g.dispatch({type:"fullscreenelementchangerequest",detail:this.fullscreenElement})}else e===E.LANG&&a!==i&&Fd(a)}connectedCallback(){var e,i;!D(this,K)&&!this.hasAttribute(E.NO_DEFAULT_STORE)&&kt(this,ho,fs).call(this),(e=D(this,K))==null||e.dispatch({type:"documentelementchangerequest",detail:V}),super.connectedCallback(),D(this,K)&&!D(this,Be)&&yt(this,Be,(i=D(this,K))==null?void 0:i.subscribe(D(this,Na))),this.enableHotkeys()}disconnectedCallback(){var e,i,a,r;(e=super.disconnectedCallback)==null||e.call(this),D(this,K)&&((i=D(this,K))==null||i.dispatch({type:"documentelementchangerequest",detail:void 0}),(a=D(this,K))==null||a.dispatch({type:p.MEDIA_TOGGLE_SUBTITLES_REQUEST,detail:!1})),D(this,Be)&&((r=D(this,Be))==null||r.call(this),yt(this,Be,void 0))}mediaSetCallback(e){var i;super.mediaSetCallback(e),(i=D(this,K))==null||i.dispatch({type:"mediaelementchangerequest",detail:e}),e.hasAttribute("tabindex")||(e.tabIndex=-1)}mediaUnsetCallback(e){var i;super.mediaUnsetCallback(e),(i=D(this,K))==null||i.dispatch({type:"mediaelementchangerequest",detail:void 0})}propagateMediaState(e,i){gu(this.mediaStateReceivers,e,i)}associateElement(e){if(!e)return;let{associatedElementSubscriptions:i}=this;if(i.has(e))return;let a=this.registerMediaStateReceiver.bind(this),r=this.unregisterMediaStateReceiver.bind(this),o=Th(e,a,r);Object.values(p).forEach(s=>{e.addEventListener(s,D(this,mo))}),i.set(e,o)}unassociateElement(e){if(!e)return;let{associatedElementSubscriptions:i}=this;if(!i.has(e))return;i.get(e)(),i.delete(e),Object.values(p).forEach(r=>{e.removeEventListener(r,D(this,mo))})}registerMediaStateReceiver(e){if(!e)return;let i=this.mediaStateReceivers;i.indexOf(e)>-1||(i.push(e),D(this,K)&&Object.entries(D(this,K).getState()).forEach(([r,o])=>{gu([e],r,o)}))}unregisterMediaStateReceiver(e){let i=this.mediaStateReceivers,a=i.indexOf(e);a<0||i.splice(a,1)}enableHotkeys(){this.addEventListener("keydown",kt(this,po,vs))}disableHotkeys(){this.removeEventListener("keydown",kt(this,po,vs)),this.removeEventListener("keyup",kt(this,wi,Pa))}get hotkeys(){return I(this,E.HOTKEYS)}set hotkeys(e){M(this,E.HOTKEYS,e)}keyboardShortcutHandler(e){var i,a,r,o,s;let l=e.target;if(((r=(a=(i=l.getAttribute(E.KEYS_USED))==null?void 0:i.split(" "))!=null?a:l==null?void 0:l.keysUsed)!=null?r:[]).map(f=>f==="Space"?" ":f).filter(Boolean).includes(e.key))return;let m,_,g;if(!D(this,zt).contains(`no${e.key.toLowerCase()}`)&&!(e.key===" "&&D(this,zt).contains("nospace")))switch(e.key){case" ":case"k":m=D(this,K).getState().mediaPaused?p.MEDIA_PLAY_REQUEST:p.MEDIA_PAUSE_REQUEST,this.dispatchEvent(new d.CustomEvent(m,{composed:!0,bubbles:!0}));break;case"m":m=this.mediaStore.getState().mediaVolumeLevel==="off"?p.MEDIA_UNMUTE_REQUEST:p.MEDIA_MUTE_REQUEST,this.dispatchEvent(new d.CustomEvent(m,{composed:!0,bubbles:!0}));break;case"f":m=this.mediaStore.getState().mediaIsFullscreen?p.MEDIA_EXIT_FULLSCREEN_REQUEST:p.MEDIA_ENTER_FULLSCREEN_REQUEST,this.dispatchEvent(new d.CustomEvent(m,{composed:!0,bubbles:!0}));break;case"c":this.dispatchEvent(new d.CustomEvent(p.MEDIA_TOGGLE_SUBTITLES_REQUEST,{composed:!0,bubbles:!0}));break;case"ArrowLeft":{let f=this.hasAttribute(E.KEYBOARD_BACKWARD_SEEK_OFFSET)?+this.getAttribute(E.KEYBOARD_BACKWARD_SEEK_OFFSET):vu;_=Math.max(((o=this.mediaStore.getState().mediaCurrentTime)!=null?o:0)-f,0),g=new d.CustomEvent(p.MEDIA_SEEK_REQUEST,{composed:!0,bubbles:!0,detail:_}),this.dispatchEvent(g);break}case"ArrowRight":{let f=this.hasAttribute(E.KEYBOARD_FORWARD_SEEK_OFFSET)?+this.getAttribute(E.KEYBOARD_FORWARD_SEEK_OFFSET):vu;_=Math.max(((s=this.mediaStore.getState().mediaCurrentTime)!=null?s:0)+f,0),g=new d.CustomEvent(p.MEDIA_SEEK_REQUEST,{composed:!0,bubbles:!0,detail:_}),this.dispatchEvent(g);break}default:break}}};zt=new WeakMap;Oa=new WeakMap;K=new WeakMap;Na=new WeakMap;Be=new WeakMap;mo=new WeakMap;ho=new WeakSet;fs=function(){var t;this.mediaStore=fu({media:this.media,fullscreenElement:this.fullscreenElement,options:{defaultSubtitles:this.hasAttribute(E.DEFAULT_SUBTITLES),defaultDuration:this.hasAttribute(E.DEFAULT_DURATION)?+this.getAttribute(E.DEFAULT_DURATION):void 0,defaultStreamType:(t=this.getAttribute(E.DEFAULT_STREAM_TYPE))!=null?t:void 0,liveEdgeOffset:this.hasAttribute(E.LIVE_EDGE_OFFSET)?+this.getAttribute(E.LIVE_EDGE_OFFSET):void 0,seekToLiveOffset:this.hasAttribute(E.SEEK_TO_LIVE_OFFSET)?+this.getAttribute(E.SEEK_TO_LIVE_OFFSET):this.hasAttribute(E.LIVE_EDGE_OFFSET)?+this.getAttribute(E.LIVE_EDGE_OFFSET):void 0,noAutoSeekToLive:this.hasAttribute(E.NO_AUTO_SEEK_TO_LIVE),noVolumePref:this.hasAttribute(E.NO_VOLUME_PREF),noSubtitlesLangPref:this.hasAttribute(E.NO_SUBTITLES_LANG_PREF)}})};wi=new WeakSet;Pa=function(t){let{key:e}=t;if(!_u.includes(e)){this.removeEventListener("keyup",kt(this,wi,Pa));return}this.keyboardShortcutHandler(t)};po=new WeakSet;vs=function(t){let{metaKey:e,altKey:i,key:a}=t;if(e||i||!_u.includes(a)){this.removeEventListener("keyup",kt(this,wi,Pa));return}[" ","ArrowLeft","ArrowRight"].includes(a)&&!(D(this,zt).contains(`no${a.toLowerCase()}`)||a===" "&&D(this,zt).contains("nospace"))&&t.preventDefault(),this.addEventListener("keyup",kt(this,wi,Pa),{once:!0})};var Eh=Object.values(n),bh=Object.values(Jn),Au=t=>{var e,i,a,r;let{observedAttributes:o}=t.constructor;!o&&((e=t.nodeName)!=null&&e.includes("-"))&&(d.customElements.upgrade(t),{observedAttributes:o}=t.constructor);let s=(r=(a=(i=t==null?void 0:t.getAttribute)==null?void 0:i.call(t,L.MEDIA_CHROME_ATTRIBUTES))==null?void 0:a.split)==null?void 0:r.call(a,/\s+/);return Array.isArray(o||s)?(o||s).filter(l=>Eh.includes(l)):[]},gh=t=>{var e,i;return(e=t.nodeName)!=null&&e.includes("-")&&d.customElements.get((i=t.nodeName)==null?void 0:i.toLowerCase())&&!(t instanceof d.customElements.get(t.nodeName.toLowerCase()))&&d.customElements.upgrade(t),bh.some(a=>a in t)},Es=t=>gh(t)||!!Au(t).length,Eu=t=>{var e;return(e=t==null?void 0:t.join)==null?void 0:e.call(t,":")},bu={[n.MEDIA_SUBTITLES_LIST]:ut,[n.MEDIA_SUBTITLES_SHOWING]:ut,[n.MEDIA_SEEKABLE]:Eu,[n.MEDIA_BUFFERED]:t=>t==null?void 0:t.map(Eu).join(" "),[n.MEDIA_PREVIEW_COORDS]:t=>t==null?void 0:t.join(" "),[n.MEDIA_RENDITION_LIST]:Od,[n.MEDIA_AUDIO_TRACK_LIST]:Pd},_h=async(t,e,i)=>{var a,r;if(t.isConnected||await Fr(0),typeof i=="boolean"||i==null)return S(t,e,i);if(typeof i=="number")return O(t,e,i);if(typeof i=="string")return M(t,e,i);if(Array.isArray(i)&&!i.length)return t.removeAttribute(e);let o=(r=(a=bu[e])==null?void 0:a.call(bu,i))!=null?r:i;return t.setAttribute(e,o)},Ah=t=>{var e;return!!((e=t.closest)!=null&&e.call(t,'*[slot="media"]'))},Zt=(t,e)=>{if(Ah(t))return;let i=(r,o)=>{var s,l;Es(r)&&o(r);let{children:u=[]}=r!=null?r:{},m=(l=(s=r==null?void 0:r.shadowRoot)==null?void 0:s.children)!=null?l:[];[...u,...m].forEach(g=>Zt(g,o))},a=t==null?void 0:t.nodeName.toLowerCase();if(a.includes("-")&&!Es(t)){d.customElements.whenDefined(a).then(()=>{i(t,e)});return}i(t,e)},gu=(t,e,i)=>{t.forEach(a=>{if(e in a){a[e]=i;return}let r=Au(a),o=e.toLowerCase();r.includes(o)&&_h(a,o,i)})},Th=(t,e,i)=>{Zt(t,e);let a=_=>{var g;let f=(g=_==null?void 0:_.composedPath()[0])!=null?g:_.target;e(f)},r=_=>{var g;let f=(g=_==null?void 0:_.composedPath()[0])!=null?g:_.target;i(f)};t.addEventListener(p.REGISTER_MEDIA_STATE_RECEIVER,a),t.addEventListener(p.UNREGISTER_MEDIA_STATE_RECEIVER,r);let o=_=>{_.forEach(g=>{let{addedNodes:f=[],removedNodes:v=[],type:N,target:T,attributeName:C}=g;N==="childList"?(Array.prototype.forEach.call(f,P=>Zt(P,e)),Array.prototype.forEach.call(v,P=>Zt(P,i))):N==="attributes"&&C===L.MEDIA_CHROME_ATTRIBUTES&&(Es(T)?e(T):i(T))})},s=[],l=_=>{let g=_.target;g.name!=="media"&&(s.forEach(f=>Zt(f,i)),s=[...g.assignedElements({flatten:!0})],s.forEach(f=>Zt(f,e)))};t.addEventListener("slotchange",l);let u=new MutationObserver(o);return u.observe(t,{childList:!0,attributes:!0,subtree:!0}),()=>{Zt(t,i),t.removeEventListener("slotchange",l),u.disconnect(),t.removeEventListener(p.REGISTER_MEDIA_STATE_RECEIVER,a),t.removeEventListener(p.UNREGISTER_MEDIA_STATE_RECEIVER,r)}};d.customElements.get("media-controller")||d.customElements.define("media-controller",fo);var gs=fo;var Ri={PLACEMENT:"placement",BOUNDS:"bounds"};function yh(t){return`
    <style>
      :host {
        --_tooltip-background-color: var(--media-tooltip-background-color, var(--media-secondary-color, rgba(20, 20, 30, .7)));
        --_tooltip-background: var(--media-tooltip-background, var(--_tooltip-background-color));
        --_tooltip-arrow-half-width: calc(var(--media-tooltip-arrow-width, 12px) / 2);
        --_tooltip-arrow-height: var(--media-tooltip-arrow-height, 5px);
        --_tooltip-arrow-background: var(--media-tooltip-arrow-color, var(--_tooltip-background-color));
        position: relative;
        pointer-events: none;
        display: var(--media-tooltip-display, inline-flex);
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        z-index: var(--media-tooltip-z-index, 1);
        background: var(--_tooltip-background);
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        font: var(--media-font,
          var(--media-font-weight, 400)
          var(--media-font-size, 13px) /
          var(--media-text-content-height, var(--media-control-height, 18px))
          var(--media-font-family, helvetica neue, segoe ui, roboto, arial, sans-serif));
        padding: var(--media-tooltip-padding, .35em .7em);
        border: var(--media-tooltip-border, none);
        border-radius: var(--media-tooltip-border-radius, 5px);
        filter: var(--media-tooltip-filter, drop-shadow(0 0 4px rgba(0, 0, 0, .2)));
        white-space: var(--media-tooltip-white-space, nowrap);
      }

      :host([hidden]) {
        display: none;
      }

      img, svg {
        display: inline-block;
      }

      #arrow {
        position: absolute;
        width: 0px;
        height: 0px;
        border-style: solid;
        display: var(--media-tooltip-arrow-display, block);
      }

      :host(:not([placement])),
      :host([placement="top"]) {
        position: absolute;
        bottom: calc(100% + var(--media-tooltip-distance, 12px));
        left: 50%;
        transform: translate(calc(-50% - var(--media-tooltip-offset-x, 0px)), 0);
      }
      :host(:not([placement])) #arrow,
      :host([placement="top"]) #arrow {
        top: 100%;
        left: 50%;
        border-width: var(--_tooltip-arrow-height) var(--_tooltip-arrow-half-width) 0 var(--_tooltip-arrow-half-width);
        border-color: var(--_tooltip-arrow-background) transparent transparent transparent;
        transform: translate(calc(-50% + var(--media-tooltip-offset-x, 0px)), 0);
      }

      :host([placement="right"]) {
        position: absolute;
        left: calc(100% + var(--media-tooltip-distance, 12px));
        top: 50%;
        transform: translate(0, -50%);
      }
      :host([placement="right"]) #arrow {
        top: 50%;
        right: 100%;
        border-width: var(--_tooltip-arrow-half-width) var(--_tooltip-arrow-height) var(--_tooltip-arrow-half-width) 0;
        border-color: transparent var(--_tooltip-arrow-background) transparent transparent;
        transform: translate(0, -50%);
      }

      :host([placement="bottom"]) {
        position: absolute;
        top: calc(100% + var(--media-tooltip-distance, 12px));
        left: 50%;
        transform: translate(calc(-50% - var(--media-tooltip-offset-x, 0px)), 0);
      }
      :host([placement="bottom"]) #arrow {
        bottom: 100%;
        left: 50%;
        border-width: 0 var(--_tooltip-arrow-half-width) var(--_tooltip-arrow-height) var(--_tooltip-arrow-half-width);
        border-color: transparent transparent var(--_tooltip-arrow-background) transparent;
        transform: translate(calc(-50% + var(--media-tooltip-offset-x, 0px)), 0);
      }

      :host([placement="left"]) {
        position: absolute;
        right: calc(100% + var(--media-tooltip-distance, 12px));
        top: 50%;
        transform: translate(0, -50%);
      }
      :host([placement="left"]) #arrow {
        top: 50%;
        left: 100%;
        border-width: var(--_tooltip-arrow-half-width) 0 var(--_tooltip-arrow-half-width) var(--_tooltip-arrow-height);
        border-color: transparent transparent transparent var(--_tooltip-arrow-background);
        transform: translate(0, -50%);
      }
      
      :host([placement="none"]) #arrow {
        display: none;
      }
    </style>
    <slot></slot>
    <div id="arrow"></div>
  `}var xi=class extends d.HTMLElement{constructor(){if(super(),this.updateXOffset=()=>{var e;if(!zr(this,{checkOpacity:!1,checkVisibilityCSS:!1}))return;let i=this.placement;if(i==="left"||i==="right"){this.style.removeProperty("--media-tooltip-offset-x");return}let a=getComputedStyle(this),r=(e=He(this,"#"+this.bounds))!=null?e:Z(this);if(!r)return;let{x:o,width:s}=r.getBoundingClientRect(),{x:l,width:u}=this.getBoundingClientRect(),m=l+u,_=o+s,g=a.getPropertyValue("--media-tooltip-offset-x"),f=g?parseFloat(g.replace("px","")):0,v=a.getPropertyValue("--media-tooltip-container-margin"),N=v?parseFloat(v.replace("px","")):0,T=l-o+f-N,C=m-_+f+N;if(T<0){this.style.setProperty("--media-tooltip-offset-x",`${T}px`);return}if(C>0){this.style.setProperty("--media-tooltip-offset-x",`${C}px`);return}this.style.removeProperty("--media-tooltip-offset-x")},!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}if(this.arrowEl=this.shadowRoot.querySelector("#arrow"),Object.prototype.hasOwnProperty.call(this,"placement")){let e=this.placement;delete this.placement,this.placement=e}}static get observedAttributes(){return[Ri.PLACEMENT,Ri.BOUNDS]}get placement(){return I(this,Ri.PLACEMENT)}set placement(e){M(this,Ri.PLACEMENT,e)}get bounds(){return I(this,Ri.BOUNDS)}set bounds(e){M(this,Ri.BOUNDS,e)}};xi.shadowRootOptions={mode:"open"};xi.getTemplateHTML=yh;d.customElements.get("media-tooltip")||d.customElements.define("media-tooltip",xi);var vo=xi;var As=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},J=(t,e,i)=>(As(t,e,"read from private field"),i?i.call(t):e.get(t)),Di=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Eo=(t,e,i,a)=>(As(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),kh=(t,e,i)=>(As(t,e,"access private method"),i),$e,Ni,It,Oi,bo,_s,Tu,St={TOOLTIP_PLACEMENT:"tooltipplacement",DISABLED:"disabled",NO_TOOLTIP:"notooltip"};function Sh(t,e={}){return`
    <style>
      :host {
        position: relative;
        font: var(--media-font,
          var(--media-font-weight, bold)
          var(--media-font-size, 14px) /
          var(--media-text-content-height, var(--media-control-height, 24px))
          var(--media-font-family, helvetica neue, segoe ui, roboto, arial, sans-serif));
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        background: var(--media-control-background, var(--media-secondary-color, rgb(20 20 30 / .7)));
        padding: var(--media-button-padding, var(--media-control-padding, 10px));
        justify-content: var(--media-button-justify-content, center);
        display: inline-flex;
        align-items: center;
        vertical-align: middle;
        box-sizing: border-box;
        transition: background .15s linear;
        pointer-events: auto;
        cursor: var(--media-cursor, pointer);
        -webkit-tap-highlight-color: transparent;
      }

      
      :host(:focus-visible) {
        box-shadow: inset 0 0 0 2px rgb(27 127 204 / .9);
        outline: 0;
      }
      
      :host(:where(:focus)) {
        box-shadow: none;
        outline: 0;
      }

      :host(:hover) {
        background: var(--media-control-hover-background, rgba(50 50 70 / .7));
      }

      svg, img, ::slotted(svg), ::slotted(img) {
        width: var(--media-button-icon-width);
        height: var(--media-button-icon-height, var(--media-control-height, 24px));
        transform: var(--media-button-icon-transform);
        transition: var(--media-button-icon-transition);
        fill: var(--media-icon-color, var(--media-primary-color, rgb(238 238 238)));
        vertical-align: middle;
        max-width: 100%;
        max-height: 100%;
        min-width: 100%;
      }

      media-tooltip {
        
        max-width: 0;
        overflow-x: clip;
        opacity: 0;
        transition: opacity .3s, max-width 0s 9s;
      }

      :host(:hover) media-tooltip,
      :host(:focus-visible) media-tooltip {
        max-width: 100vw;
        opacity: 1;
        transition: opacity .3s;
      }

      :host([notooltip]) slot[name="tooltip"] {
        display: none;
      }
    </style>

    ${this.getSlotTemplateHTML(t,e)}

    <slot name="tooltip">
      <media-tooltip part="tooltip" aria-hidden="true">
        <template shadowrootmode="${vo.shadowRootOptions.mode}">
          ${vo.getTemplateHTML({})}
        </template>
        <slot name="tooltip-content">
          ${this.getTooltipContentHTML(t)}
        </slot>
      </media-tooltip>
    </slot>
  `}function Ih(t,e){return`
    <slot></slot>
  `}function Mh(){return""}var B=class extends d.HTMLElement{constructor(){if(super(),Di(this,_s),Di(this,$e,void 0),this.preventClick=!1,this.tooltipEl=null,Di(this,Ni,e=>{this.preventClick||this.handleClick(e),setTimeout(J(this,It),0)}),Di(this,It,()=>{var e,i;(i=(e=this.tooltipEl)==null?void 0:e.updateXOffset)==null||i.call(e)}),Di(this,Oi,e=>{let{key:i}=e;if(!this.keysUsed.includes(i)){this.removeEventListener("keyup",J(this,Oi));return}this.preventClick||this.handleClick(e)}),Di(this,bo,e=>{let{metaKey:i,altKey:a,key:r}=e;if(i||a||!this.keysUsed.includes(r)){this.removeEventListener("keyup",J(this,Oi));return}this.addEventListener("keyup",J(this,Oi),{once:!0})}),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes),i=this.constructor.getTemplateHTML(e);this.shadowRoot.setHTMLUnsafe?this.shadowRoot.setHTMLUnsafe(i):this.shadowRoot.innerHTML=i}this.tooltipEl=this.shadowRoot.querySelector("media-tooltip")}static get observedAttributes(){return["disabled",St.TOOLTIP_PLACEMENT,L.MEDIA_CONTROLLER]}enable(){this.addEventListener("click",J(this,Ni)),this.addEventListener("keydown",J(this,bo)),this.tabIndex=0}disable(){this.removeEventListener("click",J(this,Ni)),this.removeEventListener("keydown",J(this,bo)),this.removeEventListener("keyup",J(this,Oi)),this.tabIndex=-1}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===L.MEDIA_CONTROLLER?(i&&((o=(r=J(this,$e))==null?void 0:r.unassociateElement)==null||o.call(r,this),Eo(this,$e,null)),a&&this.isConnected&&(Eo(this,$e,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=J(this,$e))==null?void 0:l.associateElement)==null||u.call(l,this))):e==="disabled"&&a!==i?a==null?this.enable():this.disable():e===St.TOOLTIP_PLACEMENT&&this.tooltipEl&&a!==i&&(this.tooltipEl.placement=a),J(this,It).call(this)}connectedCallback(){var e,i,a;let{style:r}=W(this.shadowRoot,":host");r.setProperty("display",`var(--media-control-display, var(--${this.localName}-display, inline-flex))`),this.hasAttribute("disabled")?this.disable():this.enable(),this.setAttribute("role","button");let o=this.getAttribute(L.MEDIA_CONTROLLER);o&&(Eo(this,$e,(e=this.getRootNode())==null?void 0:e.getElementById(o)),(a=(i=J(this,$e))==null?void 0:i.associateElement)==null||a.call(i,this)),d.customElements.whenDefined("media-tooltip").then(()=>kh(this,_s,Tu).call(this))}disconnectedCallback(){var e,i;this.disable(),(i=(e=J(this,$e))==null?void 0:e.unassociateElement)==null||i.call(e,this),Eo(this,$e,null),this.removeEventListener("mouseenter",J(this,It)),this.removeEventListener("focus",J(this,It)),this.removeEventListener("click",J(this,Ni))}get keysUsed(){return["Enter"," "]}get tooltipPlacement(){return I(this,St.TOOLTIP_PLACEMENT)}set tooltipPlacement(e){M(this,St.TOOLTIP_PLACEMENT,e)}get mediaController(){return I(this,L.MEDIA_CONTROLLER)}set mediaController(e){M(this,L.MEDIA_CONTROLLER,e)}get disabled(){return k(this,St.DISABLED)}set disabled(e){S(this,St.DISABLED,e)}get noTooltip(){return k(this,St.NO_TOOLTIP)}set noTooltip(e){S(this,St.NO_TOOLTIP,e)}handleClick(e){}};$e=new WeakMap;Ni=new WeakMap;It=new WeakMap;Oi=new WeakMap;bo=new WeakMap;_s=new WeakSet;Tu=function(){this.addEventListener("mouseenter",J(this,It)),this.addEventListener("focus",J(this,It)),this.addEventListener("click",J(this,Ni));let t=this.tooltipPlacement;t&&this.tooltipEl&&(this.tooltipEl.placement=t)};B.shadowRootOptions={mode:"open"};B.getTemplateHTML=Sh;B.getSlotTemplateHTML=Ih;B.getTooltipContentHTML=Mh;d.customElements.get("media-chrome-button")||d.customElements.define("media-chrome-button",B);var yu=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M22.13 3H3.87a.87.87 0 0 0-.87.87v13.26a.87.87 0 0 0 .87.87h3.4L9 16H5V5h16v11h-4l1.72 2h3.4a.87.87 0 0 0 .87-.87V3.87a.87.87 0 0 0-.86-.87Zm-8.75 11.44a.5.5 0 0 0-.76 0l-4.91 5.73a.5.5 0 0 0 .38.83h9.82a.501.501 0 0 0 .38-.83l-4.91-5.73Z"/>
</svg>
`;function Ch(t){return`
    <style>
      :host([${n.MEDIA_IS_AIRPLAYING}]) slot[name=icon] slot:not([name=exit]) {
        display: none !important;
      }

      
      :host(:not([${n.MEDIA_IS_AIRPLAYING}])) slot[name=icon] slot:not([name=enter]) {
        display: none !important;
      }

      :host([${n.MEDIA_IS_AIRPLAYING}]) slot[name=tooltip-enter],
      :host(:not([${n.MEDIA_IS_AIRPLAYING}])) slot[name=tooltip-exit] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="enter">${yu}</slot>
      <slot name="exit">${yu}</slot>
    </slot>
  `}function Lh(){return`
    <slot name="tooltip-enter">${h("start airplay")}</slot>
    <slot name="tooltip-exit">${h("stop airplay")}</slot>
  `}var ku=t=>{let e=t.mediaIsAirplaying?h("stop airplay"):h("start airplay");t.setAttribute("aria-label",e)},Ua=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_IS_AIRPLAYING,n.MEDIA_AIRPLAY_UNAVAILABLE]}connectedCallback(){super.connectedCallback(),ku(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_IS_AIRPLAYING&&ku(this)}get mediaIsAirplaying(){return k(this,n.MEDIA_IS_AIRPLAYING)}set mediaIsAirplaying(e){S(this,n.MEDIA_IS_AIRPLAYING,e)}get mediaAirplayUnavailable(){return I(this,n.MEDIA_AIRPLAY_UNAVAILABLE)}set mediaAirplayUnavailable(e){M(this,n.MEDIA_AIRPLAY_UNAVAILABLE,e)}handleClick(){let e=new d.CustomEvent(p.MEDIA_AIRPLAY_REQUEST,{composed:!0,bubbles:!0});this.dispatchEvent(e)}};Ua.getSlotTemplateHTML=Ch;Ua.getTooltipContentHTML=Lh;d.customElements.get("media-airplay-button")||d.customElements.define("media-airplay-button",Ua);var wh=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M22.83 5.68a2.58 2.58 0 0 0-2.3-2.5c-3.62-.24-11.44-.24-15.06 0a2.58 2.58 0 0 0-2.3 2.5c-.23 4.21-.23 8.43 0 12.64a2.58 2.58 0 0 0 2.3 2.5c3.62.24 11.44.24 15.06 0a2.58 2.58 0 0 0 2.3-2.5c.23-4.21.23-8.43 0-12.64Zm-11.39 9.45a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.92 3.92 0 0 1 .92-2.77 3.18 3.18 0 0 1 2.43-1 2.94 2.94 0 0 1 2.13.78c.364.359.62.813.74 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.17 1.61 1.61 0 0 0-1.29.58 2.79 2.79 0 0 0-.5 1.89 3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.48 1.48 0 0 0 1-.37 2.1 2.1 0 0 0 .59-1.14l1.4.44a3.23 3.23 0 0 1-1.07 1.69Zm7.22 0a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.88 3.88 0 0 1 .93-2.77 3.14 3.14 0 0 1 2.42-1 3 3 0 0 1 2.16.82 2.8 2.8 0 0 1 .73 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.21 1.61 1.61 0 0 0-1.29.58A2.79 2.79 0 0 0 15 12a3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.44 1.44 0 0 0 1-.37 2.1 2.1 0 0 0 .6-1.15l1.4.44a3.17 3.17 0 0 1-1.1 1.7Z"/>
</svg>`,Rh=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M17.73 14.09a1.4 1.4 0 0 1-1 .37 1.579 1.579 0 0 1-1.27-.58A3 3 0 0 1 15 12a2.8 2.8 0 0 1 .5-1.85 1.63 1.63 0 0 1 1.29-.57 1.47 1.47 0 0 1 1.51 1.2l1.43-.34A2.89 2.89 0 0 0 19 9.07a3 3 0 0 0-2.14-.78 3.14 3.14 0 0 0-2.42 1 3.91 3.91 0 0 0-.93 2.78 3.74 3.74 0 0 0 .92 2.66 3.07 3.07 0 0 0 2.34 1 3.07 3.07 0 0 0 1.91-.57 3.17 3.17 0 0 0 1.07-1.74l-1.4-.45c-.083.43-.3.822-.62 1.12Zm-7.22 0a1.43 1.43 0 0 1-1 .37 1.58 1.58 0 0 1-1.27-.58A3 3 0 0 1 7.76 12a2.8 2.8 0 0 1 .5-1.85 1.63 1.63 0 0 1 1.29-.57 1.47 1.47 0 0 1 1.51 1.2l1.43-.34a2.81 2.81 0 0 0-.74-1.32 2.94 2.94 0 0 0-2.13-.78 3.18 3.18 0 0 0-2.43 1 4 4 0 0 0-.92 2.78 3.74 3.74 0 0 0 .92 2.66 3.07 3.07 0 0 0 2.34 1 3.07 3.07 0 0 0 1.91-.57 3.23 3.23 0 0 0 1.07-1.74l-1.4-.45a2.06 2.06 0 0 1-.6 1.07Zm12.32-8.41a2.59 2.59 0 0 0-2.3-2.51C18.72 3.05 15.86 3 13 3c-2.86 0-5.72.05-7.53.17a2.59 2.59 0 0 0-2.3 2.51c-.23 4.207-.23 8.423 0 12.63a2.57 2.57 0 0 0 2.3 2.5c1.81.13 4.67.19 7.53.19 2.86 0 5.72-.06 7.53-.19a2.57 2.57 0 0 0 2.3-2.5c.23-4.207.23-8.423 0-12.63Zm-1.49 12.53a1.11 1.11 0 0 1-.91 1.11c-1.67.11-4.45.18-7.43.18-2.98 0-5.76-.07-7.43-.18a1.11 1.11 0 0 1-.91-1.11c-.21-4.14-.21-8.29 0-12.43a1.11 1.11 0 0 1 .91-1.11C7.24 4.56 10 4.49 13 4.49s5.76.07 7.43.18a1.11 1.11 0 0 1 .91 1.11c.21 4.14.21 8.29 0 12.43Z"/>
</svg>`;function xh(t){return`
    <style>
      :host([aria-checked="true"]) slot[name=off] {
        display: none !important;
      }

      
      :host(:not([aria-checked="true"])) slot[name=on] {
        display: none !important;
      }

      :host([aria-checked="true"]) slot[name=tooltip-enable],
      :host(:not([aria-checked="true"])) slot[name=tooltip-disable] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="on">${wh}</slot>
      <slot name="off">${Rh}</slot>
    </slot>
  `}function Dh(){return`
    <slot name="tooltip-enable">${h("Enable captions")}</slot>
    <slot name="tooltip-disable">${h("Disable captions")}</slot>
  `}var Su=t=>{t.setAttribute("aria-checked",no(t).toString())},Ha=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_SUBTITLES_LIST,n.MEDIA_SUBTITLES_SHOWING]}connectedCallback(){super.connectedCallback(),this.setAttribute("role","switch"),this.setAttribute("aria-label",h("closed captions")),Su(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_SUBTITLES_SHOWING&&Su(this)}get mediaSubtitlesList(){return Iu(this,n.MEDIA_SUBTITLES_LIST)}set mediaSubtitlesList(e){Mu(this,n.MEDIA_SUBTITLES_LIST,e)}get mediaSubtitlesShowing(){return Iu(this,n.MEDIA_SUBTITLES_SHOWING)}set mediaSubtitlesShowing(e){Mu(this,n.MEDIA_SUBTITLES_SHOWING,e)}handleClick(){this.dispatchEvent(new d.CustomEvent(p.MEDIA_TOGGLE_SUBTITLES_REQUEST,{composed:!0,bubbles:!0}))}};Ha.getSlotTemplateHTML=xh;Ha.getTooltipContentHTML=Dh;var Iu=(t,e)=>{let i=t.getAttribute(e);return i?Gt(i):[]},Mu=(t,e,i)=>{if(!(i!=null&&i.length)){t.removeAttribute(e);return}let a=ut(i);t.getAttribute(e)!==a&&t.setAttribute(e,a)};d.customElements.get("media-captions-button")||d.customElements.define("media-captions-button",Ha);var Oh='<svg aria-hidden="true" viewBox="0 0 24 24"><g><path class="cast_caf_icon_arch0" d="M1,18 L1,21 L4,21 C4,19.3 2.66,18 1,18 L1,18 Z"/><path class="cast_caf_icon_arch1" d="M1,14 L1,16 C3.76,16 6,18.2 6,21 L8,21 C8,17.13 4.87,14 1,14 L1,14 Z"/><path class="cast_caf_icon_arch2" d="M1,10 L1,12 C5.97,12 10,16.0 10,21 L12,21 C12,14.92 7.07,10 1,10 L1,10 Z"/><path class="cast_caf_icon_box" d="M21,3 L3,3 C1.9,3 1,3.9 1,5 L1,8 L3,8 L3,5 L21,5 L21,19 L14,19 L14,21 L21,21 C22.1,21 23,20.1 23,19 L23,5 C23,3.9 22.1,3 21,3 L21,3 Z"/></g></svg>',Nh='<svg aria-hidden="true" viewBox="0 0 24 24"><g><path class="cast_caf_icon_arch0" d="M1,18 L1,21 L4,21 C4,19.3 2.66,18 1,18 L1,18 Z"/><path class="cast_caf_icon_arch1" d="M1,14 L1,16 C3.76,16 6,18.2 6,21 L8,21 C8,17.13 4.87,14 1,14 L1,14 Z"/><path class="cast_caf_icon_arch2" d="M1,10 L1,12 C5.97,12 10,16.0 10,21 L12,21 C12,14.92 7.07,10 1,10 L1,10 Z"/><path class="cast_caf_icon_box" d="M21,3 L3,3 C1.9,3 1,3.9 1,5 L1,8 L3,8 L3,5 L21,5 L21,19 L14,19 L14,21 L21,21 C22.1,21 23,20.1 23,19 L23,5 C23,3.9 22.1,3 21,3 L21,3 Z"/><path class="cast_caf_icon_boxfill" d="M5,7 L5,8.63 C8,8.6 13.37,14 13.37,17 L19,17 L19,7 Z"/></g></svg>';function Ph(t){return`
    <style>
      :host([${n.MEDIA_IS_CASTING}]) slot[name=icon] slot:not([name=exit]) {
        display: none !important;
      }

      
      :host(:not([${n.MEDIA_IS_CASTING}])) slot[name=icon] slot:not([name=enter]) {
        display: none !important;
      }

      :host([${n.MEDIA_IS_CASTING}]) slot[name=tooltip-enter],
      :host(:not([${n.MEDIA_IS_CASTING}])) slot[name=tooltip-exit] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="enter">${Oh}</slot>
      <slot name="exit">${Nh}</slot>
    </slot>
  `}function Uh(){return`
    <slot name="tooltip-enter">${h("Start casting")}</slot>
    <slot name="tooltip-exit">${h("Stop casting")}</slot>
  `}var Cu=t=>{let e=t.mediaIsCasting?h("stop casting"):h("start casting");t.setAttribute("aria-label",e)},Ba=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_IS_CASTING,n.MEDIA_CAST_UNAVAILABLE]}connectedCallback(){super.connectedCallback(),Cu(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_IS_CASTING&&Cu(this)}get mediaIsCasting(){return k(this,n.MEDIA_IS_CASTING)}set mediaIsCasting(e){S(this,n.MEDIA_IS_CASTING,e)}get mediaCastUnavailable(){return I(this,n.MEDIA_CAST_UNAVAILABLE)}set mediaCastUnavailable(e){M(this,n.MEDIA_CAST_UNAVAILABLE,e)}handleClick(){let e=this.mediaIsCasting?p.MEDIA_EXIT_CAST_REQUEST:p.MEDIA_ENTER_CAST_REQUEST;this.dispatchEvent(new d.CustomEvent(e,{composed:!0,bubbles:!0}))}};Ba.getSlotTemplateHTML=Ph;Ba.getTooltipContentHTML=Uh;d.customElements.get("media-cast-button")||d.customElements.define("media-cast-button",Ba);var Cs=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Xt=(t,e,i)=>(Cs(t,e,"read from private field"),i?i.call(t):e.get(t)),mt=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Ls=(t,e,i,a)=>(Cs(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Qt=(t,e,i)=>(Cs(t,e,"access private method"),i),_o,Wa,Jt,go,Ts,ys,Lu,ks,wu,Ss,Ru,Is,xu,Ms,Du;function Hh(t){return`
    <style>
      :host {
        font: var(--media-font,
          var(--media-font-weight, normal)
          var(--media-font-size, 14px) /
          var(--media-text-content-height, var(--media-control-height, 24px))
          var(--media-font-family, helvetica neue, segoe ui, roboto, arial, sans-serif));
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        display: var(--media-dialog-display, inline-flex);
        justify-content: center;
        align-items: center;
        
        transition-behavior: allow-discrete;
        visibility: hidden;
        opacity: 0;
        transform: translateY(2px) scale(.99);
        pointer-events: none;
      }

      :host([open]) {
        transition: display .2s, visibility 0s, opacity .2s ease-out, transform .15s ease-out;
        visibility: visible;
        opacity: 1;
        transform: translateY(0) scale(1);
        pointer-events: auto;
      }

      #content {
        display: flex;
        position: relative;
        box-sizing: border-box;
        width: min(320px, 100%);
        word-wrap: break-word;
        max-height: 100%;
        overflow: auto;
        text-align: center;
        line-height: 1.4;
      }
    </style>
    ${this.getSlotTemplateHTML(t)}
  `}function Bh(t){return`
    <slot id="content"></slot>
  `}var $a={OPEN:"open",ANCHOR:"anchor"},Mt=class extends d.HTMLElement{constructor(){super(),mt(this,go),mt(this,ys),mt(this,ks),mt(this,Ss),mt(this,Is),mt(this,Ms),mt(this,_o,!1),mt(this,Wa,null),mt(this,Jt,null),this.addEventListener("invoke",this),this.addEventListener("focusout",this),this.addEventListener("keydown",this)}static get observedAttributes(){return[$a.OPEN,$a.ANCHOR]}get open(){return k(this,$a.OPEN)}set open(e){S(this,$a.OPEN,e)}handleEvent(e){switch(e.type){case"invoke":Qt(this,Ss,Ru).call(this,e);break;case"focusout":Qt(this,Is,xu).call(this,e);break;case"keydown":Qt(this,Ms,Du).call(this,e);break}}connectedCallback(){Qt(this,go,Ts).call(this),this.role||(this.role="dialog")}attributeChangedCallback(e,i,a){Qt(this,go,Ts).call(this),e===$a.OPEN&&a!==i&&(this.open?Qt(this,ys,Lu).call(this):Qt(this,ks,wu).call(this))}focus(){Ls(this,Wa,Ma());let e=!this.dispatchEvent(new Event("focus",{composed:!0,cancelable:!0})),i=!this.dispatchEvent(new Event("focusin",{composed:!0,bubbles:!0,cancelable:!0}));if(e||i)return;let a=this.querySelector('[autofocus], [tabindex]:not([tabindex="-1"]), [role="menu"]');a==null||a.focus()}get keysUsed(){return["Escape","Tab"]}};_o=new WeakMap;Wa=new WeakMap;Jt=new WeakMap;go=new WeakSet;Ts=function(){if(!Xt(this,_o)&&(Ls(this,_o,!0),!this.shadowRoot)){this.attachShadow(this.constructor.shadowRootOptions);let t=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(t),queueMicrotask(()=>{let{style:e}=W(this.shadowRoot,":host");e.setProperty("transition","display .15s, visibility .15s, opacity .15s ease-in, transform .15s ease-in")})}};ys=new WeakSet;Lu=function(){var t;(t=Xt(this,Jt))==null||t.setAttribute("aria-expanded","true"),this.dispatchEvent(new Event("open",{composed:!0,bubbles:!0})),this.addEventListener("transitionend",()=>this.focus(),{once:!0})};ks=new WeakSet;wu=function(){var t;(t=Xt(this,Jt))==null||t.setAttribute("aria-expanded","false"),this.dispatchEvent(new Event("close",{composed:!0,bubbles:!0}))};Ss=new WeakSet;Ru=function(t){Ls(this,Jt,t.relatedTarget),de(this,t.relatedTarget)||(this.open=!this.open)};Is=new WeakSet;xu=function(t){var e;de(this,t.relatedTarget)||((e=Xt(this,Wa))==null||e.focus(),Xt(this,Jt)&&Xt(this,Jt)!==t.relatedTarget&&this.open&&(this.open=!1))};Ms=new WeakSet;Du=function(t){var e,i,a,r,o;let{key:s,ctrlKey:l,altKey:u,metaKey:m}=t;l||u||m||this.keysUsed.includes(s)&&(t.preventDefault(),t.stopPropagation(),s==="Tab"?(t.shiftKey?(i=(e=this.previousElementSibling)==null?void 0:e.focus)==null||i.call(e):(r=(a=this.nextElementSibling)==null?void 0:a.focus)==null||r.call(a),this.blur()):s==="Escape"&&((o=Xt(this,Wa))==null||o.focus(),this.open=!1))};Mt.shadowRootOptions={mode:"open"};Mt.getTemplateHTML=Hh;Mt.getSlotTemplateHTML=Bh;d.customElements.get("media-chrome-dialog")||d.customElements.define("media-chrome-dialog",Mt);var Ps=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},z=(t,e,i)=>(Ps(t,e,"read from private field"),i?i.call(t):e.get(t)),ne=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Ct=(t,e,i,a)=>(Ps(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Oe=(t,e,i)=>(Ps(t,e,"access private method"),i),We,wo,Ao,To,Ne,Co,yo,ko,So,Us,Ou,Io,ws,Mo,Rs,Lo,Hs,xs,Nu,Ds,Pu,Os,Uu,Ns,Hu;function $h(t){return`
    <style>
      :host {
        --_focus-box-shadow: var(--media-focus-box-shadow, inset 0 0 0 2px rgb(27 127 204 / .9));
        --_media-range-padding: var(--media-range-padding, var(--media-control-padding, 10px));

        box-shadow: var(--_focus-visible-box-shadow, none);
        background: var(--media-control-background, var(--media-secondary-color, rgb(20 20 30 / .7)));
        height: calc(var(--media-control-height, 24px) + 2 * var(--_media-range-padding));
        display: inline-flex;
        align-items: center;
        
        vertical-align: middle;
        box-sizing: border-box;
        position: relative;
        width: 100px;
        transition: background .15s linear;
        cursor: var(--media-cursor, pointer);
        pointer-events: auto;
        touch-action: none; 
      }

      
      input[type=range]:focus {
        outline: 0;
      }
      input[type=range]:focus::-webkit-slider-runnable-track {
        outline: 0;
      }

      :host(:hover) {
        background: var(--media-control-hover-background, rgb(50 50 70 / .7));
      }

      #leftgap {
        padding-left: var(--media-range-padding-left, var(--_media-range-padding));
      }

      #rightgap {
        padding-right: var(--media-range-padding-right, var(--_media-range-padding));
      }

      #startpoint,
      #endpoint {
        position: absolute;
      }

      #endpoint {
        right: 0;
      }

      #container {
        
        width: var(--media-range-track-width, 100%);
        transform: translate(var(--media-range-track-translate-x, 0px), var(--media-range-track-translate-y, 0px));
        position: relative;
        height: 100%;
        display: flex;
        align-items: center;
        min-width: 40px;
      }

      #range {
        
        display: var(--media-time-range-hover-display, block);
        bottom: var(--media-time-range-hover-bottom, -7px);
        height: var(--media-time-range-hover-height, max(100% + 7px, 25px));
        width: 100%;
        position: absolute;
        cursor: var(--media-cursor, pointer);

        -webkit-appearance: none; 
        -webkit-tap-highlight-color: transparent;
        background: transparent; 
        margin: 0;
        z-index: 1;
      }

      @media (hover: hover) {
        #range {
          bottom: var(--media-time-range-hover-bottom, -5px);
          height: var(--media-time-range-hover-height, max(100% + 5px, 20px));
        }
      }

      
      
      #range::-webkit-slider-thumb {
        -webkit-appearance: none;
        background: transparent;
        width: .1px;
        height: .1px;
      }

      
      #range::-moz-range-thumb {
        background: transparent;
        border: transparent;
        width: .1px;
        height: .1px;
      }

      #appearance {
        height: var(--media-range-track-height, 4px);
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        position: absolute;
        
        will-change: transform;
      }

      #track {
        background: var(--media-range-track-background, rgb(255 255 255 / .2));
        border-radius: var(--media-range-track-border-radius, 1px);
        border: var(--media-range-track-border, none);
        outline: var(--media-range-track-outline);
        outline-offset: var(--media-range-track-outline-offset);
        backdrop-filter: var(--media-range-track-backdrop-filter);
        -webkit-backdrop-filter: var(--media-range-track-backdrop-filter);
        box-shadow: var(--media-range-track-box-shadow, none);
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      #progress,
      #pointer {
        position: absolute;
        height: 100%;
        will-change: width;
      }

      #progress {
        background: var(--media-range-bar-color, var(--media-primary-color, rgb(238 238 238)));
        transition: var(--media-range-track-transition);
      }

      #pointer {
        background: var(--media-range-track-pointer-background);
        border-right: var(--media-range-track-pointer-border-right);
        transition: visibility .25s, opacity .25s;
        visibility: hidden;
        opacity: 0;
      }

      @media (hover: hover) {
        :host(:hover) #pointer {
          transition: visibility .5s, opacity .5s;
          visibility: visible;
          opacity: 1;
        }
      }

      #thumb,
      ::slotted([slot=thumb]) {
        width: var(--media-range-thumb-width, 10px);
        height: var(--media-range-thumb-height, 10px);
        transition: var(--media-range-thumb-transition);
        transform: var(--media-range-thumb-transform, none);
        opacity: var(--media-range-thumb-opacity, 1);
        translate: -50%;
        position: absolute;
        left: 0;
        cursor: var(--media-cursor, pointer);
      }

      #thumb {
        border-radius: var(--media-range-thumb-border-radius, 10px);
        background: var(--media-range-thumb-background, var(--media-primary-color, rgb(238 238 238)));
        box-shadow: var(--media-range-thumb-box-shadow, 1px 1px 1px transparent);
        border: var(--media-range-thumb-border, none);
      }

      :host([disabled]) #thumb {
        background-color: #777;
      }

      .segments #appearance {
        height: var(--media-range-segment-hover-height, 7px);
      }

      #track {
        clip-path: url(#segments-clipping);
      }

      #segments {
        --segments-gap: var(--media-range-segments-gap, 2px);
        position: absolute;
        width: 100%;
        height: 100%;
      }

      #segments-clipping {
        transform: translateX(calc(var(--segments-gap) / 2));
      }

      #segments-clipping:empty {
        display: none;
      }

      #segments-clipping rect {
        height: var(--media-range-track-height, 4px);
        y: calc((var(--media-range-segment-hover-height, 7px) - var(--media-range-track-height, 4px)) / 2);
        transition: var(--media-range-segment-transition, transform .1s ease-in-out);
        transform: var(--media-range-segment-transform, scaleY(1));
        transform-origin: center;
      }
    </style>
    <div id="leftgap"></div>
    <div id="container">
      <div id="startpoint"></div>
      <div id="endpoint"></div>
      <div id="appearance">
        <div id="track" part="track">
          <div id="pointer"></div>
          <div id="progress" part="progress"></div>
        </div>
        <slot name="thumb">
          <div id="thumb" part="thumb"></div>
        </slot>
        <svg id="segments"><clipPath id="segments-clipping"></clipPath></svg>
      </div>
      <input id="range" type="range" min="0" max="1" step="any" value="0">
    </div>
    <div id="rightgap"></div>
  `}var Fe=class extends d.HTMLElement{constructor(){if(super(),ne(this,Us),ne(this,Io),ne(this,Mo),ne(this,Lo),ne(this,xs),ne(this,Ds),ne(this,Os),ne(this,Ns),ne(this,We,void 0),ne(this,wo,void 0),ne(this,Ao,void 0),ne(this,To,void 0),ne(this,Ne,{}),ne(this,Co,[]),ne(this,yo,()=>{if(this.range.matches(":focus-visible")){let{style:e}=W(this.shadowRoot,":host");e.setProperty("--_focus-visible-box-shadow","var(--_focus-box-shadow)")}}),ne(this,ko,()=>{let{style:e}=W(this.shadowRoot,":host");e.removeProperty("--_focus-visible-box-shadow")}),ne(this,So,()=>{let e=this.shadowRoot.querySelector("#segments-clipping");e&&e.parentNode.append(e)}),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes),i=this.constructor.getTemplateHTML(e);this.shadowRoot.setHTMLUnsafe?this.shadowRoot.setHTMLUnsafe(i):this.shadowRoot.innerHTML=i}this.container=this.shadowRoot.querySelector("#container"),Ct(this,Ao,this.shadowRoot.querySelector("#startpoint")),Ct(this,To,this.shadowRoot.querySelector("#endpoint")),this.range=this.shadowRoot.querySelector("#range"),this.appearance=this.shadowRoot.querySelector("#appearance")}static get observedAttributes(){return["disabled","aria-disabled",L.MEDIA_CONTROLLER]}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===L.MEDIA_CONTROLLER?(i&&((o=(r=z(this,We))==null?void 0:r.unassociateElement)==null||o.call(r,this),Ct(this,We,null)),a&&this.isConnected&&(Ct(this,We,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=z(this,We))==null?void 0:l.associateElement)==null||u.call(l,this))):(e==="disabled"||e==="aria-disabled"&&i!==a)&&(a==null?(this.range.removeAttribute(e),Oe(this,Io,ws).call(this)):(this.range.setAttribute(e,a),Oe(this,Mo,Rs).call(this)))}connectedCallback(){var e,i,a;let{style:r}=W(this.shadowRoot,":host");r.setProperty("display",`var(--media-control-display, var(--${this.localName}-display, inline-flex))`),z(this,Ne).pointer=W(this.shadowRoot,"#pointer"),z(this,Ne).progress=W(this.shadowRoot,"#progress"),z(this,Ne).thumb=W(this.shadowRoot,'#thumb, ::slotted([slot="thumb"])'),z(this,Ne).activeSegment=W(this.shadowRoot,"#segments-clipping rect:nth-child(0)");let o=this.getAttribute(L.MEDIA_CONTROLLER);o&&(Ct(this,We,(e=this.getRootNode())==null?void 0:e.getElementById(o)),(a=(i=z(this,We))==null?void 0:i.associateElement)==null||a.call(i,this)),this.updateBar(),this.shadowRoot.addEventListener("focusin",z(this,yo)),this.shadowRoot.addEventListener("focusout",z(this,ko)),Oe(this,Io,ws).call(this),st(this.container,z(this,So))}disconnectedCallback(){var e,i;Oe(this,Mo,Rs).call(this),(i=(e=z(this,We))==null?void 0:e.unassociateElement)==null||i.call(e,this),Ct(this,We,null),this.shadowRoot.removeEventListener("focusin",z(this,yo)),this.shadowRoot.removeEventListener("focusout",z(this,ko)),lt(this.container,z(this,So))}updatePointerBar(e){var i;(i=z(this,Ne).pointer)==null||i.style.setProperty("width",`${this.getPointerRatio(e)*100}%`)}updateBar(){var e,i;let a=this.range.valueAsNumber*100;(e=z(this,Ne).progress)==null||e.style.setProperty("width",`${a}%`),(i=z(this,Ne).thumb)==null||i.style.setProperty("left",`${a}%`)}updateSegments(e){let i=this.shadowRoot.querySelector("#segments-clipping");if(i.textContent="",this.container.classList.toggle("segments",!!(e!=null&&e.length)),!(e!=null&&e.length))return;let a=[...new Set([+this.range.min,...e.flatMap(o=>[o.start,o.end]),+this.range.max])];Ct(this,Co,[...a]);let r=a.pop();for(let[o,s]of a.entries()){let[l,u]=[o===0,o===a.length-1],m=l?"calc(var(--segments-gap) / -1)":`${s*100}%`,g=`calc(${((u?r:a[o+1])-s)*100}%${l||u?"":" - var(--segments-gap)"})`,f=V.createElementNS("http://www.w3.org/2000/svg","rect"),v=W(this.shadowRoot,`#segments-clipping rect:nth-child(${o+1})`);v.style.setProperty("x",m),v.style.setProperty("width",g),i.append(f)}}getPointerRatio(e){return Zd(e.clientX,e.clientY,z(this,Ao).getBoundingClientRect(),z(this,To).getBoundingClientRect())}get dragging(){return this.hasAttribute("dragging")}handleEvent(e){switch(e.type){case"pointermove":Oe(this,Ns,Hu).call(this,e);break;case"input":this.updateBar();break;case"pointerenter":Oe(this,xs,Nu).call(this,e);break;case"pointerdown":Oe(this,Lo,Hs).call(this,e);break;case"pointerup":Oe(this,Ds,Pu).call(this);break;case"pointerleave":Oe(this,Os,Uu).call(this);break}}get keysUsed(){return["ArrowUp","ArrowRight","ArrowDown","ArrowLeft"]}};We=new WeakMap;wo=new WeakMap;Ao=new WeakMap;To=new WeakMap;Ne=new WeakMap;Co=new WeakMap;yo=new WeakMap;ko=new WeakMap;So=new WeakMap;Us=new WeakSet;Ou=function(t){let e=z(this,Ne).activeSegment;if(!e)return;let i=this.getPointerRatio(t),r=`#segments-clipping rect:nth-child(${z(this,Co).findIndex((o,s,l)=>{let u=l[s+1];return u!=null&&i>=o&&i<=u})+1})`;(e.selectorText!=r||!e.style.transform)&&(e.selectorText=r,e.style.setProperty("transform","var(--media-range-segment-hover-transform, scaleY(2))"))};Io=new WeakSet;ws=function(){this.hasAttribute("disabled")||(this.addEventListener("input",this),this.addEventListener("pointerdown",this),this.addEventListener("pointerenter",this))};Mo=new WeakSet;Rs=function(){var t,e;this.removeEventListener("input",this),this.removeEventListener("pointerdown",this),this.removeEventListener("pointerenter",this),(t=d.window)==null||t.removeEventListener("pointerup",this),(e=d.window)==null||e.removeEventListener("pointermove",this)};Lo=new WeakSet;Hs=function(t){var e;Ct(this,wo,t.composedPath().includes(this.range)),(e=d.window)==null||e.addEventListener("pointerup",this)};xs=new WeakSet;Nu=function(t){var e;t.pointerType!=="mouse"&&Oe(this,Lo,Hs).call(this,t),this.addEventListener("pointerleave",this),(e=d.window)==null||e.addEventListener("pointermove",this)};Ds=new WeakSet;Pu=function(){var t;(t=d.window)==null||t.removeEventListener("pointerup",this),this.toggleAttribute("dragging",!1),this.range.disabled=this.hasAttribute("disabled")};Os=new WeakSet;Uu=function(){var t,e;this.removeEventListener("pointerleave",this),(t=d.window)==null||t.removeEventListener("pointermove",this),this.toggleAttribute("dragging",!1),this.range.disabled=this.hasAttribute("disabled"),(e=z(this,Ne).activeSegment)==null||e.style.removeProperty("transform")};Ns=new WeakSet;Hu=function(t){this.toggleAttribute("dragging",t.buttons===1||t.pointerType!=="mouse"),this.updatePointerBar(t),Oe(this,Us,Ou).call(this,t),this.dragging&&(t.pointerType!=="mouse"||!z(this,wo))&&(this.range.disabled=!0,this.range.valueAsNumber=this.getPointerRatio(t),this.range.dispatchEvent(new Event("input",{bubbles:!0,composed:!0})))};Fe.shadowRootOptions={mode:"open"};Fe.getTemplateHTML=$h;d.customElements.get("media-chrome-range")||d.customElements.define("media-chrome-range",Fe);var Bu=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Ro=(t,e,i)=>(Bu(t,e,"read from private field"),i?i.call(t):e.get(t)),Wh=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},xo=(t,e,i,a)=>(Bu(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Ve;function Fh(t){return`
    <style>
      :host {
        
        box-sizing: border-box;
        display: var(--media-control-display, var(--media-control-bar-display, inline-flex));
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        --media-loading-indicator-icon-height: 44px;
      }

      ::slotted(media-time-range),
      ::slotted(media-volume-range) {
        min-height: 100%;
      }

      ::slotted(media-time-range),
      ::slotted(media-clip-selector) {
        flex-grow: 1;
      }

      ::slotted([role="menu"]) {
        position: absolute;
      }
    </style>

    <slot></slot>
  `}var Fa=class extends d.HTMLElement{constructor(){if(super(),Wh(this,Ve,void 0),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}}static get observedAttributes(){return[L.MEDIA_CONTROLLER]}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===L.MEDIA_CONTROLLER&&(i&&((o=(r=Ro(this,Ve))==null?void 0:r.unassociateElement)==null||o.call(r,this),xo(this,Ve,null)),a&&this.isConnected&&(xo(this,Ve,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=Ro(this,Ve))==null?void 0:l.associateElement)==null||u.call(l,this)))}connectedCallback(){var e,i,a;let r=this.getAttribute(L.MEDIA_CONTROLLER);r&&(xo(this,Ve,(e=this.getRootNode())==null?void 0:e.getElementById(r)),(a=(i=Ro(this,Ve))==null?void 0:i.associateElement)==null||a.call(i,this))}disconnectedCallback(){var e,i;(i=(e=Ro(this,Ve))==null?void 0:e.unassociateElement)==null||i.call(e,this),xo(this,Ve,null)}};Ve=new WeakMap;Fa.shadowRootOptions={mode:"open"};Fa.getTemplateHTML=Fh;d.customElements.get("media-control-bar")||d.customElements.define("media-control-bar",Fa);var $u=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Do=(t,e,i)=>($u(t,e,"read from private field"),i?i.call(t):e.get(t)),Vh=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Oo=(t,e,i,a)=>($u(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Ke;function Kh(t,e={}){return`
    <style>
      :host {
        font: var(--media-font,
          var(--media-font-weight, normal)
          var(--media-font-size, 14px) /
          var(--media-text-content-height, var(--media-control-height, 24px))
          var(--media-font-family, helvetica neue, segoe ui, roboto, arial, sans-serif));
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        background: var(--media-text-background, var(--media-control-background, var(--media-secondary-color, rgb(20 20 30 / .7))));
        padding: var(--media-control-padding, 10px);
        display: inline-flex;
        justify-content: center;
        align-items: center;
        vertical-align: middle;
        box-sizing: border-box;
        text-align: center;
        pointer-events: auto;
      }

      
      :host(:focus-visible) {
        box-shadow: inset 0 0 0 2px rgb(27 127 204 / .9);
        outline: 0;
      }

      
      :host(:where(:focus)) {
        box-shadow: none;
        outline: 0;
      }
    </style>

    ${this.getSlotTemplateHTML(t,e)}
  `}function Gh(t,e){return`
    <slot></slot>
  `}var ve=class extends d.HTMLElement{constructor(){if(super(),Vh(this,Ke,void 0),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}}static get observedAttributes(){return[L.MEDIA_CONTROLLER]}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===L.MEDIA_CONTROLLER&&(i&&((o=(r=Do(this,Ke))==null?void 0:r.unassociateElement)==null||o.call(r,this),Oo(this,Ke,null)),a&&this.isConnected&&(Oo(this,Ke,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=Do(this,Ke))==null?void 0:l.associateElement)==null||u.call(l,this)))}connectedCallback(){var e,i,a;let{style:r}=W(this.shadowRoot,":host");r.setProperty("display",`var(--media-control-display, var(--${this.localName}-display, inline-flex))`);let o=this.getAttribute(L.MEDIA_CONTROLLER);o&&(Oo(this,Ke,(e=this.getRootNode())==null?void 0:e.getElementById(o)),(a=(i=Do(this,Ke))==null?void 0:i.associateElement)==null||a.call(i,this))}disconnectedCallback(){var e,i;(i=(e=Do(this,Ke))==null?void 0:e.unassociateElement)==null||i.call(e,this),Oo(this,Ke,null)}};Ke=new WeakMap;ve.shadowRootOptions={mode:"open"};ve.getTemplateHTML=Kh;ve.getSlotTemplateHTML=Gh;d.customElements.get("media-text-display")||d.customElements.define("media-text-display",ve);var Fu=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Wu=(t,e,i)=>(Fu(t,e,"read from private field"),i?i.call(t):e.get(t)),Yh=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},qh=(t,e,i,a)=>(Fu(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Va;function Zh(t,e){return`
    <slot>${De(e.mediaDuration)}</slot>
  `}var No=class extends ve{constructor(){var e;super(),Yh(this,Va,void 0),qh(this,Va,this.shadowRoot.querySelector("slot")),Wu(this,Va).textContent=De((e=this.mediaDuration)!=null?e:0)}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_DURATION]}attributeChangedCallback(e,i,a){e===n.MEDIA_DURATION&&(Wu(this,Va).textContent=De(+a)),super.attributeChangedCallback(e,i,a)}get mediaDuration(){return R(this,n.MEDIA_DURATION)}set mediaDuration(e){O(this,n.MEDIA_DURATION,e)}};Va=new WeakMap;No.getSlotTemplateHTML=Zh;d.customElements.get("media-duration-display")||d.customElements.define("media-duration-display",No);var zh={2:h("Network Error"),3:h("Decode Error"),4:h("Source Not Supported"),5:h("Encryption Error")},Qh={2:h("A network error caused the media download to fail."),3:h("A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format."),4:h("An unsupported error occurred. The server or network failed, or your browser does not support this format."),5:h("The media is encrypted and there are no keys to decrypt it.")},Bs=t=>{var e,i;return t.code===1?null:{title:(e=zh[t.code])!=null?e:`Error ${t.code}`,message:(i=Qh[t.code])!=null?i:t.message}};var Ku=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Xh=(t,e,i)=>(Ku(t,e,"read from private field"),i?i.call(t):e.get(t)),Jh=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},jh=(t,e,i,a)=>(Ku(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Po;function ep(t){return`
    <style>
      :host {
        background: rgb(20 20 30 / .8);
      }

      #content {
        display: block;
        padding: 1.2em 1.5em;
      }

      h3,
      p {
        margin-block: 0 .3em;
      }
    </style>
    <slot name="error-${t.mediaerrorcode}" id="content">
      ${Gu({code:+t.mediaerrorcode,message:t.mediaerrormessage})}
    </slot>
  `}function tp(t){return t.code&&Bs(t)!==null}function Gu(t){var e;let{title:i,message:a}=(e=Bs(t))!=null?e:{},r="";return i&&(r+=`<slot name="error-${t.code}-title"><h3>${i}</h3></slot>`),a&&(r+=`<slot name="error-${t.code}-message"><p>${a}</p></slot>`),r}var Vu=[n.MEDIA_ERROR_CODE,n.MEDIA_ERROR_MESSAGE],Pi=class extends Mt{constructor(){super(...arguments),Jh(this,Po,null)}static get observedAttributes(){return[...super.observedAttributes,...Vu]}formatErrorMessage(e){return this.constructor.formatErrorMessage(e)}attributeChangedCallback(e,i,a){var r;if(super.attributeChangedCallback(e,i,a),!Vu.includes(e))return;let o=(r=this.mediaError)!=null?r:{code:this.mediaErrorCode,message:this.mediaErrorMessage};this.open=tp(o),this.open&&(this.shadowRoot.querySelector("slot").name=`error-${this.mediaErrorCode}`,this.shadowRoot.querySelector("#content").innerHTML=this.formatErrorMessage(o))}get mediaError(){return Xh(this,Po)}set mediaError(e){jh(this,Po,e)}get mediaErrorCode(){return R(this,"mediaerrorcode")}set mediaErrorCode(e){O(this,"mediaerrorcode",e)}get mediaErrorMessage(){return I(this,"mediaerrormessage")}set mediaErrorMessage(e){M(this,"mediaerrormessage",e)}};Po=new WeakMap;Pi.getSlotTemplateHTML=ep;Pi.formatErrorMessage=Gu;d.customElements.get("media-error-dialog")||d.customElements.define("media-error-dialog",Pi);var Uo=Pi;var ip=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M16 3v2.5h3.5V9H22V3h-6ZM4 9h2.5V5.5H10V3H4v6Zm15.5 9.5H16V21h6v-6h-2.5v3.5ZM6.5 15H4v6h6v-2.5H6.5V15Z"/>
</svg>`,ap=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M18.5 6.5V3H16v6h6V6.5h-3.5ZM16 21h2.5v-3.5H22V15h-6v6ZM4 17.5h3.5V21H10v-6H4v2.5Zm3.5-11H4V9h6V3H7.5v3.5Z"/>
</svg>`;function rp(t){return`
    <style>
      :host([${n.MEDIA_IS_FULLSCREEN}]) slot[name=icon] slot:not([name=exit]) {
        display: none !important;
      }

      
      :host(:not([${n.MEDIA_IS_FULLSCREEN}])) slot[name=icon] slot:not([name=enter]) {
        display: none !important;
      }

      :host([${n.MEDIA_IS_FULLSCREEN}]) slot[name=tooltip-enter],
      :host(:not([${n.MEDIA_IS_FULLSCREEN}])) slot[name=tooltip-exit] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="enter">${ip}</slot>
      <slot name="exit">${ap}</slot>
    </slot>
  `}function op(){return`
    <slot name="tooltip-enter">${h("Enter fullscreen mode")}</slot>
    <slot name="tooltip-exit">${h("Exit fullscreen mode")}</slot>
  `}var Yu=t=>{let e=t.mediaIsFullscreen?h("exit fullscreen mode"):h("enter fullscreen mode");t.setAttribute("aria-label",e)},Ka=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_IS_FULLSCREEN,n.MEDIA_FULLSCREEN_UNAVAILABLE]}connectedCallback(){super.connectedCallback(),Yu(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_IS_FULLSCREEN&&Yu(this)}get mediaFullscreenUnavailable(){return I(this,n.MEDIA_FULLSCREEN_UNAVAILABLE)}set mediaFullscreenUnavailable(e){M(this,n.MEDIA_FULLSCREEN_UNAVAILABLE,e)}get mediaIsFullscreen(){return k(this,n.MEDIA_IS_FULLSCREEN)}set mediaIsFullscreen(e){S(this,n.MEDIA_IS_FULLSCREEN,e)}handleClick(){let e=this.mediaIsFullscreen?p.MEDIA_EXIT_FULLSCREEN_REQUEST:p.MEDIA_ENTER_FULLSCREEN_REQUEST;this.dispatchEvent(new d.CustomEvent(e,{composed:!0,bubbles:!0}))}};Ka.getSlotTemplateHTML=rp;Ka.getTooltipContentHTML=op;d.customElements.get("media-fullscreen-button")||d.customElements.define("media-fullscreen-button",Ka);var{MEDIA_TIME_IS_LIVE:Ho,MEDIA_PAUSED:Ga}=n,{MEDIA_SEEK_TO_LIVE_REQUEST:np,MEDIA_PLAY_REQUEST:sp}=p,lp='<svg viewBox="0 0 6 12"><circle cx="3" cy="6" r="2"></circle></svg>';function dp(t){return`
    <style>
      :host { --media-tooltip-display: none; }
      
      slot[name=indicator] > *,
      :host ::slotted([slot=indicator]) {
        
        min-width: auto;
        fill: var(--media-live-button-icon-color, rgb(140, 140, 140));
        color: var(--media-live-button-icon-color, rgb(140, 140, 140));
      }

      :host([${Ho}]:not([${Ga}])) slot[name=indicator] > *,
      :host([${Ho}]:not([${Ga}])) ::slotted([slot=indicator]) {
        fill: var(--media-live-button-indicator-color, rgb(255, 0, 0));
        color: var(--media-live-button-indicator-color, rgb(255, 0, 0));
      }

      :host([${Ho}]:not([${Ga}])) {
        cursor: var(--media-cursor, not-allowed);
      }

      slot[name=text]{
        text-transform: uppercase;
      }

    </style>

    <slot name="indicator">${lp}</slot>
    
    <slot name="spacer">&nbsp;</slot><slot name="text">${h("live")}</slot>
  `}var qu=t=>{let e=t.mediaPaused||!t.mediaTimeIsLive,i=e?h("seek to live"):h("playing live");t.setAttribute("aria-label",i),e?t.removeAttribute("aria-disabled"):t.setAttribute("aria-disabled","true")},Bo=class extends B{static get observedAttributes(){return[...super.observedAttributes,Ho,Ga]}connectedCallback(){super.connectedCallback(),qu(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),qu(this)}get mediaPaused(){return k(this,n.MEDIA_PAUSED)}set mediaPaused(e){S(this,n.MEDIA_PAUSED,e)}get mediaTimeIsLive(){return k(this,n.MEDIA_TIME_IS_LIVE)}set mediaTimeIsLive(e){S(this,n.MEDIA_TIME_IS_LIVE,e)}handleClick(){!this.mediaPaused&&this.mediaTimeIsLive||(this.dispatchEvent(new d.CustomEvent(np,{composed:!0,bubbles:!0})),this.hasAttribute(Ga)&&this.dispatchEvent(new d.CustomEvent(sp,{composed:!0,bubbles:!0})))}};Bo.getSlotTemplateHTML=dp;d.customElements.get("media-live-button")||d.customElements.define("media-live-button",Bo);var zu=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Ya=(t,e,i)=>(zu(t,e,"read from private field"),i?i.call(t):e.get(t)),Zu=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},qa=(t,e,i,a)=>(zu(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Ge,Wo,$o={LOADING_DELAY:"loadingdelay",NO_AUTOHIDE:"noautohide"},Qu=500,up=`
<svg aria-hidden="true" viewBox="0 0 100 100">
  <path d="M73,50c0-12.7-10.3-23-23-23S27,37.3,27,50 M30.9,50c0-10.5,8.5-19.1,19.1-19.1S69.1,39.5,69.1,50">
    <animateTransform
       attributeName="transform"
       attributeType="XML"
       type="rotate"
       dur="1s"
       from="0 50 50"
       to="360 50 50"
       repeatCount="indefinite" />
  </path>
</svg>
`;function cp(t){return`
    <style>
      :host {
        display: var(--media-control-display, var(--media-loading-indicator-display, inline-block));
        vertical-align: middle;
        box-sizing: border-box;
        --_loading-indicator-delay: var(--media-loading-indicator-transition-delay, ${Qu}ms);
      }

      #status {
        color: rgba(0,0,0,0);
        width: 0px;
        height: 0px;
      }

      :host slot[name=icon] > *,
      :host ::slotted([slot=icon]) {
        opacity: var(--media-loading-indicator-opacity, 0);
        transition: opacity 0.15s;
      }

      :host([${n.MEDIA_LOADING}]:not([${n.MEDIA_PAUSED}])) slot[name=icon] > *,
      :host([${n.MEDIA_LOADING}]:not([${n.MEDIA_PAUSED}])) ::slotted([slot=icon]) {
        opacity: var(--media-loading-indicator-opacity, 1);
        transition: opacity 0.15s var(--_loading-indicator-delay);
      }

      :host #status {
        visibility: var(--media-loading-indicator-opacity, hidden);
        transition: visibility 0.15s;
      }

      :host([${n.MEDIA_LOADING}]:not([${n.MEDIA_PAUSED}])) #status {
        visibility: var(--media-loading-indicator-opacity, visible);
        transition: visibility 0.15s var(--_loading-indicator-delay);
      }

      svg, img, ::slotted(svg), ::slotted(img) {
        width: var(--media-loading-indicator-icon-width);
        height: var(--media-loading-indicator-icon-height, 100px);
        fill: var(--media-icon-color, var(--media-primary-color, rgb(238 238 238)));
        vertical-align: middle;
      }
    </style>

    <slot name="icon">${up}</slot>
    <div id="status" role="status" aria-live="polite">${h("media loading")}</div>
  `}var Za=class extends d.HTMLElement{constructor(){if(super(),Zu(this,Ge,void 0),Zu(this,Wo,Qu),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}}static get observedAttributes(){return[L.MEDIA_CONTROLLER,n.MEDIA_PAUSED,n.MEDIA_LOADING,$o.LOADING_DELAY]}attributeChangedCallback(e,i,a){var r,o,s,l,u;e===$o.LOADING_DELAY&&i!==a?this.loadingDelay=Number(a):e===L.MEDIA_CONTROLLER&&(i&&((o=(r=Ya(this,Ge))==null?void 0:r.unassociateElement)==null||o.call(r,this),qa(this,Ge,null)),a&&this.isConnected&&(qa(this,Ge,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=Ya(this,Ge))==null?void 0:l.associateElement)==null||u.call(l,this)))}connectedCallback(){var e,i,a;let r=this.getAttribute(L.MEDIA_CONTROLLER);r&&(qa(this,Ge,(e=this.getRootNode())==null?void 0:e.getElementById(r)),(a=(i=Ya(this,Ge))==null?void 0:i.associateElement)==null||a.call(i,this))}disconnectedCallback(){var e,i;(i=(e=Ya(this,Ge))==null?void 0:e.unassociateElement)==null||i.call(e,this),qa(this,Ge,null)}get loadingDelay(){return Ya(this,Wo)}set loadingDelay(e){qa(this,Wo,e);let{style:i}=W(this.shadowRoot,":host");i.setProperty("--_loading-indicator-delay",`var(--media-loading-indicator-transition-delay, ${e}ms)`)}get mediaPaused(){return k(this,n.MEDIA_PAUSED)}set mediaPaused(e){S(this,n.MEDIA_PAUSED,e)}get mediaLoading(){return k(this,n.MEDIA_LOADING)}set mediaLoading(e){S(this,n.MEDIA_LOADING,e)}get mediaController(){return I(this,L.MEDIA_CONTROLLER)}set mediaController(e){M(this,L.MEDIA_CONTROLLER,e)}get noAutohide(){return k(this,$o.NO_AUTOHIDE)}set noAutohide(e){S(this,$o.NO_AUTOHIDE,e)}};Ge=new WeakMap;Wo=new WeakMap;Za.shadowRootOptions={mode:"open"};Za.getTemplateHTML=cp;d.customElements.get("media-loading-indicator")||d.customElements.define("media-loading-indicator",Za);var mp=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M16.5 12A4.5 4.5 0 0 0 14 8v2.18l2.45 2.45a4.22 4.22 0 0 0 .05-.63Zm2.5 0a6.84 6.84 0 0 1-.54 2.64L20 16.15A8.8 8.8 0 0 0 21 12a9 9 0 0 0-7-8.77v2.06A7 7 0 0 1 19 12ZM4.27 3 3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25A6.92 6.92 0 0 1 14 18.7v2.06A9 9 0 0 0 17.69 19l2 2.05L21 19.73l-9-9L4.27 3ZM12 4 9.91 6.09 12 8.18V4Z"/>
</svg>`,Xu=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M3 9v6h4l5 5V4L7 9H3Zm13.5 3A4.5 4.5 0 0 0 14 8v8a4.47 4.47 0 0 0 2.5-4Z"/>
</svg>`,hp=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M3 9v6h4l5 5V4L7 9H3Zm13.5 3A4.5 4.5 0 0 0 14 8v8a4.47 4.47 0 0 0 2.5-4ZM14 3.23v2.06a7 7 0 0 1 0 13.42v2.06a9 9 0 0 0 0-17.54Z"/>
</svg>`;function pp(t){return`
    <style>
      :host(:not([${n.MEDIA_VOLUME_LEVEL}])) slot[name=icon] slot:not([name=high]),
      :host([${n.MEDIA_VOLUME_LEVEL}=high]) slot[name=icon] slot:not([name=high]) {
        display: none !important;
      }

      :host([${n.MEDIA_VOLUME_LEVEL}=off]) slot[name=icon] slot:not([name=off]) {
        display: none !important;
      }

      :host([${n.MEDIA_VOLUME_LEVEL}=low]) slot[name=icon] slot:not([name=low]) {
        display: none !important;
      }

      :host([${n.MEDIA_VOLUME_LEVEL}=medium]) slot[name=icon] slot:not([name=medium]) {
        display: none !important;
      }

      :host(:not([${n.MEDIA_VOLUME_LEVEL}=off])) slot[name=tooltip-unmute],
      :host([${n.MEDIA_VOLUME_LEVEL}=off]) slot[name=tooltip-mute] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="off">${mp}</slot>
      <slot name="low">${Xu}</slot>
      <slot name="medium">${Xu}</slot>
      <slot name="high">${hp}</slot>
    </slot>
  `}function fp(){return`
    <slot name="tooltip-mute">${h("Mute")}</slot>
    <slot name="tooltip-unmute">${h("Unmute")}</slot>
  `}var Ju=t=>{let i=t.mediaVolumeLevel==="off"?h("unmute"):h("mute");t.setAttribute("aria-label",i)},za=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_VOLUME_LEVEL]}connectedCallback(){super.connectedCallback(),Ju(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_VOLUME_LEVEL&&Ju(this)}get mediaVolumeLevel(){return I(this,n.MEDIA_VOLUME_LEVEL)}set mediaVolumeLevel(e){M(this,n.MEDIA_VOLUME_LEVEL,e)}handleClick(){let e=this.mediaVolumeLevel==="off"?p.MEDIA_UNMUTE_REQUEST:p.MEDIA_MUTE_REQUEST;this.dispatchEvent(new d.CustomEvent(e,{composed:!0,bubbles:!0}))}};za.getSlotTemplateHTML=pp;za.getTooltipContentHTML=fp;d.customElements.get("media-mute-button")||d.customElements.define("media-mute-button",za);var ju=`<svg aria-hidden="true" viewBox="0 0 28 24">
  <path d="M24 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h20a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1Zm-1 16H5V5h18v14Zm-3-8h-7v5h7v-5Z"/>
</svg>`;function vp(t){return`
    <style>
      :host([${n.MEDIA_IS_PIP}]) slot[name=icon] slot:not([name=exit]) {
        display: none !important;
      }

      :host(:not([${n.MEDIA_IS_PIP}])) slot[name=icon] slot:not([name=enter]) {
        display: none !important;
      }

      :host([${n.MEDIA_IS_PIP}]) slot[name=tooltip-enter],
      :host(:not([${n.MEDIA_IS_PIP}])) slot[name=tooltip-exit] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="enter">${ju}</slot>
      <slot name="exit">${ju}</slot>
    </slot>
  `}function Ep(){return`
    <slot name="tooltip-enter">${h("Enter picture in picture mode")}</slot>
    <slot name="tooltip-exit">${h("Exit picture in picture mode")}</slot>
  `}var ec=t=>{let e=t.mediaIsPip?h("exit picture in picture mode"):h("enter picture in picture mode");t.setAttribute("aria-label",e)},Qa=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_IS_PIP,n.MEDIA_PIP_UNAVAILABLE]}connectedCallback(){super.connectedCallback(),ec(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_IS_PIP&&ec(this)}get mediaPipUnavailable(){return I(this,n.MEDIA_PIP_UNAVAILABLE)}set mediaPipUnavailable(e){M(this,n.MEDIA_PIP_UNAVAILABLE,e)}get mediaIsPip(){return k(this,n.MEDIA_IS_PIP)}set mediaIsPip(e){S(this,n.MEDIA_IS_PIP,e)}handleClick(){let e=this.mediaIsPip?p.MEDIA_EXIT_PIP_REQUEST:p.MEDIA_ENTER_PIP_REQUEST;this.dispatchEvent(new d.CustomEvent(e,{composed:!0,bubbles:!0}))}};Qa.getSlotTemplateHTML=vp;Qa.getTooltipContentHTML=Ep;d.customElements.get("media-pip-button")||d.customElements.define("media-pip-button",Qa);var bp=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Ui=(t,e,i)=>(bp(t,e,"read from private field"),i?i.call(t):e.get(t)),gp=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Lt,$s={RATES:"rates"},Ws=[1,1.2,1.5,1.7,2],jt=1;function _p(t){return`
    <style>
      :host {
        min-width: 5ch;
        padding: var(--media-button-padding, var(--media-control-padding, 10px 5px));
      }
    </style>
    <slot name="icon">${t.mediaplaybackrate||jt}x</slot>
  `}function Ap(){return h("Playback rate")}var Xa=class extends B{constructor(){var e;super(),gp(this,Lt,new Tt(this,$s.RATES,{defaultValue:Ws})),this.container=this.shadowRoot.querySelector('slot[name="icon"]'),this.container.innerHTML=`${(e=this.mediaPlaybackRate)!=null?e:jt}x`}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PLAYBACK_RATE,$s.RATES]}attributeChangedCallback(e,i,a){if(super.attributeChangedCallback(e,i,a),e===$s.RATES&&(Ui(this,Lt).value=a),e===n.MEDIA_PLAYBACK_RATE){let r=a?+a:Number.NaN,o=Number.isNaN(r)?jt:r;this.container.innerHTML=`${o}x`,this.setAttribute("aria-label",h("Playback rate {playbackRate}",{playbackRate:o}))}}get rates(){return Ui(this,Lt)}set rates(e){e?Array.isArray(e)?Ui(this,Lt).value=e.join(" "):typeof e=="string"&&(Ui(this,Lt).value=e):Ui(this,Lt).value=""}get mediaPlaybackRate(){return R(this,n.MEDIA_PLAYBACK_RATE,jt)}set mediaPlaybackRate(e){O(this,n.MEDIA_PLAYBACK_RATE,e)}handleClick(){var e,i;let a=Array.from(Ui(this,Lt).values(),s=>+s).sort((s,l)=>s-l),r=(i=(e=a.find(s=>s>this.mediaPlaybackRate))!=null?e:a[0])!=null?i:jt,o=new d.CustomEvent(p.MEDIA_PLAYBACK_RATE_REQUEST,{composed:!0,bubbles:!0,detail:r});this.dispatchEvent(o)}};Lt=new WeakMap;Xa.getSlotTemplateHTML=_p;Xa.getTooltipContentHTML=Ap;d.customElements.get("media-playback-rate-button")||d.customElements.define("media-playback-rate-button",Xa);var Tp=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="m6 21 15-9L6 3v18Z"/>
</svg>`,yp=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M6 20h4V4H6v16Zm8-16v16h4V4h-4Z"/>
</svg>`;function kp(t){return`
    <style>
      :host([${n.MEDIA_PAUSED}]) slot[name=pause],
      :host(:not([${n.MEDIA_PAUSED}])) slot[name=play] {
        display: none !important;
      }

      :host([${n.MEDIA_PAUSED}]) slot[name=tooltip-pause],
      :host(:not([${n.MEDIA_PAUSED}])) slot[name=tooltip-play] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="play">${Tp}</slot>
      <slot name="pause">${yp}</slot>
    </slot>
  `}function Sp(){return`
    <slot name="tooltip-play">${h("Play")}</slot>
    <slot name="tooltip-pause">${h("Pause")}</slot>
  `}var tc=t=>{let e=t.mediaPaused?h("play"):h("pause");t.setAttribute("aria-label",e)},Ja=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PAUSED,n.MEDIA_ENDED]}connectedCallback(){super.connectedCallback(),tc(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_PAUSED&&tc(this)}get mediaPaused(){return k(this,n.MEDIA_PAUSED)}set mediaPaused(e){S(this,n.MEDIA_PAUSED,e)}handleClick(){let e=this.mediaPaused?p.MEDIA_PLAY_REQUEST:p.MEDIA_PAUSE_REQUEST;this.dispatchEvent(new d.CustomEvent(e,{composed:!0,bubbles:!0}))}};Ja.getSlotTemplateHTML=kp;Ja.getTooltipContentHTML=Sp;d.customElements.get("media-play-button")||d.customElements.define("media-play-button",Ja);var Ye={PLACEHOLDER_SRC:"placeholdersrc",SRC:"src"};function Ip(t){return`
    <style>
      :host {
        pointer-events: none;
        display: var(--media-poster-image-display, inline-block);
        box-sizing: border-box;
      }

      img {
        max-width: 100%;
        max-height: 100%;
        min-width: 100%;
        min-height: 100%;
        background-repeat: no-repeat;
        background-position: var(--media-poster-image-background-position, var(--media-object-position, center));
        background-size: var(--media-poster-image-background-size, var(--media-object-fit, contain));
        object-fit: var(--media-object-fit, contain);
        object-position: var(--media-object-position, center);
      }
    </style>

    <img part="poster img" aria-hidden="true" id="image"/>
  `}var Mp=t=>{t.style.removeProperty("background-image")},Cp=(t,e)=>{t.style["background-image"]=`url('${e}')`},ja=class extends d.HTMLElement{static get observedAttributes(){return[Ye.PLACEHOLDER_SRC,Ye.SRC]}constructor(){if(super(),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}this.image=this.shadowRoot.querySelector("#image")}attributeChangedCallback(e,i,a){e===Ye.SRC&&(a==null?this.image.removeAttribute(Ye.SRC):this.image.setAttribute(Ye.SRC,a)),e===Ye.PLACEHOLDER_SRC&&(a==null?Mp(this.image):Cp(this.image,a))}get placeholderSrc(){return I(this,Ye.PLACEHOLDER_SRC)}set placeholderSrc(e){M(this,Ye.SRC,e)}get src(){return I(this,Ye.SRC)}set src(e){M(this,Ye.SRC,e)}};ja.shadowRootOptions={mode:"open"};ja.getTemplateHTML=Ip;d.customElements.get("media-poster-image")||d.customElements.define("media-poster-image",ja);var ic=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Lp=(t,e,i)=>(ic(t,e,"read from private field"),i?i.call(t):e.get(t)),wp=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Rp=(t,e,i,a)=>(ic(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Fo,Fs=class extends ve{constructor(){super(),wp(this,Fo,void 0),Rp(this,Fo,this.shadowRoot.querySelector("slot"))}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PREVIEW_CHAPTER]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_PREVIEW_CHAPTER&&a!==i&&a!=null&&(Lp(this,Fo).textContent=a,a!==""?this.setAttribute("aria-valuetext",`chapter: ${a}`):this.removeAttribute("aria-valuetext"))}get mediaPreviewChapter(){return I(this,n.MEDIA_PREVIEW_CHAPTER)}set mediaPreviewChapter(e){M(this,n.MEDIA_PREVIEW_CHAPTER,e)}};Fo=new WeakMap;d.customElements.get("media-preview-chapter-display")||d.customElements.define("media-preview-chapter-display",Fs);var ac=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Vo=(t,e,i)=>(ac(t,e,"read from private field"),i?i.call(t):e.get(t)),xp=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Ko=(t,e,i,a)=>(ac(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),qe;function Dp(t){return`
    <style>
      :host {
        box-sizing: border-box;
        display: var(--media-control-display, var(--media-preview-thumbnail-display, inline-block));
        overflow: hidden;
      }

      img {
        display: none;
        position: relative;
      }
    </style>
    <img crossorigin loading="eager" decoding="async">
  `}var Hi=class extends d.HTMLElement{constructor(){if(super(),xp(this,qe,void 0),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}}static get observedAttributes(){return[L.MEDIA_CONTROLLER,n.MEDIA_PREVIEW_IMAGE,n.MEDIA_PREVIEW_COORDS]}connectedCallback(){var e,i,a;let r=this.getAttribute(L.MEDIA_CONTROLLER);r&&(Ko(this,qe,(e=this.getRootNode())==null?void 0:e.getElementById(r)),(a=(i=Vo(this,qe))==null?void 0:i.associateElement)==null||a.call(i,this))}disconnectedCallback(){var e,i;(i=(e=Vo(this,qe))==null?void 0:e.unassociateElement)==null||i.call(e,this),Ko(this,qe,null)}attributeChangedCallback(e,i,a){var r,o,s,l,u;[n.MEDIA_PREVIEW_IMAGE,n.MEDIA_PREVIEW_COORDS].includes(e)&&this.update(),e===L.MEDIA_CONTROLLER&&(i&&((o=(r=Vo(this,qe))==null?void 0:r.unassociateElement)==null||o.call(r,this),Ko(this,qe,null)),a&&this.isConnected&&(Ko(this,qe,(s=this.getRootNode())==null?void 0:s.getElementById(a)),(u=(l=Vo(this,qe))==null?void 0:l.associateElement)==null||u.call(l,this)))}get mediaPreviewImage(){return I(this,n.MEDIA_PREVIEW_IMAGE)}set mediaPreviewImage(e){M(this,n.MEDIA_PREVIEW_IMAGE,e)}get mediaPreviewCoords(){let e=this.getAttribute(n.MEDIA_PREVIEW_COORDS);if(e)return e.split(/\s+/).map(i=>+i)}set mediaPreviewCoords(e){if(!e){this.removeAttribute(n.MEDIA_PREVIEW_COORDS);return}this.setAttribute(n.MEDIA_PREVIEW_COORDS,e.join(" "))}update(){let e=this.mediaPreviewCoords,i=this.mediaPreviewImage;if(!(e&&i))return;let[a,r,o,s]=e,l=i.split("#")[0],u=getComputedStyle(this),{maxWidth:m,maxHeight:_,minWidth:g,minHeight:f}=u,v=Math.min(parseInt(m)/o,parseInt(_)/s),N=Math.max(parseInt(g)/o,parseInt(f)/s),T=v<1,C=T?v:N>1?N:1,{style:P}=W(this.shadowRoot,":host"),j=W(this.shadowRoot,"img").style,ge=this.shadowRoot.querySelector("img"),bt=T?"min":"max";P.setProperty(`${bt}-width`,"initial","important"),P.setProperty(`${bt}-height`,"initial","important"),P.width=`${o*C}px`,P.height=`${s*C}px`;let ot=()=>{j.width=`${this.imgWidth*C}px`,j.height=`${this.imgHeight*C}px`,j.display="block"};ge.src!==l&&(ge.onload=()=>{this.imgWidth=ge.naturalWidth,this.imgHeight=ge.naturalHeight,ot()},ge.src=l,ot()),ot(),j.transform=`translate(-${a*C}px, -${r*C}px)`}};qe=new WeakMap;Hi.shadowRootOptions={mode:"open"};Hi.getTemplateHTML=Dp;d.customElements.get("media-preview-thumbnail")||d.customElements.define("media-preview-thumbnail",Hi);var Go=Hi;var oc=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},rc=(t,e,i)=>(oc(t,e,"read from private field"),i?i.call(t):e.get(t)),Op=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Np=(t,e,i,a)=>(oc(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),er,Vs=class extends ve{constructor(){super(),Op(this,er,void 0),Np(this,er,this.shadowRoot.querySelector("slot")),rc(this,er).textContent=De(0)}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PREVIEW_TIME]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_PREVIEW_TIME&&a!=null&&(rc(this,er).textContent=De(parseFloat(a)))}get mediaPreviewTime(){return R(this,n.MEDIA_PREVIEW_TIME)}set mediaPreviewTime(e){O(this,n.MEDIA_PREVIEW_TIME,e)}};er=new WeakMap;d.customElements.get("media-preview-time-display")||d.customElements.define("media-preview-time-display",Vs);var Bi={SEEK_OFFSET:"seekoffset"},Ks=30,Pp=t=>`
  <svg aria-hidden="true" viewBox="0 0 20 24">
    <defs>
      <style>.text{font-size:8px;font-family:Arial-BoldMT, Arial;font-weight:700;}</style>
    </defs>
    <text class="text value" transform="translate(2.18 19.87)">${t}</text>
    <path d="M10 6V3L4.37 7 10 10.94V8a5.54 5.54 0 0 1 1.9 10.48v2.12A7.5 7.5 0 0 0 10 6Z"/>
  </svg>`;function Up(t,e){return`
    <slot name="icon">${Pp(e.seekOffset)}</slot>
  `}function Hp(){return h("Seek backward")}var Bp=0,tr=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_CURRENT_TIME,Bi.SEEK_OFFSET]}connectedCallback(){super.connectedCallback(),this.seekOffset=R(this,Bi.SEEK_OFFSET,Ks)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===Bi.SEEK_OFFSET&&(this.seekOffset=R(this,Bi.SEEK_OFFSET,Ks))}get seekOffset(){return R(this,Bi.SEEK_OFFSET,Ks)}set seekOffset(e){O(this,Bi.SEEK_OFFSET,e),this.setAttribute("aria-label",h("seek back {seekOffset} seconds",{seekOffset:this.seekOffset})),qr(Zr(this,"icon"),this.seekOffset)}get mediaCurrentTime(){return R(this,n.MEDIA_CURRENT_TIME,Bp)}set mediaCurrentTime(e){O(this,n.MEDIA_CURRENT_TIME,e)}handleClick(){let e=Math.max(this.mediaCurrentTime-this.seekOffset,0),i=new d.CustomEvent(p.MEDIA_SEEK_REQUEST,{composed:!0,bubbles:!0,detail:e});this.dispatchEvent(i)}};tr.getSlotTemplateHTML=Up;tr.getTooltipContentHTML=Hp;d.customElements.get("media-seek-backward-button")||d.customElements.define("media-seek-backward-button",tr);var $i={SEEK_OFFSET:"seekoffset"},Gs=30,$p=t=>`
  <svg aria-hidden="true" viewBox="0 0 20 24">
    <defs>
      <style>.text{font-size:8px;font-family:Arial-BoldMT, Arial;font-weight:700;}</style>
    </defs>
    <text class="text value" transform="translate(8.9 19.87)">${t}</text>
    <path d="M10 6V3l5.61 4L10 10.94V8a5.54 5.54 0 0 0-1.9 10.48v2.12A7.5 7.5 0 0 1 10 6Z"/>
  </svg>`;function Wp(t,e){return`
    <slot name="icon">${$p(e.seekOffset)}</slot>
  `}function Fp(){return h("Seek forward")}var Vp=0,ir=class extends B{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_CURRENT_TIME,$i.SEEK_OFFSET]}connectedCallback(){super.connectedCallback(),this.seekOffset=R(this,$i.SEEK_OFFSET,Gs)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===$i.SEEK_OFFSET&&(this.seekOffset=R(this,$i.SEEK_OFFSET,Gs))}get seekOffset(){return R(this,$i.SEEK_OFFSET,Gs)}set seekOffset(e){O(this,$i.SEEK_OFFSET,e),this.setAttribute("aria-label",h("seek forward {seekOffset} seconds",{seekOffset:this.seekOffset})),qr(Zr(this,"icon"),this.seekOffset)}get mediaCurrentTime(){return R(this,n.MEDIA_CURRENT_TIME,Vp)}set mediaCurrentTime(e){O(this,n.MEDIA_CURRENT_TIME,e)}handleClick(){let e=this.mediaCurrentTime+this.seekOffset,i=new d.CustomEvent(p.MEDIA_SEEK_REQUEST,{composed:!0,bubbles:!0,detail:e});this.dispatchEvent(i)}};ir.getSlotTemplateHTML=Wp;ir.getTooltipContentHTML=Fp;d.customElements.get("media-seek-forward-button")||d.customElements.define("media-seek-forward-button",ir);var lc=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Ys=(t,e,i)=>(lc(t,e,"read from private field"),i?i.call(t):e.get(t)),Kp=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Gp=(t,e,i,a)=>(lc(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Wi,ei={REMAINING:"remaining",SHOW_DURATION:"showduration",NO_TOGGLE:"notoggle"},nc=[...Object.values(ei),n.MEDIA_CURRENT_TIME,n.MEDIA_DURATION,n.MEDIA_SEEKABLE],sc=["Enter"," "],Yp="&nbsp;/&nbsp;",qs=(t,{timesSep:e=Yp}={})=>{var i,a;let r=(i=t.mediaCurrentTime)!=null?i:0,[,o]=(a=t.mediaSeekable)!=null?a:[],s=0;Number.isFinite(t.mediaDuration)?s=t.mediaDuration:Number.isFinite(o)&&(s=o);let l=t.remaining?De(0-(s-r)):De(r);return t.showDuration?`${l}${e}${De(s)}`:l},qp="video not loaded, unknown time.",Zp=t=>{var e;let i=t.mediaCurrentTime,[,a]=(e=t.mediaSeekable)!=null?e:[],r=null;if(Number.isFinite(t.mediaDuration)?r=t.mediaDuration:Number.isFinite(a)&&(r=a),i==null||r===null){t.setAttribute("aria-valuetext",qp);return}let o=t.remaining?$t(0-(r-i)):$t(i);if(!t.showDuration){t.setAttribute("aria-valuetext",o);return}let s=$t(r),l=`${o} of ${s}`;t.setAttribute("aria-valuetext",l)};function zp(t,e){return`
    <slot>${qs(e)}</slot>
  `}var Yo=class extends ve{constructor(){super(),Kp(this,Wi,void 0),Gp(this,Wi,this.shadowRoot.querySelector("slot")),Ys(this,Wi).innerHTML=`${qs(this)}`}static get observedAttributes(){return[...super.observedAttributes,...nc,"disabled"]}connectedCallback(){let{style:e}=W(this.shadowRoot,":host(:hover:not([notoggle]))");e.setProperty("cursor","var(--media-cursor, pointer)"),e.setProperty("background","var(--media-control-hover-background, rgba(50 50 70 / .7))"),this.hasAttribute("disabled")||this.enable(),this.setAttribute("role","progressbar"),this.setAttribute("aria-label",h("playback time"));let i=a=>{let{key:r}=a;if(!sc.includes(r)){this.removeEventListener("keyup",i);return}this.toggleTimeDisplay()};this.addEventListener("keydown",a=>{let{metaKey:r,altKey:o,key:s}=a;if(r||o||!sc.includes(s)){this.removeEventListener("keyup",i);return}this.addEventListener("keyup",i)}),this.addEventListener("click",this.toggleTimeDisplay),super.connectedCallback()}toggleTimeDisplay(){this.noToggle||(this.hasAttribute("remaining")?this.removeAttribute("remaining"):this.setAttribute("remaining",""))}disconnectedCallback(){this.disable(),super.disconnectedCallback()}attributeChangedCallback(e,i,a){nc.includes(e)?this.update():e==="disabled"&&a!==i&&(a==null?this.enable():this.disable()),super.attributeChangedCallback(e,i,a)}enable(){this.tabIndex=0}disable(){this.tabIndex=-1}get remaining(){return k(this,ei.REMAINING)}set remaining(e){S(this,ei.REMAINING,e)}get showDuration(){return k(this,ei.SHOW_DURATION)}set showDuration(e){S(this,ei.SHOW_DURATION,e)}get noToggle(){return k(this,ei.NO_TOGGLE)}set noToggle(e){S(this,ei.NO_TOGGLE,e)}get mediaDuration(){return R(this,n.MEDIA_DURATION)}set mediaDuration(e){O(this,n.MEDIA_DURATION,e)}get mediaCurrentTime(){return R(this,n.MEDIA_CURRENT_TIME)}set mediaCurrentTime(e){O(this,n.MEDIA_CURRENT_TIME,e)}get mediaSeekable(){let e=this.getAttribute(n.MEDIA_SEEKABLE);if(e)return e.split(":").map(i=>+i)}set mediaSeekable(e){if(e==null){this.removeAttribute(n.MEDIA_SEEKABLE);return}this.setAttribute(n.MEDIA_SEEKABLE,e.join(":"))}update(){let e=qs(this);Zp(this),e!==Ys(this,Wi).innerHTML&&(Ys(this,Wi).innerHTML=e)}};Wi=new WeakMap;Yo.getSlotTemplateHTML=zp;d.customElements.get("media-time-display")||d.customElements.define("media-time-display",Yo);var dc=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},ae=(t,e,i)=>(dc(t,e,"read from private field"),i?i.call(t):e.get(t)),Ze=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Me=(t,e,i,a)=>(dc(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Qp=(t,e,i,a)=>({set _(r){Me(t,e,r,i)},get _(){return ae(t,e,a)}}),Fi,qo,Vi,ar,Zo,zo,Qo,Ki,ti,Xo,Jo=class{constructor(e,i,a){Ze(this,Fi,void 0),Ze(this,qo,void 0),Ze(this,Vi,void 0),Ze(this,ar,void 0),Ze(this,Zo,void 0),Ze(this,zo,void 0),Ze(this,Qo,void 0),Ze(this,Ki,void 0),Ze(this,ti,0),Ze(this,Xo,(r=performance.now())=>{Me(this,ti,requestAnimationFrame(ae(this,Xo))),Me(this,ar,performance.now()-ae(this,Vi));let o=1e3/this.fps;if(ae(this,ar)>o){Me(this,Vi,r-ae(this,ar)%o);let s=1e3/((r-ae(this,qo))/++Qp(this,Zo)._),l=(r-ae(this,zo))/1e3/this.duration,u=ae(this,Qo)+l*this.playbackRate;u-ae(this,Fi).valueAsNumber>0?Me(this,Ki,this.playbackRate/this.duration/s):(Me(this,Ki,.995*ae(this,Ki)),u=ae(this,Fi).valueAsNumber+ae(this,Ki)),this.callback(u)}}),Me(this,Fi,e),this.callback=i,this.fps=a}start(){ae(this,ti)===0&&(Me(this,Vi,performance.now()),Me(this,qo,ae(this,Vi)),Me(this,Zo,0),ae(this,Xo).call(this))}stop(){ae(this,ti)!==0&&(cancelAnimationFrame(ae(this,ti)),Me(this,ti,0))}update({start:e,duration:i,playbackRate:a}){let r=e-ae(this,Fi).valueAsNumber,o=Math.abs(i-this.duration);(r>0||r<-.03||o>=.5)&&this.callback(e),Me(this,Qo,e),Me(this,zo,performance.now()),this.duration=i,this.playbackRate=a}};Fi=new WeakMap;qo=new WeakMap;Vi=new WeakMap;ar=new WeakMap;Zo=new WeakMap;zo=new WeakMap;Qo=new WeakMap;Ki=new WeakMap;ti=new WeakMap;Xo=new WeakMap;var Xs=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},ee=(t,e,i)=>(Xs(t,e,"read from private field"),i?i.call(t):e.get(t)),re=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},ze=(t,e,i,a)=>(Xs(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),le=(t,e,i)=>(Xs(t,e,"access private method"),i),Gi,ii,tn,or,an,en,nr,sr,Yi,qi,rr,Js,uc,Zs,rn,js,on,el,nn,tl,zs,cc,lr,sn,Qs,mc,Xp="video not loaded, unknown time.",Jp=t=>{let e=t.range,i=$t(+hc(t)),a=$t(+t.mediaSeekableEnd),r=i&&a?`${i} of ${a}`:Xp;e.setAttribute("aria-valuetext",r)};function jp(t){return`
    ${Fe.getTemplateHTML(t)}
    <style>
      :host {
        --media-box-border-radius: 4px;
        --media-box-padding-left: 10px;
        --media-box-padding-right: 10px;
        --media-preview-border-radius: var(--media-box-border-radius);
        --media-box-arrow-offset: var(--media-box-border-radius);
        --_control-background: var(--media-control-background, var(--media-secondary-color, rgb(20 20 30 / .7)));
        --_preview-background: var(--media-preview-background, var(--_control-background));

        
        contain: layout;
      }

      #buffered {
        background: var(--media-time-range-buffered-color, rgb(255 255 255 / .4));
        position: absolute;
        height: 100%;
        will-change: width;
      }

      #preview-rail,
      #current-rail {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 100%;
        pointer-events: none;
        will-change: transform;
      }

      [part~="box"] {
        width: min-content;
        
        position: absolute;
        bottom: 100%;
        flex-direction: column;
        align-items: center;
        transform: translateX(-50%);
      }

      [part~="current-box"] {
        display: var(--media-current-box-display, var(--media-box-display, flex));
        margin: var(--media-current-box-margin, var(--media-box-margin, 0 0 5px));
        visibility: hidden;
      }

      [part~="preview-box"] {
        display: var(--media-preview-box-display, var(--media-box-display, flex));
        margin: var(--media-preview-box-margin, var(--media-box-margin, 0 0 5px));
        transition-property: var(--media-preview-transition-property, visibility, opacity);
        transition-duration: var(--media-preview-transition-duration-out, .25s);
        transition-delay: var(--media-preview-transition-delay-out, 0s);
        visibility: hidden;
        opacity: 0;
      }

      :host(:is([${n.MEDIA_PREVIEW_IMAGE}], [${n.MEDIA_PREVIEW_TIME}])[dragging]) [part~="preview-box"] {
        transition-duration: var(--media-preview-transition-duration-in, .5s);
        transition-delay: var(--media-preview-transition-delay-in, .25s);
        visibility: visible;
        opacity: 1;
      }

      @media (hover: hover) {
        :host(:is([${n.MEDIA_PREVIEW_IMAGE}], [${n.MEDIA_PREVIEW_TIME}]):hover) [part~="preview-box"] {
          transition-duration: var(--media-preview-transition-duration-in, .5s);
          transition-delay: var(--media-preview-transition-delay-in, .25s);
          visibility: visible;
          opacity: 1;
        }
      }

      media-preview-thumbnail,
      ::slotted(media-preview-thumbnail) {
        visibility: hidden;
        
        transition: visibility 0s .25s;
        transition-delay: calc(var(--media-preview-transition-delay-out, 0s) + var(--media-preview-transition-duration-out, .25s));
        background: var(--media-preview-thumbnail-background, var(--_preview-background));
        box-shadow: var(--media-preview-thumbnail-box-shadow, 0 0 4px rgb(0 0 0 / .2));
        max-width: var(--media-preview-thumbnail-max-width, 180px);
        max-height: var(--media-preview-thumbnail-max-height, 160px);
        min-width: var(--media-preview-thumbnail-min-width, 120px);
        min-height: var(--media-preview-thumbnail-min-height, 80px);
        border: var(--media-preview-thumbnail-border);
        border-radius: var(--media-preview-thumbnail-border-radius,
          var(--media-preview-border-radius) var(--media-preview-border-radius) 0 0);
      }

      :host([${n.MEDIA_PREVIEW_IMAGE}][dragging]) media-preview-thumbnail,
      :host([${n.MEDIA_PREVIEW_IMAGE}][dragging]) ::slotted(media-preview-thumbnail) {
        transition-delay: var(--media-preview-transition-delay-in, .25s);
        visibility: visible;
      }

      @media (hover: hover) {
        :host([${n.MEDIA_PREVIEW_IMAGE}]:hover) media-preview-thumbnail,
        :host([${n.MEDIA_PREVIEW_IMAGE}]:hover) ::slotted(media-preview-thumbnail) {
          transition-delay: var(--media-preview-transition-delay-in, .25s);
          visibility: visible;
        }

        :host([${n.MEDIA_PREVIEW_TIME}]:hover) {
          --media-time-range-hover-display: block;
        }
      }

      media-preview-chapter-display,
      ::slotted(media-preview-chapter-display) {
        font-size: var(--media-font-size, 13px);
        line-height: 17px;
        min-width: 0;
        visibility: hidden;
        
        transition: min-width 0s, border-radius 0s, margin 0s, padding 0s, visibility 0s;
        transition-delay: calc(var(--media-preview-transition-delay-out, 0s) + var(--media-preview-transition-duration-out, .25s));
        background: var(--media-preview-chapter-background, var(--_preview-background));
        border-radius: var(--media-preview-chapter-border-radius,
          var(--media-preview-border-radius) var(--media-preview-border-radius)
          var(--media-preview-border-radius) var(--media-preview-border-radius));
        padding: var(--media-preview-chapter-padding, 3.5px 9px);
        margin: var(--media-preview-chapter-margin, 0 0 5px);
        text-shadow: var(--media-preview-chapter-text-shadow, 0 0 4px rgb(0 0 0 / .75));
      }

      :host([${n.MEDIA_PREVIEW_IMAGE}]) media-preview-chapter-display,
      :host([${n.MEDIA_PREVIEW_IMAGE}]) ::slotted(media-preview-chapter-display) {
        transition-delay: var(--media-preview-transition-delay-in, .25s);
        border-radius: var(--media-preview-chapter-border-radius, 0);
        padding: var(--media-preview-chapter-padding, 3.5px 9px 0);
        margin: var(--media-preview-chapter-margin, 0);
        min-width: 100%;
      }

      media-preview-chapter-display[${n.MEDIA_PREVIEW_CHAPTER}],
      ::slotted(media-preview-chapter-display[${n.MEDIA_PREVIEW_CHAPTER}]) {
        visibility: visible;
      }

      media-preview-chapter-display:not([aria-valuetext]),
      ::slotted(media-preview-chapter-display:not([aria-valuetext])) {
        display: none;
      }

      media-preview-time-display,
      ::slotted(media-preview-time-display),
      media-time-display,
      ::slotted(media-time-display) {
        font-size: var(--media-font-size, 13px);
        line-height: 17px;
        min-width: 0;
        
        transition: min-width 0s, border-radius 0s;
        transition-delay: calc(var(--media-preview-transition-delay-out, 0s) + var(--media-preview-transition-duration-out, .25s));
        background: var(--media-preview-time-background, var(--_preview-background));
        border-radius: var(--media-preview-time-border-radius,
          var(--media-preview-border-radius) var(--media-preview-border-radius)
          var(--media-preview-border-radius) var(--media-preview-border-radius));
        padding: var(--media-preview-time-padding, 3.5px 9px);
        margin: var(--media-preview-time-margin, 0);
        text-shadow: var(--media-preview-time-text-shadow, 0 0 4px rgb(0 0 0 / .75));
        transform: translateX(min(
          max(calc(50% - var(--_box-width) / 2),
          calc(var(--_box-shift, 0))),
          calc(var(--_box-width) / 2 - 50%)
        ));
      }

      :host([${n.MEDIA_PREVIEW_IMAGE}]) media-preview-time-display,
      :host([${n.MEDIA_PREVIEW_IMAGE}]) ::slotted(media-preview-time-display) {
        transition-delay: var(--media-preview-transition-delay-in, .25s);
        border-radius: var(--media-preview-time-border-radius,
          0 0 var(--media-preview-border-radius) var(--media-preview-border-radius));
        min-width: 100%;
      }

      :host([${n.MEDIA_PREVIEW_TIME}]:hover) {
        --media-time-range-hover-display: block;
      }

      [part~="arrow"],
      ::slotted([part~="arrow"]) {
        display: var(--media-box-arrow-display, inline-block);
        transform: translateX(min(
          max(calc(50% - var(--_box-width) / 2 + var(--media-box-arrow-offset)),
          calc(var(--_box-shift, 0))),
          calc(var(--_box-width) / 2 - 50% - var(--media-box-arrow-offset))
        ));
        
        border-color: transparent;
        border-top-color: var(--media-box-arrow-background, var(--_control-background));
        border-width: var(--media-box-arrow-border-width,
          var(--media-box-arrow-height, 5px) var(--media-box-arrow-width, 6px) 0);
        border-style: solid;
        justify-content: center;
        height: 0;
      }
    </style>
    <div id="preview-rail">
      <slot name="preview" part="box preview-box">
        <media-preview-thumbnail>
          <template shadowrootmode="${Go.shadowRootOptions.mode}">
            ${Go.getTemplateHTML({})}
          </template>
        </media-preview-thumbnail>
        <media-preview-chapter-display></media-preview-chapter-display>
        <media-preview-time-display></media-preview-time-display>
        <slot name="preview-arrow"><div part="arrow"></div></slot>
      </slot>
    </div>
    <div id="current-rail">
      <slot name="current" part="box current-box">
        
      </slot>
    </div>
  `}var jo=(t,e=t.mediaCurrentTime)=>{let i=Number.isFinite(t.mediaSeekableStart)?t.mediaSeekableStart:0,a=Number.isFinite(t.mediaDuration)?t.mediaDuration:t.mediaSeekableEnd;if(Number.isNaN(a))return 0;let r=(e-i)/(a-i);return Math.max(0,Math.min(r,1))},hc=(t,e=t.range.valueAsNumber)=>{let i=Number.isFinite(t.mediaSeekableStart)?t.mediaSeekableStart:0,a=Number.isFinite(t.mediaDuration)?t.mediaDuration:t.mediaSeekableEnd;return Number.isNaN(a)?0:e*(a-i)+i},dr=class extends Fe{constructor(){super(),re(this,qi),re(this,Js),re(this,rn),re(this,on),re(this,nn),re(this,zs),re(this,lr),re(this,Qs),re(this,Gi,void 0),re(this,ii,void 0),re(this,tn,void 0),re(this,or,void 0),re(this,an,void 0),re(this,en,void 0),re(this,nr,void 0),re(this,sr,void 0),re(this,Yi,void 0),re(this,Zs,a=>{this.dragging||(Ai(a)&&(this.range.valueAsNumber=a),this.updateBar())}),this.shadowRoot.querySelector("#track").insertAdjacentHTML("afterbegin",'<div id="buffered" part="buffered"></div>'),ze(this,tn,this.shadowRoot.querySelectorAll('[part~="box"]')),ze(this,an,this.shadowRoot.querySelector('[part~="preview-box"]')),ze(this,en,this.shadowRoot.querySelector('[part~="current-box"]'));let i=getComputedStyle(this);ze(this,nr,parseInt(i.getPropertyValue("--media-box-padding-left"))),ze(this,sr,parseInt(i.getPropertyValue("--media-box-padding-right"))),ze(this,ii,new Jo(this.range,ee(this,Zs),60))}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PAUSED,n.MEDIA_DURATION,n.MEDIA_SEEKABLE,n.MEDIA_CURRENT_TIME,n.MEDIA_PREVIEW_IMAGE,n.MEDIA_PREVIEW_TIME,n.MEDIA_PREVIEW_CHAPTER,n.MEDIA_BUFFERED,n.MEDIA_PLAYBACK_RATE,n.MEDIA_LOADING,n.MEDIA_ENDED]}connectedCallback(){var e;super.connectedCallback(),this.range.setAttribute("aria-label",h("seek")),le(this,qi,rr).call(this),ze(this,Gi,this.getRootNode()),(e=ee(this,Gi))==null||e.addEventListener("transitionstart",this)}disconnectedCallback(){var e;super.disconnectedCallback(),le(this,qi,rr).call(this),(e=ee(this,Gi))==null||e.removeEventListener("transitionstart",this),ze(this,Gi,null)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),i!=a&&(e===n.MEDIA_CURRENT_TIME||e===n.MEDIA_PAUSED||e===n.MEDIA_ENDED||e===n.MEDIA_LOADING||e===n.MEDIA_DURATION||e===n.MEDIA_SEEKABLE?(ee(this,ii).update({start:jo(this),duration:this.mediaSeekableEnd-this.mediaSeekableStart,playbackRate:this.mediaPlaybackRate}),le(this,qi,rr).call(this),Jp(this)):e===n.MEDIA_BUFFERED&&this.updateBufferedBar(),(e===n.MEDIA_DURATION||e===n.MEDIA_SEEKABLE)&&(this.mediaChaptersCues=ee(this,Yi),this.updateBar()))}get mediaChaptersCues(){return ee(this,Yi)}set mediaChaptersCues(e){var i;ze(this,Yi,e),this.updateSegments((i=ee(this,Yi))==null?void 0:i.map(a=>({start:jo(this,a.startTime),end:jo(this,a.endTime)})))}get mediaPaused(){return k(this,n.MEDIA_PAUSED)}set mediaPaused(e){S(this,n.MEDIA_PAUSED,e)}get mediaLoading(){return k(this,n.MEDIA_LOADING)}set mediaLoading(e){S(this,n.MEDIA_LOADING,e)}get mediaDuration(){return R(this,n.MEDIA_DURATION)}set mediaDuration(e){O(this,n.MEDIA_DURATION,e)}get mediaCurrentTime(){return R(this,n.MEDIA_CURRENT_TIME)}set mediaCurrentTime(e){O(this,n.MEDIA_CURRENT_TIME,e)}get mediaPlaybackRate(){return R(this,n.MEDIA_PLAYBACK_RATE,1)}set mediaPlaybackRate(e){O(this,n.MEDIA_PLAYBACK_RATE,e)}get mediaBuffered(){let e=this.getAttribute(n.MEDIA_BUFFERED);return e?e.split(" ").map(i=>i.split(":").map(a=>+a)):[]}set mediaBuffered(e){if(!e){this.removeAttribute(n.MEDIA_BUFFERED);return}let i=e.map(a=>a.join(":")).join(" ");this.setAttribute(n.MEDIA_BUFFERED,i)}get mediaSeekable(){let e=this.getAttribute(n.MEDIA_SEEKABLE);if(e)return e.split(":").map(i=>+i)}set mediaSeekable(e){if(e==null){this.removeAttribute(n.MEDIA_SEEKABLE);return}this.setAttribute(n.MEDIA_SEEKABLE,e.join(":"))}get mediaSeekableEnd(){var e;let[,i=this.mediaDuration]=(e=this.mediaSeekable)!=null?e:[];return i}get mediaSeekableStart(){var e;let[i=0]=(e=this.mediaSeekable)!=null?e:[];return i}get mediaPreviewImage(){return I(this,n.MEDIA_PREVIEW_IMAGE)}set mediaPreviewImage(e){M(this,n.MEDIA_PREVIEW_IMAGE,e)}get mediaPreviewTime(){return R(this,n.MEDIA_PREVIEW_TIME)}set mediaPreviewTime(e){O(this,n.MEDIA_PREVIEW_TIME,e)}get mediaEnded(){return k(this,n.MEDIA_ENDED)}set mediaEnded(e){S(this,n.MEDIA_ENDED,e)}updateBar(){super.updateBar(),this.updateBufferedBar(),this.updateCurrentBox()}updateBufferedBar(){var e;let i=this.mediaBuffered;if(!i.length)return;let a;if(this.mediaEnded)a=1;else{let o=this.mediaCurrentTime,[,s=this.mediaSeekableStart]=(e=i.find(([l,u])=>l<=o&&o<=u))!=null?e:[];a=jo(this,s)}let{style:r}=W(this.shadowRoot,"#buffered");r.setProperty("width",`${a*100}%`)}updateCurrentBox(){if(!this.shadowRoot.querySelector('slot[name="current"]').assignedElements().length)return;let i=W(this.shadowRoot,"#current-rail"),a=W(this.shadowRoot,'[part~="current-box"]'),r=le(this,rn,js).call(this,ee(this,en)),o=le(this,on,el).call(this,r,this.range.valueAsNumber),s=le(this,nn,tl).call(this,r,this.range.valueAsNumber);i.style.transform=`translateX(${o})`,i.style.setProperty("--_range-width",`${r.range.width}`),a.style.setProperty("--_box-shift",`${s}`),a.style.setProperty("--_box-width",`${r.box.width}px`),a.style.setProperty("visibility","initial")}handleEvent(e){switch(super.handleEvent(e),e.type){case"input":le(this,Qs,mc).call(this);break;case"pointermove":le(this,zs,cc).call(this,e);break;case"pointerup":case"pointerleave":le(this,lr,sn).call(this,null);break;case"transitionstart":de(e.target,this)&&setTimeout(()=>le(this,qi,rr).call(this),0);break}}};Gi=new WeakMap;ii=new WeakMap;tn=new WeakMap;or=new WeakMap;an=new WeakMap;en=new WeakMap;nr=new WeakMap;sr=new WeakMap;Yi=new WeakMap;qi=new WeakSet;rr=function(){le(this,Js,uc).call(this)?ee(this,ii).start():ee(this,ii).stop()};Js=new WeakSet;uc=function(){return this.isConnected&&!this.mediaPaused&&!this.mediaLoading&&!this.mediaEnded&&this.mediaSeekableEnd>0&&zr(this)};Zs=new WeakMap;rn=new WeakSet;js=function(t){var e;let a=((e=this.getAttribute("bounds")?He(this,`#${this.getAttribute("bounds")}`):this.parentElement)!=null?e:this).getBoundingClientRect(),r=this.range.getBoundingClientRect(),o=t.offsetWidth,s=-(r.left-a.left-o/2),l=a.right-r.left-o/2;return{box:{width:o,min:s,max:l},bounds:a,range:r}};on=new WeakSet;el=function(t,e){let i=`${e*100}%`,{width:a,min:r,max:o}=t.box;if(!a)return i;if(Number.isNaN(r)||(i=`max(${`calc(1 / var(--_range-width) * 100 * ${r}% + var(--media-box-padding-left))`}, ${i})`),!Number.isNaN(o)){let l=`calc(1 / var(--_range-width) * 100 * ${o}% - var(--media-box-padding-right))`;i=`min(${i}, ${l})`}return i};nn=new WeakSet;tl=function(t,e){let{width:i,min:a,max:r}=t.box,o=e*t.range.width;if(o<a+ee(this,nr)){let s=t.range.left-t.bounds.left-ee(this,nr);return`${o-i/2+s}px`}if(o>r-ee(this,sr)){let s=t.bounds.right-t.range.right-ee(this,sr);return`${o+i/2-s-t.range.width}px`}return 0};zs=new WeakSet;cc=function(t){let e=[...ee(this,tn)].some(_=>t.composedPath().includes(_));if(!this.dragging&&(e||!t.composedPath().includes(this))){le(this,lr,sn).call(this,null);return}let i=this.mediaSeekableEnd;if(!i)return;let a=W(this.shadowRoot,"#preview-rail"),r=W(this.shadowRoot,'[part~="preview-box"]'),o=le(this,rn,js).call(this,ee(this,an)),s=(t.clientX-o.range.left)/o.range.width;s=Math.max(0,Math.min(1,s));let l=le(this,on,el).call(this,o,s),u=le(this,nn,tl).call(this,o,s);a.style.transform=`translateX(${l})`,a.style.setProperty("--_range-width",`${o.range.width}`),r.style.setProperty("--_box-shift",`${u}`),r.style.setProperty("--_box-width",`${o.box.width}px`);let m=Math.round(ee(this,or))-Math.round(s*i);Math.abs(m)<1&&s>.01&&s<.99||(ze(this,or,s*i),le(this,lr,sn).call(this,ee(this,or)))};lr=new WeakSet;sn=function(t){this.dispatchEvent(new d.CustomEvent(p.MEDIA_PREVIEW_REQUEST,{composed:!0,bubbles:!0,detail:t}))};Qs=new WeakSet;mc=function(){ee(this,ii).stop();let t=hc(this);this.dispatchEvent(new d.CustomEvent(p.MEDIA_SEEK_REQUEST,{composed:!0,bubbles:!0,detail:t}))};dr.shadowRootOptions={mode:"open"};dr.getTemplateHTML=jp;d.customElements.get("media-time-range")||d.customElements.define("media-time-range",dr);var ef=1,tf=t=>t.mediaMuted?0:t.mediaVolume,af=t=>`${Math.round(t*100)}%`,il=class extends Fe{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_VOLUME,n.MEDIA_MUTED,n.MEDIA_VOLUME_UNAVAILABLE]}constructor(){super(),this.range.addEventListener("input",()=>{let e=this.range.value,i=new d.CustomEvent(p.MEDIA_VOLUME_REQUEST,{composed:!0,bubbles:!0,detail:e});this.dispatchEvent(i)})}connectedCallback(){super.connectedCallback(),this.range.setAttribute("aria-label",h("volume"))}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),(e===n.MEDIA_VOLUME||e===n.MEDIA_MUTED)&&(this.range.valueAsNumber=tf(this),this.range.setAttribute("aria-valuetext",af(this.range.valueAsNumber)),this.updateBar())}get mediaVolume(){return R(this,n.MEDIA_VOLUME,ef)}set mediaVolume(e){O(this,n.MEDIA_VOLUME,e)}get mediaMuted(){return k(this,n.MEDIA_MUTED)}set mediaMuted(e){S(this,n.MEDIA_MUTED,e)}get mediaVolumeUnavailable(){return I(this,n.MEDIA_VOLUME_UNAVAILABLE)}set mediaVolumeUnavailable(e){M(this,n.MEDIA_VOLUME_UNAVAILABLE,e)}};d.customElements.get("media-volume-range")||d.customElements.define("media-volume-range",il);var b=require("@mux/mux-video/base"),w=require("@mux/playback-core");var gc=require("@mux/playback-core");var oi=require("@mux/playback-core");function pc(t){let e="";return Object.entries(t).forEach(([i,a])=>{a!=null&&(e+=`${ln(i)}: ${a}; `)}),e?e.trim():void 0}function ln(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function dn(t){return t.replace(/[-_]([a-z])/g,(e,i)=>i.toUpperCase())}function ue(t){if(t==null)return;let e=+t;return Number.isNaN(e)?void 0:e}function al(t){let e=rf(t).toString();return e?"?"+e:""}function rf(t){let e={};for(let i in t)t[i]!=null&&(e[i]=t[i]);return new URLSearchParams(e)}var rl=(t,e)=>!t||!e?!1:t.contains(e)?!0:rl(t,e.getRootNode().host);var fc="mux.com",of=()=>{try{return"3.5.1"}catch{}return"UNKNOWN"},nf=of(),cn=()=>nf,vc=(t,{token:e,customDomain:i=fc,thumbnailTime:a,programTime:r}={})=>{var l;let o=e==null?a:void 0,{aud:s}=(l=(0,oi.parseJwt)(e))!=null?l:{};if(!(e&&s!=="t"))return`https://image.${i}/${t}/thumbnail.webp${al({token:e,time:o,program_time:r})}`},Ec=(t,{token:e,customDomain:i=fc,programStartTime:a,programEndTime:r}={})=>{var s;let{aud:o}=(s=(0,oi.parseJwt)(e))!=null?s:{};if(!(e&&o!=="s"))return`https://image.${i}/${t}/storyboard.vtt${al({token:e,format:"webp",program_start_time:a,program_end_time:r})}`},ur=t=>{if(t){if([oi.StreamTypes.LIVE,oi.StreamTypes.ON_DEMAND].includes(t))return t;if(t!=null&&t.includes("live"))return oi.StreamTypes.LIVE}};var sf={crossorigin:"crossOrigin",playsinline:"playsInline"};function bc(t){var e;return(e=sf[t])!=null?e:dn(t)}var ai,ri,ce,un=class{constructor(e,i){he(this,ai);he(this,ri);he(this,ce,[]);Te(this,ai,e),Te(this,ri,i)}[Symbol.iterator](){return U(this,ce).values()}get length(){return U(this,ce).length}get value(){var e;return(e=U(this,ce).join(" "))!=null?e:""}set value(e){var i;e!==this.value&&(Te(this,ce,[]),this.add(...(i=e==null?void 0:e.split(" "))!=null?i:[]))}toString(){return this.value}item(e){return U(this,ce)[e]}values(){return U(this,ce).values()}keys(){return U(this,ce).keys()}forEach(e){U(this,ce).forEach(e)}add(...e){var i,a;e.forEach(r=>{this.contains(r)||U(this,ce).push(r)}),!(this.value===""&&!((i=U(this,ai))!=null&&i.hasAttribute(`${U(this,ri)}`)))&&((a=U(this,ai))==null||a.setAttribute(`${U(this,ri)}`,`${this.value}`))}remove(...e){var i;e.forEach(a=>{U(this,ce).splice(U(this,ce).indexOf(a),1)}),(i=U(this,ai))==null||i.setAttribute(`${U(this,ri)}`,`${this.value}`)}contains(e){return U(this,ce).includes(e)}toggle(e,i){return typeof i!="undefined"?i?(this.add(e),!0):(this.remove(e),!1):this.contains(e)?(this.remove(e),!1):(this.add(e),!0)}replace(e,i){this.remove(e),this.add(i)}};ai=new WeakMap,ri=new WeakMap,ce=new WeakMap;var _c=`[mux-player ${cn()}]`;function Qe(...t){console.warn(_c,...t)}function Ee(...t){console.error(_c,...t)}function ol(t){var i;let e=(i=t.message)!=null?i:"";t.context&&(e+=` ${t.context}`),t.file&&(e+=` ${(0,gc.i18n)("Read more: ")}
https://github.com/muxinc/elements/blob/main/errors/${t.file}`),Qe(e)}var se={AUTOPLAY:"autoplay",CROSSORIGIN:"crossorigin",LOOP:"loop",MUTED:"muted",PLAYSINLINE:"playsinline",PRELOAD:"preload"},wt={VOLUME:"volume",PLAYBACKRATE:"playbackrate",MUTED:"muted"},Z1={...se,...wt},Tc=Object.freeze({length:0,start(t){let e=t>>>0;if(e>=this.length)throw new DOMException(`Failed to execute 'start' on 'TimeRanges': The index provided (${e}) is greater than or equal to the maximum bound (${this.length}).`);return 0},end(t){let e=t>>>0;if(e>=this.length)throw new DOMException(`Failed to execute 'end' on 'TimeRanges': The index provided (${e}) is greater than or equal to the maximum bound (${this.length}).`);return 0}}),lf=Object.values(se).filter(t=>se.PLAYSINLINE!==t),df=Object.values(wt),uf=[...lf,...df],nl=class extends ye.HTMLElement{static get observedAttributes(){return uf}constructor(){super()}attributeChangedCallback(e,i,a){var r,o;switch(e){case wt.MUTED:{this.media&&(this.media.muted=a!=null,this.media.defaultMuted=a!=null);return}case wt.VOLUME:{let s=(r=ue(a))!=null?r:1;this.media&&(this.media.volume=s);return}case wt.PLAYBACKRATE:{let s=(o=ue(a))!=null?o:1;this.media&&(this.media.playbackRate=s,this.media.defaultPlaybackRate=s);return}}}play(){var e,i;return(i=(e=this.media)==null?void 0:e.play())!=null?i:Promise.reject()}pause(){var e;(e=this.media)==null||e.pause()}load(){var e;(e=this.media)==null||e.load()}get media(){var e;return(e=this.shadowRoot)==null?void 0:e.querySelector("mux-video")}get audioTracks(){return this.media.audioTracks}get videoTracks(){return this.media.videoTracks}get audioRenditions(){return this.media.audioRenditions}get videoRenditions(){return this.media.videoRenditions}get paused(){var e,i;return(i=(e=this.media)==null?void 0:e.paused)!=null?i:!0}get duration(){var e,i;return(i=(e=this.media)==null?void 0:e.duration)!=null?i:NaN}get ended(){var e,i;return(i=(e=this.media)==null?void 0:e.ended)!=null?i:!1}get buffered(){var e,i;return(i=(e=this.media)==null?void 0:e.buffered)!=null?i:Tc}get seekable(){var e,i;return(i=(e=this.media)==null?void 0:e.seekable)!=null?i:Tc}get readyState(){var e,i;return(i=(e=this.media)==null?void 0:e.readyState)!=null?i:0}get videoWidth(){var e,i;return(i=(e=this.media)==null?void 0:e.videoWidth)!=null?i:0}get videoHeight(){var e,i;return(i=(e=this.media)==null?void 0:e.videoHeight)!=null?i:0}get currentSrc(){var e,i;return(i=(e=this.media)==null?void 0:e.currentSrc)!=null?i:""}get currentTime(){var e,i;return(i=(e=this.media)==null?void 0:e.currentTime)!=null?i:0}set currentTime(e){this.media&&(this.media.currentTime=Number(e))}get volume(){var e,i;return(i=(e=this.media)==null?void 0:e.volume)!=null?i:1}set volume(e){this.media&&(this.media.volume=Number(e))}get playbackRate(){var e,i;return(i=(e=this.media)==null?void 0:e.playbackRate)!=null?i:1}set playbackRate(e){this.media&&(this.media.playbackRate=Number(e))}get defaultPlaybackRate(){var e;return(e=ue(this.getAttribute(wt.PLAYBACKRATE)))!=null?e:1}set defaultPlaybackRate(e){e!=null?this.setAttribute(wt.PLAYBACKRATE,`${e}`):this.removeAttribute(wt.PLAYBACKRATE)}get crossOrigin(){return cr(this,se.CROSSORIGIN)}set crossOrigin(e){this.setAttribute(se.CROSSORIGIN,`${e}`)}get autoplay(){return cr(this,se.AUTOPLAY)!=null}set autoplay(e){e?this.setAttribute(se.AUTOPLAY,typeof e=="string"?e:""):this.removeAttribute(se.AUTOPLAY)}get loop(){return cr(this,se.LOOP)!=null}set loop(e){e?this.setAttribute(se.LOOP,""):this.removeAttribute(se.LOOP)}get muted(){var e,i;return(i=(e=this.media)==null?void 0:e.muted)!=null?i:!1}set muted(e){this.media&&(this.media.muted=!!e)}get defaultMuted(){return cr(this,se.MUTED)!=null}set defaultMuted(e){e?this.setAttribute(se.MUTED,""):this.removeAttribute(se.MUTED)}get playsInline(){return cr(this,se.PLAYSINLINE)!=null}set playsInline(e){Ee("playsInline is set to true by default and is not currently supported as a setter.")}get preload(){return this.media?this.media.preload:this.getAttribute("preload")}set preload(e){["","none","metadata","auto"].includes(e)?this.setAttribute(se.PRELOAD,e):this.removeAttribute(se.PRELOAD)}};function cr(t,e){return t.media?t.media.getAttribute(e):t.getAttribute(e)}var sl=nl;var Mc=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},x=(t,e,i)=>(Mc(t,e,"read from private field"),i?i.call(t):e.get(t)),Xe=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},ht=(t,e,i,a)=>(Mc(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Zi,mn,ni,mr,Rt,xt,Dt,si,zi,hn,Pe,yc=1,kc=0,cf=1,mf={processCallback(t,e,i){if(i){for(let[a,r]of e)if(a in i){let o=i[a];typeof o=="boolean"&&r instanceof me&&typeof r.element[r.attributeName]=="boolean"?r.booleanValue=o:typeof o=="function"&&r instanceof me?r.element[r.attributeName]=o:r.value=o}}}},pt=class extends d.DocumentFragment{constructor(e,i,a=mf){var r;super(),Xe(this,Zi,void 0),Xe(this,mn,void 0),this.append(e.content.cloneNode(!0)),ht(this,Zi,Cc(this)),ht(this,mn,a),(r=a.createCallback)==null||r.call(a,this,x(this,Zi),i),a.processCallback(this,x(this,Zi),i)}update(e){x(this,mn).processCallback(this,x(this,Zi),e)}};Zi=new WeakMap;mn=new WeakMap;var Cc=(t,e=[])=>{let i,a;for(let r of t.attributes||[])if(r.value.includes("{{")){let o=new ll;for([i,a]of Ic(r.value))if(!i)o.append(a);else{let s=new me(t,r.name,r.namespaceURI);o.append(s),e.push([a,s])}r.value=o.toString()}for(let r of t.childNodes)if(r.nodeType===yc&&!(r instanceof HTMLTemplateElement))Cc(r,e);else{let o=r.data;if(r.nodeType===yc||o.includes("{{")){let s=[];if(o)for([i,a]of Ic(o))if(!i)s.push(new Text(a));else{let l=new ft(t);s.push(l),e.push([a,l])}else if(r instanceof HTMLTemplateElement){let l=new hr(t,r);s.push(l),e.push([l.expression,l])}r.replaceWith(...s.flatMap(l=>l.replacementNodes||[l]))}}return e},Sc={},Ic=t=>{let e="",i=0,a=Sc[t],r=0,o;if(a)return a;for(a=[];o=t[r];r++)o==="{"&&t[r+1]==="{"&&t[r-1]!=="\\"&&t[r+2]&&++i==1?(e&&a.push([kc,e]),e="",r++):o==="}"&&t[r+1]==="}"&&t[r-1]!=="\\"&&!--i?(a.push([cf,e.trim()]),e="",r++):e+=o||"";return e&&a.push([kc,(i>0?"{{":"")+e]),Sc[t]=a},hf=11,pn=class{get value(){return""}set value(e){}toString(){return this.value}},Lc=new WeakMap,ll=class{constructor(){Xe(this,ni,[])}[Symbol.iterator](){return x(this,ni).values()}get length(){return x(this,ni).length}item(e){return x(this,ni)[e]}append(...e){for(let i of e)i instanceof me&&Lc.set(i,this),x(this,ni).push(i)}toString(){return x(this,ni).join("")}};ni=new WeakMap;var me=class extends pn{constructor(e,i,a){super(),Xe(this,si),Xe(this,mr,""),Xe(this,Rt,void 0),Xe(this,xt,void 0),Xe(this,Dt,void 0),ht(this,Rt,e),ht(this,xt,i),ht(this,Dt,a)}get attributeName(){return x(this,xt)}get attributeNamespace(){return x(this,Dt)}get element(){return x(this,Rt)}get value(){return x(this,mr)}set value(e){x(this,mr)!==e&&(ht(this,mr,e),!x(this,si,zi)||x(this,si,zi).length===1?e==null?x(this,Rt).removeAttributeNS(x(this,Dt),x(this,xt)):x(this,Rt).setAttributeNS(x(this,Dt),x(this,xt),e):x(this,Rt).setAttributeNS(x(this,Dt),x(this,xt),x(this,si,zi).toString()))}get booleanValue(){return x(this,Rt).hasAttributeNS(x(this,Dt),x(this,xt))}set booleanValue(e){if(!x(this,si,zi)||x(this,si,zi).length===1)this.value=e?"":null;else throw new DOMException("Value is not fully templatized")}};mr=new WeakMap;Rt=new WeakMap;xt=new WeakMap;Dt=new WeakMap;si=new WeakSet;zi=function(){return Lc.get(this)};var ft=class extends pn{constructor(e,i){super(),Xe(this,hn,void 0),Xe(this,Pe,void 0),ht(this,hn,e),ht(this,Pe,i?[...i]:[new Text])}get replacementNodes(){return x(this,Pe)}get parentNode(){return x(this,hn)}get nextSibling(){return x(this,Pe)[x(this,Pe).length-1].nextSibling}get previousSibling(){return x(this,Pe)[0].previousSibling}get value(){return x(this,Pe).map(e=>e.textContent).join("")}set value(e){this.replace(e)}replace(...e){let i=e.flat().flatMap(a=>a==null?[new Text]:a.forEach?[...a]:a.nodeType===hf?[...a.childNodes]:a.nodeType?[a]:[new Text(a)]);i.length||i.push(new Text),ht(this,Pe,pf(x(this,Pe)[0].parentNode,x(this,Pe),i,this.nextSibling))}};hn=new WeakMap;Pe=new WeakMap;var hr=class extends ft{constructor(e,i){let a=i.getAttribute("directive")||i.getAttribute("type"),r=i.getAttribute("expression")||i.getAttribute(a)||"";r.startsWith("{{")&&(r=r.trim().slice(2,-2).trim()),super(e),this.expression=r,this.template=i,this.directive=a}};function pf(t,e,i,a=null){let r=0,o,s,l,u=i.length,m=e.length;for(;r<u&&r<m&&e[r]==i[r];)r++;for(;r<u&&r<m&&i[u-1]==e[m-1];)a=i[--m,--u];if(r==m)for(;r<u;)t.insertBefore(i[r++],a);if(r==u)for(;r<m;)t.removeChild(e[r++]);else{for(o=e[r];r<u;)l=i[r++],s=o?o.nextSibling:a,o==l?o=s:r<u&&i[r]==s?(t.replaceChild(l,o),o=s):t.insertBefore(l,o);for(;o!=a;)s=o.nextSibling,t.removeChild(o),o=s}return i}var wc={string:t=>String(t)},vn=class{constructor(e){this.template=e,this.state=void 0}},li=new WeakMap,di=new WeakMap,dl={partial:(t,e)=>{e[t.expression]=new vn(t.template)},if:(t,e)=>{var i;if(xc(t.expression,e))if(li.get(t)!==t.template){li.set(t,t.template);let a=new pt(t.template,e,En);t.replace(a),di.set(t,a)}else(i=di.get(t))==null||i.update(e);else t.replace(""),li.delete(t),di.delete(t)}},ff=Object.keys(dl),En={processCallback(t,e,i){var a,r;if(i)for(let[o,s]of e){if(s instanceof hr){if(!s.directive){let u=ff.find(m=>s.template.hasAttribute(m));u&&(s.directive=u,s.expression=s.template.getAttribute(u))}(a=dl[s.directive])==null||a.call(dl,s,i);continue}let l=xc(o,i);if(l instanceof vn){li.get(s)!==l.template?(li.set(s,l.template),l=new pt(l.template,l.state,En),s.value=l,di.set(s,l)):(r=di.get(s))==null||r.update(l.state);continue}l?(s instanceof me&&s.attributeName.startsWith("aria-")&&(l=String(l)),s instanceof me?typeof l=="boolean"?s.booleanValue=l:typeof l=="function"?s.element[s.attributeName]=l:s.value=l:(s.value=l,li.delete(s),di.delete(s))):s instanceof me?s.value=void 0:(s.value=void 0,li.delete(s),di.delete(s))}}},Rc={"!":t=>!t,"!!":t=>!!t,"==":(t,e)=>t==e,"!=":(t,e)=>t!=e,">":(t,e)=>t>e,">=":(t,e)=>t>=e,"<":(t,e)=>t<e,"<=":(t,e)=>t<=e,"??":(t,e)=>t!=null?t:e,"|":(t,e)=>{var i;return(i=wc[e])==null?void 0:i.call(wc,t)}};function vf(t){return Ef(t,{boolean:/true|false/,number:/-?\d+\.?\d*/,string:/(["'])((?:\\.|[^\\])*?)\1/,operator:/[!=><][=!]?|\?\?|\|/,ws:/\s+/,param:/[$a-z_][$\w]*/i}).filter(({type:e})=>e!=="ws")}function xc(t,e={}){var i,a,r,o,s,l,u;let m=vf(t);if(m.length===0||m.some(({type:_})=>!_))return pr(t);if(((i=m[0])==null?void 0:i.token)===">"){let _=e[(a=m[1])==null?void 0:a.token];if(!_)return pr(t);let g={...e};_.state=g;let f=m.slice(2);for(let v=0;v<f.length;v+=3){let N=(r=f[v])==null?void 0:r.token,T=(o=f[v+1])==null?void 0:o.token,C=(s=f[v+2])==null?void 0:s.token;N&&T==="="&&(g[N]=fr(C,e))}return _}if(m.length===1)return fn(m[0])?fr(m[0].token,e):pr(t);if(m.length===2){let _=(l=m[0])==null?void 0:l.token,g=Rc[_];if(!g||!fn(m[1]))return pr(t);let f=fr(m[1].token,e);return g(f)}if(m.length===3){let _=(u=m[1])==null?void 0:u.token,g=Rc[_];if(!g||!fn(m[0])||!fn(m[2]))return pr(t);let f=fr(m[0].token,e);if(_==="|")return g(f,m[2].token);let v=fr(m[2].token,e);return g(f,v)}}function pr(t){return console.warn(`Warning: invalid expression \`${t}\``),!1}function fn({type:t}){return["number","boolean","string","param"].includes(t)}function fr(t,e){let i=t[0],a=t.slice(-1);return t==="true"||t==="false"?t==="true":i===a&&["'",'"'].includes(i)?t.slice(1,-1):Wr(t)?parseFloat(t):e[t]}function Ef(t,e){let i,a,r,o=[];for(;t;){r=null,i=t.length;for(let s in e)a=e[s].exec(t),a&&a.index<i&&(r={token:a[0],type:s,matches:a.slice(1)},i=a.index);i&&o.push({token:t.substr(0,i),type:void 0}),r&&o.push(r),t=t.substr(i+(r?r.token.length:0))}return o}var fl=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},ml=(t,e,i)=>(fl(t,e,"read from private field"),i?i.call(t):e.get(t)),vr=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},ui=(t,e,i,a)=>(fl(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),ul=(t,e,i)=>(fl(t,e,"access private method"),i),Qi,bn,Xi,hl,Dc,gn,pl,cl={mediatargetlivewindow:"targetlivewindow",mediastreamtype:"streamtype"},Oc=V.createElement("template");Oc.innerHTML=`
  <style>
    :host {
      display: inline-block;
      line-height: 0;
    }

    media-controller {
      width: 100%;
      height: 100%;
    }

    media-captions-button:not([mediasubtitleslist]),
    media-captions-menu:not([mediasubtitleslist]),
    media-captions-menu-button:not([mediasubtitleslist]),
    media-audio-track-menu[mediaaudiotrackunavailable],
    media-audio-track-menu-button[mediaaudiotrackunavailable],
    media-rendition-menu[mediarenditionunavailable],
    media-rendition-menu-button[mediarenditionunavailable],
    media-volume-range[mediavolumeunavailable],
    media-airplay-button[mediaairplayunavailable],
    media-fullscreen-button[mediafullscreenunavailable],
    media-cast-button[mediacastunavailable],
    media-pip-button[mediapipunavailable] {
      display: none;
    }
  </style>
`;var ci=class extends d.HTMLElement{constructor(){super(),vr(this,hl),vr(this,gn),vr(this,Qi,void 0),vr(this,bn,void 0),vr(this,Xi,void 0),this.shadowRoot?this.renderRoot=this.shadowRoot:(this.renderRoot=this.attachShadow({mode:"open"}),this.createRenderer());let e=new MutationObserver(i=>{var a;this.mediaController&&!((a=this.mediaController)!=null&&a.breakpointsComputed)||i.some(r=>{let o=r.target;return o===this?!0:o.localName!=="media-controller"?!1:!!(cl[r.attributeName]||r.attributeName.startsWith("breakpoint"))})&&this.render()});e.observe(this,{attributes:!0}),e.observe(this.renderRoot,{attributes:!0,subtree:!0}),this.addEventListener(Ce.BREAKPOINTS_COMPUTED,this.render),ul(this,hl,Dc).call(this,"template")}get mediaController(){return this.renderRoot.querySelector("media-controller")}get template(){var e;return(e=ml(this,Qi))!=null?e:this.constructor.template}set template(e){ui(this,Xi,null),ui(this,Qi,e),this.createRenderer()}get props(){var e,i,a;let r=[...Array.from((i=(e=this.mediaController)==null?void 0:e.attributes)!=null?i:[]).filter(({name:s})=>cl[s]||s.startsWith("breakpoint")),...Array.from(this.attributes)],o={};for(let s of r){let l=(a=cl[s.name])!=null?a:Hd(s.name),{value:u}=s;u!=null?(Wr(u)&&(u=parseFloat(u)),o[l]=u===""?!0:u):o[l]=!1}return o}attributeChangedCallback(e,i,a){e==="template"&&i!=a&&ul(this,gn,pl).call(this)}connectedCallback(){ul(this,gn,pl).call(this)}createRenderer(){this.template&&this.template!==ml(this,bn)&&(ui(this,bn,this.template),this.renderer=new pt(this.template,this.props,this.constructor.processor),this.renderRoot.textContent="",this.renderRoot.append(Oc.content.cloneNode(!0),this.renderer))}render(){var e;(e=this.renderer)==null||e.update(this.props)}};Qi=new WeakMap;bn=new WeakMap;Xi=new WeakMap;hl=new WeakSet;Dc=function(t){if(Object.prototype.hasOwnProperty.call(this,t)){let e=this[t];delete this[t],this[t]=e}};gn=new WeakSet;pl=function(){var t;let e=this.getAttribute("template");if(!e||e===ml(this,Xi))return;let i=this.getRootNode(),a=(t=i==null?void 0:i.getElementById)==null?void 0:t.call(i,e);if(a){ui(this,Xi,e),ui(this,Qi,a),this.createRenderer();return}bf(e)&&(ui(this,Xi,e),gf(e).then(r=>{let o=V.createElement("template");o.innerHTML=r,ui(this,Qi,o),this.createRenderer()}).catch(console.error))};ci.observedAttributes=["template"];ci.processor=En;function bf(t){if(!/^(\/|\.\/|https?:\/\/)/.test(t))return!1;let e=/^https?:\/\//.test(t)?void 0:location.origin;try{new URL(t,e)}catch{return!1}return!0}async function gf(t){let e=await fetch(t);if(e.status!==200)throw new Error(`Failed to load resource: the server responded with a status of ${e.status}`);return e.text()}d.customElements.get("media-theme")||d.customElements.define("media-theme",ci);var Nc=`:host {
  --media-control-display: var(--controls);
  --media-loading-indicator-display: var(--loading-indicator);
  --media-dialog-display: var(--dialog);
  --media-play-button-display: var(--play-button);
  --media-live-button-display: var(--live-button);
  --media-seek-backward-button-display: var(--seek-backward-button);
  --media-seek-forward-button-display: var(--seek-forward-button);
  --media-mute-button-display: var(--mute-button);
  --media-captions-button-display: var(--captions-button);
  --media-captions-menu-button-display: var(--captions-menu-button, var(--media-captions-button-display));
  --media-rendition-menu-button-display: var(--rendition-menu-button);
  --media-audio-track-menu-button-display: var(--audio-track-menu-button);
  --media-airplay-button-display: var(--airplay-button);
  --media-pip-button-display: var(--pip-button);
  --media-fullscreen-button-display: var(--fullscreen-button);
  --media-cast-button-display: var(--cast-button, var(--_cast-button-drm-display));
  --media-playback-rate-button-display: var(--playback-rate-button);
  --media-playback-rate-menu-button-display: var(--playback-rate-menu-button);
  --media-volume-range-display: var(--volume-range);
  --media-time-range-display: var(--time-range);
  --media-time-display-display: var(--time-display);
  --media-duration-display-display: var(--duration-display);
  --media-title-display-display: var(--title-display);

  display: inline-block;
  line-height: 0;
  width: 100%;
}

a {
  color: #fff;
  font-size: 0.9em;
  text-decoration: underline;
}

media-theme {
  display: inline-block;
  line-height: 0;
  width: 100%;
  height: 100%;
  direction: ltr;
}

media-poster-image {
  display: inline-block;
  line-height: 0;
  width: 100%;
  height: 100%;
}

media-poster-image:not([src]):not([placeholdersrc]) {
  display: none;
}

::part(top),
[part~='top'] {
  --media-control-display: var(--controls, var(--top-controls));
  --media-play-button-display: var(--play-button, var(--top-play-button));
  --media-live-button-display: var(--live-button, var(--top-live-button));
  --media-seek-backward-button-display: var(--seek-backward-button, var(--top-seek-backward-button));
  --media-seek-forward-button-display: var(--seek-forward-button, var(--top-seek-forward-button));
  --media-mute-button-display: var(--mute-button, var(--top-mute-button));
  --media-captions-button-display: var(--captions-button, var(--top-captions-button));
  --media-captions-menu-button-display: var(
    --captions-menu-button,
    var(--media-captions-button-display, var(--top-captions-menu-button))
  );
  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--top-rendition-menu-button));
  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--top-audio-track-menu-button));
  --media-airplay-button-display: var(--airplay-button, var(--top-airplay-button));
  --media-pip-button-display: var(--pip-button, var(--top-pip-button));
  --media-fullscreen-button-display: var(--fullscreen-button, var(--top-fullscreen-button));
  --media-cast-button-display: var(--cast-button, var(--top-cast-button, var(--_cast-button-drm-display)));
  --media-playback-rate-button-display: var(--playback-rate-button, var(--top-playback-rate-button));
  --media-playback-rate-menu-button-display: var(
    --captions-menu-button,
    var(--media-playback-rate-button-display, var(--top-playback-rate-menu-button))
  );
  --media-volume-range-display: var(--volume-range, var(--top-volume-range));
  --media-time-range-display: var(--time-range, var(--top-time-range));
  --media-time-display-display: var(--time-display, var(--top-time-display));
  --media-duration-display-display: var(--duration-display, var(--top-duration-display));
  --media-title-display-display: var(--title-display, var(--top-title-display));
}

::part(center),
[part~='center'] {
  --media-control-display: var(--controls, var(--center-controls));
  --media-play-button-display: var(--play-button, var(--center-play-button));
  --media-live-button-display: var(--live-button, var(--center-live-button));
  --media-seek-backward-button-display: var(--seek-backward-button, var(--center-seek-backward-button));
  --media-seek-forward-button-display: var(--seek-forward-button, var(--center-seek-forward-button));
  --media-mute-button-display: var(--mute-button, var(--center-mute-button));
  --media-captions-button-display: var(--captions-button, var(--center-captions-button));
  --media-captions-menu-button-display: var(
    --captions-menu-button,
    var(--media-captions-button-display, var(--center-captions-menu-button))
  );
  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--center-rendition-menu-button));
  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--center-audio-track-menu-button));
  --media-airplay-button-display: var(--airplay-button, var(--center-airplay-button));
  --media-pip-button-display: var(--pip-button, var(--center-pip-button));
  --media-fullscreen-button-display: var(--fullscreen-button, var(--center-fullscreen-button));
  --media-cast-button-display: var(--cast-button, var(--center-cast-button, var(--_cast-button-drm-display)));
  --media-playback-rate-button-display: var(--playback-rate-button, var(--center-playback-rate-button));
  --media-playback-rate-menu-button-display: var(
    --playback-rate-menu-button,
    var(--media-playback-rate-button-display, var(--center-playback-rate-menu-button))
  );
  --media-volume-range-display: var(--volume-range, var(--center-volume-range));
  --media-time-range-display: var(--time-range, var(--center-time-range));
  --media-time-display-display: var(--time-display, var(--center-time-display));
  --media-duration-display-display: var(--duration-display, var(--center-duration-display));
}

::part(bottom),
[part~='bottom'] {
  --media-control-display: var(--controls, var(--bottom-controls));
  --media-play-button-display: var(--play-button, var(--bottom-play-button));
  --media-live-button-display: var(--live-button, var(--bottom-live-button));
  --media-seek-backward-button-display: var(--seek-backward-button, var(--bottom-seek-backward-button));
  --media-seek-forward-button-display: var(--seek-forward-button, var(--bottom-seek-forward-button));
  --media-mute-button-display: var(--mute-button, var(--bottom-mute-button));
  --media-captions-button-display: var(--captions-button, var(--bottom-captions-button));
  --media-captions-menu-button-display: var(
    --captions-menu-button,
    var(--media-captions-button-display, var(--bottom-captions-menu-button))
  );
  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--bottom-rendition-menu-button));
  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--bottom-audio-track-menu-button));
  --media-airplay-button-display: var(--airplay-button, var(--bottom-airplay-button));
  --media-pip-button-display: var(--pip-button, var(--bottom-pip-button));
  --media-fullscreen-button-display: var(--fullscreen-button, var(--bottom-fullscreen-button));
  --media-cast-button-display: var(--cast-button, var(--bottom-cast-button, var(--_cast-button-drm-display)));
  --media-playback-rate-button-display: var(--playback-rate-button, var(--bottom-playback-rate-button));
  --media-playback-rate-menu-button-display: var(
    --playback-rate-menu-button,
    var(--media-playback-rate-button-display, var(--bottom-playback-rate-menu-button))
  );
  --media-volume-range-display: var(--volume-range, var(--bottom-volume-range));
  --media-time-range-display: var(--time-range, var(--bottom-time-range));
  --media-time-display-display: var(--time-display, var(--bottom-time-display));
  --media-duration-display-display: var(--duration-display, var(--bottom-duration-display));
  --media-title-display-display: var(--title-display, var(--bottom-title-display));
}

:host([no-tooltips]) {
  --media-tooltip-display: none;
}
`;var Er=new WeakMap,El=class t{constructor(e,i){this.element=e;this.type=i;this.element.addEventListener(this.type,this);let a=Er.get(this.element);a&&a.set(this.type,this)}set(e){if(typeof e=="function")this.handleEvent=e.bind(this.element);else if(typeof e=="object"&&typeof e.handleEvent=="function")this.handleEvent=e.handleEvent.bind(e);else{this.element.removeEventListener(this.type,this);let i=Er.get(this.element);i&&i.delete(this.type)}}static for(e){Er.has(e.element)||Er.set(e.element,new Map);let i=e.attributeName.slice(2),a=Er.get(e.element);return a&&a.has(i)?a.get(i):new t(e.element,i)}};function Af(t,e){return t instanceof me&&t.attributeName.startsWith("on")?(El.for(t).set(e),t.element.removeAttributeNS(t.attributeNamespace,t.attributeName),!0):!1}function Tf(t,e){return e instanceof _n&&t instanceof ft?(e.renderInto(t),!0):!1}function yf(t,e){return e instanceof DocumentFragment&&t instanceof ft?(e.childNodes.length&&t.replace(...e.childNodes),!0):!1}function kf(t,e){if(t instanceof me){let i=t.attributeNamespace,a=t.element.getAttributeNS(i,t.attributeName);return String(e)!==a&&(t.value=String(e)),!0}return t.value=String(e),!0}function Sf(t,e){if(t instanceof me&&e instanceof Element){let i=t.element;return i[t.attributeName]!==e&&(t.element.removeAttributeNS(t.attributeNamespace,t.attributeName),i[t.attributeName]=e),!0}return!1}function If(t,e){if(typeof e=="boolean"&&t instanceof me){let i=t.attributeNamespace,a=t.element.hasAttributeNS(i,t.attributeName);return e!==a&&(t.booleanValue=e),!0}return!1}function Mf(t,e){return e===!1&&t instanceof ft?(t.replace(""),!0):!1}function Cf(t,e){Sf(t,e)||If(t,e)||Af(t,e)||Mf(t,e)||Tf(t,e)||yf(t,e)||kf(t,e)}var vl=new Map,Pc=new WeakMap,Uc=new WeakMap,_n=class{constructor(e,i,a){this.strings=e;this.values=i;this.processor=a;this.stringsKey=this.strings.join("")}get template(){if(vl.has(this.stringsKey))return vl.get(this.stringsKey);{let e=_i.createElement("template"),i=this.strings.length-1;return e.innerHTML=this.strings.reduce((a,r,o)=>a+r+(o<i?`{{ ${o} }}`:""),""),vl.set(this.stringsKey,e),e}}renderInto(e){var r;let i=this.template;if(Pc.get(e)!==i){Pc.set(e,i);let o=new pt(i,this.values,this.processor);Uc.set(e,o),e instanceof ft?e.replace(...o.children):e.appendChild(o);return}let a=Uc.get(e);(r=a==null?void 0:a.update)==null||r.call(a,this.values)}},Lf={processCallback(t,e,i){var a;if(i){for(let[r,o]of e)if(r in i){let s=(a=i[r])!=null?a:"";Cf(o,s)}}}};function br(t,...e){return new _n(t,e,Lf)}function Hc(t,e){t.renderInto(e)}var gr=require("@mux/playback-core"),wf=t=>{let{tokens:e}=t;return e.drm?":host(:not([cast-receiver])) { --_cast-button-drm-display: none; }":""},Bc=t=>br`
  <style>
    ${wf(t)}
    ${Nc}
  </style>
  ${Of(t)}
`,Rf=t=>{let e=t.hotKeys?`${t.hotKeys}`:"";return ur(t.streamType)==="live"&&(e+=" noarrowleft noarrowright"),e},xf={TOP:"top",CENTER:"center",BOTTOM:"bottom",LAYER:"layer",MEDIA_LAYER:"media-layer",POSTER_LAYER:"poster-layer",VERTICAL_LAYER:"vertical-layer",CENTERED_LAYER:"centered-layer",GESTURE_LAYER:"gesture-layer",CONTROLLER_LAYER:"controller",BUTTON:"button",RANGE:"range",DISPLAY:"display",CONTROL_BAR:"control-bar",MENU_BUTTON:"menu-button",MENU:"menu",OPTION:"option",POSTER:"poster",LIVE:"live",PLAY:"play",PRE_PLAY:"pre-play",SEEK_BACKWARD:"seek-backward",SEEK_FORWARD:"seek-forward",MUTE:"mute",CAPTIONS:"captions",AIRPLAY:"airplay",PIP:"pip",FULLSCREEN:"fullscreen",CAST:"cast",PLAYBACK_RATE:"playback-rate",VOLUME:"volume",TIME:"time",TITLE:"title",AUDIO_TRACK:"audio-track",RENDITION:"rendition"},Df=Object.values(xf).join(", "),Of=t=>{var e,i,a,r,o,s,l,u,m,_,g,f,v,N,T,C,P,j,ge,bt,ot,ca,ma,ha,pa,fa,va,Ea,ba,ga,_a,Aa,$r,nt,Ta;return br`
  <media-theme
    template="${t.themeTemplate||!1}"
    defaultstreamtype="${(e=t.defaultStreamType)!=null?e:!1}"
    hotkeys="${Rf(t)||!1}"
    nohotkeys="${t.noHotKeys||!t.hasSrc||!1}"
    noautoseektolive="${!!((i=t.streamType)!=null&&i.includes(gr.StreamTypes.LIVE))&&t.targetLiveWindow!==0}"
    novolumepref="${t.novolumepref||!1}"
    disabled="${!t.hasSrc||t.isDialogOpen}"
    audio="${(a=t.audio)!=null?a:!1}"
    style="${(r=pc({"--media-primary-color":t.primaryColor,"--media-secondary-color":t.secondaryColor,"--media-accent-color":t.accentColor}))!=null?r:!1}"
    defaultsubtitles="${!t.defaultHiddenCaptions}"
    forwardseekoffset="${(o=t.forwardSeekOffset)!=null?o:!1}"
    backwardseekoffset="${(s=t.backwardSeekOffset)!=null?s:!1}"
    playbackrates="${(l=t.playbackRates)!=null?l:!1}"
    defaultshowremainingtime="${(u=t.defaultShowRemainingTime)!=null?u:!1}"
    defaultduration="${(m=t.defaultDuration)!=null?m:!1}"
    hideduration="${(_=t.hideDuration)!=null?_:!1}"
    title="${(g=t.title)!=null?g:!1}"
    videotitle="${(f=t.videoTitle)!=null?f:!1}"
    proudlydisplaymuxbadge="${(v=t.proudlyDisplayMuxBadge)!=null?v:!1}"
    exportparts="${Df}"
    onclose="${t.onCloseErrorDialog}"
    onfocusin="${t.onFocusInErrorDialog}"
  >
    <mux-video
      slot="media"
      target-live-window="${(N=t.targetLiveWindow)!=null?N:!1}"
      stream-type="${(T=ur(t.streamType))!=null?T:!1}"
      crossorigin="${(C=t.crossOrigin)!=null?C:""}"
      playsinline
      autoplay="${(P=t.autoplay)!=null?P:!1}"
      muted="${(j=t.muted)!=null?j:!1}"
      loop="${(ge=t.loop)!=null?ge:!1}"
      preload="${(bt=t.preload)!=null?bt:!1}"
      debug="${(ot=t.debug)!=null?ot:!1}"
      prefer-cmcd="${(ca=t.preferCmcd)!=null?ca:!1}"
      disable-tracking="${(ma=t.disableTracking)!=null?ma:!1}"
      disable-cookies="${(ha=t.disableCookies)!=null?ha:!1}"
      prefer-playback="${(pa=t.preferPlayback)!=null?pa:!1}"
      start-time="${t.startTime!=null?t.startTime:!1}"
      beacon-collection-domain="${(fa=t.beaconCollectionDomain)!=null?fa:!1}"
      player-init-time="${(va=t.playerInitTime)!=null?va:!1}"
      player-software-name="${(Ea=t.playerSoftwareName)!=null?Ea:!1}"
      player-software-version="${(ba=t.playerSoftwareVersion)!=null?ba:!1}"
      env-key="${(ga=t.envKey)!=null?ga:!1}"
      custom-domain="${(_a=t.customDomain)!=null?_a:!1}"
      src="${t.src?t.src:t.playbackId?(0,gr.toMuxVideoURL)(t):!1}"
      cast-src="${t.src?t.src:t.playbackId?(0,gr.toMuxVideoURL)(t):!1}"
      cast-receiver="${(Aa=t.castReceiver)!=null?Aa:!1}"
      drm-token="${(nt=($r=t.tokens)==null?void 0:$r.drm)!=null?nt:!1}"
      exportparts="video"
    >
      ${t.storyboard?br`<track label="thumbnails" default kind="metadata" src="${t.storyboard}" />`:br``}
      <slot></slot>
    </mux-video>
    <slot name="poster" slot="poster">
      <media-poster-image
        part="poster"
        exportparts="poster, img"
        src="${t.poster?t.poster:!1}"
        placeholdersrc="${(Ta=t.placeholder)!=null?Ta:!1}"
      ></media-poster-image>
    </slot>
  </media-theme>
`};var A=require("@mux/playback-core"),$c=t=>t.charAt(0).toUpperCase()+t.slice(1),Nf=(t,e=!1)=>{var i,a;if(t.muxCode){let r=$c((i=t.errorCategory)!=null?i:"video"),o=(0,A.errorCategoryToTokenNameOrPrefix)((a=t.errorCategory)!=null?a:A.MuxErrorCategory.VIDEO);if(t.muxCode===A.MuxErrorCode.NETWORK_OFFLINE)return(0,A.i18n)("Your device appears to be offline",e);if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_EXPIRED)return(0,A.i18n)("{category} URL has expired",e).format({category:r});if([A.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH,A.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,A.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING,A.MuxErrorCode.NETWORK_TOKEN_MALFORMED].includes(t.muxCode))return(0,A.i18n)("{category} URL is formatted incorrectly",e).format({category:r});if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_MISSING)return(0,A.i18n)("Invalid {categoryName} URL",e).format({categoryName:o});if(t.muxCode===A.MuxErrorCode.NETWORK_NOT_FOUND)return(0,A.i18n)("{category} does not exist",e).format({category:r});if(t.muxCode===A.MuxErrorCode.NETWORK_NOT_READY){let s=t.streamType==="live"?"Live stream":"Video";return(0,A.i18n)("{mediaType} is not currently available",e).format({mediaType:s})}}if(t.code){if(t.code===A.MediaError.MEDIA_ERR_NETWORK)return(0,A.i18n)("Network Error",e);if(t.code===A.MediaError.MEDIA_ERR_DECODE)return(0,A.i18n)("Media Error",e);if(t.code===A.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED)return(0,A.i18n)("Source Not Supported",e)}return(0,A.i18n)("Error",e)},Pf=(t,e=!1)=>{var i,a;if(t.muxCode){let r=$c((i=t.errorCategory)!=null?i:"video"),o=(0,A.errorCategoryToTokenNameOrPrefix)((a=t.errorCategory)!=null?a:A.MuxErrorCategory.VIDEO);return t.muxCode===A.MuxErrorCode.NETWORK_OFFLINE?(0,A.i18n)("Check your internet connection and try reloading this video.",e):t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_EXPIRED?(0,A.i18n)("The video\u2019s secured {tokenNamePrefix}-token has expired.",e).format({tokenNamePrefix:o}):t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH?(0,A.i18n)("The video\u2019s playback ID does not match the one encoded in the {tokenNamePrefix}-token.",e).format({tokenNamePrefix:o}):t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_MALFORMED?(0,A.i18n)("{category} URL is formatted incorrectly",e).format({category:r}):[A.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,A.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING].includes(t.muxCode)?(0,A.i18n)("The {tokenNamePrefix}-token is formatted with incorrect information.",e).format({tokenNamePrefix:o}):[A.MuxErrorCode.NETWORK_TOKEN_MISSING,A.MuxErrorCode.NETWORK_INVALID_URL].includes(t.muxCode)?(0,A.i18n)("The video URL or {tokenNamePrefix}-token are formatted with incorrect or incomplete information.",e).format({tokenNamePrefix:o}):t.muxCode===A.MuxErrorCode.NETWORK_NOT_FOUND?"":t.message}return t.code&&(t.code===A.MediaError.MEDIA_ERR_NETWORK||t.code===A.MediaError.MEDIA_ERR_DECODE||t.code===A.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED),t.message},Wc=(t,e=!1)=>{let i=Nf(t,e).toString(),a=Pf(t,e).toString();return{title:i,message:a}},Uf=t=>{if(t.muxCode){if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_EXPIRED)return"403-expired-token.md";if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_MALFORMED)return"403-malformatted-token.md";if([A.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,A.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING].includes(t.muxCode))return"403-incorrect-aud-value.md";if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH)return"403-playback-id-mismatch.md";if(t.muxCode===A.MuxErrorCode.NETWORK_TOKEN_MISSING)return"missing-signed-tokens.md";if(t.muxCode===A.MuxErrorCode.NETWORK_NOT_FOUND)return"404-not-found.md";if(t.muxCode===A.MuxErrorCode.NETWORK_NOT_READY)return"412-not-playable.md"}if(t.code){if(t.code===A.MediaError.MEDIA_ERR_NETWORK)return"";if(t.code===A.MediaError.MEDIA_ERR_DECODE)return"media-decode-error.md";if(t.code===A.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED)return"media-src-not-supported.md"}return""},bl=(t,e)=>{let i=Uf(t);return{message:t.message,context:t.context,file:i}};var Fc=`<template id="media-theme-gerwig">
  <style>
    @keyframes pre-play-hide {
      0% {
        transform: scale(1);
        opacity: 1;
      }

      30% {
        transform: scale(0.7);
      }

      100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }

    :host {
      --_primary-color: var(--media-primary-color, #fff);
      --_secondary-color: var(--media-secondary-color, transparent);
      --_accent-color: var(--media-accent-color, #fa50b5);
      --_text-color: var(--media-text-color, #000);

      --media-icon-color: var(--_primary-color);
      --media-control-background: var(--_secondary-color);
      --media-control-hover-background: var(--_accent-color);
      --media-time-buffered-color: rgba(255, 255, 255, 0.4);
      --media-preview-time-text-shadow: none;
      --media-control-height: 14px;
      --media-control-padding: 6px;
      --media-tooltip-container-margin: 6px;
      --media-tooltip-distance: 18px;

      color: var(--_primary-color);
      display: inline-block;
      width: 100%;
      height: 100%;
    }

    :host([audio]) {
      --_secondary-color: var(--media-secondary-color, black);
      --media-preview-time-text-shadow: none;
    }

    :host([audio]) ::slotted([slot='media']) {
      height: 0px;
    }

    :host([audio]) media-loading-indicator {
      display: none;
    }

    :host([audio]) media-controller {
      background: transparent;
    }

    :host([audio]) media-controller::part(vertical-layer) {
      background: transparent;
    }

    :host([audio]) media-control-bar {
      width: 100%;
      background-color: var(--media-control-background);
    }

    /*
     * 0.433s is the transition duration for VTT Regions.
     * Borrowed here, so the captions don't move too fast.
     */
    media-controller {
      --media-webkit-text-track-transform: translateY(0) scale(0.98);
      --media-webkit-text-track-transition: transform 0.433s ease-out 0.3s;
    }
    media-controller:is([mediapaused], :not([userinactive])) {
      --media-webkit-text-track-transform: translateY(-50px) scale(0.98);
      --media-webkit-text-track-transition: transform 0.15s ease;
    }

    /*
     * CSS specific to iOS devices.
     * See: https://stackoverflow.com/questions/30102792/css-media-query-to-target-only-ios-devices/60220757#60220757
     */
    @supports (-webkit-touch-callout: none) {
      /* Disable subtitle adjusting for iOS Safari */
      media-controller[mediaisfullscreen] {
        --media-webkit-text-track-transform: unset;
        --media-webkit-text-track-transition: unset;
      }
    }

    media-time-range {
      --media-box-padding-left: 6px;
      --media-box-padding-right: 6px;
      --media-range-bar-color: var(--_accent-color);
      --media-time-range-buffered-color: var(--_primary-color);
      --media-range-track-color: transparent;
      --media-range-track-background: rgba(255, 255, 255, 0.4);
      --media-range-thumb-background: radial-gradient(
        circle,
        #000 0%,
        #000 25%,
        var(--_accent-color) 25%,
        var(--_accent-color)
      );
      --media-range-thumb-width: 12px;
      --media-range-thumb-height: 12px;
      --media-range-thumb-transform: scale(0);
      --media-range-thumb-transition: transform 0.3s;
      --media-range-thumb-opacity: 1;
      --media-preview-background: var(--_primary-color);
      --media-box-arrow-background: var(--_primary-color);
      --media-preview-thumbnail-border: 5px solid var(--_primary-color);
      --media-preview-border-radius: 5px;
      --media-text-color: var(--_text-color);
      --media-control-hover-background: transparent;
      --media-preview-chapter-text-shadow: none;
      color: var(--_accent-color);
      padding: 0 6px;
    }

    :host([audio]) media-time-range {
      --media-preview-time-padding: 1.5px 6px;
      --media-preview-box-margin: 0 0 -5px;
    }

    media-time-range:hover {
      --media-range-thumb-transform: scale(1);
    }

    media-preview-thumbnail {
      border-bottom-width: 0;
    }

    [part~='menu'] {
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.1);
      bottom: 50px;
      padding: 2.5px 10px;
    }

    [part~='menu']::part(indicator) {
      fill: var(--_accent-color);
    }

    [part~='menu']::part(menu-item) {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 6px 10px;
      min-height: 34px;
    }

    [part~='menu']::part(checked) {
      font-weight: 700;
    }

    media-captions-menu,
    media-rendition-menu,
    media-audio-track-menu,
    media-playback-rate-menu {
      position: absolute; /* ensure they don't take up space in DOM on load */
      --media-menu-background: var(--_primary-color);
      --media-menu-item-checked-background: transparent;
      --media-text-color: var(--_text-color);
      --media-menu-item-hover-background: transparent;
      --media-menu-item-hover-outline: var(--_accent-color) solid 1px;
    }

    media-rendition-menu {
      min-width: 140px;
    }

    /* The icon is a circle so make it 16px high instead of 14px for more balance. */
    media-audio-track-menu-button {
      --media-control-padding: 5px;
      --media-control-height: 16px;
    }

    media-playback-rate-menu-button {
      --media-control-padding: 6px 3px;
      min-width: 4.4ch;
    }

    media-playback-rate-menu {
      --media-menu-flex-direction: row;
      --media-menu-item-checked-background: var(--_accent-color);
      --media-menu-item-checked-indicator-display: none;
      margin-right: 6px;
      padding: 0;
      --media-menu-gap: 0.25em;
    }

    media-playback-rate-menu[part~='menu']::part(menu-item) {
      padding: 6px 6px 6px 8px;
    }

    media-playback-rate-menu[part~='menu']::part(checked) {
      color: #fff;
    }

    :host(:not([audio])) media-time-range {
      /* Adding px is required here for calc() */
      --media-range-padding: 0px;
      background: transparent;
      z-index: 10;
      height: 10px;
      bottom: -3px;
      width: 100%;
    }

    media-control-bar :is([role='button'], [role='switch'], button) {
      line-height: 0;
    }

    media-control-bar :is([part*='button'], [part*='range'], [part*='display']) {
      border-radius: 3px;
    }

    .spacer {
      flex-grow: 1;
      background-color: var(--media-control-background, rgba(20, 20, 30, 0.7));
    }

    media-control-bar[slot~='top-chrome'] {
      min-height: 42px;
      pointer-events: none;
    }

    media-control-bar {
      --gradient-steps:
        hsl(0 0% 0% / 0) 0%, hsl(0 0% 0% / 0.013) 8.1%, hsl(0 0% 0% / 0.049) 15.5%, hsl(0 0% 0% / 0.104) 22.5%,
        hsl(0 0% 0% / 0.175) 29%, hsl(0 0% 0% / 0.259) 35.3%, hsl(0 0% 0% / 0.352) 41.2%, hsl(0 0% 0% / 0.45) 47.1%,
        hsl(0 0% 0% / 0.55) 52.9%, hsl(0 0% 0% / 0.648) 58.8%, hsl(0 0% 0% / 0.741) 64.7%, hsl(0 0% 0% / 0.825) 71%,
        hsl(0 0% 0% / 0.896) 77.5%, hsl(0 0% 0% / 0.951) 84.5%, hsl(0 0% 0% / 0.987) 91.9%, hsl(0 0% 0%) 100%;
    }

    :host([title]:not([audio])) media-control-bar[slot='top-chrome']::before {
      content: '';
      position: absolute;
      width: 100%;
      padding-bottom: min(100px, 25%);
      background: linear-gradient(to top, var(--gradient-steps));
      opacity: 0.8;
      pointer-events: none;
    }

    :host(:not([audio])) media-control-bar[part~='bottom']::before {
      content: '';
      position: absolute;
      width: 100%;
      bottom: 0;
      left: 0;
      padding-bottom: min(100px, 25%);
      background: linear-gradient(to bottom, var(--gradient-steps));
      opacity: 0.8;
      z-index: 1;
      pointer-events: none;
    }

    media-control-bar[part~='bottom'] > * {
      z-index: 20;
    }

    media-control-bar[part~='bottom'] {
      padding: 6px 6px;
    }

    media-control-bar[slot~='top-chrome'] > * {
      --media-control-background: transparent;
      --media-control-hover-background: transparent;
      position: relative;
    }

    media-controller::part(vertical-layer) {
      transition: background-color 1s;
    }

    media-controller:is([mediapaused], :not([userinactive]))::part(vertical-layer) {
      background-color: var(--controls-backdrop-color, var(--controls, transparent));
      transition: background-color 0.25s;
    }

    .center-controls {
      --media-button-icon-width: 100%;
      --media-button-icon-height: auto;
      --media-tooltip-display: none;
      pointer-events: none;
      width: 100%;
      display: flex;
      flex-flow: row;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(0 0 2px rgb(0 0 0 / 0.25)) drop-shadow(0 0 6px rgb(0 0 0 / 0.25));
      paint-order: stroke;
      stroke: rgba(102, 102, 102, 1);
      stroke-width: 0.3px;
      text-shadow:
        0 0 2px rgb(0 0 0 / 0.25),
        0 0 6px rgb(0 0 0 / 0.25);
    }

    .center-controls media-play-button {
      --media-control-background: transparent;
      --media-control-hover-background: transparent;
      --media-control-padding: 0;
      width: 40px;
    }

    [breakpointsm] .center-controls media-play-button {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      transition: background 0.4s;
      padding: 24px;
      --media-control-background: #000;
      --media-control-hover-background: var(--_accent-color);
    }

    .center-controls media-seek-backward-button,
    .center-controls media-seek-forward-button {
      --media-control-background: transparent;
      --media-control-hover-background: transparent;
      padding: 0;
      margin: 0 20px;
      width: max(33px, min(8%, 40px));
    }

    [breakpointsm]:not([audio]) .center-controls.pre-playback {
      display: grid;
      align-items: initial;
      justify-content: initial;
      height: 100%;
      overflow: hidden;
    }

    [breakpointsm]:not([audio]) .center-controls.pre-playback media-play-button {
      place-self: var(--_pre-playback-place, center);
      grid-area: 1 / 1;
      margin: 16px;
    }

    /* Show and hide controls or pre-playback state */

    [breakpointsm]:is([mediahasplayed], :not([mediapaused])):not([audio])
      .center-controls.pre-playback
      media-play-button {
      /* Using \`forwards\` would lead to a laggy UI after the animation got in the end state */
      animation: 0.3s linear pre-play-hide;
      opacity: 0;
      pointer-events: none;
    }

    .autoplay-unmute {
      --media-control-hover-background: transparent;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      filter: drop-shadow(0 0 2px rgb(0 0 0 / 0.25)) drop-shadow(0 0 6px rgb(0 0 0 / 0.25));
    }

    .autoplay-unmute-btn {
      --media-control-height: 16px;
      border-radius: 8px;
      background: #000;
      color: var(--_primary-color);
      display: flex;
      align-items: center;
      padding: 8px 16px;
      font-size: 18px;
      font-weight: 500;
      cursor: pointer;
    }

    .autoplay-unmute-btn:hover {
      background: var(--_accent-color);
    }

    [breakpointsm] .autoplay-unmute-btn {
      --media-control-height: 30px;
      padding: 14px 24px;
      font-size: 26px;
    }

    .autoplay-unmute-btn svg {
      margin: 0 6px 0 0;
    }

    [breakpointsm] .autoplay-unmute-btn svg {
      margin: 0 10px 0 0;
    }

    media-controller:not([audio]):not([mediahasplayed]) *:is(media-control-bar, media-time-range) {
      display: none;
    }

    media-error-dialog:not([mediaerrorcode]) {
      opacity: 0;
    }

    media-loading-indicator {
      --media-loading-icon-width: 100%;
      --media-button-icon-height: auto;
      display: var(--media-control-display, var(--media-loading-indicator-display, flex));
      pointer-events: none;
      position: absolute;
      width: min(15%, 150px);
      flex-flow: row;
      align-items: center;
      justify-content: center;
    }

    /* Intentionally don't target the div for transition but the children
     of the div. Prevents messing with media-chrome's autohide feature. */
    media-loading-indicator + div * {
      transition: opacity 0.15s;
      opacity: 1;
    }

    media-loading-indicator[medialoading]:not([mediapaused]) ~ div > * {
      opacity: 0;
      transition-delay: 400ms;
    }

    media-volume-range {
      width: min(100%, 100px);
      --media-range-padding-left: 10px;
      --media-range-padding-right: 10px;
      --media-range-thumb-width: 12px;
      --media-range-thumb-height: 12px;
      --media-range-thumb-background: radial-gradient(
        circle,
        #000 0%,
        #000 25%,
        var(--_primary-color) 25%,
        var(--_primary-color)
      );
      --media-control-hover-background: none;
    }

    media-time-display {
      white-space: nowrap;
    }

    /* Generic style for explicitly disabled controls */
    media-control-bar[part~='bottom'] [disabled],
    media-control-bar[part~='bottom'] [aria-disabled='true'] {
      opacity: 60%;
      cursor: not-allowed;
    }

    media-text-display {
      --media-font-size: 16px;
      --media-control-padding: 14px;
      font-weight: 500;
    }

    media-play-button.animated *:is(g, path) {
      transition: all 0.3s;
    }

    media-play-button.animated[mediapaused] .pause-icon-pt1 {
      opacity: 0;
    }

    media-play-button.animated[mediapaused] .pause-icon-pt2 {
      transform-origin: center center;
      transform: scaleY(0);
    }

    media-play-button.animated[mediapaused] .play-icon {
      clip-path: inset(0 0 0 0);
    }

    media-play-button.animated:not([mediapaused]) .play-icon {
      clip-path: inset(0 0 0 100%);
    }

    media-seek-forward-button,
    media-seek-backward-button {
      --media-font-weight: 400;
    }

    .mute-icon {
      display: inline-block;
    }

    .mute-icon :is(path, g) {
      transition: opacity 0.5s;
    }

    .muted {
      opacity: 0;
    }

    media-mute-button[mediavolumelevel='low'] :is(.volume-medium, .volume-high),
    media-mute-button[mediavolumelevel='medium'] :is(.volume-high) {
      opacity: 0;
    }

    media-mute-button[mediavolumelevel='off'] .unmuted {
      opacity: 0;
    }

    media-mute-button[mediavolumelevel='off'] .muted {
      opacity: 1;
    }

    /**
     * Our defaults for these buttons are to hide them at small sizes
     * users can override this with CSS
     */
    media-controller:not([breakpointsm]):not([audio]) {
      --bottom-play-button: none;
      --bottom-seek-backward-button: none;
      --bottom-seek-forward-button: none;
      --bottom-time-display: none;
      --bottom-playback-rate-menu-button: none;
      --bottom-pip-button: none;
    }

    [part='mux-badge'] {
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 2;
      opacity: 0.6;
      transition:
        opacity 0.2s ease-in-out,
        bottom 0.2s ease-in-out;
    }

    [part='mux-badge']:hover {
      opacity: 1;
    }

    [part='mux-badge'] a {
      font-size: 14px;
      font-family: var(--_font-family);
      color: var(--_primary-color);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    [part='mux-badge'] .mux-badge-text {
      transition: opacity 0.5s ease-in-out;
      opacity: 0;
    }

    [part='mux-badge'] .mux-badge-logo {
      width: 40px;
      height: auto;
      display: inline-block;
    }

    [part='mux-badge'] .mux-badge-logo svg {
      width: 100%;
      height: 100%;
      fill: white;
    }

    media-controller:not([userinactive]):not([mediahasplayed]) [part='mux-badge'],
    media-controller:not([userinactive]) [part='mux-badge'],
    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] {
      transition: bottom 0.1s ease-in-out;
    }

    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] {
      transition: bottom 0.2s ease-in-out 0.62s;
    }

    media-controller:not([userinactive]) [part='mux-badge'] .mux-badge-text,
    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] .mux-badge-text {
      opacity: 1;
    }

    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] .mux-badge-text {
      opacity: 0;
    }

    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] {
      bottom: 10px;
    }

    media-controller:not([userinactive]):not([mediahasplayed]) [part='mux-badge'] {
      bottom: 10px;
    }

    media-controller:not([userinactive])[mediahasplayed] [part='mux-badge'],
    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] {
      bottom: calc(28px + var(--media-control-height, 0px) + var(--media-control-padding, 0px) * 2);
    }
  </style>

  <template partial="TitleDisplay">
    <template if="videotitle">
      <template if="videotitle != true">
        <media-text-display part="top title display" class="title-display">{{videotitle}}</media-text-display>
      </template>
    </template>
    <template if="!videotitle">
      <template if="title">
        <media-text-display part="top title display" class="title-display">{{title}}</media-text-display>
      </template>
    </template>
  </template>

  <template partial="PlayButton">
    <media-play-button
      part="{{section ?? 'bottom'}} play button"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
      class="animated"
    >
      <svg aria-hidden="true" viewBox="0 0 18 14" slot="icon">
        <g class="play-icon">
          <path
            d="M15.5987 6.2911L3.45577 0.110898C2.83667 -0.204202 2.06287 0.189698 2.06287 0.819798V13.1802C2.06287 13.8103 2.83667 14.2042 3.45577 13.8891L15.5987 7.7089C16.2178 7.3938 16.2178 6.6061 15.5987 6.2911Z"
          />
        </g>
        <g class="pause-icon">
          <path
            class="pause-icon-pt1"
            d="M5.90709 0H2.96889C2.46857 0 2.06299 0.405585 2.06299 0.9059V13.0941C2.06299 13.5944 2.46857 14 2.96889 14H5.90709C6.4074 14 6.81299 13.5944 6.81299 13.0941V0.9059C6.81299 0.405585 6.4074 0 5.90709 0Z"
          />
          <path
            class="pause-icon-pt2"
            d="M15.1571 0H12.2189C11.7186 0 11.313 0.405585 11.313 0.9059V13.0941C11.313 13.5944 11.7186 14 12.2189 14H15.1571C15.6574 14 16.063 13.5944 16.063 13.0941V0.9059C16.063 0.405585 15.6574 0 15.1571 0Z"
          />
        </g>
      </svg>
    </media-play-button>
  </template>

  <template partial="PrePlayButton">
    <media-play-button
      part="{{section ?? 'center'}} play button pre-play"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    >
      <svg aria-hidden="true" viewBox="0 0 18 14" slot="icon" style="transform: translate(3px, 0)">
        <path
          d="M15.5987 6.2911L3.45577 0.110898C2.83667 -0.204202 2.06287 0.189698 2.06287 0.819798V13.1802C2.06287 13.8103 2.83667 14.2042 3.45577 13.8891L15.5987 7.7089C16.2178 7.3938 16.2178 6.6061 15.5987 6.2911Z"
        />
      </svg>
    </media-play-button>
  </template>

  <template partial="SeekBackwardButton">
    <media-seek-backward-button
      seekoffset="{{backwardseekoffset}}"
      part="{{section ?? 'bottom'}} seek-backward button"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    >
      <svg viewBox="0 0 22 14" aria-hidden="true" slot="icon">
        <path
          d="M3.65 2.07888L0.0864 6.7279C-0.0288 6.87812 -0.0288 7.12188 0.0864 7.2721L3.65 11.9211C3.7792 12.0896 4 11.9703 4 11.7321V2.26787C4 2.02968 3.7792 1.9104 3.65 2.07888Z"
        />
        <text transform="translate(6 12)" style="font-size: 14px; font-family: 'ArialMT', 'Arial'">
          {{backwardseekoffset}}
        </text>
      </svg>
    </media-seek-backward-button>
  </template>

  <template partial="SeekForwardButton">
    <media-seek-forward-button
      seekoffset="{{forwardseekoffset}}"
      part="{{section ?? 'bottom'}} seek-forward button"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    >
      <svg viewBox="0 0 22 14" aria-hidden="true" slot="icon">
        <g>
          <text transform="translate(-1 12)" style="font-size: 14px; font-family: 'ArialMT', 'Arial'">
            {{forwardseekoffset}}
          </text>
          <path
            d="M18.35 11.9211L21.9136 7.2721C22.0288 7.12188 22.0288 6.87812 21.9136 6.7279L18.35 2.07888C18.2208 1.91041 18 2.02968 18 2.26787V11.7321C18 11.9703 18.2208 12.0896 18.35 11.9211Z"
          />
        </g>
      </svg>
    </media-seek-forward-button>
  </template>

  <template partial="MuteButton">
    <media-mute-button part="bottom mute button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <svg viewBox="0 0 18 14" slot="icon" class="mute-icon" aria-hidden="true">
        <g class="unmuted">
          <path
            d="M6.76786 1.21233L3.98606 3.98924H1.19937C0.593146 3.98924 0.101743 4.51375 0.101743 5.1607V6.96412L0 6.99998L0.101743 7.03583V8.83926C0.101743 9.48633 0.593146 10.0108 1.19937 10.0108H3.98606L6.76773 12.7877C7.23561 13.2547 8 12.9007 8 12.2171V1.78301C8 1.09925 7.23574 0.745258 6.76786 1.21233Z"
          />
          <path
            class="volume-low"
            d="M10 3.54781C10.7452 4.55141 11.1393 5.74511 11.1393 6.99991C11.1393 8.25471 10.7453 9.44791 10 10.4515L10.7988 11.0496C11.6734 9.87201 12.1356 8.47161 12.1356 6.99991C12.1356 5.52821 11.6735 4.12731 10.7988 2.94971L10 3.54781Z"
          />
          <path
            class="volume-medium"
            d="M12.3778 2.40086C13.2709 3.76756 13.7428 5.35806 13.7428 7.00026C13.7428 8.64246 13.2709 10.233 12.3778 11.5992L13.2106 12.1484C14.2107 10.6185 14.739 8.83796 14.739 7.00016C14.739 5.16236 14.2107 3.38236 13.2106 1.85156L12.3778 2.40086Z"
          />
          <path
            class="volume-high"
            d="M15.5981 0.75L14.7478 1.2719C15.7937 2.9919 16.3468 4.9723 16.3468 7C16.3468 9.0277 15.7937 11.0082 14.7478 12.7281L15.5981 13.25C16.7398 11.3722 17.343 9.211 17.343 7C17.343 4.789 16.7398 2.6268 15.5981 0.75Z"
          />
        </g>
        <g class="muted">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M4.39976 4.98924H1.19937C1.19429 4.98924 1.17777 4.98961 1.15296 5.01609C1.1271 5.04369 1.10174 5.09245 1.10174 5.1607V8.83926C1.10174 8.90761 1.12714 8.95641 1.15299 8.984C1.17779 9.01047 1.1943 9.01084 1.19937 9.01084H4.39977L7 11.6066V2.39357L4.39976 4.98924ZM7.47434 1.92006C7.4743 1.9201 7.47439 1.92002 7.47434 1.92006V1.92006ZM6.76773 12.7877L3.98606 10.0108H1.19937C0.593146 10.0108 0.101743 9.48633 0.101743 8.83926V7.03583L0 6.99998L0.101743 6.96412V5.1607C0.101743 4.51375 0.593146 3.98924 1.19937 3.98924H3.98606L6.76786 1.21233C7.23574 0.745258 8 1.09925 8 1.78301V12.2171C8 12.9007 7.23561 13.2547 6.76773 12.7877Z"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M15.2677 9.30323C15.463 9.49849 15.7796 9.49849 15.9749 9.30323C16.1701 9.10796 16.1701 8.79138 15.9749 8.59612L14.2071 6.82841L15.9749 5.06066C16.1702 4.8654 16.1702 4.54882 15.9749 4.35355C15.7796 4.15829 15.4631 4.15829 15.2678 4.35355L13.5 6.1213L11.7322 4.35348C11.537 4.15822 11.2204 4.15822 11.0251 4.35348C10.8298 4.54874 10.8298 4.86532 11.0251 5.06058L12.7929 6.82841L11.0251 8.59619C10.8299 8.79146 10.8299 9.10804 11.0251 9.3033C11.2204 9.49856 11.537 9.49856 11.7323 9.3033L13.5 7.53552L15.2677 9.30323Z"
          />
        </g>
      </svg>
    </media-mute-button>
  </template>

  <template partial="PipButton">
    <media-pip-button part="bottom pip button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="icon">
        <path
          d="M15.9891 0H2.011C0.9004 0 0 0.9003 0 2.0109V11.989C0 13.0996 0.9004 14 2.011 14H15.9891C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.9891 0ZM17 11.9891C17 12.5465 16.5465 13 15.9891 13H2.011C1.4536 13 1.0001 12.5465 1.0001 11.9891V2.0109C1.0001 1.4535 1.4536 0.9999 2.011 0.9999H15.9891C16.5465 0.9999 17 1.4535 17 2.0109V11.9891Z"
        />
        <path
          d="M15.356 5.67822H8.19523C8.03253 5.67822 7.90063 5.81012 7.90063 5.97282V11.3836C7.90063 11.5463 8.03253 11.6782 8.19523 11.6782H15.356C15.5187 11.6782 15.6506 11.5463 15.6506 11.3836V5.97282C15.6506 5.81012 15.5187 5.67822 15.356 5.67822Z"
        />
      </svg>
    </media-pip-button>
  </template>

  <template partial="CaptionsMenu">
    <media-captions-menu-button part="bottom captions button">
      <svg aria-hidden="true" viewBox="0 0 18 14" slot="on">
        <path
          d="M15.989 0H2.011C0.9004 0 0 0.9003 0 2.0109V11.9891C0 13.0997 0.9004 14 2.011 14H15.989C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.989 0ZM4.2292 8.7639C4.5954 9.1902 5.0935 9.4031 5.7233 9.4031C6.1852 9.4031 6.5544 9.301 6.8302 9.0969C7.1061 8.8933 7.2863 8.614 7.3702 8.26H8.4322C8.3062 8.884 8.0093 9.3733 7.5411 9.7273C7.0733 10.0813 6.4703 10.2581 5.732 10.2581C5.108 10.2581 4.5699 10.1219 4.1168 9.8489C3.6637 9.5759 3.3141 9.1946 3.0685 8.7058C2.8224 8.2165 2.6994 7.6511 2.6994 7.009C2.6994 6.3611 2.8224 5.7927 3.0685 5.3034C3.3141 4.8146 3.6637 4.4323 4.1168 4.1559C4.5699 3.88 5.108 3.7418 5.732 3.7418C6.4703 3.7418 7.0733 3.922 7.5411 4.2818C8.0094 4.6422 8.3062 5.1461 8.4322 5.794H7.3702C7.2862 5.4283 7.106 5.1368 6.8302 4.921C6.5544 4.7052 6.1852 4.5968 5.7233 4.5968C5.0934 4.5968 4.5954 4.8116 4.2292 5.2404C3.8635 5.6696 3.6804 6.259 3.6804 7.009C3.6804 7.7531 3.8635 8.3381 4.2292 8.7639ZM11.0974 8.7639C11.4636 9.1902 11.9617 9.4031 12.5915 9.4031C13.0534 9.4031 13.4226 9.301 13.6984 9.0969C13.9743 8.8933 14.1545 8.614 14.2384 8.26H15.3004C15.1744 8.884 14.8775 9.3733 14.4093 9.7273C13.9415 10.0813 13.3385 10.2581 12.6002 10.2581C11.9762 10.2581 11.4381 10.1219 10.985 9.8489C10.5319 9.5759 10.1823 9.1946 9.9367 8.7058C9.6906 8.2165 9.5676 7.6511 9.5676 7.009C9.5676 6.3611 9.6906 5.7927 9.9367 5.3034C10.1823 4.8146 10.5319 4.4323 10.985 4.1559C11.4381 3.88 11.9762 3.7418 12.6002 3.7418C13.3385 3.7418 13.9415 3.922 14.4093 4.2818C14.8776 4.6422 15.1744 5.1461 15.3004 5.794H14.2384C14.1544 5.4283 13.9742 5.1368 13.6984 4.921C13.4226 4.7052 13.0534 4.5968 12.5915 4.5968C11.9616 4.5968 11.4636 4.8116 11.0974 5.2404C10.7317 5.6696 10.5486 6.259 10.5486 7.009C10.5486 7.7531 10.7317 8.3381 11.0974 8.7639Z"
        />
      </svg>
      <svg aria-hidden="true" viewBox="0 0 18 14" slot="off">
        <path
          d="M5.73219 10.258C5.10819 10.258 4.57009 10.1218 4.11699 9.8488C3.66389 9.5758 3.31429 9.1945 3.06869 8.7057C2.82259 8.2164 2.69958 7.651 2.69958 7.0089C2.69958 6.361 2.82259 5.7926 3.06869 5.3033C3.31429 4.8145 3.66389 4.4322 4.11699 4.1558C4.57009 3.8799 5.10819 3.7417 5.73219 3.7417C6.47049 3.7417 7.07348 3.9219 7.54128 4.2817C8.00958 4.6421 8.30638 5.146 8.43238 5.7939H7.37039C7.28639 5.4282 7.10618 5.1367 6.83039 4.9209C6.55459 4.7051 6.18538 4.5967 5.72348 4.5967C5.09358 4.5967 4.59559 4.8115 4.22939 5.2403C3.86369 5.6695 3.68058 6.2589 3.68058 7.0089C3.68058 7.753 3.86369 8.338 4.22939 8.7638C4.59559 9.1901 5.09368 9.403 5.72348 9.403C6.18538 9.403 6.55459 9.3009 6.83039 9.0968C7.10629 8.8932 7.28649 8.6139 7.37039 8.2599H8.43238C8.30638 8.8839 8.00948 9.3732 7.54128 9.7272C7.07348 10.0812 6.47049 10.258 5.73219 10.258Z"
        />
        <path
          d="M12.6003 10.258C11.9763 10.258 11.4382 10.1218 10.9851 9.8488C10.532 9.5758 10.1824 9.1945 9.93685 8.7057C9.69075 8.2164 9.56775 7.651 9.56775 7.0089C9.56775 6.361 9.69075 5.7926 9.93685 5.3033C10.1824 4.8145 10.532 4.4322 10.9851 4.1558C11.4382 3.8799 11.9763 3.7417 12.6003 3.7417C13.3386 3.7417 13.9416 3.9219 14.4094 4.2817C14.8777 4.6421 15.1745 5.146 15.3005 5.7939H14.2385C14.1545 5.4282 13.9743 5.1367 13.6985 4.9209C13.4227 4.7051 13.0535 4.5967 12.5916 4.5967C11.9617 4.5967 11.4637 4.8115 11.0975 5.2403C10.7318 5.6695 10.5487 6.2589 10.5487 7.0089C10.5487 7.753 10.7318 8.338 11.0975 8.7638C11.4637 9.1901 11.9618 9.403 12.5916 9.403C13.0535 9.403 13.4227 9.3009 13.6985 9.0968C13.9744 8.8932 14.1546 8.6139 14.2385 8.2599H15.3005C15.1745 8.8839 14.8776 9.3732 14.4094 9.7272C13.9416 10.0812 13.3386 10.258 12.6003 10.258Z"
        />
        <path
          d="M15.9891 1C16.5465 1 17 1.4535 17 2.011V11.9891C17 12.5465 16.5465 13 15.9891 13H2.0109C1.4535 13 1 12.5465 1 11.9891V2.0109C1 1.4535 1.4535 0.9999 2.0109 0.9999L15.9891 1ZM15.9891 0H2.0109C0.9003 0 0 0.9003 0 2.0109V11.9891C0 13.0997 0.9003 14 2.0109 14H15.9891C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.9891 0Z"
        />
      </svg>
    </media-captions-menu-button>
    <media-captions-menu
      hidden
      anchor="auto"
      part="bottom captions menu"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
      exportparts="menu-item"
    >
      <div slot="checked-indicator">
        <style>
          .indicator {
            position: relative;
            top: 1px;
            width: 0.9em;
            height: auto;
            fill: var(--_accent-color);
            margin-right: 5px;
          }

          [aria-checked='false'] .indicator {
            display: none;
          }
        </style>
        <svg viewBox="0 0 14 18" class="indicator">
          <path
            d="M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027"
            fill-rule="evenodd"
          />
        </svg></div
    ></media-captions-menu>
  </template>

  <template partial="AirplayButton">
    <media-airplay-button part="bottom airplay button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="icon">
        <path
          d="M16.1383 0H1.8618C0.8335 0 0 0.8335 0 1.8617V10.1382C0 11.1664 0.8335 12 1.8618 12H3.076C3.1204 11.9433 3.1503 11.8785 3.2012 11.826L4.004 11H1.8618C1.3866 11 1 10.6134 1 10.1382V1.8617C1 1.3865 1.3866 0.9999 1.8618 0.9999H16.1383C16.6135 0.9999 17.0001 1.3865 17.0001 1.8617V10.1382C17.0001 10.6134 16.6135 11 16.1383 11H13.9961L14.7989 11.826C14.8499 11.8785 14.8798 11.9432 14.9241 12H16.1383C17.1665 12 18.0001 11.1664 18.0001 10.1382V1.8617C18 0.8335 17.1665 0 16.1383 0Z"
        />
        <path
          d="M9.55061 8.21903C9.39981 8.06383 9.20001 7.98633 9.00011 7.98633C8.80021 7.98633 8.60031 8.06383 8.44951 8.21903L4.09771 12.697C3.62471 13.1838 3.96961 13.9998 4.64831 13.9998H13.3518C14.0304 13.9998 14.3754 13.1838 13.9023 12.697L9.55061 8.21903Z"
        />
      </svg>
    </media-airplay-button>
  </template>

  <template partial="FullscreenButton">
    <media-fullscreen-button part="bottom fullscreen button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="enter">
        <path
          d="M1.00745 4.39539L1.01445 1.98789C1.01605 1.43049 1.47085 0.978289 2.02835 0.979989L6.39375 0.992589L6.39665 -0.007411L2.03125 -0.020011C0.920646 -0.023211 0.0176463 0.874489 0.0144463 1.98509L0.00744629 4.39539H1.00745Z"
        />
        <path
          d="M17.0144 2.03431L17.0076 4.39541H18.0076L18.0144 2.03721C18.0176 0.926712 17.1199 0.0237125 16.0093 0.0205125L11.6439 0.0078125L11.641 1.00781L16.0064 1.02041C16.5638 1.02201 17.016 1.47681 17.0144 2.03431Z"
        />
        <path
          d="M16.9925 9.60498L16.9855 12.0124C16.9839 12.5698 16.5291 13.022 15.9717 13.0204L11.6063 13.0078L11.6034 14.0078L15.9688 14.0204C17.0794 14.0236 17.9823 13.1259 17.9855 12.0153L17.9925 9.60498H16.9925Z"
        />
        <path
          d="M0.985626 11.9661L0.992426 9.60498H-0.0074737L-0.0142737 11.9632C-0.0174737 13.0738 0.880226 13.9767 1.99083 13.98L6.35623 13.9926L6.35913 12.9926L1.99373 12.98C1.43633 12.9784 0.983926 12.5236 0.985626 11.9661Z"
        />
      </svg>
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="exit">
        <path
          d="M5.39655 -0.0200195L5.38955 2.38748C5.38795 2.94488 4.93315 3.39708 4.37565 3.39538L0.0103463 3.38278L0.00744629 4.38278L4.37285 4.39538C5.48345 4.39858 6.38635 3.50088 6.38965 2.39028L6.39665 -0.0200195H5.39655Z"
        />
        <path
          d="M12.6411 2.36891L12.6479 0.0078125H11.6479L11.6411 2.36601C11.6379 3.47651 12.5356 4.37951 13.6462 4.38271L18.0116 4.39531L18.0145 3.39531L13.6491 3.38271C13.0917 3.38111 12.6395 2.92641 12.6411 2.36891Z"
        />
        <path
          d="M12.6034 14.0204L12.6104 11.613C12.612 11.0556 13.0668 10.6034 13.6242 10.605L17.9896 10.6176L17.9925 9.61759L13.6271 9.60499C12.5165 9.60179 11.6136 10.4995 11.6104 11.6101L11.6034 14.0204H12.6034Z"
        />
        <path
          d="M5.359 11.6315L5.3522 13.9926H6.3522L6.359 11.6344C6.3622 10.5238 5.4645 9.62088 4.3539 9.61758L-0.0115043 9.60498L-0.0144043 10.605L4.351 10.6176C4.9084 10.6192 5.3607 11.074 5.359 11.6315Z"
        />
      </svg>
    </media-fullscreen-button>
  </template>

  <template partial="CastButton">
    <media-cast-button part="bottom cast button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="enter">
        <path
          d="M16.0072 0H2.0291C0.9185 0 0.0181 0.9003 0.0181 2.011V5.5009C0.357 5.5016 0.6895 5.5275 1.0181 5.5669V2.011C1.0181 1.4536 1.4716 1 2.029 1H16.0072C16.5646 1 17.0181 1.4536 17.0181 2.011V11.9891C17.0181 12.5465 16.5646 13 16.0072 13H8.4358C8.4746 13.3286 8.4999 13.6611 8.4999 13.9999H16.0071C17.1177 13.9999 18.018 13.0996 18.018 11.989V2.011C18.0181 0.9003 17.1178 0 16.0072 0ZM0 6.4999V7.4999C3.584 7.4999 6.5 10.4159 6.5 13.9999H7.5C7.5 9.8642 4.1357 6.4999 0 6.4999ZM0 8.7499V9.7499C2.3433 9.7499 4.25 11.6566 4.25 13.9999H5.25C5.25 11.1049 2.895 8.7499 0 8.7499ZM0.0181 11V14H3.0181C3.0181 12.3431 1.675 11 0.0181 11Z"
        />
      </svg>
      <svg viewBox="0 0 18 14" aria-hidden="true" slot="exit">
        <path
          d="M15.9891 0H2.01103C0.900434 0 3.35947e-05 0.9003 3.35947e-05 2.011V5.5009C0.338934 5.5016 0.671434 5.5275 1.00003 5.5669V2.011C1.00003 1.4536 1.45353 1 2.01093 1H15.9891C16.5465 1 17 1.4536 17 2.011V11.9891C17 12.5465 16.5465 13 15.9891 13H8.41773C8.45653 13.3286 8.48183 13.6611 8.48183 13.9999H15.989C17.0996 13.9999 17.9999 13.0996 17.9999 11.989V2.011C18 0.9003 17.0997 0 15.9891 0ZM-0.0180664 6.4999V7.4999C3.56593 7.4999 6.48193 10.4159 6.48193 13.9999H7.48193C7.48193 9.8642 4.11763 6.4999 -0.0180664 6.4999ZM-0.0180664 8.7499V9.7499C2.32523 9.7499 4.23193 11.6566 4.23193 13.9999H5.23193C5.23193 11.1049 2.87693 8.7499 -0.0180664 8.7499ZM3.35947e-05 11V14H3.00003C3.00003 12.3431 1.65693 11 3.35947e-05 11Z"
        />
        <path d="M2.15002 5.634C5.18352 6.4207 7.57252 8.8151 8.35282 11.8499H15.8501V2.1499H2.15002V5.634Z" />
      </svg>
    </media-cast-button>
  </template>

  <template partial="LiveButton">
    <media-live-button part="{{section ?? 'top'}} live button" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <span slot="text">Live</span>
    </media-live-button>
  </template>

  <template partial="PlaybackRateMenu">
    <media-playback-rate-menu-button part="bottom playback-rate button"></media-playback-rate-menu-button>
    <media-playback-rate-menu
      hidden
      anchor="auto"
      rates="{{playbackrates}}"
      exportparts="menu-item"
      part="bottom playback-rate menu"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    ></media-playback-rate-menu>
  </template>

  <template partial="VolumeRange">
    <media-volume-range
      part="bottom volume range"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    ></media-volume-range>
  </template>

  <template partial="TimeDisplay">
    <media-time-display
      remaining="{{defaultshowremainingtime}}"
      showduration="{{!hideduration}}"
      part="bottom time display"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    ></media-time-display>
  </template>

  <template partial="TimeRange">
    <media-time-range part="bottom time range" disabled="{{disabled}}" aria-disabled="{{disabled}}">
      <media-preview-thumbnail slot="preview"></media-preview-thumbnail>
      <media-preview-chapter-display slot="preview"></media-preview-chapter-display>
      <media-preview-time-display slot="preview"></media-preview-time-display>
      <div slot="preview" part="arrow"></div>
    </media-time-range>
  </template>

  <template partial="AudioTrackMenu">
    <media-audio-track-menu-button part="bottom audio-track button">
      <svg aria-hidden="true" slot="icon" viewBox="0 0 18 16">
        <path d="M9 15A7 7 0 1 1 9 1a7 7 0 0 1 0 14Zm0 1A8 8 0 1 0 9 0a8 8 0 0 0 0 16Z" />
        <path
          d="M5.2 6.3a.5.5 0 0 1 .5.5v2.4a.5.5 0 1 1-1 0V6.8a.5.5 0 0 1 .5-.5Zm2.4-2.4a.5.5 0 0 1 .5.5v7.2a.5.5 0 0 1-1 0V4.4a.5.5 0 0 1 .5-.5ZM10 5.5a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm2.4-.8a.5.5 0 0 1 .5.5v5.6a.5.5 0 0 1-1 0V5.2a.5.5 0 0 1 .5-.5Z"
        />
      </svg>
    </media-audio-track-menu-button>
    <media-audio-track-menu
      hidden
      anchor="auto"
      part="bottom audio-track menu"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
      exportparts="menu-item"
    >
      <div slot="checked-indicator">
        <style>
          .indicator {
            position: relative;
            top: 1px;
            width: 0.9em;
            height: auto;
            fill: var(--_accent-color);
            margin-right: 5px;
          }

          [aria-checked='false'] .indicator {
            display: none;
          }
        </style>
        <svg viewBox="0 0 14 18" class="indicator">
          <path
            d="M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027"
            fill-rule="evenodd"
          />
        </svg>
      </div>
    </media-audio-track-menu>
  </template>

  <template partial="RenditionMenu">
    <media-rendition-menu-button part="bottom rendition button">
      <svg aria-hidden="true" slot="icon" viewBox="0 0 18 14">
        <path
          d="M2.25 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM9 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm6.75 0a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"
        />
      </svg>
    </media-rendition-menu-button>
    <media-rendition-menu
      hidden
      anchor="auto"
      part="bottom rendition menu"
      disabled="{{disabled}}"
      aria-disabled="{{disabled}}"
    >
      <div slot="checked-indicator">
        <style>
          .indicator {
            position: relative;
            top: 1px;
            width: 0.9em;
            height: auto;
            fill: var(--_accent-color);
            margin-right: 5px;
          }

          [aria-checked='false'] .indicator {
            opacity: 0;
          }
        </style>
        <svg viewBox="0 0 14 18" class="indicator">
          <path
            d="M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027"
            fill-rule="evenodd"
          />
        </svg>
      </div>
    </media-rendition-menu>
  </template>

  <template partial="MuxBadge">
    <div part="mux-badge">
      <a href="https://www.mux.com/player" target="_blank">
        <span class="mux-badge-text">Powered by</span>
        <div class="mux-badge-logo">
          <svg
            viewBox="0 0 1600 500"
            style="fill-rule: evenodd; clip-rule: evenodd; stroke-linejoin: round; stroke-miterlimit: 2"
          >
            <g>
              <path
                d="M994.287,93.486c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m0,-93.486c-34.509,-0 -62.484,27.976 -62.484,62.486l0,187.511c0,68.943 -56.09,125.033 -125.032,125.033c-68.942,-0 -125.03,-56.09 -125.03,-125.033l0,-187.511c0,-34.51 -27.976,-62.486 -62.485,-62.486c-34.509,-0 -62.484,27.976 -62.484,62.486l0,187.511c0,137.853 112.149,250.003 249.999,250.003c137.851,-0 250.001,-112.15 250.001,-250.003l0,-187.511c0,-34.51 -27.976,-62.486 -62.485,-62.486"
                style="fill-rule: nonzero"
              ></path>
              <path
                d="M1537.51,468.511c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m-275.883,-218.509l-143.33,143.329c-24.402,24.402 -24.402,63.966 0,88.368c24.402,24.402 63.967,24.402 88.369,-0l143.33,-143.329l143.328,143.329c24.402,24.4 63.967,24.402 88.369,-0c24.403,-24.402 24.403,-63.966 0.001,-88.368l-143.33,-143.329l0.001,-0.004l143.329,-143.329c24.402,-24.402 24.402,-63.965 0,-88.367c-24.402,-24.402 -63.967,-24.402 -88.369,-0l-143.329,143.328l-143.329,-143.328c-24.402,-24.401 -63.967,-24.402 -88.369,-0c-24.402,24.402 -24.402,63.965 0,88.367l143.329,143.329l0,0.004Z"
                style="fill-rule: nonzero"
              ></path>
              <path
                d="M437.511,468.521c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m23.915,-463.762c-23.348,-9.672 -50.226,-4.327 -68.096,13.544l-143.331,143.329l-143.33,-143.329c-17.871,-17.871 -44.747,-23.216 -68.096,-13.544c-23.349,9.671 -38.574,32.455 -38.574,57.729l0,375.026c0,34.51 27.977,62.486 62.487,62.486c34.51,-0 62.486,-27.976 62.486,-62.486l0,-224.173l80.843,80.844c24.404,24.402 63.965,24.402 88.369,-0l80.843,-80.844l0,224.173c0,34.51 27.976,62.486 62.486,62.486c34.51,-0 62.486,-27.976 62.486,-62.486l0,-375.026c0,-25.274 -15.224,-48.058 -38.573,-57.729"
                style="fill-rule: nonzero"
              ></path>
            </g>
          </svg>
        </div>
      </a>
    </div>
  </template>

  <media-controller
    part="controller"
    defaultstreamtype="{{defaultstreamtype ?? 'on-demand'}}"
    breakpoints="sm:470"
    gesturesdisabled="{{disabled}}"
    hotkeys="{{hotkeys}}"
    nohotkeys="{{nohotkeys}}"
    novolumepref="{{novolumepref}}"
    audio="{{audio}}"
    noautoseektolive="{{noautoseektolive}}"
    defaultsubtitles="{{defaultsubtitles}}"
    defaultduration="{{defaultduration ?? false}}"
    keyboardforwardseekoffset="{{forwardseekoffset}}"
    keyboardbackwardseekoffset="{{backwardseekoffset}}"
    exportparts="layer, media-layer, poster-layer, vertical-layer, centered-layer, gesture-layer"
    style="--_pre-playback-place:{{preplaybackplace ?? 'center'}}"
  >
    <slot name="media" slot="media"></slot>
    <slot name="poster" slot="poster"></slot>

    <media-loading-indicator slot="centered-chrome" noautohide></media-loading-indicator>
    <media-error-dialog slot="dialog" noautohide></media-error-dialog>

    <template if="!audio">
      <!-- Pre-playback UI -->
      <!-- same for both on-demand and live -->
      <div slot="centered-chrome" class="center-controls pre-playback">
        <template if="!breakpointsm">{{>PlayButton section="center"}}</template>
        <template if="breakpointsm">{{>PrePlayButton section="center"}}</template>
      </div>

      <!-- Mux Badge -->
      <template if="proudlydisplaymuxbadge"> {{>MuxBadge}} </template>

      <!-- Autoplay centered unmute button -->
      <!--
        todo: figure out how show this with available state variables
        needs to show when:
        - autoplay is enabled
        - playback has been successful
        - audio is muted
        - in place / instead of the pre-plaback play button
        - not to show again after user has interacted with this button
          - OR user has interacted with the mute button in the control bar
      -->
      <!--
        There should be a >MuteButton to the left of the "Unmute" text, but a templating bug
        makes it appear even if commented out in the markup, add it back when code is un-commented
      -->
      <!-- <div slot="centered-chrome" class="autoplay-unmute">
        <div role="button" class="autoplay-unmute-btn">Unmute</div>
      </div> -->

      <template if="streamtype == 'on-demand'">
        <template if="breakpointsm">
          <media-control-bar part="control-bar top" slot="top-chrome">{{>TitleDisplay}} </media-control-bar>
        </template>
        {{>TimeRange}}
        <media-control-bar part="control-bar bottom">
          {{>PlayButton}} {{>SeekBackwardButton}} {{>SeekForwardButton}} {{>TimeDisplay}} {{>MuteButton}}
          {{>VolumeRange}}
          <div class="spacer"></div>
          {{>RenditionMenu}} {{>PlaybackRateMenu}} {{>AudioTrackMenu}} {{>CaptionsMenu}} {{>AirplayButton}}
          {{>CastButton}} {{>PipButton}} {{>FullscreenButton}}
        </media-control-bar>
      </template>

      <template if="streamtype == 'live'">
        <media-control-bar part="control-bar top" slot="top-chrome">
          {{>LiveButton}}
          <template if="breakpointsm"> {{>TitleDisplay}} </template>
        </media-control-bar>
        <template if="targetlivewindow > 0">{{>TimeRange}}</template>
        <media-control-bar part="control-bar bottom">
          {{>PlayButton}}
          <template if="targetlivewindow > 0">{{>SeekBackwardButton}} {{>SeekForwardButton}}</template>
          {{>MuteButton}} {{>VolumeRange}}
          <div class="spacer"></div>
          {{>RenditionMenu}} {{>AudioTrackMenu}} {{>CaptionsMenu}} {{>AirplayButton}} {{>CastButton}} {{>PipButton}}
          {{>FullscreenButton}}
        </media-control-bar>
      </template>
    </template>

    <template if="audio">
      <template if="streamtype == 'on-demand'">
        <template if="title">
          <media-control-bar part="control-bar top">{{>TitleDisplay}}</media-control-bar>
        </template>
        <media-control-bar part="control-bar bottom">
          {{>PlayButton}}
          <template if="breakpointsm"> {{>SeekBackwardButton}} {{>SeekForwardButton}} </template>
          {{>MuteButton}}
          <template if="breakpointsm">{{>VolumeRange}}</template>
          {{>TimeDisplay}} {{>TimeRange}}
          <template if="breakpointsm">{{>PlaybackRateMenu}}</template>
          {{>AirplayButton}} {{>CastButton}}
        </media-control-bar>
      </template>

      <template if="streamtype == 'live'">
        <template if="title">
          <media-control-bar part="control-bar top">{{>TitleDisplay}}</media-control-bar>
        </template>
        <media-control-bar part="control-bar bottom">
          {{>PlayButton}} {{>LiveButton section="bottom"}} {{>MuteButton}}
          <template if="breakpointsm">
            {{>VolumeRange}}
            <template if="targetlivewindow > 0"> {{>SeekBackwardButton}} {{>SeekForwardButton}} </template>
          </template>
          <template if="targetlivewindow > 0"> {{>TimeDisplay}} {{>TimeRange}} </template>
          <template if="!targetlivewindow"><div class="spacer"></div></template>
          {{>AirplayButton}} {{>CastButton}}
        </media-control-bar>
      </template>
    </template>

    <slot></slot>
  </media-controller>
</template>
`;function Vc({anchor:t,floating:e,placement:i}){let a=Bf({anchor:t,floating:e}),{x:r,y:o}=Wf(a,i);return{x:r,y:o}}function Bf({anchor:t,floating:e}){return{anchor:$f(t,e.offsetParent),floating:{x:0,y:0,width:e.offsetWidth,height:e.offsetHeight}}}function $f(t,e){var i;let a=t.getBoundingClientRect(),r=(i=e==null?void 0:e.getBoundingClientRect())!=null?i:{x:0,y:0};return{x:a.x-r.x,y:a.y-r.y,width:a.width,height:a.height}}function Wf({anchor:t,floating:e},i){let a=Ff(i)==="x"?"y":"x",r=a==="y"?"height":"width",o=Kc(i),s=t.x+t.width/2-e.width/2,l=t.y+t.height/2-e.height/2,u=t[r]/2-e[r]/2,m;switch(o){case"top":m={x:s,y:t.y-e.height};break;case"bottom":m={x:s,y:t.y+t.height};break;case"right":m={x:t.x+t.width,y:l};break;case"left":m={x:t.x-e.width,y:l};break;default:m={x:t.x,y:t.y}}switch(i.split("-")[1]){case"start":m[a]-=u;break;case"end":m[a]+=u;break}return m}function Kc(t){return t.split("-")[0]}function Ff(t){return["top","bottom"].includes(Kc(t))?"y":"x"}var Ot=class extends Event{constructor({action:e="auto",relatedTarget:i,...a}){super("invoke",a),this.action=e,this.relatedTarget=i}},An=class extends Event{constructor({newState:e,oldState:i,...a}){super("toggle",a),this.newState=e,this.oldState=i}};var wl=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},H=(t,e,i)=>(wl(t,e,"read from private field"),i?i.call(t):e.get(t)),G=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Je=(t,e,i,a)=>(wl(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),q=(t,e,i)=>(wl(t,e,"access private method"),i),je,hi,Nt,Tn,yn,pi,Tr,gl,Gc,In,kn,_l,Al,Yc,Tl,qc,yl,Zc,Ji,ji,ea,yr,Mn,Rl,kl,zc,xl,Qc,Sl,Xc,Dl,Jc,Il,jc,Ml,em,_r,Cn,Cl,tm,Ar,Ln,Sn,Ll;function et({type:t,text:e,value:i,checked:a}){let r=V.createElement("media-chrome-menu-item");r.type=t!=null?t:"",r.part.add("menu-item"),t&&r.part.add(t),r.value=i,r.checked=a;let o=V.createElement("span");return o.textContent=e,r.append(o),r}function Ue(t,e){let i=t.querySelector(`:scope > [slot="${e}"]`);if((i==null?void 0:i.nodeName)=="SLOT"&&(i=i.assignedElements({flatten:!0})[0]),i)return i=i.cloneNode(!0),i;let a=t.shadowRoot.querySelector(`[name="${e}"] > svg`);return a?a.cloneNode(!0):""}function Vf(t){return`
    <style>
      :host {
        font: var(--media-font,
          var(--media-font-weight, normal)
          var(--media-font-size, 14px) /
          var(--media-text-content-height, var(--media-control-height, 24px))
          var(--media-font-family, helvetica neue, segoe ui, roboto, arial, sans-serif));
        color: var(--media-text-color, var(--media-primary-color, rgb(238 238 238)));
        --_menu-bg: rgb(20 20 30 / .8);
        background: var(--media-menu-background, var(--media-control-background, var(--media-secondary-color, var(--_menu-bg))));
        border-radius: var(--media-menu-border-radius);
        border: var(--media-menu-border, none);
        display: var(--media-menu-display, inline-flex);
        transition: var(--media-menu-transition-in,
          visibility 0s,
          opacity .2s ease-out,
          transform .15s ease-out,
          left .2s ease-in-out,
          min-width .2s ease-in-out,
          min-height .2s ease-in-out
        ) !important;
        
        visibility: var(--media-menu-visibility, visible);
        opacity: var(--media-menu-opacity, 1);
        max-height: var(--media-menu-max-height, var(--_menu-max-height, 300px));
        transform: var(--media-menu-transform-in, translateY(0) scale(1));
        flex-direction: column;
        
        min-height: 0;
        position: relative;
        bottom: var(--_menu-bottom);
        box-sizing: border-box;
      } 

      @-moz-document url-prefix() {
        :host{
          --_menu-bg: rgb(20 20 30);
        }
      }

      :host([hidden]) {
        transition: var(--media-menu-transition-out,
          visibility .15s ease-in,
          opacity .15s ease-in,
          transform .15s ease-in
        ) !important;
        visibility: var(--media-menu-hidden-visibility, hidden);
        opacity: var(--media-menu-hidden-opacity, 0);
        max-height: var(--media-menu-hidden-max-height,
          var(--media-menu-max-height, var(--_menu-max-height, 300px)));
        transform: var(--media-menu-transform-out, translateY(2px) scale(.99));
        pointer-events: none;
      }

      :host([slot="submenu"]) {
        background: none;
        width: 100%;
        min-height: 100%;
        position: absolute;
        bottom: 0;
        right: -100%;
      }

      #container {
        display: flex;
        flex-direction: column;
        min-height: 0;
        transition: transform .2s ease-out;
        transform: translate(0, 0);
      }

      #container.has-expanded {
        transition: transform .2s ease-in;
        transform: translate(-100%, 0);
      }

      button {
        background: none;
        color: inherit;
        border: none;
        padding: 0;
        font: inherit;
        outline: inherit;
        display: inline-flex;
        align-items: center;
      }

      slot[name="header"][hidden] {
        display: none;
      }

      slot[name="header"] > *,
      slot[name="header"]::slotted(*) {
        padding: .4em .7em;
        border-bottom: 1px solid rgb(255 255 255 / .25);
        cursor: var(--media-cursor, default);
      }

      slot[name="header"] > button[part~="back"],
      slot[name="header"]::slotted(button[part~="back"]) {
        cursor: var(--media-cursor, pointer);
      }

      svg[part~="back"] {
        height: var(--media-menu-icon-height, var(--media-control-height, 24px));
        fill: var(--media-icon-color, var(--media-primary-color, rgb(238 238 238)));
        display: block;
        margin-right: .5ch;
      }

      slot:not([name]) {
        gap: var(--media-menu-gap);
        flex-direction: var(--media-menu-flex-direction, column);
        overflow: var(--media-menu-overflow, hidden auto);
        display: flex;
        min-height: 0;
      }

      :host([role="menu"]) slot:not([name]) {
        padding-block: .4em;
      }

      slot:not([name])::slotted([role="menu"]) {
        background: none;
      }

      media-chrome-menu-item > span {
        margin-right: .5ch;
        max-width: var(--media-menu-item-max-width);
        text-overflow: ellipsis;
        overflow: hidden;
      }
    </style>
    <style id="layout-row" media="width:0">

      slot[name="header"] > *,
      slot[name="header"]::slotted(*) {
        padding: .4em .5em;
      }

      slot:not([name]) {
        gap: var(--media-menu-gap, .25em);
        flex-direction: var(--media-menu-flex-direction, row);
        padding-inline: .5em;
      }

      media-chrome-menu-item {
        padding: .3em .5em;
      }

      media-chrome-menu-item[aria-checked="true"] {
        background: var(--media-menu-item-checked-background, rgb(255 255 255 / .2));
      }

      
      media-chrome-menu-item::part(checked-indicator) {
        display: var(--media-menu-item-checked-indicator-display, none);
      }
    </style>
    <div id="container">
      <slot name="header" hidden>
        <button part="back button" aria-label="Back to previous menu">
          <slot name="back-icon">
            <svg aria-hidden="true" viewBox="0 0 20 24" part="back indicator">
              <path d="m11.88 17.585.742-.669-4.2-4.665 4.2-4.666-.743-.669-4.803 5.335 4.803 5.334Z"/>
            </svg>
          </slot>
          <slot name="title"></slot>
        </button>
      </slot>
      <slot></slot>
    </div>
    <slot name="checked-indicator" hidden></slot>
  `}var mi={STYLE:"style",HIDDEN:"hidden",DISABLED:"disabled",ANCHOR:"anchor"},oe=class extends d.HTMLElement{constructor(){if(super(),G(this,gl),G(this,kn),G(this,Al),G(this,Tl),G(this,yl),G(this,ea),G(this,Mn),G(this,kl),G(this,xl),G(this,Sl),G(this,Dl),G(this,Il),G(this,Ml),G(this,_r),G(this,Cl),G(this,Ar),G(this,Sn),G(this,je,null),G(this,hi,null),G(this,Nt,null),G(this,Tn,new Set),G(this,yn,void 0),G(this,pi,!1),G(this,Tr,null),G(this,In,()=>{let e=H(this,Tn),i=new Set(this.items);for(let a of e)i.has(a)||this.dispatchEvent(new CustomEvent("removemenuitem",{detail:a}));for(let a of i)e.has(a)||this.dispatchEvent(new CustomEvent("addmenuitem",{detail:a}));Je(this,Tn,i)}),G(this,Ji,()=>{q(this,ea,yr).call(this),q(this,Mn,Rl).call(this,!1)}),G(this,ji,()=>{q(this,ea,yr).call(this)}),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}this.container=this.shadowRoot.querySelector("#container"),this.defaultSlot=this.shadowRoot.querySelector("slot:not([name])"),this.shadowRoot.addEventListener("slotchange",this),Je(this,yn,new MutationObserver(H(this,In))),H(this,yn).observe(this.defaultSlot,{childList:!0})}static get observedAttributes(){return[mi.DISABLED,mi.HIDDEN,mi.STYLE,mi.ANCHOR,L.MEDIA_CONTROLLER]}static formatMenuItemText(e,i){return e}enable(){this.addEventListener("click",this),this.addEventListener("focusout",this),this.addEventListener("keydown",this),this.addEventListener("invoke",this),this.addEventListener("toggle",this)}disable(){this.removeEventListener("click",this),this.removeEventListener("focusout",this),this.removeEventListener("keyup",this),this.removeEventListener("invoke",this),this.removeEventListener("toggle",this)}handleEvent(e){switch(e.type){case"slotchange":q(this,gl,Gc).call(this,e);break;case"invoke":q(this,Al,Yc).call(this,e);break;case"click":q(this,kl,zc).call(this,e);break;case"toggle":q(this,Sl,Xc).call(this,e);break;case"focusout":q(this,Il,jc).call(this,e);break;case"keydown":q(this,Ml,em).call(this,e);break}}connectedCallback(){var e,i;Je(this,Tr,rs(this.shadowRoot,":host")),q(this,kn,_l).call(this),this.hasAttribute("disabled")||this.enable(),this.role||(this.role="menu"),Je(this,je,Yr(this)),(i=(e=H(this,je))==null?void 0:e.associateElement)==null||i.call(e,this),this.hidden||(st(kr(this),H(this,Ji)),st(this,H(this,ji)))}disconnectedCallback(){var e,i;lt(kr(this),H(this,Ji)),lt(this,H(this,ji)),this.disable(),(i=(e=H(this,je))==null?void 0:e.unassociateElement)==null||i.call(e,this),Je(this,je,null)}attributeChangedCallback(e,i,a){var r,o,s,l;e===mi.HIDDEN&&a!==i?(H(this,pi)||Je(this,pi,!0),this.hidden?q(this,yl,Zc).call(this):q(this,Tl,qc).call(this),this.dispatchEvent(new An({oldState:this.hidden?"open":"closed",newState:this.hidden?"closed":"open",bubbles:!0}))):e===L.MEDIA_CONTROLLER?(i&&((o=(r=H(this,je))==null?void 0:r.unassociateElement)==null||o.call(r,this),Je(this,je,null)),a&&this.isConnected&&(Je(this,je,Yr(this)),(l=(s=H(this,je))==null?void 0:s.associateElement)==null||l.call(s,this))):e===mi.DISABLED&&a!==i?a==null?this.enable():this.disable():e===mi.STYLE&&a!==i&&q(this,kn,_l).call(this)}formatMenuItemText(e,i){return this.constructor.formatMenuItemText(e,i)}get anchor(){return this.getAttribute("anchor")}set anchor(e){this.setAttribute("anchor",`${e}`)}get anchorElement(){var e;return this.anchor?(e=Wt(this))==null?void 0:e.querySelector(`#${this.anchor}`):null}get items(){return this.defaultSlot.assignedElements({flatten:!0}).filter(Kf)}get radioGroupItems(){return this.items.filter(e=>e.role==="menuitemradio")}get checkedItems(){return this.items.filter(e=>e.checked)}get value(){var e,i;return(i=(e=this.checkedItems[0])==null?void 0:e.value)!=null?i:""}set value(e){let i=this.items.find(a=>a.value===e);i&&q(this,Sn,Ll).call(this,i)}focus(){if(Je(this,hi,Ma()),this.items.length){q(this,Ar,Ln).call(this,this.items[0]),this.items[0].focus();return}let e=this.querySelector('[autofocus], [tabindex]:not([tabindex="-1"]), [role="menu"]');e==null||e.focus()}handleSelect(e){var i;let a=q(this,_r,Cn).call(this,e);a&&(q(this,Sn,Ll).call(this,a,a.type==="checkbox"),H(this,Nt)&&!this.hidden&&((i=H(this,hi))==null||i.focus(),this.hidden=!0))}get keysUsed(){return["Enter","Escape","Tab"," ","ArrowDown","ArrowUp","Home","End"]}handleMove(e){var i,a;let{key:r}=e,o=this.items,s=(a=(i=q(this,_r,Cn).call(this,e))!=null?i:q(this,Cl,tm).call(this))!=null?a:o[0],l=o.indexOf(s),u=Math.max(0,l);r==="ArrowDown"?u++:r==="ArrowUp"?u--:e.key==="Home"?u=0:e.key==="End"&&(u=o.length-1),u<0&&(u=o.length-1),u>o.length-1&&(u=0),q(this,Ar,Ln).call(this,o[u]),o[u].focus()}};je=new WeakMap;hi=new WeakMap;Nt=new WeakMap;Tn=new WeakMap;yn=new WeakMap;pi=new WeakMap;Tr=new WeakMap;gl=new WeakSet;Gc=function(t){let e=t.target;for(let i of e.assignedNodes({flatten:!0}))i.nodeType===3&&i.textContent.trim()===""&&i.remove();if(["header","title"].includes(e.name)){let i=this.shadowRoot.querySelector('slot[name="header"]');i.hidden=e.assignedNodes().length===0}e.name||H(this,In).call(this)};In=new WeakMap;kn=new WeakSet;_l=function(){var t;let e=this.shadowRoot.querySelector("#layout-row"),i=(t=getComputedStyle(this).getPropertyValue("--media-menu-layout"))==null?void 0:t.trim();e.setAttribute("media",i==="row"?"":"width:0")};Al=new WeakSet;Yc=function(t){Je(this,Nt,t.relatedTarget),de(this,t.relatedTarget)||(this.hidden=!this.hidden)};Tl=new WeakSet;qc=function(){var t;(t=H(this,Nt))==null||t.setAttribute("aria-expanded","true"),this.addEventListener("transitionend",()=>this.focus(),{once:!0}),st(kr(this),H(this,Ji)),st(this,H(this,ji))};yl=new WeakSet;Zc=function(){var t;(t=H(this,Nt))==null||t.setAttribute("aria-expanded","false"),lt(kr(this),H(this,Ji)),lt(this,H(this,ji))};Ji=new WeakMap;ji=new WeakMap;ea=new WeakSet;yr=function(t){if(this.hasAttribute("mediacontroller")&&!this.anchor||this.hidden||!this.anchorElement)return;let{x:e,y:i}=Vc({anchor:this.anchorElement,floating:this,placement:"top-start"});t!=null||(t=this.offsetWidth);let r=kr(this).getBoundingClientRect(),o=r.width-e-t,s=r.height-i-this.offsetHeight,{style:l}=H(this,Tr);l.setProperty("position","absolute"),l.setProperty("right",`${Math.max(0,o)}px`),l.setProperty("--_menu-bottom",`${s}px`);let u=getComputedStyle(this),_=l.getPropertyValue("--_menu-bottom")===u.bottom?s:parseFloat(u.bottom),g=r.height-_-parseFloat(u.marginBottom);this.style.setProperty("--_menu-max-height",`${g}px`)};Mn=new WeakSet;Rl=function(t){let e=this.querySelector('[role="menuitem"][aria-haspopup][aria-expanded="true"]'),i=e==null?void 0:e.querySelector('[role="menu"]'),{style:a}=H(this,Tr);if(t||a.setProperty("--media-menu-transition-in","none"),i){let r=i.offsetHeight,o=Math.max(i.offsetWidth,e.offsetWidth);this.style.setProperty("min-width",`${o}px`),this.style.setProperty("min-height",`${r}px`),q(this,ea,yr).call(this,o)}else this.style.removeProperty("min-width"),this.style.removeProperty("min-height"),q(this,ea,yr).call(this);a.removeProperty("--media-menu-transition-in")};kl=new WeakSet;zc=function(t){var e;if(t.stopPropagation(),t.composedPath().includes(H(this,xl,Qc))){(e=H(this,hi))==null||e.focus(),this.hidden=!0;return}let i=q(this,_r,Cn).call(this,t);!i||i.hasAttribute("disabled")||(q(this,Ar,Ln).call(this,i),this.handleSelect(t))};xl=new WeakSet;Qc=function(){var t;return(t=this.shadowRoot.querySelector('slot[name="header"]').assignedElements({flatten:!0}))==null?void 0:t.find(i=>i.matches('button[part~="back"]'))};Sl=new WeakSet;Xc=function(t){if(t.target===this)return;q(this,Dl,Jc).call(this);let e=Array.from(this.querySelectorAll('[role="menuitem"][aria-haspopup]'));for(let i of e)i.invokeTargetElement!=t.target&&t.newState=="open"&&i.getAttribute("aria-expanded")=="true"&&!i.invokeTargetElement.hidden&&i.invokeTargetElement.dispatchEvent(new Ot({relatedTarget:i}));for(let i of e)i.setAttribute("aria-expanded",`${!i.submenuElement.hidden}`);q(this,Mn,Rl).call(this,!0)};Dl=new WeakSet;Jc=function(){let e=this.querySelector('[role="menuitem"] > [role="menu"]:not([hidden])');this.container.classList.toggle("has-expanded",!!e)};Il=new WeakSet;jc=function(t){var e;de(this,t.relatedTarget)||(H(this,pi)&&((e=H(this,hi))==null||e.focus()),H(this,Nt)&&H(this,Nt)!==t.relatedTarget&&!this.hidden&&(this.hidden=!0))};Ml=new WeakSet;em=function(t){var e,i,a,r,o;let{key:s,ctrlKey:l,altKey:u,metaKey:m}=t;if(!(l||u||m)&&this.keysUsed.includes(s))if(t.preventDefault(),t.stopPropagation(),s==="Tab"){if(H(this,pi)){this.hidden=!0;return}t.shiftKey?(i=(e=this.previousElementSibling)==null?void 0:e.focus)==null||i.call(e):(r=(a=this.nextElementSibling)==null?void 0:a.focus)==null||r.call(a),this.blur()}else s==="Escape"?((o=H(this,hi))==null||o.focus(),H(this,pi)&&(this.hidden=!0)):s==="Enter"||s===" "?this.handleSelect(t):this.handleMove(t)};_r=new WeakSet;Cn=function(t){return t.composedPath().find(e=>["menuitemradio","menuitemcheckbox"].includes(e.role))};Cl=new WeakSet;tm=function(){return this.items.find(t=>t.tabIndex===0)};Ar=new WeakSet;Ln=function(t){for(let e of this.items)e.tabIndex=e===t?0:-1};Sn=new WeakSet;Ll=function(t,e){let i=[...this.checkedItems];t.type==="radio"&&this.radioGroupItems.forEach(a=>a.checked=!1),e?t.checked=!t.checked:t.checked=!0,this.checkedItems.some((a,r)=>a!=i[r])&&this.dispatchEvent(new Event("change",{bubbles:!0,composed:!0}))};oe.shadowRootOptions={mode:"open"};oe.getTemplateHTML=Vf;function Kf(t){return["menuitem","menuitemradio","menuitemcheckbox"].includes(t==null?void 0:t.role)}function kr(t){var e;return(e=t.getAttribute("bounds")?He(t,`#${t.getAttribute("bounds")}`):Z(t)||t.parentElement)!=null?e:t}d.customElements.get("media-chrome-menu")||d.customElements.define("media-chrome-menu",oe);var Bl=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},it=(t,e,i)=>(Bl(t,e,"read from private field"),i?i.call(t):e.get(t)),vt=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Ol=(t,e,i,a)=>(Bl(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),tt=(t,e,i)=>(Bl(t,e,"access private method"),i),wn,Ir,Nl,im,$l,am,Wl,rm,at,ta,Mr,Pl,om,Rn,Ul;function Gf(t){return`
    <style>
      :host {
        transition: var(--media-menu-item-transition,
          background .15s linear,
          opacity .2s ease-in-out
        );
        outline: var(--media-menu-item-outline, 0);
        outline-offset: var(--media-menu-item-outline-offset, -1px);
        cursor: var(--media-cursor, pointer);
        display: flex;
        align-items: center;
        align-self: stretch;
        justify-self: stretch;
        white-space: nowrap;
        white-space-collapse: collapse;
        text-wrap: nowrap;
        padding: .4em .8em .4em 1em;
      }

      :host(:focus-visible) {
        box-shadow: var(--media-menu-item-focus-shadow, inset 0 0 0 2px rgb(27 127 204 / .9));
        outline: var(--media-menu-item-hover-outline, 0);
        outline-offset: var(--media-menu-item-hover-outline-offset,  var(--media-menu-item-outline-offset, -1px));
      }

      :host(:hover) {
        cursor: var(--media-cursor, pointer);
        background: var(--media-menu-item-hover-background, rgb(92 92 102 / .5));
        outline: var(--media-menu-item-hover-outline);
        outline-offset: var(--media-menu-item-hover-outline-offset,  var(--media-menu-item-outline-offset, -1px));
      }

      :host([aria-checked="true"]) {
        background: var(--media-menu-item-checked-background);
      }

      :host([hidden]) {
        display: none;
      }

      :host([disabled]) {
        pointer-events: none;
        color: rgba(255, 255, 255, .3);
      }

      slot:not([name]) {
        width: 100%;
      }

      slot:not([name="submenu"]) {
        display: inline-flex;
        align-items: center;
        transition: inherit;
        opacity: var(--media-menu-item-opacity, 1);
      }

      slot[name="description"] {
        justify-content: end;
      }

      slot[name="description"] > span {
        display: inline-block;
        margin-inline: 1em .2em;
        max-width: var(--media-menu-item-description-max-width, 100px);
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: .8em;
        font-weight: 400;
        text-align: right;
        position: relative;
        top: .04em;
      }

      slot[name="checked-indicator"] {
        display: none;
      }

      :host(:is([role="menuitemradio"],[role="menuitemcheckbox"])) slot[name="checked-indicator"] {
        display: var(--media-menu-item-checked-indicator-display, inline-block);
      }

      
      svg, img, ::slotted(svg), ::slotted(img) {
        height: var(--media-menu-item-icon-height, var(--media-control-height, 24px));
        fill: var(--media-icon-color, var(--media-primary-color, rgb(238 238 238)));
        display: block;
      }

      
      [part~="indicator"],
      ::slotted([part~="indicator"]) {
        fill: var(--media-menu-item-indicator-fill,
          var(--media-icon-color, var(--media-primary-color, rgb(238 238 238))));
        height: var(--media-menu-item-indicator-height, 1.25em);
        margin-right: .5ch;
      }

      [part~="checked-indicator"] {
        visibility: hidden;
      }

      :host([aria-checked="true"]) [part~="checked-indicator"] {
        visibility: visible;
      }
    </style>
    <slot name="checked-indicator">
      <svg aria-hidden="true" viewBox="0 1 24 24" part="checked-indicator indicator">
        <path d="m10 15.17 9.193-9.191 1.414 1.414-10.606 10.606-6.364-6.364 1.414-1.414 4.95 4.95Z"/>
      </svg>
    </slot>
    <slot name="prefix"></slot>
    <slot></slot>
    <slot name="description"></slot>
    <slot name="suffix">
      ${this.getSuffixSlotInnerHTML(t)}
    </slot>
    <slot name="submenu"></slot>
  `}function Yf(t){return""}var we={TYPE:"type",VALUE:"value",CHECKED:"checked",DISABLED:"disabled"},rt=class extends d.HTMLElement{constructor(){if(super(),vt(this,Nl),vt(this,$l),vt(this,Wl),vt(this,ta),vt(this,Pl),vt(this,Rn),vt(this,wn,!1),vt(this,Ir,void 0),vt(this,at,()=>{var e,i;this.setAttribute("submenusize",`${this.submenuElement.items.length}`);let a=this.shadowRoot.querySelector('slot[name="description"]'),r=(e=this.submenuElement.checkedItems)==null?void 0:e[0],o=(i=r==null?void 0:r.dataset.description)!=null?i:r==null?void 0:r.text,s=V.createElement("span");s.textContent=o!=null?o:"",a.replaceChildren(s)}),!this.shadowRoot){this.attachShadow(this.constructor.shadowRootOptions);let e=Y(this.attributes);this.shadowRoot.innerHTML=this.constructor.getTemplateHTML(e)}this.shadowRoot.addEventListener("slotchange",this)}static get observedAttributes(){return[we.TYPE,we.DISABLED,we.CHECKED,we.VALUE]}enable(){this.hasAttribute("tabindex")||this.setAttribute("tabindex","-1"),Sr(this)&&!this.hasAttribute("aria-checked")&&this.setAttribute("aria-checked","false"),this.addEventListener("click",this),this.addEventListener("keydown",this)}disable(){this.removeAttribute("tabindex"),this.removeEventListener("click",this),this.removeEventListener("keydown",this),this.removeEventListener("keyup",this)}handleEvent(e){switch(e.type){case"slotchange":tt(this,Nl,im).call(this,e);break;case"click":this.handleClick(e);break;case"keydown":tt(this,Pl,om).call(this,e);break;case"keyup":tt(this,ta,Mr).call(this,e);break}}attributeChangedCallback(e,i,a){e===we.CHECKED&&Sr(this)&&!it(this,wn)?this.setAttribute("aria-checked",a!=null?"true":"false"):e===we.TYPE&&a!==i?this.role="menuitem"+a:e===we.DISABLED&&a!==i&&(a==null?this.enable():this.disable())}connectedCallback(){this.hasAttribute(we.DISABLED)||this.enable(),this.role="menuitem"+this.type,Ol(this,Ir,Hl(this,this.parentNode)),tt(this,Rn,Ul).call(this)}disconnectedCallback(){this.disable(),tt(this,Rn,Ul).call(this),Ol(this,Ir,null)}get invokeTarget(){return this.getAttribute("invoketarget")}set invokeTarget(e){this.setAttribute("invoketarget",`${e}`)}get invokeTargetElement(){var e;return this.invokeTarget?(e=Wt(this))==null?void 0:e.querySelector(`#${this.invokeTarget}`):this.submenuElement}get submenuElement(){return this.shadowRoot.querySelector('slot[name="submenu"]').assignedElements({flatten:!0})[0]}get type(){var e;return(e=this.getAttribute(we.TYPE))!=null?e:""}set type(e){this.setAttribute(we.TYPE,`${e}`)}get value(){var e;return(e=this.getAttribute(we.VALUE))!=null?e:this.text}set value(e){this.setAttribute(we.VALUE,e)}get text(){var e;return((e=this.textContent)!=null?e:"").trim()}get checked(){if(Sr(this))return this.getAttribute("aria-checked")==="true"}set checked(e){Sr(this)&&(Ol(this,wn,!0),this.setAttribute("aria-checked",e?"true":"false"),e?this.part.add("checked"):this.part.remove("checked"))}handleClick(e){Sr(this)||this.invokeTargetElement&&de(this,e.target)&&this.invokeTargetElement.dispatchEvent(new Ot({relatedTarget:this}))}get keysUsed(){return["Enter"," "]}};wn=new WeakMap;Ir=new WeakMap;Nl=new WeakSet;im=function(t){let e=t.target;if(!(e!=null&&e.name))for(let a of e.assignedNodes({flatten:!0}))a instanceof Text&&a.textContent.trim()===""&&a.remove();e.name==="submenu"&&(this.submenuElement?tt(this,$l,am).call(this):tt(this,Wl,rm).call(this))};$l=new WeakSet;am=async function(){this.setAttribute("aria-haspopup","menu"),this.setAttribute("aria-expanded",`${!this.submenuElement.hidden}`),this.submenuElement.addEventListener("change",it(this,at)),this.submenuElement.addEventListener("addmenuitem",it(this,at)),this.submenuElement.addEventListener("removemenuitem",it(this,at)),it(this,at).call(this)};Wl=new WeakSet;rm=function(){this.removeAttribute("aria-haspopup"),this.removeAttribute("aria-expanded"),this.submenuElement.removeEventListener("change",it(this,at)),this.submenuElement.removeEventListener("addmenuitem",it(this,at)),this.submenuElement.removeEventListener("removemenuitem",it(this,at)),it(this,at).call(this)};at=new WeakMap;ta=new WeakSet;Mr=function(t){let{key:e}=t;if(!this.keysUsed.includes(e)){this.removeEventListener("keyup",tt(this,ta,Mr));return}this.handleClick(t)};Pl=new WeakSet;om=function(t){let{metaKey:e,altKey:i,key:a}=t;if(e||i||!this.keysUsed.includes(a)){this.removeEventListener("keyup",tt(this,ta,Mr));return}this.addEventListener("keyup",tt(this,ta,Mr),{once:!0})};Rn=new WeakSet;Ul=function(){var t;let e=(t=it(this,Ir))==null?void 0:t.radioGroupItems;if(!e)return;let i=e.filter(a=>a.getAttribute("aria-checked")==="true").pop();i||(i=e[0]);for(let a of e)a.setAttribute("aria-checked","false");i==null||i.setAttribute("aria-checked","true")};rt.shadowRootOptions={mode:"open"};rt.getTemplateHTML=Gf;rt.getSuffixSlotInnerHTML=Yf;function Sr(t){return t.type==="radio"||t.type==="checkbox"}function Hl(t,e){if(!t)return null;let{host:i}=t.getRootNode();return!e&&i?Hl(t,i):e!=null&&e.items?e:Hl(e,e==null?void 0:e.parentNode)}d.customElements.get("media-chrome-menu-item")||d.customElements.define("media-chrome-menu-item",rt);function qf(t){return`
    ${oe.getTemplateHTML(t)}
    <style>
      :host {
        --_menu-bg: rgb(20 20 30 / .8);
        background: var(--media-settings-menu-background,
            var(--media-menu-background,
              var(--media-control-background,
                var(--media-secondary-color, var(--_menu-bg)))));
        min-width: var(--media-settings-menu-min-width, 170px);
        border-radius: 2px 2px 0 0;
        overflow: hidden;
      }

      @-moz-document url-prefix() {
        :host{
          --_menu-bg: rgb(20 20 30);
        }
      }

      :host([role="menu"]) {
        
        justify-content: end;
      }

      slot:not([name]) {
        justify-content: var(--media-settings-menu-justify-content);
        flex-direction: var(--media-settings-menu-flex-direction, column);
        overflow: visible;
      }

      #container.has-expanded {
        --media-settings-menu-item-opacity: 0;
      }
    </style>
  `}var Cr=class extends oe{get anchorElement(){return this.anchor!=="auto"?super.anchorElement:Z(this).querySelector("media-settings-menu-button")}};Cr.getTemplateHTML=qf;d.customElements.get("media-settings-menu")||d.customElements.define("media-settings-menu",Cr);function Zf(t){return`
    ${rt.getTemplateHTML.call(this,t)}
    <style>
      slot:not([name="submenu"]) {
        opacity: var(--media-settings-menu-item-opacity, var(--media-menu-item-opacity));
      }

      :host([aria-expanded="true"]:hover) {
        background: transparent;
      }
    </style>
  `}function zf(t){return`
    <svg aria-hidden="true" viewBox="0 0 20 24">
      <path d="m8.12 17.585-.742-.669 4.2-4.665-4.2-4.666.743-.669 4.803 5.335-4.803 5.334Z"/>
    </svg>
  `}var fi=class extends rt{};fi.shadowRootOptions={mode:"open"};fi.getTemplateHTML=Zf;fi.getSuffixSlotInnerHTML=zf;d.customElements.get("media-settings-menu-item")||d.customElements.define("media-settings-menu-item",fi);var be=class extends B{connectedCallback(){super.connectedCallback(),this.invokeTargetElement&&this.setAttribute("aria-haspopup","menu")}get invokeTarget(){return this.getAttribute("invoketarget")}set invokeTarget(e){this.setAttribute("invoketarget",`${e}`)}get invokeTargetElement(){var e;return this.invokeTarget?(e=Wt(this))==null?void 0:e.querySelector(`#${this.invokeTarget}`):null}handleClick(){var e;(e=this.invokeTargetElement)==null||e.dispatchEvent(new Ot({relatedTarget:this}))}};d.customElements.get("media-chrome-menu-button")||d.customElements.define("media-chrome-menu-button",be);function Qf(){return`
    <style>
      :host([aria-expanded="true"]) slot[name=tooltip] {
        display: none;
      }
    </style>
    <slot name="icon">
      <svg aria-hidden="true" viewBox="0 0 24 24">
        <path d="M4.5 14.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm7.5 0a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm7.5 0a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"/>
      </svg>
    </slot>
  `}function Xf(){return h("Settings")}var ia=class extends be{static get observedAttributes(){return[...super.observedAttributes,"target"]}connectedCallback(){super.connectedCallback(),this.setAttribute("aria-label",h("settings"))}get invokeTargetElement(){return this.invokeTarget!=null?super.invokeTargetElement:Z(this).querySelector("media-settings-menu")}};ia.getSlotTemplateHTML=Qf;ia.getTooltipContentHTML=Xf;d.customElements.get("media-settings-menu-button")||d.customElements.define("media-settings-menu-button",ia);var Gl=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},nm=(t,e,i)=>(Gl(t,e,"read from private field"),i?i.call(t):e.get(t)),xn=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Fl=(t,e,i,a)=>(Gl(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),Dn=(t,e,i)=>(Gl(t,e,"access private method"),i),Lr,Pn,On,Vl,Nn,Kl,Un=class extends oe{constructor(){super(...arguments),xn(this,On),xn(this,Nn),xn(this,Lr,[]),xn(this,Pn,void 0)}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_AUDIO_TRACK_LIST,n.MEDIA_AUDIO_TRACK_ENABLED,n.MEDIA_AUDIO_TRACK_UNAVAILABLE]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_AUDIO_TRACK_ENABLED&&i!==a?this.value=a:e===n.MEDIA_AUDIO_TRACK_LIST&&i!==a&&(Fl(this,Lr,Ud(a!=null?a:"")),Dn(this,On,Vl).call(this))}connectedCallback(){super.connectedCallback(),this.addEventListener("change",Dn(this,Nn,Kl))}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("change",Dn(this,Nn,Kl))}get anchorElement(){var e;return this.anchor!=="auto"?super.anchorElement:(e=Z(this))==null?void 0:e.querySelector("media-audio-track-menu-button")}get mediaAudioTrackList(){return nm(this,Lr)}set mediaAudioTrackList(e){Fl(this,Lr,e),Dn(this,On,Vl).call(this)}get mediaAudioTrackEnabled(){var e;return(e=I(this,n.MEDIA_AUDIO_TRACK_ENABLED))!=null?e:""}set mediaAudioTrackEnabled(e){M(this,n.MEDIA_AUDIO_TRACK_ENABLED,e)}};Lr=new WeakMap;Pn=new WeakMap;On=new WeakSet;Vl=function(){if(nm(this,Pn)===JSON.stringify(this.mediaAudioTrackList))return;Fl(this,Pn,JSON.stringify(this.mediaAudioTrackList));let t=this.mediaAudioTrackList;this.defaultSlot.textContent="";for(let e of t){let i=this.formatMenuItemText(e.label,e),a=et({type:"radio",text:i,value:`${e.id}`,checked:e.enabled});a.prepend(Ue(this,"checked-indicator")),this.defaultSlot.append(a)}};Nn=new WeakSet;Kl=function(){if(this.value==null)return;let t=new d.CustomEvent(p.MEDIA_AUDIO_TRACK_REQUEST,{composed:!0,bubbles:!0,detail:this.value});this.dispatchEvent(t)};d.customElements.get("media-audio-track-menu")||d.customElements.define("media-audio-track-menu",Un);var Jf=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M11 17H9.5V7H11v10Zm-3-3H6.5v-4H8v4Zm6-5h-1.5v6H14V9Zm3 7h-1.5V8H17v8Z"/>
  <path d="M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2s10 4.477 10 10Zm-2 0a8 8 0 1 0-16 0 8 8 0 0 0 16 0Z"/>
</svg>`;function jf(){return`
    <style>
      :host([aria-expanded="true"]) slot[name=tooltip] {
        display: none;
      }
    </style>
    <slot name="icon">${Jf}</slot>
  `}function ev(){return h("Audio")}var aa=class extends be{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_AUDIO_TRACK_ENABLED,n.MEDIA_AUDIO_TRACK_UNAVAILABLE]}connectedCallback(){super.connectedCallback(),this.setAttribute("aria-label",h("Audio"))}get invokeTargetElement(){var e;return this.invokeTarget!=null?super.invokeTargetElement:(e=Z(this))==null?void 0:e.querySelector("media-audio-track-menu")}get mediaAudioTrackEnabled(){var e;return(e=I(this,n.MEDIA_AUDIO_TRACK_ENABLED))!=null?e:""}set mediaAudioTrackEnabled(e){M(this,n.MEDIA_AUDIO_TRACK_ENABLED,e)}};aa.getSlotTemplateHTML=jf;aa.getTooltipContentHTML=ev;d.customElements.get("media-audio-track-menu-button")||d.customElements.define("media-audio-track-menu-button",aa);var Ql=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},tv=(t,e,i)=>(Ql(t,e,"read from private field"),i?i.call(t):e.get(t)),Yl=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},iv=(t,e,i,a)=>(Ql(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),ql=(t,e,i)=>(Ql(t,e,"access private method"),i),Bn,Zl,dm,Hn,zl,av=`
  <svg aria-hidden="true" viewBox="0 0 26 24" part="captions-indicator indicator">
    <path d="M22.83 5.68a2.58 2.58 0 0 0-2.3-2.5c-3.62-.24-11.44-.24-15.06 0a2.58 2.58 0 0 0-2.3 2.5c-.23 4.21-.23 8.43 0 12.64a2.58 2.58 0 0 0 2.3 2.5c3.62.24 11.44.24 15.06 0a2.58 2.58 0 0 0 2.3-2.5c.23-4.21.23-8.43 0-12.64Zm-11.39 9.45a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.92 3.92 0 0 1 .92-2.77 3.18 3.18 0 0 1 2.43-1 2.94 2.94 0 0 1 2.13.78c.364.359.62.813.74 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.17 1.61 1.61 0 0 0-1.29.58 2.79 2.79 0 0 0-.5 1.89 3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.48 1.48 0 0 0 1-.37 2.1 2.1 0 0 0 .59-1.14l1.4.44a3.23 3.23 0 0 1-1.07 1.69Zm7.22 0a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.88 3.88 0 0 1 .93-2.77 3.14 3.14 0 0 1 2.42-1 3 3 0 0 1 2.16.82 2.8 2.8 0 0 1 .73 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.21 1.61 1.61 0 0 0-1.29.58A2.79 2.79 0 0 0 15 12a3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.44 1.44 0 0 0 1-.37 2.1 2.1 0 0 0 .6-1.15l1.4.44a3.17 3.17 0 0 1-1.1 1.7Z"/>
  </svg>`;function rv(t){return`
    ${oe.getTemplateHTML(t)}
    <slot name="captions-indicator" hidden>${av}</slot>
  `}var wr=class extends oe{constructor(){super(...arguments),Yl(this,Zl),Yl(this,Hn),Yl(this,Bn,void 0)}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_SUBTITLES_LIST,n.MEDIA_SUBTITLES_SHOWING]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_SUBTITLES_LIST&&i!==a?ql(this,Zl,dm).call(this):e===n.MEDIA_SUBTITLES_SHOWING&&i!==a&&(this.value=a)}connectedCallback(){super.connectedCallback(),this.addEventListener("change",ql(this,Hn,zl))}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("change",ql(this,Hn,zl))}get anchorElement(){return this.anchor!=="auto"?super.anchorElement:Z(this).querySelector("media-captions-menu-button")}get mediaSubtitlesList(){return sm(this,n.MEDIA_SUBTITLES_LIST)}set mediaSubtitlesList(e){lm(this,n.MEDIA_SUBTITLES_LIST,e)}get mediaSubtitlesShowing(){return sm(this,n.MEDIA_SUBTITLES_SHOWING)}set mediaSubtitlesShowing(e){lm(this,n.MEDIA_SUBTITLES_SHOWING,e)}};Bn=new WeakMap;Zl=new WeakSet;dm=function(){var t;if(tv(this,Bn)===JSON.stringify(this.mediaSubtitlesList))return;iv(this,Bn,JSON.stringify(this.mediaSubtitlesList)),this.defaultSlot.textContent="";let e=!this.value,i=et({type:"radio",text:this.formatMenuItemText(h("Off")),value:"off",checked:e});i.prepend(Ue(this,"checked-indicator")),this.defaultSlot.append(i);let a=this.mediaSubtitlesList;for(let r of a){let o=et({type:"radio",text:this.formatMenuItemText(r.label,r),value:oo(r),checked:this.value==oo(r)});o.prepend(Ue(this,"checked-indicator")),((t=r.kind)!=null?t:"subs")==="captions"&&o.append(Ue(this,"captions-indicator")),this.defaultSlot.append(o)}};Hn=new WeakSet;zl=function(){let t=this.mediaSubtitlesShowing,e=this.getAttribute(n.MEDIA_SUBTITLES_SHOWING),i=this.value!==e;if(t!=null&&t.length&&i&&this.dispatchEvent(new d.CustomEvent(p.MEDIA_DISABLE_SUBTITLES_REQUEST,{composed:!0,bubbles:!0,detail:t})),!this.value||!i)return;let a=new d.CustomEvent(p.MEDIA_SHOW_SUBTITLES_REQUEST,{composed:!0,bubbles:!0,detail:this.value});this.dispatchEvent(a)};wr.getTemplateHTML=rv;var sm=(t,e)=>{let i=t.getAttribute(e);return i?Gt(i):[]},lm=(t,e,i)=>{if(!(i!=null&&i.length)){t.removeAttribute(e);return}let a=ut(i);t.getAttribute(e)!==a&&t.setAttribute(e,a)};d.customElements.get("media-captions-menu")||d.customElements.define("media-captions-menu",wr);var ov=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M22.83 5.68a2.58 2.58 0 0 0-2.3-2.5c-3.62-.24-11.44-.24-15.06 0a2.58 2.58 0 0 0-2.3 2.5c-.23 4.21-.23 8.43 0 12.64a2.58 2.58 0 0 0 2.3 2.5c3.62.24 11.44.24 15.06 0a2.58 2.58 0 0 0 2.3-2.5c.23-4.21.23-8.43 0-12.64Zm-11.39 9.45a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.92 3.92 0 0 1 .92-2.77 3.18 3.18 0 0 1 2.43-1 2.94 2.94 0 0 1 2.13.78c.364.359.62.813.74 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.17 1.61 1.61 0 0 0-1.29.58 2.79 2.79 0 0 0-.5 1.89 3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.48 1.48 0 0 0 1-.37 2.1 2.1 0 0 0 .59-1.14l1.4.44a3.23 3.23 0 0 1-1.07 1.69Zm7.22 0a3.07 3.07 0 0 1-1.91.57 3.06 3.06 0 0 1-2.34-1 3.75 3.75 0 0 1-.92-2.67 3.88 3.88 0 0 1 .93-2.77 3.14 3.14 0 0 1 2.42-1 3 3 0 0 1 2.16.82 2.8 2.8 0 0 1 .73 1.31l-1.43.35a1.49 1.49 0 0 0-1.51-1.21 1.61 1.61 0 0 0-1.29.58A2.79 2.79 0 0 0 15 12a3 3 0 0 0 .49 1.93 1.61 1.61 0 0 0 1.27.58 1.44 1.44 0 0 0 1-.37 2.1 2.1 0 0 0 .6-1.15l1.4.44a3.17 3.17 0 0 1-1.1 1.7Z"/>
</svg>`,nv=`<svg aria-hidden="true" viewBox="0 0 26 24">
  <path d="M17.73 14.09a1.4 1.4 0 0 1-1 .37 1.579 1.579 0 0 1-1.27-.58A3 3 0 0 1 15 12a2.8 2.8 0 0 1 .5-1.85 1.63 1.63 0 0 1 1.29-.57 1.47 1.47 0 0 1 1.51 1.2l1.43-.34A2.89 2.89 0 0 0 19 9.07a3 3 0 0 0-2.14-.78 3.14 3.14 0 0 0-2.42 1 3.91 3.91 0 0 0-.93 2.78 3.74 3.74 0 0 0 .92 2.66 3.07 3.07 0 0 0 2.34 1 3.07 3.07 0 0 0 1.91-.57 3.17 3.17 0 0 0 1.07-1.74l-1.4-.45c-.083.43-.3.822-.62 1.12Zm-7.22 0a1.43 1.43 0 0 1-1 .37 1.58 1.58 0 0 1-1.27-.58A3 3 0 0 1 7.76 12a2.8 2.8 0 0 1 .5-1.85 1.63 1.63 0 0 1 1.29-.57 1.47 1.47 0 0 1 1.51 1.2l1.43-.34a2.81 2.81 0 0 0-.74-1.32 2.94 2.94 0 0 0-2.13-.78 3.18 3.18 0 0 0-2.43 1 4 4 0 0 0-.92 2.78 3.74 3.74 0 0 0 .92 2.66 3.07 3.07 0 0 0 2.34 1 3.07 3.07 0 0 0 1.91-.57 3.23 3.23 0 0 0 1.07-1.74l-1.4-.45a2.06 2.06 0 0 1-.6 1.07Zm12.32-8.41a2.59 2.59 0 0 0-2.3-2.51C18.72 3.05 15.86 3 13 3c-2.86 0-5.72.05-7.53.17a2.59 2.59 0 0 0-2.3 2.51c-.23 4.207-.23 8.423 0 12.63a2.57 2.57 0 0 0 2.3 2.5c1.81.13 4.67.19 7.53.19 2.86 0 5.72-.06 7.53-.19a2.57 2.57 0 0 0 2.3-2.5c.23-4.207.23-8.423 0-12.63Zm-1.49 12.53a1.11 1.11 0 0 1-.91 1.11c-1.67.11-4.45.18-7.43.18-2.98 0-5.76-.07-7.43-.18a1.11 1.11 0 0 1-.91-1.11c-.21-4.14-.21-8.29 0-12.43a1.11 1.11 0 0 1 .91-1.11C7.24 4.56 10 4.49 13 4.49s5.76.07 7.43.18a1.11 1.11 0 0 1 .91 1.11c.21 4.14.21 8.29 0 12.43Z"/>
</svg>`;function sv(){return`
    <style>
      :host([aria-checked="true"]) slot[name=off] {
        display: none !important;
      }

      
      :host(:not([aria-checked="true"])) slot[name=on] {
        display: none !important;
      }

      :host([aria-expanded="true"]) slot[name=tooltip] {
        display: none;
      }
    </style>

    <slot name="icon">
      <slot name="on">${ov}</slot>
      <slot name="off">${nv}</slot>
    </slot>
  `}function lv(){return h("Captions")}var um=t=>{t.setAttribute("aria-checked",no(t).toString())},ra=class extends be{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_SUBTITLES_LIST,n.MEDIA_SUBTITLES_SHOWING]}connectedCallback(){super.connectedCallback(),this.setAttribute("aria-label",h("closed captions")),um(this)}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_SUBTITLES_SHOWING&&um(this)}get invokeTargetElement(){var e;return this.invokeTarget!=null?super.invokeTargetElement:(e=Z(this))==null?void 0:e.querySelector("media-captions-menu")}get mediaSubtitlesList(){return cm(this,n.MEDIA_SUBTITLES_LIST)}set mediaSubtitlesList(e){mm(this,n.MEDIA_SUBTITLES_LIST,e)}get mediaSubtitlesShowing(){return cm(this,n.MEDIA_SUBTITLES_SHOWING)}set mediaSubtitlesShowing(e){mm(this,n.MEDIA_SUBTITLES_SHOWING,e)}};ra.getSlotTemplateHTML=sv;ra.getTooltipContentHTML=lv;var cm=(t,e)=>{let i=t.getAttribute(e);return i?Gt(i):[]},mm=(t,e,i)=>{if(!(i!=null&&i.length)){t.removeAttribute(e);return}let a=ut(i);t.getAttribute(e)!==a&&t.setAttribute(e,a)};d.customElements.get("media-captions-menu-button")||d.customElements.define("media-captions-menu-button",ra);var hm=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},oa=(t,e,i)=>(hm(t,e,"read from private field"),i?i.call(t):e.get(t)),Xl=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},Rr=(t,e,i)=>(hm(t,e,"access private method"),i),Pt,xr,$n,Wn,jl,Jl={RATES:"rates"},Fn=class extends oe{constructor(){super(),Xl(this,xr),Xl(this,Wn),Xl(this,Pt,new Tt(this,Jl.RATES,{defaultValue:Ws})),Rr(this,xr,$n).call(this)}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PLAYBACK_RATE,Jl.RATES]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_PLAYBACK_RATE&&i!=a?this.value=a:e===Jl.RATES&&i!=a&&(oa(this,Pt).value=a,Rr(this,xr,$n).call(this))}connectedCallback(){super.connectedCallback(),this.addEventListener("change",Rr(this,Wn,jl))}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("change",Rr(this,Wn,jl))}get anchorElement(){return this.anchor!=="auto"?super.anchorElement:Z(this).querySelector("media-playback-rate-menu-button")}get rates(){return oa(this,Pt)}set rates(e){e?Array.isArray(e)?oa(this,Pt).value=e.join(" "):typeof e=="string"&&(oa(this,Pt).value=e):oa(this,Pt).value="",Rr(this,xr,$n).call(this)}get mediaPlaybackRate(){return R(this,n.MEDIA_PLAYBACK_RATE,jt)}set mediaPlaybackRate(e){O(this,n.MEDIA_PLAYBACK_RATE,e)}};Pt=new WeakMap;xr=new WeakSet;$n=function(){this.defaultSlot.textContent="";for(let t of oa(this,Pt)){let e=et({type:"radio",text:this.formatMenuItemText(`${t}x`,t),value:t,checked:this.mediaPlaybackRate===Number(t)});e.prepend(Ue(this,"checked-indicator")),this.defaultSlot.append(e)}};Wn=new WeakSet;jl=function(){if(!this.value)return;let t=new d.CustomEvent(p.MEDIA_PLAYBACK_RATE_REQUEST,{composed:!0,bubbles:!0,detail:this.value});this.dispatchEvent(t)};d.customElements.get("media-playback-rate-menu")||d.customElements.define("media-playback-rate-menu",Fn);var Vn=1;function dv(t){return`
    <style>
      :host {
        min-width: 5ch;
        padding: var(--media-button-padding, var(--media-control-padding, 10px 5px));
      }
      
      :host([aria-expanded="true"]) slot[name=tooltip] {
        display: none;
      }
    </style>
    <slot name="icon">${t.mediaplaybackrate||Vn}x</slot>
  `}function uv(){return h("Playback rate")}var na=class extends be{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_PLAYBACK_RATE]}constructor(){var e;super(),this.container=this.shadowRoot.querySelector('slot[name="icon"]'),this.container.innerHTML=`${(e=this.mediaPlaybackRate)!=null?e:Vn}x`}attributeChangedCallback(e,i,a){if(super.attributeChangedCallback(e,i,a),e===n.MEDIA_PLAYBACK_RATE){let r=a?+a:Number.NaN,o=Number.isNaN(r)?Vn:r;this.container.innerHTML=`${o}x`,this.setAttribute("aria-label",h("Playback rate {playbackRate}",{playbackRate:o}))}}get invokeTargetElement(){return this.invokeTarget!=null?super.invokeTargetElement:Z(this).querySelector("media-playback-rate-menu")}get mediaPlaybackRate(){return R(this,n.MEDIA_PLAYBACK_RATE,Vn)}set mediaPlaybackRate(e){O(this,n.MEDIA_PLAYBACK_RATE,e)}};na.getSlotTemplateHTML=dv;na.getTooltipContentHTML=uv;d.customElements.get("media-playback-rate-menu-button")||d.customElements.define("media-playback-rate-menu-button",na);var td=(t,e,i)=>{if(!e.has(t))throw TypeError("Cannot "+i)},Dr=(t,e,i)=>(td(t,e,"read from private field"),i?i.call(t):e.get(t)),Kn=(t,e,i)=>{if(e.has(t))throw TypeError("Cannot add the same private member more than once");e instanceof WeakSet?e.add(t):e.set(t,i)},pm=(t,e,i,a)=>(td(t,e,"write to private field"),a?a.call(t,i):e.set(t,i),i),sa=(t,e,i)=>(td(t,e,"access private method"),i),Or,da,la,Nr,Gn,ed,Yn=class extends oe{constructor(){super(...arguments),Kn(this,la),Kn(this,Gn),Kn(this,Or,[]),Kn(this,da,{})}static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_RENDITION_LIST,n.MEDIA_RENDITION_SELECTED,n.MEDIA_RENDITION_UNAVAILABLE,n.MEDIA_HEIGHT]}attributeChangedCallback(e,i,a){super.attributeChangedCallback(e,i,a),e===n.MEDIA_RENDITION_SELECTED&&i!==a?(this.value=a!=null?a:"auto",sa(this,la,Nr).call(this)):e===n.MEDIA_RENDITION_LIST&&i!==a?(pm(this,Or,Nd(a)),sa(this,la,Nr).call(this)):e===n.MEDIA_HEIGHT&&i!==a&&sa(this,la,Nr).call(this)}connectedCallback(){super.connectedCallback(),this.addEventListener("change",sa(this,Gn,ed))}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("change",sa(this,Gn,ed))}get anchorElement(){return this.anchor!=="auto"?super.anchorElement:Z(this).querySelector("media-rendition-menu-button")}get mediaRenditionList(){return Dr(this,Or)}set mediaRenditionList(e){pm(this,Or,e),sa(this,la,Nr).call(this)}get mediaRenditionSelected(){return I(this,n.MEDIA_RENDITION_SELECTED)}set mediaRenditionSelected(e){M(this,n.MEDIA_RENDITION_SELECTED,e)}get mediaHeight(){return R(this,n.MEDIA_HEIGHT)}set mediaHeight(e){O(this,n.MEDIA_HEIGHT,e)}};Or=new WeakMap;da=new WeakMap;la=new WeakSet;Nr=function(){if(Dr(this,da).mediaRenditionList===JSON.stringify(this.mediaRenditionList)&&Dr(this,da).mediaHeight===this.mediaHeight)return;Dr(this,da).mediaRenditionList=JSON.stringify(this.mediaRenditionList),Dr(this,da).mediaHeight=this.mediaHeight;let t=this.mediaRenditionList.sort((o,s)=>s.height-o.height);for(let o of t)o.selected=o.id===this.mediaRenditionSelected;this.defaultSlot.textContent="";let e=!this.mediaRenditionSelected;for(let o of t){let s=this.formatMenuItemText(`${Math.min(o.width,o.height)}p`,o),l=et({type:"radio",text:s,value:`${o.id}`,checked:o.selected&&!e});l.prepend(Ue(this,"checked-indicator")),this.defaultSlot.append(l)}let i=e?this.formatMenuItemText(`${h("Auto")} (${this.mediaHeight}p)`):this.formatMenuItemText(h("Auto")),a=et({type:"radio",text:i,value:"auto",checked:e}),r=this.mediaHeight>0?`${h("Auto")} (${this.mediaHeight}p)`:h("Auto");a.dataset.description=r,a.prepend(Ue(this,"checked-indicator")),this.defaultSlot.append(a)};Gn=new WeakSet;ed=function(){if(this.value==null)return;let t=new d.CustomEvent(p.MEDIA_RENDITION_REQUEST,{composed:!0,bubbles:!0,detail:this.value});this.dispatchEvent(t)};d.customElements.get("media-rendition-menu")||d.customElements.define("media-rendition-menu",Yn);var cv=`<svg aria-hidden="true" viewBox="0 0 24 24">
  <path d="M13.5 2.5h2v6h-2v-2h-11v-2h11v-2Zm4 2h4v2h-4v-2Zm-12 4h2v6h-2v-2h-3v-2h3v-2Zm4 2h12v2h-12v-2Zm1 4h2v6h-2v-2h-8v-2h8v-2Zm4 2h7v2h-7v-2Z" />
</svg>`;function mv(){return`
    <style>
      :host([aria-expanded="true"]) slot[name=tooltip] {
        display: none;
      }
    </style>
    <slot name="icon">${cv}</slot>
  `}function hv(){return h("Quality")}var ua=class extends be{static get observedAttributes(){return[...super.observedAttributes,n.MEDIA_RENDITION_SELECTED,n.MEDIA_RENDITION_UNAVAILABLE,n.MEDIA_HEIGHT]}connectedCallback(){super.connectedCallback(),this.setAttribute("aria-label",h("quality"))}get invokeTargetElement(){return this.invokeTarget!=null?super.invokeTargetElement:Z(this).querySelector("media-rendition-menu")}get mediaRenditionSelected(){return I(this,n.MEDIA_RENDITION_SELECTED)}set mediaRenditionSelected(e){M(this,n.MEDIA_RENDITION_SELECTED,e)}get mediaHeight(){return R(this,n.MEDIA_HEIGHT)}set mediaHeight(e){O(this,n.MEDIA_HEIGHT,e)}};ua.getSlotTemplateHTML=mv;ua.getTooltipContentHTML=hv;d.customElements.get("media-rendition-menu-button")||d.customElements.define("media-rendition-menu-button",ua);var id=_i.createElement("template");"innerHTML"in id&&(id.innerHTML=Fc);var fm,vm,qn=class extends ci{};qn.template=(vm=(fm=id.content)==null?void 0:fm.children)==null?void 0:vm[0];ye.customElements.get("media-theme-gerwig")||ye.customElements.define("media-theme-gerwig",qn);var pv="gerwig",Et={SRC:"src",POSTER:"poster"},c={STYLE:"style",DEFAULT_HIDDEN_CAPTIONS:"default-hidden-captions",PRIMARY_COLOR:"primary-color",SECONDARY_COLOR:"secondary-color",ACCENT_COLOR:"accent-color",FORWARD_SEEK_OFFSET:"forward-seek-offset",BACKWARD_SEEK_OFFSET:"backward-seek-offset",PLAYBACK_TOKEN:"playback-token",THUMBNAIL_TOKEN:"thumbnail-token",STORYBOARD_TOKEN:"storyboard-token",DRM_TOKEN:"drm-token",STORYBOARD_SRC:"storyboard-src",THUMBNAIL_TIME:"thumbnail-time",AUDIO:"audio",NOHOTKEYS:"nohotkeys",HOTKEYS:"hotkeys",PLAYBACK_RATES:"playbackrates",DEFAULT_SHOW_REMAINING_TIME:"default-show-remaining-time",DEFAULT_DURATION:"default-duration",TITLE:"title",VIDEO_TITLE:"video-title",PLACEHOLDER:"placeholder",THEME:"theme",DEFAULT_STREAM_TYPE:"default-stream-type",TARGET_LIVE_WINDOW:"target-live-window",EXTRA_SOURCE_PARAMS:"extra-source-params",NO_VOLUME_PREF:"no-volume-pref",CAST_RECEIVER:"cast-receiver",NO_TOOLTIPS:"no-tooltips",PROUDLY_DISPLAY_MUX_BADGE:"proudly-display-mux-badge"},ad=["audio","backwardseekoffset","defaultduration","defaultshowremainingtime","defaultsubtitles","noautoseektolive","disabled","exportparts","forwardseekoffset","hideduration","hotkeys","nohotkeys","playbackrates","defaultstreamtype","streamtype","style","targetlivewindow","template","title","videotitle","novolumepref","proudlydisplaymuxbadge"];function fv(t,e){var a,r;return{src:!t.playbackId&&t.src,playbackId:t.playbackId,hasSrc:!!t.playbackId||!!t.src||!!t.currentSrc,poster:t.poster,storyboard:t.storyboard,storyboardSrc:t.getAttribute(c.STORYBOARD_SRC),placeholder:t.getAttribute("placeholder"),themeTemplate:Ev(t),thumbnailTime:!t.tokens.thumbnail&&t.thumbnailTime,autoplay:t.autoplay,crossOrigin:t.crossOrigin,loop:t.loop,noHotKeys:t.hasAttribute(c.NOHOTKEYS),hotKeys:t.getAttribute(c.HOTKEYS),muted:t.muted,paused:t.paused,preload:t.preload,envKey:t.envKey,preferCmcd:t.preferCmcd,debug:t.debug,disableTracking:t.disableTracking,disableCookies:t.disableCookies,tokens:t.tokens,beaconCollectionDomain:t.beaconCollectionDomain,maxResolution:t.maxResolution,minResolution:t.minResolution,programStartTime:t.programStartTime,programEndTime:t.programEndTime,assetStartTime:t.assetStartTime,assetEndTime:t.assetEndTime,renditionOrder:t.renditionOrder,metadata:t.metadata,playerInitTime:t.playerInitTime,playerSoftwareName:t.playerSoftwareName,playerSoftwareVersion:t.playerSoftwareVersion,startTime:t.startTime,preferPlayback:t.preferPlayback,audio:t.audio,defaultStreamType:t.defaultStreamType,targetLiveWindow:t.getAttribute(b.Attributes.TARGET_LIVE_WINDOW),streamType:ur(t.getAttribute(b.Attributes.STREAM_TYPE)),primaryColor:t.getAttribute(c.PRIMARY_COLOR),secondaryColor:t.getAttribute(c.SECONDARY_COLOR),accentColor:t.getAttribute(c.ACCENT_COLOR),forwardSeekOffset:t.forwardSeekOffset,backwardSeekOffset:t.backwardSeekOffset,defaultHiddenCaptions:t.defaultHiddenCaptions,defaultDuration:t.defaultDuration,defaultShowRemainingTime:t.defaultShowRemainingTime,hideDuration:bv(t),playbackRates:t.getAttribute(c.PLAYBACK_RATES),customDomain:(a=t.getAttribute(b.Attributes.CUSTOM_DOMAIN))!=null?a:void 0,title:t.getAttribute(c.TITLE),videoTitle:(r=t.getAttribute(c.VIDEO_TITLE))!=null?r:t.getAttribute(c.TITLE),novolumepref:t.hasAttribute(c.NO_VOLUME_PREF),proudlyDisplayMuxBadge:t.hasAttribute(c.PROUDLY_DISPLAY_MUX_BADGE),castReceiver:t.castReceiver,...e,extraSourceParams:t.extraSourceParams}}var vv=Uo.formatErrorMessage;Uo.formatErrorMessage=t=>{var e,i;if(t instanceof b.MediaError){let a=Wc(t,!1);return`
      ${a!=null&&a.title?`<h3>${a.title}</h3>`:""}
      ${a!=null&&a.message||a!=null&&a.linkUrl?`<p>
        ${a==null?void 0:a.message}
        ${a!=null&&a.linkUrl?`<a
              href="${a.linkUrl}"
              target="_blank"
              rel="external noopener"
              aria-label="${(e=a.linkText)!=null?e:""} ${(0,w.i18n)("(opens in a new window)")}"
              >${(i=a.linkText)!=null?i:a.linkUrl}</a
            >`:""}
      </p>`:""}
    `}return vv(t)};function Ev(t){var i,a;let e=t.theme;if(e){let r=(a=(i=t.getRootNode())==null?void 0:i.getElementById)==null?void 0:a.call(i,e);if(r&&r instanceof HTMLTemplateElement)return r;e.startsWith("media-theme-")||(e=`media-theme-${e}`);let o=ye.customElements.get(e);if(o!=null&&o.template)return o.template}}function bv(t){var i;let e=(i=t.mediaController)==null?void 0:i.querySelector("media-time-display");return e&&getComputedStyle(e).getPropertyValue("--media-duration-display-display").trim()==="none"}function Em(t){let e=t.videoTitle?{video_title:t.videoTitle}:{};return t.getAttributeNames().filter(i=>i.startsWith("metadata-")).reduce((i,a)=>{let r=t.getAttribute(a);return r!==null&&(i[a.replace(/^metadata-/,"").replace(/-/g,"_")]=r),i},e)}var gv=Object.values(b.Attributes),_v=Object.values(Et),Av=Object.values(c),rd=cn(),od="mux-player",bm={isDialogOpen:!1},Tv={redundant_streams:!0},Pr,Ur,Hr,Bt,Br,Ei,$,Ut,gm,sd,vi,_m,Am,Tm,ym,nd=class extends sl{constructor(){super();he(this,$);he(this,Pr);he(this,Ur,!1);he(this,Hr,{});he(this,Bt,!0);he(this,Br,new un(this,"hotkeys"));he(this,Ei,{...bm,onCloseErrorDialog:i=>{var r;((r=i.composedPath()[0])==null?void 0:r.localName)==="media-error-dialog"&&X(this,$,sd).call(this,{isDialogOpen:!1})},onFocusInErrorDialog:i=>{var o;if(((o=i.composedPath()[0])==null?void 0:o.localName)!=="media-error-dialog")return;rl(this,_i.activeElement)||i.preventDefault()}});Te(this,Pr,(0,w.generatePlayerInitTime)()),this.attachShadow({mode:"open"}),X(this,$,gm).call(this),this.isConnected&&X(this,$,Ut).call(this)}static get NAME(){return od}static get VERSION(){return rd}static get observedAttributes(){var i;return[...(i=sl.observedAttributes)!=null?i:[],..._v,...gv,...Av]}get mediaTheme(){var i;return(i=this.shadowRoot)==null?void 0:i.querySelector("media-theme")}get mediaController(){var i,a;return(a=(i=this.mediaTheme)==null?void 0:i.shadowRoot)==null?void 0:a.querySelector("media-controller")}connectedCallback(){let i=this.media;i&&(i.metadata=Em(this))}attributeChangedCallback(i,a,r){switch(X(this,$,Ut).call(this),super.attributeChangedCallback(i,a,r),i){case c.HOTKEYS:U(this,Br).value=r;break;case c.THUMBNAIL_TIME:{r!=null&&this.tokens.thumbnail&&Qe((0,w.i18n)("Use of thumbnail-time with thumbnail-token is currently unsupported. Ignore thumbnail-time.").toString());break}case c.THUMBNAIL_TOKEN:{if(r){let s=(0,w.parseJwt)(r);if(s){let{aud:l}=s,u=w.MuxJWTAud.THUMBNAIL;l!==u&&Qe((0,w.i18n)("The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.").format({aud:l,expectedAud:u,tokenNamePrefix:"thumbnail"}))}}break}case c.STORYBOARD_TOKEN:{if(r){let s=(0,w.parseJwt)(r);if(s){let{aud:l}=s,u=w.MuxJWTAud.STORYBOARD;l!==u&&Qe((0,w.i18n)("The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.").format({aud:l,expectedAud:u,tokenNamePrefix:"storyboard"}))}}break}case c.DRM_TOKEN:{if(r){let s=(0,w.parseJwt)(r);if(s){let{aud:l}=s,u=w.MuxJWTAud.DRM;l!==u&&Qe((0,w.i18n)("The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.").format({aud:l,expectedAud:u,tokenNamePrefix:"drm"}))}}break}case b.Attributes.PLAYBACK_ID:{r!=null&&r.includes("?token")&&Ee((0,w.i18n)("The specificed playback ID {playbackId} contains a token which must be provided via the playback-token attribute.").format({playbackId:r}));break}case b.Attributes.STREAM_TYPE:r&&![w.StreamTypes.LIVE,w.StreamTypes.ON_DEMAND,w.StreamTypes.UNKNOWN].includes(r)?["ll-live","live:dvr","ll-live:dvr"].includes(this.streamType)?this.targetLiveWindow=r.includes("dvr")?Number.POSITIVE_INFINITY:0:ol({file:"invalid-stream-type.md",message:(0,w.i18n)("Invalid stream-type value supplied: `{streamType}`. Please provide stream-type as either: `on-demand` or `live`").format({streamType:this.streamType})}):r===w.StreamTypes.LIVE?this.getAttribute(c.TARGET_LIVE_WINDOW)==null&&(this.targetLiveWindow=0):this.targetLiveWindow=Number.NaN}[b.Attributes.PLAYBACK_ID,Et.SRC,c.PLAYBACK_TOKEN].includes(i)&&a!==r&&Te(this,Ei,{...U(this,Ei),...bm}),X(this,$,vi).call(this,{[bc(i)]:r})}async requestFullscreen(i){var a;if(!(!this.mediaController||this.mediaController.hasAttribute(n.MEDIA_IS_FULLSCREEN)))return(a=this.mediaController)==null||a.dispatchEvent(new ye.CustomEvent(p.MEDIA_ENTER_FULLSCREEN_REQUEST,{composed:!0,bubbles:!0})),new Promise((r,o)=>{var s;(s=this.mediaController)==null||s.addEventListener(Ce.MEDIA_IS_FULLSCREEN,()=>r(),{once:!0})})}async exitFullscreen(){var i;if(!(!this.mediaController||!this.mediaController.hasAttribute(n.MEDIA_IS_FULLSCREEN)))return(i=this.mediaController)==null||i.dispatchEvent(new ye.CustomEvent(p.MEDIA_EXIT_FULLSCREEN_REQUEST,{composed:!0,bubbles:!0})),new Promise((a,r)=>{var o;(o=this.mediaController)==null||o.addEventListener(Ce.MEDIA_IS_FULLSCREEN,()=>a(),{once:!0})})}get preferCmcd(){var i;return(i=this.getAttribute(b.Attributes.PREFER_CMCD))!=null?i:void 0}set preferCmcd(i){i!==this.preferCmcd&&(i?w.CmcdTypeValues.includes(i)?this.setAttribute(b.Attributes.PREFER_CMCD,i):Qe(`Invalid value for preferCmcd. Must be one of ${w.CmcdTypeValues.join()}`):this.removeAttribute(b.Attributes.PREFER_CMCD))}get hasPlayed(){var i,a;return(a=(i=this.mediaController)==null?void 0:i.hasAttribute(n.MEDIA_HAS_PLAYED))!=null?a:!1}get inLiveWindow(){var i;return(i=this.mediaController)==null?void 0:i.hasAttribute(n.MEDIA_TIME_IS_LIVE)}get _hls(){var i;return(i=this.media)==null?void 0:i._hls}get mux(){var i;return(i=this.media)==null?void 0:i.mux}get theme(){var i;return(i=this.getAttribute(c.THEME))!=null?i:pv}set theme(i){this.setAttribute(c.THEME,`${i}`)}get themeProps(){let i=this.mediaTheme;if(!i)return;let a={};for(let r of i.getAttributeNames()){if(ad.includes(r))continue;let o=i.getAttribute(r);a[dn(r)]=o===""?!0:o}return a}set themeProps(i){var r,o;X(this,$,Ut).call(this);let a={...this.themeProps,...i};for(let s in a){if(ad.includes(s))continue;let l=i==null?void 0:i[s];typeof l=="boolean"||l==null?(r=this.mediaTheme)==null||r.toggleAttribute(ln(s),!!l):(o=this.mediaTheme)==null||o.setAttribute(ln(s),l)}}get playbackId(){var i;return(i=this.getAttribute(b.Attributes.PLAYBACK_ID))!=null?i:void 0}set playbackId(i){i?this.setAttribute(b.Attributes.PLAYBACK_ID,i):this.removeAttribute(b.Attributes.PLAYBACK_ID)}get src(){var i,a;return this.playbackId?(i=Ht(this,Et.SRC))!=null?i:void 0:(a=this.getAttribute(Et.SRC))!=null?a:void 0}set src(i){i?this.setAttribute(Et.SRC,i):this.removeAttribute(Et.SRC)}get poster(){var r;let i=this.getAttribute(Et.POSTER);if(i!=null)return i;let{tokens:a}=this;if(a.playback&&!a.thumbnail){Qe("Missing expected thumbnail token. No poster image will be shown");return}if(this.playbackId&&!this.audio)return vc(this.playbackId,{customDomain:this.customDomain,thumbnailTime:(r=this.thumbnailTime)!=null?r:this.startTime,programTime:this.programStartTime,token:a.thumbnail})}set poster(i){i||i===""?this.setAttribute(Et.POSTER,i):this.removeAttribute(Et.POSTER)}get storyboardSrc(){var i;return(i=this.getAttribute(c.STORYBOARD_SRC))!=null?i:void 0}set storyboardSrc(i){i?this.setAttribute(c.STORYBOARD_SRC,i):this.removeAttribute(c.STORYBOARD_SRC)}get storyboard(){let{tokens:i}=this;if(this.storyboardSrc&&!i.storyboard)return this.storyboardSrc;if(!(this.audio||!this.playbackId||!this.streamType||[w.StreamTypes.LIVE,w.StreamTypes.UNKNOWN].includes(this.streamType)||i.playback&&!i.storyboard))return Ec(this.playbackId,{customDomain:this.customDomain,token:i.storyboard,programStartTime:this.programStartTime,programEndTime:this.programEndTime})}get audio(){return this.hasAttribute(c.AUDIO)}set audio(i){if(!i){this.removeAttribute(c.AUDIO);return}this.setAttribute(c.AUDIO,"")}get hotkeys(){return U(this,Br)}get nohotkeys(){return this.hasAttribute(c.NOHOTKEYS)}set nohotkeys(i){if(!i){this.removeAttribute(c.NOHOTKEYS);return}this.setAttribute(c.NOHOTKEYS,"")}get thumbnailTime(){return ue(this.getAttribute(c.THUMBNAIL_TIME))}set thumbnailTime(i){this.setAttribute(c.THUMBNAIL_TIME,`${i}`)}get videoTitle(){var i,a;return(a=(i=this.getAttribute(c.VIDEO_TITLE))!=null?i:this.getAttribute(c.TITLE))!=null?a:""}set videoTitle(i){i!==this.videoTitle&&(i?this.setAttribute(c.VIDEO_TITLE,i):this.removeAttribute(c.VIDEO_TITLE))}get placeholder(){var i;return(i=Ht(this,c.PLACEHOLDER))!=null?i:""}set placeholder(i){this.setAttribute(c.PLACEHOLDER,`${i}`)}get primaryColor(){var a,r;let i=this.getAttribute(c.PRIMARY_COLOR);if(i!=null||this.mediaTheme&&(i=(r=(a=ye.getComputedStyle(this.mediaTheme))==null?void 0:a.getPropertyValue("--_primary-color"))==null?void 0:r.trim(),i))return i}set primaryColor(i){this.setAttribute(c.PRIMARY_COLOR,`${i}`)}get secondaryColor(){var a,r;let i=this.getAttribute(c.SECONDARY_COLOR);if(i!=null||this.mediaTheme&&(i=(r=(a=ye.getComputedStyle(this.mediaTheme))==null?void 0:a.getPropertyValue("--_secondary-color"))==null?void 0:r.trim(),i))return i}set secondaryColor(i){this.setAttribute(c.SECONDARY_COLOR,`${i}`)}get accentColor(){var a,r;let i=this.getAttribute(c.ACCENT_COLOR);if(i!=null||this.mediaTheme&&(i=(r=(a=ye.getComputedStyle(this.mediaTheme))==null?void 0:a.getPropertyValue("--_accent-color"))==null?void 0:r.trim(),i))return i}set accentColor(i){this.setAttribute(c.ACCENT_COLOR,`${i}`)}get defaultShowRemainingTime(){return this.hasAttribute(c.DEFAULT_SHOW_REMAINING_TIME)}set defaultShowRemainingTime(i){i?this.setAttribute(c.DEFAULT_SHOW_REMAINING_TIME,""):this.removeAttribute(c.DEFAULT_SHOW_REMAINING_TIME)}get playbackRates(){if(this.hasAttribute(c.PLAYBACK_RATES))return this.getAttribute(c.PLAYBACK_RATES).trim().split(/\s*,?\s+/).map(i=>Number(i)).filter(i=>!Number.isNaN(i)).sort((i,a)=>i-a)}set playbackRates(i){if(!i){this.removeAttribute(c.PLAYBACK_RATES);return}this.setAttribute(c.PLAYBACK_RATES,i.join(" "))}get forwardSeekOffset(){var i;return(i=ue(this.getAttribute(c.FORWARD_SEEK_OFFSET)))!=null?i:10}set forwardSeekOffset(i){this.setAttribute(c.FORWARD_SEEK_OFFSET,`${i}`)}get backwardSeekOffset(){var i;return(i=ue(this.getAttribute(c.BACKWARD_SEEK_OFFSET)))!=null?i:10}set backwardSeekOffset(i){this.setAttribute(c.BACKWARD_SEEK_OFFSET,`${i}`)}get defaultHiddenCaptions(){return this.hasAttribute(c.DEFAULT_HIDDEN_CAPTIONS)}set defaultHiddenCaptions(i){i?this.setAttribute(c.DEFAULT_HIDDEN_CAPTIONS,""):this.removeAttribute(c.DEFAULT_HIDDEN_CAPTIONS)}get defaultDuration(){return ue(this.getAttribute(c.DEFAULT_DURATION))}set defaultDuration(i){i==null?this.removeAttribute(c.DEFAULT_DURATION):this.setAttribute(c.DEFAULT_DURATION,`${i}`)}get playerInitTime(){return this.hasAttribute(b.Attributes.PLAYER_INIT_TIME)?ue(this.getAttribute(b.Attributes.PLAYER_INIT_TIME)):U(this,Pr)}set playerInitTime(i){i!=this.playerInitTime&&(i==null?this.removeAttribute(b.Attributes.PLAYER_INIT_TIME):this.setAttribute(b.Attributes.PLAYER_INIT_TIME,`${+i}`))}get playerSoftwareName(){var i;return(i=this.getAttribute(b.Attributes.PLAYER_SOFTWARE_NAME))!=null?i:od}get playerSoftwareVersion(){var i;return(i=this.getAttribute(b.Attributes.PLAYER_SOFTWARE_VERSION))!=null?i:rd}get beaconCollectionDomain(){var i;return(i=this.getAttribute(b.Attributes.BEACON_COLLECTION_DOMAIN))!=null?i:void 0}set beaconCollectionDomain(i){i!==this.beaconCollectionDomain&&(i?this.setAttribute(b.Attributes.BEACON_COLLECTION_DOMAIN,i):this.removeAttribute(b.Attributes.BEACON_COLLECTION_DOMAIN))}get maxResolution(){var i;return(i=this.getAttribute(b.Attributes.MAX_RESOLUTION))!=null?i:void 0}set maxResolution(i){i!==this.maxResolution&&(i?this.setAttribute(b.Attributes.MAX_RESOLUTION,i):this.removeAttribute(b.Attributes.MAX_RESOLUTION))}get minResolution(){var i;return(i=this.getAttribute(b.Attributes.MIN_RESOLUTION))!=null?i:void 0}set minResolution(i){i!==this.minResolution&&(i?this.setAttribute(b.Attributes.MIN_RESOLUTION,i):this.removeAttribute(b.Attributes.MIN_RESOLUTION))}get renditionOrder(){var i;return(i=this.getAttribute(b.Attributes.RENDITION_ORDER))!=null?i:void 0}set renditionOrder(i){i!==this.renditionOrder&&(i?this.setAttribute(b.Attributes.RENDITION_ORDER,i):this.removeAttribute(b.Attributes.RENDITION_ORDER))}get programStartTime(){return ue(this.getAttribute(b.Attributes.PROGRAM_START_TIME))}set programStartTime(i){i==null?this.removeAttribute(b.Attributes.PROGRAM_START_TIME):this.setAttribute(b.Attributes.PROGRAM_START_TIME,`${i}`)}get programEndTime(){return ue(this.getAttribute(b.Attributes.PROGRAM_END_TIME))}set programEndTime(i){i==null?this.removeAttribute(b.Attributes.PROGRAM_END_TIME):this.setAttribute(b.Attributes.PROGRAM_END_TIME,`${i}`)}get assetStartTime(){return ue(this.getAttribute(b.Attributes.ASSET_START_TIME))}set assetStartTime(i){i==null?this.removeAttribute(b.Attributes.ASSET_START_TIME):this.setAttribute(b.Attributes.ASSET_START_TIME,`${i}`)}get assetEndTime(){return ue(this.getAttribute(b.Attributes.ASSET_END_TIME))}set assetEndTime(i){i==null?this.removeAttribute(b.Attributes.ASSET_END_TIME):this.setAttribute(b.Attributes.ASSET_END_TIME,`${i}`)}get extraSourceParams(){return this.hasAttribute(c.EXTRA_SOURCE_PARAMS)?[...new URLSearchParams(this.getAttribute(c.EXTRA_SOURCE_PARAMS)).entries()].reduce((i,[a,r])=>(i[a]=r,i),{}):Tv}set extraSourceParams(i){i==null?this.removeAttribute(c.EXTRA_SOURCE_PARAMS):this.setAttribute(c.EXTRA_SOURCE_PARAMS,new URLSearchParams(i).toString())}get customDomain(){var i;return(i=this.getAttribute(b.Attributes.CUSTOM_DOMAIN))!=null?i:void 0}set customDomain(i){i!==this.customDomain&&(i?this.setAttribute(b.Attributes.CUSTOM_DOMAIN,i):this.removeAttribute(b.Attributes.CUSTOM_DOMAIN))}get envKey(){var i;return(i=Ht(this,b.Attributes.ENV_KEY))!=null?i:void 0}set envKey(i){this.setAttribute(b.Attributes.ENV_KEY,`${i}`)}get noVolumePref(){return this.hasAttribute(c.NO_VOLUME_PREF)}set noVolumePref(i){i?this.setAttribute(c.NO_VOLUME_PREF,""):this.removeAttribute(c.NO_VOLUME_PREF)}get debug(){return Ht(this,b.Attributes.DEBUG)!=null}set debug(i){i?this.setAttribute(b.Attributes.DEBUG,""):this.removeAttribute(b.Attributes.DEBUG)}get disableTracking(){return Ht(this,b.Attributes.DISABLE_TRACKING)!=null}set disableTracking(i){this.toggleAttribute(b.Attributes.DISABLE_TRACKING,!!i)}get disableCookies(){return Ht(this,b.Attributes.DISABLE_COOKIES)!=null}set disableCookies(i){i?this.setAttribute(b.Attributes.DISABLE_COOKIES,""):this.removeAttribute(b.Attributes.DISABLE_COOKIES)}get streamType(){var i,a,r;return(r=(a=this.getAttribute(b.Attributes.STREAM_TYPE))!=null?a:(i=this.media)==null?void 0:i.streamType)!=null?r:w.StreamTypes.UNKNOWN}set streamType(i){this.setAttribute(b.Attributes.STREAM_TYPE,`${i}`)}get defaultStreamType(){var i,a,r;return(r=(a=this.getAttribute(c.DEFAULT_STREAM_TYPE))!=null?a:(i=this.mediaController)==null?void 0:i.getAttribute(c.DEFAULT_STREAM_TYPE))!=null?r:w.StreamTypes.ON_DEMAND}set defaultStreamType(i){i?this.setAttribute(c.DEFAULT_STREAM_TYPE,i):this.removeAttribute(c.DEFAULT_STREAM_TYPE)}get targetLiveWindow(){var i,a;return this.hasAttribute(c.TARGET_LIVE_WINDOW)?+this.getAttribute(c.TARGET_LIVE_WINDOW):(a=(i=this.media)==null?void 0:i.targetLiveWindow)!=null?a:Number.NaN}set targetLiveWindow(i){i==this.targetLiveWindow||Number.isNaN(i)&&Number.isNaN(this.targetLiveWindow)||(i==null?this.removeAttribute(c.TARGET_LIVE_WINDOW):this.setAttribute(c.TARGET_LIVE_WINDOW,`${+i}`))}get liveEdgeStart(){var i;return(i=this.media)==null?void 0:i.liveEdgeStart}get startTime(){return ue(Ht(this,b.Attributes.START_TIME))}set startTime(i){this.setAttribute(b.Attributes.START_TIME,`${i}`)}get preferPlayback(){let i=this.getAttribute(b.Attributes.PREFER_PLAYBACK);if(i===w.PlaybackTypes.MSE||i===w.PlaybackTypes.NATIVE)return i}set preferPlayback(i){i!==this.preferPlayback&&(i===w.PlaybackTypes.MSE||i===w.PlaybackTypes.NATIVE?this.setAttribute(b.Attributes.PREFER_PLAYBACK,i):this.removeAttribute(b.Attributes.PREFER_PLAYBACK))}get metadata(){var i;return(i=this.media)==null?void 0:i.metadata}set metadata(i){if(X(this,$,Ut).call(this),!this.media){Ee("underlying media element missing when trying to set metadata. metadata will not be set.");return}this.media.metadata={...Em(this),...i}}get _hlsConfig(){var i;return(i=this.media)==null?void 0:i._hlsConfig}set _hlsConfig(i){if(X(this,$,Ut).call(this),!this.media){Ee("underlying media element missing when trying to set _hlsConfig. _hlsConfig will not be set.");return}this.media._hlsConfig=i}async addCuePoints(i){var a;if(X(this,$,Ut).call(this),!this.media){Ee("underlying media element missing when trying to addCuePoints. cuePoints will not be added.");return}return(a=this.media)==null?void 0:a.addCuePoints(i)}get activeCuePoint(){var i;return(i=this.media)==null?void 0:i.activeCuePoint}get cuePoints(){var i,a;return(a=(i=this.media)==null?void 0:i.cuePoints)!=null?a:[]}addChapters(i){var a;if(X(this,$,Ut).call(this),!this.media){Ee("underlying media element missing when trying to addChapters. chapters will not be added.");return}return(a=this.media)==null?void 0:a.addChapters(i)}get activeChapter(){var i;return(i=this.media)==null?void 0:i.activeChapter}get chapters(){var i,a;return(a=(i=this.media)==null?void 0:i.chapters)!=null?a:[]}getStartDate(){var i;return(i=this.media)==null?void 0:i.getStartDate()}get currentPdt(){var i;return(i=this.media)==null?void 0:i.currentPdt}get tokens(){let i=this.getAttribute(c.PLAYBACK_TOKEN),a=this.getAttribute(c.DRM_TOKEN),r=this.getAttribute(c.THUMBNAIL_TOKEN),o=this.getAttribute(c.STORYBOARD_TOKEN);return{...U(this,Hr),...i!=null?{playback:i}:{},...a!=null?{drm:a}:{},...r!=null?{thumbnail:r}:{},...o!=null?{storyboard:o}:{}}}set tokens(i){Te(this,Hr,i!=null?i:{})}get playbackToken(){var i;return(i=this.getAttribute(c.PLAYBACK_TOKEN))!=null?i:void 0}set playbackToken(i){this.setAttribute(c.PLAYBACK_TOKEN,`${i}`)}get drmToken(){var i;return(i=this.getAttribute(c.DRM_TOKEN))!=null?i:void 0}set drmToken(i){this.setAttribute(c.DRM_TOKEN,`${i}`)}get thumbnailToken(){var i;return(i=this.getAttribute(c.THUMBNAIL_TOKEN))!=null?i:void 0}set thumbnailToken(i){this.setAttribute(c.THUMBNAIL_TOKEN,`${i}`)}get storyboardToken(){var i;return(i=this.getAttribute(c.STORYBOARD_TOKEN))!=null?i:void 0}set storyboardToken(i){this.setAttribute(c.STORYBOARD_TOKEN,`${i}`)}addTextTrack(i,a,r,o){var l;let s=(l=this.media)==null?void 0:l.nativeEl;if(s)return(0,w.addTextTrack)(s,i,a,r,o)}removeTextTrack(i){var r;let a=(r=this.media)==null?void 0:r.nativeEl;if(a)return(0,w.removeTextTrack)(a,i)}get textTracks(){var i;return(i=this.media)==null?void 0:i.textTracks}get castReceiver(){var i;return(i=this.getAttribute(c.CAST_RECEIVER))!=null?i:void 0}set castReceiver(i){i!==this.castReceiver&&(i?this.setAttribute(c.CAST_RECEIVER,i):this.removeAttribute(c.CAST_RECEIVER))}get castCustomData(){var i;return(i=this.media)==null?void 0:i.castCustomData}set castCustomData(i){if(!this.media){Ee("underlying media element missing when trying to set castCustomData. castCustomData will not be set.");return}this.media.castCustomData=i}get noTooltips(){return this.hasAttribute(c.NO_TOOLTIPS)}set noTooltips(i){if(!i){this.removeAttribute(c.NO_TOOLTIPS);return}this.setAttribute(c.NO_TOOLTIPS,"")}get proudlyDisplayMuxBadge(){return this.hasAttribute(c.PROUDLY_DISPLAY_MUX_BADGE)}set proudlyDisplayMuxBadge(i){i?this.setAttribute(c.PROUDLY_DISPLAY_MUX_BADGE,""):this.removeAttribute(c.PROUDLY_DISPLAY_MUX_BADGE)}};Pr=new WeakMap,Ur=new WeakMap,Hr=new WeakMap,Bt=new WeakMap,Br=new WeakMap,Ei=new WeakMap,$=new WeakSet,Ut=function(){var i,a,r,o;if(!U(this,Ur)){Te(this,Ur,!0),X(this,$,vi).call(this);try{if(customElements.upgrade(this.mediaTheme),!(this.mediaTheme instanceof ye.HTMLElement))throw""}catch{Ee("<media-theme> failed to upgrade!")}try{customElements.upgrade(this.media)}catch{Ee("underlying media element failed to upgrade!")}try{if(customElements.upgrade(this.mediaController),!(this.mediaController instanceof gs))throw""}catch{Ee("<media-controller> failed to upgrade!")}X(this,$,_m).call(this),X(this,$,Am).call(this),X(this,$,Tm).call(this),Te(this,Bt,(a=(i=this.mediaController)==null?void 0:i.hasAttribute(y.USER_INACTIVE))!=null?a:!0),X(this,$,ym).call(this),(r=this.media)==null||r.addEventListener("streamtypechange",()=>X(this,$,vi).call(this)),(o=this.media)==null||o.addEventListener("loadstart",()=>X(this,$,vi).call(this))}},gm=function(){var i,a;try{(i=window==null?void 0:window.CSS)==null||i.registerProperty({name:"--media-primary-color",syntax:"<color>",inherits:!0}),(a=window==null?void 0:window.CSS)==null||a.registerProperty({name:"--media-secondary-color",syntax:"<color>",inherits:!0})}catch{}},sd=function(i){Object.assign(U(this,Ei),i),X(this,$,vi).call(this)},vi=function(i={}){Hc(Bc(fv(this,{...U(this,Ei),...i})),this.shadowRoot)},_m=function(){let i=r=>{var l,u;if(!(r!=null&&r.startsWith("theme-")))return;let o=r.replace(/^theme-/,"");if(ad.includes(o))return;let s=this.getAttribute(r);s!=null?(l=this.mediaTheme)==null||l.setAttribute(o,s):(u=this.mediaTheme)==null||u.removeAttribute(o)};new MutationObserver(r=>{for(let{attributeName:o}of r)i(o)}).observe(this,{attributes:!0}),this.getAttributeNames().forEach(i)},Am=function(){let i=a=>{var s;let r=(s=this.media)==null?void 0:s.error;if(!(r instanceof b.MediaError)){let{message:l,code:u}=r!=null?r:{};r=new b.MediaError(l,u)}if(!(r!=null&&r.fatal)){Qe(r),r.data&&Qe(`${r.name} data:`,r.data);return}let o=bl(r,!1);o.message&&ol(o),Ee(r),r.data&&Ee(`${r.name} data:`,r.data),X(this,$,sd).call(this,{isDialogOpen:!0})};this.addEventListener("error",i),this.media&&(this.media.errorTranslator=(a={})=>{var o,s,l;if(!(((o=this.media)==null?void 0:o.error)instanceof b.MediaError))return a;let r=bl((s=this.media)==null?void 0:s.error,!1);return{player_error_code:(l=this.media)==null?void 0:l.error.code,player_error_message:r.message?String(r.message):a.player_error_message,player_error_context:r.context?String(r.context):a.player_error_context}})},Tm=function(){var a,r,o,s;let i=()=>X(this,$,vi).call(this);(r=(a=this.media)==null?void 0:a.textTracks)==null||r.addEventListener("addtrack",i),(s=(o=this.media)==null?void 0:o.textTracks)==null||s.addEventListener("removetrack",i)},ym=function(){var m,_;if(!/Firefox/i.test(navigator.userAgent))return;let a,r=new WeakMap,o=()=>this.streamType===w.StreamTypes.LIVE&&!this.secondaryColor&&this.offsetWidth>=800,s=(g,f,v=!1)=>{if(o())return;Array.from(g&&g.activeCues||[]).forEach(T=>{if(!(!T.snapToLines||T.line<-5||T.line>=0&&T.line<10))if(!f||this.paused){let C=T.text.split(`
`).length,P=-3;this.streamType===w.StreamTypes.LIVE&&(P=-2);let j=P-C;if(T.line===j&&!v)return;r.has(T)||r.set(T,T.line),T.line=j}else setTimeout(()=>{T.line=r.get(T)||"auto"},500)})},l=()=>{var g,f;s(a,(f=(g=this.mediaController)==null?void 0:g.hasAttribute(y.USER_INACTIVE))!=null?f:!1)},u=()=>{var v,N;let f=Array.from(((N=(v=this.mediaController)==null?void 0:v.media)==null?void 0:N.textTracks)||[]).filter(T=>["subtitles","captions"].includes(T.kind)&&T.mode==="showing")[0];f!==a&&(a==null||a.removeEventListener("cuechange",l)),a=f,a==null||a.addEventListener("cuechange",l),s(a,U(this,Bt))};u(),(m=this.textTracks)==null||m.addEventListener("change",u),(_=this.textTracks)==null||_.addEventListener("addtrack",u),this.addEventListener("userinactivechange",()=>{var f,v;let g=(v=(f=this.mediaController)==null?void 0:f.hasAttribute(y.USER_INACTIVE))!=null?v:!0;U(this,Bt)!==g&&(Te(this,Bt,g),s(a,U(this,Bt)))})};function Ht(t,e){return t.media?t.media.getAttribute(e):t.getAttribute(e)}var yv=nd;
