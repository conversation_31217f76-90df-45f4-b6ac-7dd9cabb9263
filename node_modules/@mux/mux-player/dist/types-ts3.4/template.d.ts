import 'media-chrome/dist/media-theme-element.js';
import { MuxTemplateProps } from './types';
export declare const template: (props: MuxTemplateProps) => import("./html").TemplateResult;
export declare const Parts: {
    TOP: string;
    CENTER: string;
    BOTTOM: string;
    LAYER: string;
    MEDIA_LAYER: string;
    POSTER_LAYER: string;
    VERTICAL_LAYER: string;
    CENTERED_LAYER: string;
    GESTURE_LAYER: string;
    CONTROLLER_LAYER: string;
    BUTTON: string;
    RANGE: string;
    DISPLAY: string;
    CONTROL_BAR: string;
    MENU_BUTTON: string;
    MENU: string;
    OPTION: string;
    POSTER: string;
    LIVE: string;
    PLAY: string;
    PRE_PLAY: string;
    SEEK_BACKWARD: string;
    SEEK_FORWARD: string;
    MUTE: string;
    CAPTIONS: string;
    AIRPLAY: string;
    PIP: string;
    FULLSCREEN: string;
    CAST: string;
    PLAYBACK_RATE: string;
    VOLUME: string;
    TIME: string;
    TITLE: string;
    AUDIO_TRACK: string;
    RENDITION: string;
};
export declare const partsListStr: string;
export declare const content: (props: MuxTemplateProps) => import("./html").TemplateResult;
