var d=e=>{throw TypeError(e)};var c=(e,t,n)=>t.has(e)||d("Cannot "+n);var p=(e,t,n)=>(c(e,t,"read from private field"),n?n.call(e):t.get(e)),f=(e,t,n)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),g=(e,t,n,u)=>(c(e,t,"write to private field"),u?u.call(e,n):t.set(e,n),n);var o=class{addEventListener(){}removeEventListener(){}dispatchEvent(t){return!0}};if(typeof DocumentFragment=="undefined"){class e extends o{}globalThis.DocumentFragment=e}var s=class extends o{},l=class extends o{},h={get(e){},define(e,t,n){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(s)}},r,i=class{constructor(t,n={}){f(this,r);g(this,r,n==null?void 0:n.detail)}get detail(){return p(this,r)}initCustomEvent(){}};r=new WeakMap;function y(e,t){return new s}var x={document:{createElement:y},DocumentFragment,customElements:h,CustomEvent:i,EventTarget:o,HTMLElement:s,HTMLVideoElement:l},E=typeof window=="undefined"||typeof globalThis.customElements=="undefined",m=E?x:globalThis,v=E?x.document:globalThis.document;import"@mux/mux-video/ads";import b from"@mux/mux-player/base";import{AdsPlayerMixin as T}from"@mux/mux-player/ads/mixin";export*from"@mux/mux-player/base";var a=class extends T(b){};m.customElements.get("mux-player")||(m.customElements.define("mux-player",a),m.MuxPlayerElement=a);var G=a;export{G as default};
