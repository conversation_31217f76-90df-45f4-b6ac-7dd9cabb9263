"use strict";var n=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var k=Object.prototype.hasOwnProperty;var u=e=>{throw TypeError(e)};var L=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},T=(e,t,r,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let d of m(t))!k.call(e,d)&&d!==r&&n(e,d,{get:()=>t[d],enumerable:!(l=g(t,d))||l.enumerable});return e};var _=e=>T(n({},"__esModule",{value:!0}),e);var c=(e,t,r)=>t.has(e)||u("Cannot "+r);var A=(e,t,r)=>(c(e,t,"read from private field"),r?r.call(e):t.get(e)),h=(e,t,r)=>t.has(e)?u("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),b=(e,t,r,l)=>(c(e,t,"write to private field"),l?l.call(e,r):t.set(e,r),r);var f={};L(f,{AdsPlayerMixin:()=>C,Attributes:()=>s});module.exports=_(f);var s={AD_TAG_URL:"ad-tag-url",ALLOW_AD_BLOCKER:"allow-ad-blocker"};function C(e){var r;class t extends e{constructor(){super(...arguments);h(this,r,[])}static get observedAttributes(){return[...super.observedAttributes,...Object.values(s)]}connectedCallback(){super.connectedCallback(),this.media&&(this.media.allowAdBlocker=this.allowAdBlocker,this.media.adTagUrl=this.adTagUrl),this.addEventListener("adbreakstart",()=>{var a,i;(a=this.mediaTheme)==null||a.toggleAttribute("mediaadbreak",!0),b(this,r,Array.from(((i=this.media)==null?void 0:i.querySelectorAll("track"))||[])),A(this,r).forEach(o=>o.remove())}),this.addEventListener("adbreakend",()=>{var a,i;(a=this.mediaTheme)==null||a.toggleAttribute("mediaadbreak",!1),(i=this.media)==null||i.append(...A(this,r))})}attributeChangedCallback(a,i,o){if(super.attributeChangedCallback(a,i,o),!!this.media)switch(a){case s.ALLOW_AD_BLOCKER:this.media.allowAdBlocker=this.allowAdBlocker;break;case s.AD_TAG_URL:this.media.adTagUrl=this.adTagUrl;break}}get allowAdBlocker(){return this.hasAttribute(s.ALLOW_AD_BLOCKER)}set allowAdBlocker(a){a!==this.allowAdBlocker&&this.toggleAttribute(s.ALLOW_AD_BLOCKER,!!a)}get adTagUrl(){var a;return(a=this.getAttribute(s.AD_TAG_URL))!=null?a:void 0}set adTagUrl(a){a!==this.adTagUrl&&(a?this.setAttribute(s.AD_TAG_URL,a):this.removeAttribute(s.AD_TAG_URL))}}return r=new WeakMap,t}
