var n=t=>{throw TypeError(t)};var A=(t,a,r)=>a.has(t)||n("Cannot "+r);var o=(t,a,r)=>(A(t,a,"read from private field"),r?r.call(t):a.get(t)),u=(t,a,r)=>a.has(t)?n("Cannot add the same private member more than once"):a instanceof WeakSet?a.add(t):a.set(t,r),c=(t,a,r,l)=>(A(t,a,"write to private field"),l?l.call(t,r):a.set(t,r),r);var s={AD_TAG_URL:"ad-tag-url",ALLOW_AD_BLOCKER:"allow-ad-blocker"};function g(t){var r;class a extends t{constructor(){super(...arguments);u(this,r,[])}static get observedAttributes(){return[...super.observedAttributes,...Object.values(s)]}connectedCallback(){super.connectedCallback(),this.media&&(this.media.allowAdBlocker=this.allowAdBlocker,this.media.adTagUrl=this.adTagUrl),this.addEventListener("adbreakstart",()=>{var e,i;(e=this.mediaTheme)==null||e.toggleAttribute("mediaadbreak",!0),c(this,r,Array.from(((i=this.media)==null?void 0:i.querySelectorAll("track"))||[])),o(this,r).forEach(d=>d.remove())}),this.addEventListener("adbreakend",()=>{var e,i;(e=this.mediaTheme)==null||e.toggleAttribute("mediaadbreak",!1),(i=this.media)==null||i.append(...o(this,r))})}attributeChangedCallback(e,i,d){if(super.attributeChangedCallback(e,i,d),!!this.media)switch(e){case s.ALLOW_AD_BLOCKER:this.media.allowAdBlocker=this.allowAdBlocker;break;case s.AD_TAG_URL:this.media.adTagUrl=this.adTagUrl;break}}get allowAdBlocker(){return this.hasAttribute(s.ALLOW_AD_BLOCKER)}set allowAdBlocker(e){e!==this.allowAdBlocker&&this.toggleAttribute(s.ALLOW_AD_BLOCKER,!!e)}get adTagUrl(){var e;return(e=this.getAttribute(s.AD_TAG_URL))!=null?e:void 0}set adTagUrl(e){e!==this.adTagUrl&&(e?this.setAttribute(s.AD_TAG_URL,e):this.removeAttribute(s.AD_TAG_URL))}}return r=new WeakMap,a}export{g as AdsPlayerMixin,s as Attributes};
