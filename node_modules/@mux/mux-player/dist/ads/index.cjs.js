"use strict";var w=Object.create;var c=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var A=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,F=Object.prototype.hasOwnProperty;var x=e=>{throw TypeError(e)};var G=(e,t)=>{for(var n in t)c(e,n,{get:t[n],enumerable:!0})},d=(e,t,n,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let m of A(t))!F.call(e,m)&&m!==n&&c(e,m,{get:()=>t[m],enumerable:!(a=C(t,m))||a.enumerable});return e},s=(e,t,n)=>(d(e,t,"default"),n&&d(n,t,"default")),O=(e,t,n)=>(n=e!=null?w(D(e)):{},d(t||!e||!e.__esModule?c(n,"default",{value:e,enumerable:!0}):n,e)),P=e=>d(c({},"__esModule",{value:!0}),e);var E=(e,t,n)=>t.has(e)||x("Cannot "+n);var h=(e,t,n)=>(E(e,t,"read from private field"),n?n.call(e):t.get(e)),y=(e,t,n)=>t.has(e)?x("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),b=(e,t,n,a)=>(E(e,t,"write to private field"),a?a.call(e,n):t.set(e,n),n);var o={};G(o,{default:()=>N});module.exports=P(o);var r=class{addEventListener(){}removeEventListener(){}dispatchEvent(t){return!0}};if(typeof DocumentFragment=="undefined"){class e extends r{}globalThis.DocumentFragment=e}var l=class extends r{},f=class extends r{},k={get(e){},define(e,t,n){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(l)}},i,g=class{constructor(t,n={}){y(this,i);b(this,i,n==null?void 0:n.detail)}get detail(){return h(this,i)}initCustomEvent(){}};i=new WeakMap;function K(e,t){return new l}var T={document:{createElement:K},DocumentFragment,customElements:k,CustomEvent:g,EventTarget:r,HTMLElement:l,HTMLVideoElement:f},_=typeof window=="undefined"||typeof globalThis.customElements=="undefined",p=_?T:globalThis,B=_?T.document:globalThis.document;var j=require("@mux/mux-video/ads"),v=O(require("@mux/mux-player/base")),M=require("@mux/mux-player/ads/mixin");s(o,require("@mux/mux-player/base"),module.exports);var u=class extends(0,M.AdsPlayerMixin)(v.default){};p.customElements.get("mux-player")||(p.customElements.define("mux-player",u),p.MuxPlayerElement=u);var N=u;
