"use strict";var w=Object.create;var c=Object.defineProperty;var C=Object.getOwnPropertyDescriptor;var v=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,F=Object.prototype.hasOwnProperty;var h=e=>{throw TypeError(e)};var G=(e,t)=>{for(var n in t)c(e,n,{get:t[n],enumerable:!0})},i=(e,t,n,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let a of v(t))!F.call(e,a)&&a!==n&&c(e,a,{get:()=>t[a],enumerable:!(l=C(t,a))||l.enumerable});return e},s=(e,t,n)=>(i(e,t,"default"),n&&i(n,t,"default")),P=(e,t,n)=>(n=e!=null?w(D(e)):{},i(t||!e||!e.__esModule?c(n,"default",{value:e,enumerable:!0}):n,e)),N=e=>i(c({},"__esModule",{value:!0}),e);var E=(e,t,n)=>t.has(e)||h("Cannot "+n);var b=(e,t,n)=>(E(e,t,"read from private field"),n?n.call(e):t.get(e)),y=(e,t,n)=>t.has(e)?h("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),_=(e,t,n,l)=>(E(e,t,"write to private field"),l?l.call(e,n):t.set(e,n),n);var o={};G(o,{default:()=>A});module.exports=N(o);var r=class{addEventListener(){}removeEventListener(){}dispatchEvent(t){return!0}};if(typeof DocumentFragment=="undefined"){class e extends r{}globalThis.DocumentFragment=e}var m=class extends r{},p=class extends r{},S={get(e){},define(e,t,n){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(m)}},u,f=class{constructor(t,n={}){y(this,u);_(this,u,n==null?void 0:n.detail)}get detail(){return b(this,u)}initCustomEvent(){}};u=new WeakMap;function k(e,t){return new m}var x={document:{createElement:k},DocumentFragment,customElements:S,CustomEvent:f,EventTarget:r,HTMLElement:m,HTMLVideoElement:p},T=typeof window=="undefined"||typeof globalThis.customElements=="undefined",d=T?x:globalThis,M=T?x.document:globalThis.document;var q=require("@mux/mux-video"),g=P(require("@mux/mux-player/base"));s(o,require("@mux/mux-player/base"),module.exports);d.customElements.get("mux-player")||(d.customElements.define("mux-player",g.default),d.MuxPlayerElement=g.default);var A=g.default;
