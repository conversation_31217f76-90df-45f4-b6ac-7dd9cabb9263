var c=e=>{throw TypeError(e)};var d=(e,t,n)=>t.has(e)||c("Cannot "+n);var g=(e,t,n)=>(d(e,t,"read from private field"),n?n.call(e):t.get(e)),p=(e,t,n)=>t.has(e)?c("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),f=(e,t,n,i)=>(d(e,t,"write to private field"),i?i.call(e,n):t.set(e,n),n);var o=class{addEventListener(){}removeEventListener(){}dispatchEvent(t){return!0}};if(typeof DocumentFragment=="undefined"){class e extends o{}globalThis.DocumentFragment=e}var s=class extends o{},a=class extends o{},b={get(e){},define(e,t,n){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(s)}},r,m=class{constructor(t,n={}){p(this,r);f(this,r,n==null?void 0:n.detail)}get detail(){return g(this,r)}initCustomEvent(){}};r=new WeakMap;function y(e,t){return new s}var h={document:{createElement:y},DocumentFragment,customElements:b,CustomEvent:m,EventTarget:o,HTMLElement:s,HTMLVideoElement:a},E=typeof window=="undefined"||typeof globalThis.customElements=="undefined",l=E?h:globalThis,x=E?h.document:globalThis.document;import"@mux/mux-video";import u from"@mux/mux-player/base";export*from"@mux/mux-player/base";l.customElements.get("mux-player")||(l.customElements.define("mux-player",u),l.MuxPlayerElement=u);var F=u;export{F as default};
