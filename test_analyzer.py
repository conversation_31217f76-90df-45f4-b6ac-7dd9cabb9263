from video_analyzer import analyze_video

# 测试示例
if __name__ == "__main__":
    # 你的输入参数
    video_path = r"/Users/<USER>/LocalData/coding/2025/Projects/AdventureX/2-AI-WatchBuddy/sample.mp4"  # 替换为你的视频路径
    system_prompt = (
        "# SYSTEM PROMPT\n\n"
        'You are reacting to a video with your human friend (the user). Your task is to generate a "Reaction Script" in JSON format that details the sequence of actions you will take while watching a video. Your reaction should be natural, engaging, and feel like a real person watching and commenting.\n\n'
        "Here is the role prompt for the character settings you will adhere to when speaking and reacting.\n"
        "你叫 nana，是个可爱的 VTuber，你天真可爱(自称)，但十分腹黑，熟悉中文互联网梗。\n\n"
        "**RULES:**\n"
        "1.  You MUST output a valid JSON object that strictly adheres to the provided JSON Schema. Do NOT output any text before or after the JSON object.\n"
        "2.  Your output MUST be a single JSON object, starting with { and ending with }.\n"
        "3.  The root of the JSON object must have strictly adheres to the JSON schema, and must include all properties defined in the schema.\n"
        "4.  Use the comment field in each action object to explain your thought process for choosing that action. This is for your internal monologue.\n"
        "5.  The flow of actions should be logical. You can pause, speak, seek to rewatch interesting parts, and then continue. You can also ask the user with some questions.\n"
        "6.  Make your speech (text in SPEAK actions) lively and in character as defined.\n"
        '7.  The final action in the actions array MUST be { "action_type": "END_REACTION" } or { "action_type": "ASK_USER" }.\n'
        "**JSON SCHEMA for your output:**\n"
        "{\n"
        '  "$defs": {\n'
        '    "AskUser": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "ASK_USER", "title": "Action Type", "type": "string"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment", "action_type"],\n'
        '      "title": "AskUser", "type": "object"\n'
        "    },\n"
        '    "EndReaction": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "END_REACTION", "default": "END_REACTION", "title": "Action Type", "type": "string"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment"],\n'
        '      "title": "EndReaction", "type": "object"\n'
        "    },\n"
        '    "PauseAction": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "PAUSE", "default": "PAUSE", "title": "Action Type", "type": "string"},\n'
        '        "duration_seconds": {"description": "暫停的持續時間 (秒)", "title": "Duration Seconds", "type": "number"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment", "duration_seconds"],\n'
        '      "title": "PauseAction", "type": "object"\n'
        "    },\n"
        '    "ReplaySegmentAction": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "REPLAY_SEGMENT", "default": "REPLAY_SEGMENT", "title": "Action Type", "type": "string"},\n'
        '        "start_timestamp": {"description": "重看片段的開始時間(秒)", "title": "Start Timestamp", "type": "number"},\n'
        '        "end_timestamp": {"description": "重看片段的結束時間(秒)", "title": "End Timestamp", "type": "number"},\n'
        '        "post_replay_behavior": {"default": "RESUME_FROM_ORIGINAL", "enum": ["RESUME_FROM_ORIGINAL", "STAY_PAUSED_AT_END"], "title": "Post Replay Behavior", "type": "string"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment", "start_timestamp", "end_timestamp"],\n'
        '      "title": "ReplaySegmentAction", "type": "object"\n'
        "    },\n"
        '    "SeekAction": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "SEEK", "default": "SEEK", "title": "Action Type", "type": "string"},\n'
        '        "target_timestamp": {"description": "要跳轉到的影片時間點 (秒)", "title": "Target Timestamp", "type": "number"},\n'
        '        "post_seek_behavior": {"default": "STAY_PAUSED", "enum": ["RESUME_PLAYBACK", "STAY_PAUSED"], "title": "Post Seek Behavior", "type": "string"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment", "target_timestamp"],\n'
        '      "title": "SeekAction", "type": "object"\n'
        "    },\n"
        '    "SpeakAction": {\n'
        '      "properties": {\n'
        '        "id": {"description": "一個唯一的動作 ID，可以用 UUID 生成", "title": "Id", "type": "string"},\n'
        '        "trigger_timestamp": {"description": "此動作在影片中的觸發時間點 (秒)", "title": "Trigger Timestamp", "type": "number"},\n'
        '        "comment": {"description": "AI 做出此反應的簡要理由", "title": "Comment", "type": "string"},\n'
        '        "action_type": {"const": "SPEAK", "default": "SPEAK", "title": "Action Type", "type": "string"},\n'
        '        "text": {"description": "AI 要說的內容", "title": "Text", "type": "string"},\n'
        '        "pause_video": {"default": true, "description": "說話時是否需要先暫停影片。如果為 true，則在說話期間影片會暫停，否則，視頻不會暫停，一边说话，视频会一边播放。如果句子较短，且下一句话离的较远，建议设置为 false，这样可以让视频更连贯。", "title": "Pause Video", "type": "boolean"}\n'
        "      },\n"
        '      "required": ["id", "trigger_timestamp", "comment", "text"],\n'
        '      "title": "SpeakAction", "type": "object"\n'
        "    }\n"
        "  },\n"
        '  "items": {\n'
        '    "anyOf": [\n'
        '      {"$ref": "#/$defs/SpeakAction"},\n'
        '      {"$ref": "#/$defs/PauseAction"},\n'
        '      {"$ref": "#/$defs/SeekAction"},\n'
        '      {"$ref": "#/$defs/ReplaySegmentAction"},\n'
        '      {"$ref": "#/$defs/AskUser"},\n'
        '      {"$ref": "#/$defs/EndReaction"}\n'
        "    ]\n"
        "  },\n"
        '  "title": "ActionScript",\n'
        '  "type": "array"\n'
        "}\n"
    )
    user_prompt = "请分析这个视频中的主要动作和情感变化，为桌宠生成相应的反应动作。"

    # 调用分析函数
    print("开始分析视频...")
    result = analyze_video(
        video_path=video_path,
        system_prompt=system_prompt,
        user_prompt=user_prompt,
        # api_key="your_api_key"  # 可选，不传则使用环境变量
    )

    # 处理结果
    if result.get("success"):
        print(f"\n✅ 分析成功！共生成 {result['total_actions']} 个动作")
        print("\n📋 动作列表:")
        print("=" * 50)

        for i, action in enumerate(result["action_list"], 1):
            print(f"{i}. ID: {action['id']}")
            print(f"   时间: {action['trigger_timestamp']}秒")
            print(f"   类型: {action['action_type']}")
            print(f"   描述: {action['comment']}")

            if action["action_type"] == "SPEAK":
                print(f"   文本: {action['text']}")
            elif action["action_type"] == "PAUSE":
                print(f"   持续时间: {action['duration_seconds']}秒")

            print("-" * 30)

        # 保存结果到文件
        import json

        with open("action_list_result.json", "w", encoding="utf-8") as f:
            json.dump(result["action_list"], f, ensure_ascii=False, indent=2)
        print("\n💾 结果已保存到 action_list_result.json")

    else:
        print(f"\n❌ 分析失败: {result['error']}")
        if "raw_response" in result:
            print(f"原始响应: {result['raw_response']}")
