"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/castable-video";
exports.ids = ["vendor-chunks/castable-video"];
exports.modules = {

/***/ "(ssr)/./node_modules/castable-video/castable-mixin.js":
/*!*******************************************************!*\
  !*** ./node_modules/castable-video/castable-mixin.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CastableMediaMixin: () => (/* binding */ CastableMediaMixin),\n/* harmony export */   CastableVideoMixin: () => (/* binding */ CastableVideoMixin)\n/* harmony export */ });\n/* harmony import */ var _castable_remote_playback_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./castable-remote-playback.js */ \"(ssr)/./node_modules/castable-video/castable-remote-playback.js\");\n/* harmony import */ var _castable_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./castable-utils.js */ \"(ssr)/./node_modules/castable-video/castable-utils.js\");\n/* global chrome */\n\n\n\n/**\n * CastableMediaMixin\n *\n * This mixin function provides a way to compose multiple classes.\n * @see https://justinfagnani.com/2015/12/21/real-mixins-with-javascript-classes/\n *\n * @param  {HTMLMediaElement} superclass - HTMLMediaElement or an extended class of it.\n * @return {CastableMedia}\n */\nconst CastableMediaMixin = (superclass) =>\n  class CastableMedia extends superclass {\n\n    static observedAttributes = [\n      ...(superclass.observedAttributes ?? []),\n      'cast-src',\n      'cast-content-type',\n      'cast-stream-type',\n      'cast-receiver',\n    ];\n\n    #localState = { paused: false };\n    #castOptions = (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.getDefaultCastOptions)();\n    #castCustomData;\n    #remote;\n\n    get remote() {\n      if (this.#remote) return this.#remote;\n\n      if ((0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.requiresCastFramework)()) {\n        // No need to load the Cast framework if it's disabled.\n        if (!this.disableRemotePlayback) {\n          (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.loadCastFramework)();\n        }\n\n        _castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.privateProps.set(this, {\n          loadOnPrompt: () => this.#loadOnPrompt()\n        });\n\n        return (this.#remote = new _castable_remote_playback_js__WEBPACK_IMPORTED_MODULE_0__.RemotePlayback(this));\n      }\n\n      return super.remote;\n    }\n\n    get #castPlayer() {\n      return _castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.privateProps.get(this.remote)?.getCastPlayer?.();\n    }\n\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      super.attributeChangedCallback(attrName, oldValue, newValue);\n\n      if (attrName === 'cast-receiver' && newValue) {\n        this.#castOptions.receiverApplicationId = newValue;\n        return;\n      }\n\n      if (!this.#castPlayer) return;\n\n      switch (attrName) {\n        case 'cast-stream-type':\n        case 'cast-src':\n          this.load();\n          break;\n      }\n    }\n\n    async #loadOnPrompt() {\n      // Pause locally when the session is created.\n      this.#localState.paused = super.paused;\n      super.pause();\n\n      // Sync over the muted state but not volume, 100% is different on TV's :P\n      this.muted = super.muted;\n\n      try {\n        await this.load();\n      } catch (err) {\n        console.error(err);\n      }\n    }\n\n    async load() {\n      if (!this.#castPlayer) return super.load();\n\n      const mediaInfo = new chrome.cast.media.MediaInfo(this.castSrc, this.castContentType);\n      mediaInfo.customData = this.castCustomData;\n\n      // Manually add text tracks with a `src` attribute.\n      // M3U8's load text tracks in the receiver, handle these in the media loaded event.\n      const subtitles = [...this.querySelectorAll('track')].filter(\n        ({ kind, src }) => src && (kind === 'subtitles' || kind === 'captions')\n      );\n\n      const activeTrackIds = [];\n      let textTrackIdCount = 0;\n\n      if (subtitles.length) {\n        mediaInfo.tracks = subtitles.map((trackEl) => {\n          const trackId = ++textTrackIdCount;\n          // only activate 1 subtitle text track.\n          if (activeTrackIds.length === 0 && trackEl.track.mode === 'showing') {\n            activeTrackIds.push(trackId);\n          }\n\n          const track = new chrome.cast.media.Track(\n            trackId,\n            chrome.cast.media.TrackType.TEXT\n          );\n          track.trackContentId = trackEl.src;\n          track.trackContentType = 'text/vtt';\n          track.subtype =\n            trackEl.kind === 'captions'\n              ? chrome.cast.media.TextTrackType.CAPTIONS\n              : chrome.cast.media.TextTrackType.SUBTITLES;\n          track.name = trackEl.label;\n          track.language = trackEl.srclang;\n          return track;\n        });\n      }\n\n      if (this.castStreamType === 'live') {\n        mediaInfo.streamType = chrome.cast.media.StreamType.LIVE;\n      } else {\n        mediaInfo.streamType = chrome.cast.media.StreamType.BUFFERED;\n      }\n\n      mediaInfo.metadata = new chrome.cast.media.GenericMediaMetadata();\n      mediaInfo.metadata.title = this.title;\n      mediaInfo.metadata.images = [{ url: this.poster }];\n\n      if ((0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.isHls)(this.castSrc)) {\n        const segmentFormat = await (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.getPlaylistSegmentFormat)(this.castSrc);\n        const isFragmentedMP4 = segmentFormat?.includes('m4s') || segmentFormat?.includes('mp4');\n        if (isFragmentedMP4) {\n          mediaInfo.hlsSegmentFormat = chrome.cast.media.HlsSegmentFormat.FMP4;\n          mediaInfo.hlsVideoSegmentFormat = chrome.cast.media.HlsVideoSegmentFormat.FMP4;\n        }\n      }\n\n      const request = new chrome.cast.media.LoadRequest(mediaInfo);\n      request.currentTime = super.currentTime ?? 0;\n      request.autoplay = !this.#localState.paused;\n      request.activeTrackIds = activeTrackIds;\n\n      await (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_1__.currentSession)()?.loadMedia(request);\n\n      this.dispatchEvent(new Event('volumechange'));\n    }\n\n    play() {\n      if (this.#castPlayer) {\n        if (this.#castPlayer.isPaused) {\n          this.#castPlayer.controller?.playOrPause();\n        }\n        return;\n      }\n      return super.play();\n    }\n\n    pause() {\n      if (this.#castPlayer) {\n        if (!this.#castPlayer.isPaused) {\n          this.#castPlayer.controller?.playOrPause();\n        }\n        return;\n      }\n      super.pause();\n    }\n\n    /**\n     * @see https://developers.google.com/cast/docs/reference/web_sender/cast.framework.CastOptions\n     * @readonly\n     *\n     * @typedef {Object} CastOptions\n     * @property {string} [receiverApplicationId='CC1AD845'] - The app id of the cast receiver.\n     * @property {string} [autoJoinPolicy='origin_scoped'] - The auto join policy.\n     * @property {string} [language='en-US'] - The language to use for the cast receiver.\n     * @property {boolean} [androidReceiverCompatible=false] - Whether to use the Cast Connect.\n     * @property {boolean} [resumeSavedSession=true] - Whether to resume the last session.\n     *\n     * @return {CastOptions}\n     */\n    get castOptions() {\n      return this.#castOptions;\n    }\n\n    get castReceiver() {\n      return this.getAttribute('cast-receiver') ?? undefined;\n    }\n\n    set castReceiver(val) {\n      if (this.castReceiver == val) return;\n      this.setAttribute('cast-receiver', `${val}`);\n    }\n\n    // Allow the cast source url to be different than <video src>, could be a blob.\n    get castSrc() {\n      // Try the first <source src> for usage with even more native markup.\n      return (\n        this.getAttribute('cast-src') ??\n        this.querySelector('source')?.src ??\n        this.currentSrc\n      );\n    }\n\n    set castSrc(val) {\n      if (this.castSrc == val) return;\n      this.setAttribute('cast-src', `${val}`);\n    }\n\n    get castContentType() {\n      return this.getAttribute('cast-content-type') ?? undefined;\n    }\n\n    set castContentType(val) {\n      this.setAttribute('cast-content-type', `${val}`);\n    }\n\n    get castStreamType() {\n      // NOTE: Per https://github.com/video-dev/media-ui-extensions/issues/3 `streamType` may yield `\"unknown\"`\n      return this.getAttribute('cast-stream-type') ?? this.streamType ?? undefined;\n    }\n\n    set castStreamType(val) {\n      this.setAttribute('cast-stream-type', `${val}`);\n    }\n\n    get castCustomData() {\n      return this.#castCustomData;\n    }\n\n    set castCustomData(val) {\n      const valType = typeof val;\n      if (!['object', 'undefined'].includes(valType)) {\n        console.error(`castCustomData must be nullish or an object but value was of type ${valType}`);\n        return;\n      }\n\n      this.#castCustomData = val;\n    }\n\n    get readyState() {\n      if (this.#castPlayer) {\n        switch (this.#castPlayer.playerState) {\n          case chrome.cast.media.PlayerState.IDLE:\n            return 0;\n          case chrome.cast.media.PlayerState.BUFFERING:\n            return 2;\n          default:\n            return 3;\n        }\n      }\n      return super.readyState;\n    }\n\n    get paused() {\n      if (this.#castPlayer) return this.#castPlayer.isPaused;\n      return super.paused;\n    }\n\n    get muted() {\n      if (this.#castPlayer) return this.#castPlayer?.isMuted;\n      return super.muted;\n    }\n\n    set muted(val) {\n      if (this.#castPlayer) {\n        if (\n          (val && !this.#castPlayer.isMuted) ||\n          (!val && this.#castPlayer.isMuted)\n        ) {\n          this.#castPlayer.controller?.muteOrUnmute();\n        }\n        return;\n      }\n      super.muted = val;\n    }\n\n    get volume() {\n      if (this.#castPlayer) return this.#castPlayer?.volumeLevel ?? 1;\n      return super.volume;\n    }\n\n    set volume(val) {\n      if (this.#castPlayer) {\n        this.#castPlayer.volumeLevel = +val;\n        this.#castPlayer.controller?.setVolumeLevel();\n        return;\n      }\n      super.volume = val;\n    }\n\n    get duration() {\n      // castPlayer duration returns `0` when no media is loaded.\n      if (this.#castPlayer && this.#castPlayer?.isMediaLoaded) {\n        return this.#castPlayer?.duration ?? NaN;\n      }\n      return super.duration;\n    }\n\n    get currentTime() {\n      if (this.#castPlayer && this.#castPlayer?.isMediaLoaded) {\n        return this.#castPlayer?.currentTime ?? 0;\n      }\n      return super.currentTime;\n    }\n\n    set currentTime(val) {\n      if (this.#castPlayer) {\n        this.#castPlayer.currentTime = val;\n        this.#castPlayer.controller?.seek();\n        return;\n      }\n      super.currentTime = val;\n    }\n  };\n\nconst CastableVideoMixin = CastableMediaMixin;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/castable-video/castable-mixin.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/castable-video/castable-remote-playback.js":
/*!*****************************************************************!*\
  !*** ./node_modules/castable-video/castable-remote-playback.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemotePlayback: () => (/* binding */ RemotePlayback)\n/* harmony export */ });\n/* harmony import */ var _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./castable-utils.js */ \"(ssr)/./node_modules/castable-video/castable-utils.js\");\n/* global chrome, cast */\n\n\nconst remoteInstances = new _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.IterableWeakSet();\nconst castElementRef = new WeakSet();\n\nlet cf;\n\n(0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.onCastApiAvailable)(() => {\n  if (!globalThis.chrome?.cast?.isAvailable) {\n    // Useful to see in verbose logs if this shows undefined or false.\n    console.debug('chrome.cast.isAvailable', globalThis.chrome?.cast?.isAvailable);\n    return;\n  }\n\n  if (!cf) {\n    cf = cast.framework;\n\n    (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)().addEventListener(cf.CastContextEventType.CAST_STATE_CHANGED, (e) => {\n      remoteInstances.forEach((r) => _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.privateProps.get(r).onCastStateChanged?.(e));\n    });\n\n    (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)().addEventListener(cf.CastContextEventType.SESSION_STATE_CHANGED, (e) => {\n      remoteInstances.forEach((r) => _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.privateProps.get(r).onSessionStateChanged?.(e));\n    });\n\n    remoteInstances.forEach((r) => _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.privateProps.get(r).init?.());\n  }\n});\n\n\nlet remotePlaybackCallbackIdCount = 0;\n\n/**\n * Remote Playback shim for the Google cast SDK.\n * https://w3c.github.io/remote-playback/\n */\nclass RemotePlayback extends EventTarget {\n  #media;\n  #isInit;\n  #remotePlayer;\n  #remoteListeners;\n  #state = 'disconnected';\n  #available = false;\n  #callbacks = new Set();\n  #callbackIds = new WeakMap();\n\n  constructor(media) {\n    super();\n\n    this.#media = media;\n\n    remoteInstances.add(this);\n    _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.privateProps.set(this, {\n      init: () => this.#init(),\n      onCastStateChanged: () => this.#onCastStateChanged(),\n      onSessionStateChanged: () => this.#onSessionStateChanged(),\n      getCastPlayer: () => this.#castPlayer,\n    });\n\n    this.#init();\n  }\n\n  get #castPlayer() {\n    if (castElementRef.has(this.#media)) return this.#remotePlayer;\n    return undefined;\n  }\n\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/API/RemotePlayback/state\n   * @return {'disconnected'|'connecting'|'connected'}\n   */\n  get state() {\n    return this.#state;\n  }\n\n  async watchAvailability(callback) {\n    if (this.#media.disableRemotePlayback) {\n      throw new _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.InvalidStateError('disableRemotePlayback attribute is present.');\n    }\n\n    this.#callbackIds.set(callback, ++remotePlaybackCallbackIdCount);\n    this.#callbacks.add(callback);\n\n    // https://w3c.github.io/remote-playback/#getting-the-remote-playback-devices-availability-information\n    queueMicrotask(() => callback(this.#hasDevicesAvailable()));\n\n    return remotePlaybackCallbackIdCount;\n  }\n\n  async cancelWatchAvailability(callback) {\n    if (this.#media.disableRemotePlayback) {\n      throw new _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.InvalidStateError('disableRemotePlayback attribute is present.');\n    }\n\n    if (callback) {\n      this.#callbacks.delete(callback);\n    } else {\n      this.#callbacks.clear();\n    }\n  }\n\n  async prompt() {\n    if (this.#media.disableRemotePlayback) {\n      throw new _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.InvalidStateError('disableRemotePlayback attribute is present.');\n    }\n\n    if (!globalThis.chrome?.cast?.isAvailable) {\n      throw new _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.NotSupportedError('The RemotePlayback API is disabled on this platform.');\n    }\n\n    const willDisconnect = castElementRef.has(this.#media);\n    castElementRef.add(this.#media);\n\n    (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.setCastOptions)(this.#media.castOptions);\n\n    Object.entries(this.#remoteListeners).forEach(([event, listener]) => {\n      this.#remotePlayer.controller.addEventListener(event, listener);\n    });\n\n    try {\n      // Open browser cast menu.\n      await (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)().requestSession();\n    } catch (err) {\n      // If there will be no disconnect, reset some state here.\n      if (!willDisconnect) {\n        castElementRef.delete(this.#media);\n      }\n\n      // Don't throw an error if disconnecting or cancelling.\n      if (err === 'cancel') {\n        return;\n      }\n\n      throw new Error(err);\n    }\n\n    _castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.privateProps.get(this.#media)?.loadOnPrompt?.();\n  }\n\n  #disconnect() {\n    if (!castElementRef.has(this.#media)) return;\n\n    Object.entries(this.#remoteListeners).forEach(([event, listener]) => {\n      this.#remotePlayer.controller.removeEventListener(event, listener);\n    });\n\n    castElementRef.delete(this.#media);\n\n    // isMuted is not in savedPlayerState. should we sync this back to local?\n    this.#media.muted = this.#remotePlayer.isMuted;\n    this.#media.currentTime = this.#remotePlayer.savedPlayerState.currentTime;\n    if (this.#remotePlayer.savedPlayerState.isPaused === false) {\n      this.#media.play();\n    }\n  }\n\n  #hasDevicesAvailable() {\n    // Cast state: NO_DEVICES_AVAILABLE, NOT_CONNECTED, CONNECTING, CONNECTED\n    // https://developers.google.com/cast/docs/reference/web_sender/cast.framework#.CastState\n    const castState = (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)()?.getCastState();\n    return castState && castState !== 'NO_DEVICES_AVAILABLE';\n  }\n\n  #onCastStateChanged() {\n    // Cast state: NO_DEVICES_AVAILABLE, NOT_CONNECTED, CONNECTING, CONNECTED\n    // https://developers.google.com/cast/docs/reference/web_sender/cast.framework#.CastState\n    const castState = (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)().getCastState();\n\n    if (castElementRef.has(this.#media)) {\n      if (castState === 'CONNECTING') {\n        this.#state = 'connecting';\n        this.dispatchEvent(new Event('connecting'));\n      }\n    }\n\n    if (!this.#available && castState?.includes('CONNECT')) {\n      this.#available = true;\n      for (let callback of this.#callbacks) callback(true);\n    }\n    else if (this.#available && (!castState || castState === 'NO_DEVICES_AVAILABLE')) {\n      this.#available = false;\n      for (let callback of this.#callbacks) callback(false);\n    }\n  }\n\n  async #onSessionStateChanged() {\n    // Session states: NO_SESSION, SESSION_STARTING, SESSION_STARTED, SESSION_START_FAILED,\n    //                 SESSION_ENDING, SESSION_ENDED, SESSION_RESUMED\n    // https://developers.google.com/cast/docs/reference/web_sender/cast.framework#.SessionState\n\n    const { SESSION_RESUMED } = cf.SessionState;\n    if ((0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.castContext)().getSessionState() === SESSION_RESUMED) {\n      /**\n       * Figure out if this was the video that started the resumed session.\n       * @TODO make this more specific than just checking against the video src!! (WL)\n       *\n       * If this video element can get the same unique id on each browser refresh\n       * it would be possible to pass this unique id w/ `LoadRequest.customData`\n       * and verify against currentMedia().customData below.\n       */\n      if (this.#media.castSrc === (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.currentMedia)()?.media.contentId) {\n        castElementRef.add(this.#media);\n\n        Object.entries(this.#remoteListeners).forEach(([event, listener]) => {\n          this.#remotePlayer.controller.addEventListener(event, listener);\n        });\n\n        /**\n         * There is cast framework resume session bug when you refresh the page a few\n         * times the this.#remotePlayer.currentTime will not be in sync with the receiver :(\n         * The below status request syncs it back up.\n         */\n        try {\n          await (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.getMediaStatus)(new chrome.cast.media.GetStatusRequest());\n        } catch (error) {\n          console.error(error);\n        }\n\n        // Dispatch the play, playing events manually to sync remote playing state.\n        this.#remoteListeners[cf.RemotePlayerEventType.IS_PAUSED_CHANGED]();\n        this.#remoteListeners[cf.RemotePlayerEventType.PLAYER_STATE_CHANGED]();\n      }\n    }\n  }\n\n  #init() {\n    if (!cf || this.#isInit) return;\n    this.#isInit = true;\n\n    (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.setCastOptions)(this.#media.castOptions);\n\n    /**\n     * @TODO add listeners for addtrack, removetrack (WL)\n     * This only has an impact on <track> with a `src` because these have to be\n     * loaded manually in the load() method. This will require a new load() call\n     * for each added/removed track w/ src.\n     */\n    this.#media.textTracks.addEventListener('change', () => this.#updateRemoteTextTrack());\n\n    this.#onCastStateChanged();\n\n    this.#remotePlayer = new cf.RemotePlayer();\n    new cf.RemotePlayerController(this.#remotePlayer);\n\n    this.#remoteListeners = {\n      [cf.RemotePlayerEventType.IS_CONNECTED_CHANGED]: ({ value }) => {\n        if (value === true) {\n          this.#state = 'connected';\n          this.dispatchEvent(new Event('connect'));\n        } else {\n          this.#disconnect();\n          this.#state = 'disconnected';\n          this.dispatchEvent(new Event('disconnect'));\n        }\n      },\n      [cf.RemotePlayerEventType.DURATION_CHANGED]: () => {\n        this.#media.dispatchEvent(new Event('durationchange'));\n      },\n      [cf.RemotePlayerEventType.VOLUME_LEVEL_CHANGED]: () => {\n        this.#media.dispatchEvent(new Event('volumechange'));\n      },\n      [cf.RemotePlayerEventType.IS_MUTED_CHANGED]: () => {\n        this.#media.dispatchEvent(new Event('volumechange'));\n      },\n      [cf.RemotePlayerEventType.CURRENT_TIME_CHANGED]: () => {\n        if (!this.#castPlayer?.isMediaLoaded) return;\n        this.#media.dispatchEvent(new Event('timeupdate'));\n      },\n      [cf.RemotePlayerEventType.VIDEO_INFO_CHANGED]: () => {\n        this.#media.dispatchEvent(new Event('resize'));\n      },\n      [cf.RemotePlayerEventType.IS_PAUSED_CHANGED]: () => {\n        this.#media.dispatchEvent(new Event(this.paused ? 'pause' : 'play'));\n      },\n      [cf.RemotePlayerEventType.PLAYER_STATE_CHANGED]: () => {\n        // Player states: IDLE, PLAYING, PAUSED, BUFFERING\n        // https://developers.google.com/cast/docs/reference/web_sender/chrome.cast.media#.PlayerState\n\n        // pause event is handled above.\n        if (this.#castPlayer?.playerState === chrome.cast.media.PlayerState.PAUSED) {\n          return;\n        }\n\n        this.#media.dispatchEvent(\n          new Event(\n            {\n              [chrome.cast.media.PlayerState.PLAYING]: 'playing',\n              [chrome.cast.media.PlayerState.BUFFERING]: 'waiting',\n              [chrome.cast.media.PlayerState.IDLE]: 'emptied',\n            }[this.#castPlayer?.playerState]\n          )\n        );\n      },\n      [cf.RemotePlayerEventType.IS_MEDIA_LOADED_CHANGED]: async () => {\n        if (!this.#castPlayer?.isMediaLoaded) return;\n\n        // mediaInfo is not immediately available due to a bug? wait one tick\n        await Promise.resolve();\n        this.#onRemoteMediaLoaded();\n      },\n    };\n  }\n\n  #onRemoteMediaLoaded() {\n    this.#updateRemoteTextTrack();\n  }\n\n  async #updateRemoteTextTrack() {\n    if (!this.#castPlayer) return;\n\n    // Get the tracks w/ trackId's that have been loaded; manually or via a playlist like a M3U8 or MPD.\n    const remoteTracks = this.#remotePlayer.mediaInfo?.tracks ?? [];\n    const remoteSubtitles = remoteTracks.filter(\n      ({ type }) => type === chrome.cast.media.TrackType.TEXT\n    );\n\n    const localSubtitles = [...this.#media.textTracks].filter(\n      ({ kind }) => kind === 'subtitles' || kind === 'captions'\n    );\n\n    // Create a new array from the local subs w/ the trackId's from the remote subs.\n    const subtitles = remoteSubtitles\n      .map(({ language, name, trackId }) => {\n        // Find the corresponding local text track and assign the trackId.\n        const { mode } =\n          localSubtitles.find(\n            (local) => local.language === language && local.label === name\n          ) ?? {};\n        if (mode) return { mode, trackId };\n        return false;\n      })\n      .filter(Boolean);\n\n    const hiddenSubtitles = subtitles.filter(\n      ({ mode }) => mode !== 'showing'\n    );\n    const hiddenTrackIds = hiddenSubtitles.map(({ trackId }) => trackId);\n    const showingSubtitle = subtitles.find(({ mode }) => mode === 'showing');\n\n    // Note this could also include audio or video tracks, diff against local state.\n    const activeTrackIds =\n      (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.currentSession)()?.getSessionObj().media[0]\n        ?.activeTrackIds ?? [];\n    let requestTrackIds = activeTrackIds;\n\n    if (activeTrackIds.length) {\n      // Filter out all local hidden subtitle trackId's.\n      requestTrackIds = requestTrackIds.filter(\n        (id) => !hiddenTrackIds.includes(id)\n      );\n    }\n\n    if (showingSubtitle?.trackId) {\n      requestTrackIds = [...requestTrackIds, showingSubtitle.trackId];\n    }\n\n    // Remove duplicate ids.\n    requestTrackIds = [...new Set(requestTrackIds)];\n\n    const arrayEquals = (a, b) =>\n      a.length === b.length && a.every((a) => b.includes(a));\n    if (!arrayEquals(activeTrackIds, requestTrackIds)) {\n      try {\n        const request = new chrome.cast.media.EditTracksInfoRequest(\n          requestTrackIds\n        );\n        await (0,_castable_utils_js__WEBPACK_IMPORTED_MODULE_0__.editTracksInfo)(request);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/castable-video/castable-remote-playback.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/castable-video/castable-utils.js":
/*!*******************************************************!*\
  !*** ./node_modules/castable-video/castable-utils.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InvalidStateError: () => (/* binding */ InvalidStateError),\n/* harmony export */   IterableWeakSet: () => (/* binding */ IterableWeakSet),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   NotSupportedError: () => (/* binding */ NotSupportedError),\n/* harmony export */   castContext: () => (/* binding */ castContext),\n/* harmony export */   currentMedia: () => (/* binding */ currentMedia),\n/* harmony export */   currentSession: () => (/* binding */ currentSession),\n/* harmony export */   editTracksInfo: () => (/* binding */ editTracksInfo),\n/* harmony export */   getDefaultCastOptions: () => (/* binding */ getDefaultCastOptions),\n/* harmony export */   getMediaStatus: () => (/* binding */ getMediaStatus),\n/* harmony export */   getPlaylistSegmentFormat: () => (/* binding */ getPlaylistSegmentFormat),\n/* harmony export */   isHls: () => (/* binding */ isHls),\n/* harmony export */   loadCastFramework: () => (/* binding */ loadCastFramework),\n/* harmony export */   onCastApiAvailable: () => (/* binding */ onCastApiAvailable),\n/* harmony export */   privateProps: () => (/* binding */ privateProps),\n/* harmony export */   requiresCastFramework: () => (/* binding */ requiresCastFramework),\n/* harmony export */   setCastOptions: () => (/* binding */ setCastOptions)\n/* harmony export */ });\n/* global WeakRef */\n\nconst privateProps = new WeakMap();\n\nclass InvalidStateError extends Error {}\nclass NotSupportedError extends Error {}\nclass NotFoundError extends Error {}\n\nconst HLS_RESPONSE_HEADERS = ['application/x-mpegURL','application/vnd.apple.mpegurl','audio/mpegurl']\n\n// Fallback to a plain Set if WeakRef is not available.\nconst IterableWeakSet = globalThis.WeakRef ?\n  class extends Set {\n    add(el) {\n      super.add(new WeakRef(el));\n    }\n    forEach(fn) {\n      super.forEach((ref) => {\n        const value = ref.deref();\n        if (value) fn(value);\n      });\n    }\n  } : Set;\n\nfunction onCastApiAvailable(callback) {\n  if (!globalThis.chrome?.cast?.isAvailable) {\n    globalThis.__onGCastApiAvailable = () => {\n      // The globalThis.__onGCastApiAvailable callback alone is not reliable for\n      // the added cast.framework. It's loaded in a separate JS file.\n      // https://www.gstatic.com/eureka/clank/101/cast_sender.js\n      // https://www.gstatic.com/cast/sdk/libs/sender/1.0/cast_framework.js\n      customElements\n        .whenDefined('google-cast-button')\n        .then(callback);\n    };\n  } else if (!globalThis.cast?.framework) {\n    customElements\n      .whenDefined('google-cast-button')\n      .then(callback);\n  } else {\n    callback();\n  }\n}\n\nfunction requiresCastFramework() {\n  // todo: exclude for Android>=56 which supports the Remote Playback API natively.\n  return globalThis.chrome;\n}\n\nfunction loadCastFramework() {\n  const sdkUrl = 'https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1';\n  if (globalThis.chrome?.cast || document.querySelector(`script[src=\"${sdkUrl}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = sdkUrl;\n  document.head.append(script);\n}\n\nfunction castContext() {\n  return globalThis.cast?.framework?.CastContext.getInstance();\n}\n\nfunction currentSession() {\n  return castContext()?.getCurrentSession();\n}\n\nfunction currentMedia() {\n  return currentSession()?.getSessionObj().media[0];\n}\n\nfunction editTracksInfo(request) {\n  return new Promise((resolve, reject) => {\n    currentMedia().editTracksInfo(request, resolve, reject);\n  });\n}\n\nfunction getMediaStatus(request) {\n  return new Promise((resolve, reject) => {\n    currentMedia().getStatus(request, resolve, reject);\n  });\n}\n\nfunction setCastOptions(options) {\n  return castContext().setOptions({\n    ...getDefaultCastOptions(),\n    ...options,\n  });\n}\n\nfunction getDefaultCastOptions() {\n  return {\n    // Set the receiver application ID to your own (created in the\n    // Google Cast Developer Console), or optionally\n    // use the chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID\n    receiverApplicationId: 'CC1AD845',\n\n    // Auto join policy can be one of the following three:\n    // ORIGIN_SCOPED - Auto connect from same appId and page origin\n    // TAB_AND_ORIGIN_SCOPED - Auto connect from same appId, page origin, and tab\n    // PAGE_SCOPED - No auto connect\n    autoJoinPolicy: 'origin_scoped',\n\n    // The following flag enables Cast Connect(requires Chrome 87 or higher)\n    // https://developers.googleblog.com/2020/08/introducing-cast-connect-android-tv.html\n    androidReceiverCompatible: false,\n\n    language: 'en-US',\n    resumeSavedSession: true,\n  };\n}\n\n//Get the segment format given the end of the URL (.m4s, .ts, etc)\nfunction getFormat(segment) {\n  if (!segment) return undefined;\n\n  const regex = /\\.([a-zA-Z0-9]+)(?:\\?.*)?$/;\n  const match = segment.match(regex);\n  return match ? match[1] : null;\n}\n\nfunction parsePlaylistUrls(playlistContent) {\n  const lines = playlistContent.split('\\n');\n  const urls = [];\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n\n    // Locate available video playlists and get the next line which is the URI (https://datatracker.ietf.org/doc/html/draft-pantos-hls-rfc8216bis-17#section-4.4.6.2)\n    if (line.startsWith('#EXT-X-STREAM-INF')) {\n      const nextLine = lines[i + 1] ? lines[i + 1].trim() : '';\n      if (nextLine && !nextLine.startsWith('#')) {\n        urls.push(nextLine);\n      }\n    }\n  }\n\n  return urls;\n}\n\nfunction parseSegment(playlistContent){\n  const lines = playlistContent.split('\\n');\n\n  const url = lines.find(line => !line.trim().startsWith('#') && line.trim() !== '');\n\n  return url;\n}\n\nasync function isHls(url) {\n  try {\n    const response = await fetch(url, {method: 'HEAD'});\n    const contentType = response.headers.get('Content-Type');\n\n    return HLS_RESPONSE_HEADERS.some((header) => contentType === header);\n  } catch (err) {\n    console.error('Error while trying to get the Content-Type of the manifest', err);\n    return false;\n  }\n}\n\nasync function getPlaylistSegmentFormat(url) {\n  try {\n    const mainManifestContent = await (await fetch(url)).text();\n    let availableChunksContent = mainManifestContent;\n\n    const playlists = parsePlaylistUrls(mainManifestContent);\n    if (playlists.length > 0) {    \n      const chosenPlaylistUrl = new URL(playlists[0], url).toString();\n      availableChunksContent = await (await fetch(chosenPlaylistUrl)).text();\n    }\n\n    const segment = parseSegment(availableChunksContent);\n    const format = getFormat(segment);\n    return format\n  } catch (err) {\n    console.error('Error while trying to parse the manifest playlist', err);\n    return undefined;\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/castable-video/castable-utils.js\n");

/***/ })

};
;