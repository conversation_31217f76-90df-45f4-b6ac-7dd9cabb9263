"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dash-video-element";
exports.ids = ["vendor-chunks/dash-video-element"];
exports.modules = {

/***/ "(ssr)/./node_modules/dash-video-element/dist/dash-video-element.js":
/*!********************************************************************!*\
  !*** ./node_modules/dash-video-element/dist/dash-video-element.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ dash_video_element_default)\n/* harmony export */ });\n/* harmony import */ var custom_media_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! custom-media-element */ \"(ssr)/./node_modules/custom-media-element/dist/custom-media-element.js\");\n\nclass DashVideoElement extends custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement {\n  static shadowRootOptions = { ...custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement.shadowRootOptions };\n  static getTemplateHTML = (attrs) => {\n    const { src, ...rest } = attrs;\n    return custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement.getTemplateHTML(rest);\n  };\n  #apiInit;\n  attributeChangedCallback(attrName, oldValue, newValue) {\n    if (attrName !== \"src\") {\n      super.attributeChangedCallback(attrName, oldValue, newValue);\n    }\n    if (attrName === \"src\" && oldValue != newValue) {\n      this.load();\n    }\n  }\n  async load() {\n    if (!this.#apiInit) {\n      this.#apiInit = true;\n      const Dash = await __webpack_require__.e(/*! import() */ \"vendor-chunks/dashjs\").then(__webpack_require__.bind(__webpack_require__, /*! dashjs */ \"(ssr)/./node_modules/dashjs/dist/modern/esm/dash.all.min.js\"));\n      this.api = Dash.MediaPlayer().create();\n      this.api.initialize(this.nativeEl, this.src, this.autoplay);\n    } else {\n      this.api.attachSource(this.src);\n    }\n  }\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"dash-video\")) {\n  globalThis.customElements.define(\"dash-video\", DashVideoElement);\n}\nvar dash_video_element_default = DashVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dash-video-element/dist/dash-video-element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/dash-video-element/dist/react.js":
/*!*******************************************************!*\
  !*** ./node_modules/dash-video-element/dist/react.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _dash_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dash-video-element.js */ \"(ssr)/./node_modules/dash-video-element/dist/dash-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent({ react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue }) {\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef((props, ref)=>{\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n            if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (false) {}\n        if ( true && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    });\n    ReactComponent.displayName = displayName ?? elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"dash-video\",\n    elementClass: _dash_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dash-video-element/dist/react.js\n");

/***/ })

};
;