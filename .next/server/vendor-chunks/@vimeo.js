"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vimeo";
exports.ids = ["vendor-chunks/@vimeo"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vimeo/player/dist/player.es.js":
/*!******************************************************!*\
  !*** ./node_modules/@vimeo/player/dist/player.es.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*! @vimeo/player v2.29.0 | (c) 2025 Vimeo | MIT License | https://github.com/vimeo/player.js */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function () {\n    return exports;\n  };\n  var exports = {},\n    Op = Object.prototype,\n    hasOwn = Op.hasOwnProperty,\n    defineProperty = Object.defineProperty || function (obj, key, desc) {\n      obj[key] = desc.value;\n    },\n    $Symbol = \"function\" == typeof Symbol ? Symbol : {},\n    iteratorSymbol = $Symbol.iterator || \"@@iterator\",\n    asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\",\n    toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function (obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator,\n      generator = Object.create(protoGenerator.prototype),\n      context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf,\n    NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function (method) {\n      define(prototype, method, function (arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg,\n          value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function (value) {\n          invoke(\"next\", value, resolve, reject);\n        }, function (err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function (unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function (error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function (method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function (resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function (method, arg) {\n      if (\"executing\" === state) throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method) throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg;;) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method) context.sent = context._sent = context.arg;else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state) throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel) continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method,\n      method = delegate.iterator[methodName];\n    if (undefined === method) return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = undefined, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type) return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next) return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1,\n          next = function next() {\n            for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next;\n            return next.value = undefined, next.done = !0, next;\n          };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: undefined,\n      done: !0\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function (genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function (genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function (arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function () {\n    return this;\n  }), define(Gp, \"toString\", function () {\n    return \"[object Generator]\";\n  }), exports.keys = function (val) {\n    var object = Object(val),\n      keys = [];\n    for (var key in object) keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length;) {\n        var key = keys.pop();\n        if (key in object) return next.value = key, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function (skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined);\n    },\n    stop: function () {\n      this.done = !0;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type) throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function (exception) {\n      if (this.done) throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = undefined), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i],\n          record = entry.completion;\n        if (\"root\" === entry.tryLoc) return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"),\n            hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n          } else {\n            if (!hasFinally) throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function (type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function (record, afterLoc) {\n      if (\"throw\" === record.type) throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function (finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function (tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function (iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      }, \"next\" === this.method && (this.arg = undefined), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n  return _wrapNativeSuper(Class);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\n/**\n * @module lib/functions\n */\n\n/**\n * Check to see this is a node environment.\n * @type {Boolean}\n */\n/* global global */\nvar isNode = typeof global !== 'undefined' && {}.toString.call(global) === '[object global]';\n\n/**\n * Get the name of the method for a given getter or setter.\n *\n * @param {string} prop The name of the property.\n * @param {string} type Either “get” or “set”.\n * @return {string}\n */\nfunction getMethodName(prop, type) {\n  if (prop.indexOf(type.toLowerCase()) === 0) {\n    return prop;\n  }\n  return \"\".concat(type.toLowerCase()).concat(prop.substr(0, 1).toUpperCase()).concat(prop.substr(1));\n}\n\n/**\n * Check to see if the object is a DOM Element.\n *\n * @param {*} element The object to check.\n * @return {boolean}\n */\nfunction isDomElement(element) {\n  return Boolean(element && element.nodeType === 1 && 'nodeName' in element && element.ownerDocument && element.ownerDocument.defaultView);\n}\n\n/**\n * Check to see whether the value is a number.\n *\n * @see http://dl.dropboxusercontent.com/u/35146/js/tests/isNumber.html\n * @param {*} value The value to check.\n * @param {boolean} integer Check if the value is an integer.\n * @return {boolean}\n */\nfunction isInteger(value) {\n  // eslint-disable-next-line eqeqeq\n  return !isNaN(parseFloat(value)) && isFinite(value) && Math.floor(value) == value;\n}\n\n/**\n * Check to see if the URL is a Vimeo url.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoUrl(url) {\n  return /^(https?:)?\\/\\/((((player|www)\\.)?vimeo\\.com)|((player\\.)?[a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))(?=$|\\/)/.test(url);\n}\n\n/**\n * Check to see if the URL is for a Vimeo embed.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoEmbed(url) {\n  var expr = /^https:\\/\\/player\\.((vimeo\\.com)|([a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))\\/video\\/\\d+/;\n  return expr.test(url);\n}\nfunction getOembedDomain(url) {\n  var match = (url || '').match(/^(?:https?:)?(?:\\/\\/)?([^/?]+)/);\n  var domain = (match && match[1] || '').replace('player.', '');\n  var customDomains = ['.videoji.hk', '.vimeo.work', '.videoji.cn'];\n  for (var _i = 0, _customDomains = customDomains; _i < _customDomains.length; _i++) {\n    var customDomain = _customDomains[_i];\n    if (domain.endsWith(customDomain)) {\n      return domain;\n    }\n  }\n  return 'vimeo.com';\n}\n\n/**\n * Get the Vimeo URL from an element.\n * The element must have either a data-vimeo-id or data-vimeo-url attribute.\n *\n * @param {object} oEmbedParameters The oEmbed parameters.\n * @return {string}\n */\nfunction getVimeoUrl() {\n  var oEmbedParameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var id = oEmbedParameters.id;\n  var url = oEmbedParameters.url;\n  var idOrUrl = id || url;\n  if (!idOrUrl) {\n    throw new Error('An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.');\n  }\n  if (isInteger(idOrUrl)) {\n    return \"https://vimeo.com/\".concat(idOrUrl);\n  }\n  if (isVimeoUrl(idOrUrl)) {\n    return idOrUrl.replace('http:', 'https:');\n  }\n  if (id) {\n    throw new TypeError(\"\\u201C\".concat(id, \"\\u201D is not a valid video id.\"));\n  }\n  throw new TypeError(\"\\u201C\".concat(idOrUrl, \"\\u201D is not a vimeo.com url.\"));\n}\n\n/* eslint-disable max-params */\n/**\n * A utility method for attaching and detaching event handlers\n *\n * @param {EventTarget} target\n * @param {string | string[]} eventName\n * @param {function} callback\n * @param {'addEventListener' | 'on'} onName\n * @param {'removeEventListener' | 'off'} offName\n * @return {{cancel: (function(): void)}}\n */\nvar subscribe = function subscribe(target, eventName, callback) {\n  var onName = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'addEventListener';\n  var offName = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'removeEventListener';\n  var eventNames = typeof eventName === 'string' ? [eventName] : eventName;\n  eventNames.forEach(function (evName) {\n    target[onName](evName, callback);\n  });\n  return {\n    cancel: function cancel() {\n      return eventNames.forEach(function (evName) {\n        return target[offName](evName, callback);\n      });\n    }\n  };\n};\n\nvar arrayIndexOfSupport = typeof Array.prototype.indexOf !== 'undefined';\nvar postMessageSupport = typeof window !== 'undefined' && typeof window.postMessage !== 'undefined';\nif (!isNode && (!arrayIndexOfSupport || !postMessageSupport)) {\n  throw new Error('Sorry, the Vimeo Player API is not available in this browser.');\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\n/*!\n * weakmap-polyfill v2.0.4 - ECMAScript6 WeakMap polyfill\n * https://github.com/polygonplanet/weakmap-polyfill\n * Copyright (c) 2015-2021 polygonplanet <<EMAIL>>\n * @license MIT\n */\n\n(function (self) {\n\n  if (self.WeakMap) {\n    return;\n  }\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  var hasDefine = Object.defineProperty && function () {\n    try {\n      // Avoid IE8's broken Object.defineProperty\n      return Object.defineProperty({}, 'x', {\n        value: 1\n      }).x === 1;\n    } catch (e) {}\n  }();\n  var defineProperty = function (object, name, value) {\n    if (hasDefine) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n  self.WeakMap = function () {\n    // ECMA-262 23.3 WeakMap Objects\n    function WeakMap() {\n      if (this === void 0) {\n        throw new TypeError(\"Constructor WeakMap requires 'new'\");\n      }\n      defineProperty(this, '_id', genId('_WeakMap'));\n\n      // ECMA-262 23.3.1.1 WeakMap([iterable])\n      if (arguments.length > 0) {\n        // Currently, WeakMap `iterable` argument is not supported\n        throw new TypeError('WeakMap iterable is not supported');\n      }\n    }\n\n    // ECMA-262 23.3.3.2 WeakMap.prototype.delete(key)\n    defineProperty(WeakMap.prototype, 'delete', function (key) {\n      checkInstance(this, 'delete');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        delete key[this._id];\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.3 WeakMap.prototype.get(key)\n    defineProperty(WeakMap.prototype, 'get', function (key) {\n      checkInstance(this, 'get');\n      if (!isObject(key)) {\n        return void 0;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return entry[1];\n      }\n      return void 0;\n    });\n\n    // ECMA-262 23.3.3.4 WeakMap.prototype.has(key)\n    defineProperty(WeakMap.prototype, 'has', function (key) {\n      checkInstance(this, 'has');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.5 WeakMap.prototype.set(key, value)\n    defineProperty(WeakMap.prototype, 'set', function (key, value) {\n      checkInstance(this, 'set');\n      if (!isObject(key)) {\n        throw new TypeError('Invalid value used as weak map key');\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        entry[1] = value;\n        return this;\n      }\n      defineProperty(key, this._id, [key, value]);\n      return this;\n    });\n    function checkInstance(x, methodName) {\n      if (!isObject(x) || !hasOwnProperty.call(x, '_id')) {\n        throw new TypeError(methodName + ' method called on incompatible receiver ' + typeof x);\n      }\n    }\n    function genId(prefix) {\n      return prefix + '_' + rand() + '.' + rand();\n    }\n    function rand() {\n      return Math.random().toString().substring(2);\n    }\n    defineProperty(WeakMap, '_polyfill', true);\n    return WeakMap;\n  }();\n  function isObject(x) {\n    return Object(x) === x;\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof commonjsGlobal !== 'undefined' ? commonjsGlobal : commonjsGlobal);\n\nvar npo_src = createCommonjsModule(function (module) {\n/*! Native Promise Only\n    v0.8.1 (c) Kyle Simpson\n    MIT License: http://getify.mit-license.org\n*/\n\n(function UMD(name, context, definition) {\n  // special form of UMD for polyfilling across evironments\n  context[name] = context[name] || definition();\n  if ( module.exports) {\n    module.exports = context[name];\n  }\n})(\"Promise\", typeof commonjsGlobal != \"undefined\" ? commonjsGlobal : commonjsGlobal, function DEF() {\n\n  var builtInProp,\n    cycle,\n    scheduling_queue,\n    ToString = Object.prototype.toString,\n    timer = typeof setImmediate != \"undefined\" ? function timer(fn) {\n      return setImmediate(fn);\n    } : setTimeout;\n\n  // dammit, IE8.\n  try {\n    Object.defineProperty({}, \"x\", {});\n    builtInProp = function builtInProp(obj, name, val, config) {\n      return Object.defineProperty(obj, name, {\n        value: val,\n        writable: true,\n        configurable: config !== false\n      });\n    };\n  } catch (err) {\n    builtInProp = function builtInProp(obj, name, val) {\n      obj[name] = val;\n      return obj;\n    };\n  }\n\n  // Note: using a queue instead of array for efficiency\n  scheduling_queue = function Queue() {\n    var first, last, item;\n    function Item(fn, self) {\n      this.fn = fn;\n      this.self = self;\n      this.next = void 0;\n    }\n    return {\n      add: function add(fn, self) {\n        item = new Item(fn, self);\n        if (last) {\n          last.next = item;\n        } else {\n          first = item;\n        }\n        last = item;\n        item = void 0;\n      },\n      drain: function drain() {\n        var f = first;\n        first = last = cycle = void 0;\n        while (f) {\n          f.fn.call(f.self);\n          f = f.next;\n        }\n      }\n    };\n  }();\n  function schedule(fn, self) {\n    scheduling_queue.add(fn, self);\n    if (!cycle) {\n      cycle = timer(scheduling_queue.drain);\n    }\n  }\n\n  // promise duck typing\n  function isThenable(o) {\n    var _then,\n      o_type = typeof o;\n    if (o != null && (o_type == \"object\" || o_type == \"function\")) {\n      _then = o.then;\n    }\n    return typeof _then == \"function\" ? _then : false;\n  }\n  function notify() {\n    for (var i = 0; i < this.chain.length; i++) {\n      notifyIsolated(this, this.state === 1 ? this.chain[i].success : this.chain[i].failure, this.chain[i]);\n    }\n    this.chain.length = 0;\n  }\n\n  // NOTE: This is a separate function to isolate\n  // the `try..catch` so that other code can be\n  // optimized better\n  function notifyIsolated(self, cb, chain) {\n    var ret, _then;\n    try {\n      if (cb === false) {\n        chain.reject(self.msg);\n      } else {\n        if (cb === true) {\n          ret = self.msg;\n        } else {\n          ret = cb.call(void 0, self.msg);\n        }\n        if (ret === chain.promise) {\n          chain.reject(TypeError(\"Promise-chain cycle\"));\n        } else if (_then = isThenable(ret)) {\n          _then.call(ret, chain.resolve, chain.reject);\n        } else {\n          chain.resolve(ret);\n        }\n      }\n    } catch (err) {\n      chain.reject(err);\n    }\n  }\n  function resolve(msg) {\n    var _then,\n      self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    try {\n      if (_then = isThenable(msg)) {\n        schedule(function () {\n          var def_wrapper = new MakeDefWrapper(self);\n          try {\n            _then.call(msg, function $resolve$() {\n              resolve.apply(def_wrapper, arguments);\n            }, function $reject$() {\n              reject.apply(def_wrapper, arguments);\n            });\n          } catch (err) {\n            reject.call(def_wrapper, err);\n          }\n        });\n      } else {\n        self.msg = msg;\n        self.state = 1;\n        if (self.chain.length > 0) {\n          schedule(notify, self);\n        }\n      }\n    } catch (err) {\n      reject.call(new MakeDefWrapper(self), err);\n    }\n  }\n  function reject(msg) {\n    var self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    self.msg = msg;\n    self.state = 2;\n    if (self.chain.length > 0) {\n      schedule(notify, self);\n    }\n  }\n  function iteratePromises(Constructor, arr, resolver, rejecter) {\n    for (var idx = 0; idx < arr.length; idx++) {\n      (function IIFE(idx) {\n        Constructor.resolve(arr[idx]).then(function $resolver$(msg) {\n          resolver(idx, msg);\n        }, rejecter);\n      })(idx);\n    }\n  }\n  function MakeDefWrapper(self) {\n    this.def = self;\n    this.triggered = false;\n  }\n  function MakeDef(self) {\n    this.promise = self;\n    this.state = 0;\n    this.triggered = false;\n    this.chain = [];\n    this.msg = void 0;\n  }\n  function Promise(executor) {\n    if (typeof executor != \"function\") {\n      throw TypeError(\"Not a function\");\n    }\n    if (this.__NPO__ !== 0) {\n      throw TypeError(\"Not a promise\");\n    }\n\n    // instance shadowing the inherited \"brand\"\n    // to signal an already \"initialized\" promise\n    this.__NPO__ = 1;\n    var def = new MakeDef(this);\n    this[\"then\"] = function then(success, failure) {\n      var o = {\n        success: typeof success == \"function\" ? success : true,\n        failure: typeof failure == \"function\" ? failure : false\n      };\n      // Note: `then(..)` itself can be borrowed to be used against\n      // a different promise constructor for making the chained promise,\n      // by substituting a different `this` binding.\n      o.promise = new this.constructor(function extractChain(resolve, reject) {\n        if (typeof resolve != \"function\" || typeof reject != \"function\") {\n          throw TypeError(\"Not a function\");\n        }\n        o.resolve = resolve;\n        o.reject = reject;\n      });\n      def.chain.push(o);\n      if (def.state !== 0) {\n        schedule(notify, def);\n      }\n      return o.promise;\n    };\n    this[\"catch\"] = function $catch$(failure) {\n      return this.then(void 0, failure);\n    };\n    try {\n      executor.call(void 0, function publicResolve(msg) {\n        resolve.call(def, msg);\n      }, function publicReject(msg) {\n        reject.call(def, msg);\n      });\n    } catch (err) {\n      reject.call(def, err);\n    }\n  }\n  var PromisePrototype = builtInProp({}, \"constructor\", Promise, /*configurable=*/false);\n\n  // Note: Android 4 cannot use `Object.defineProperty(..)` here\n  Promise.prototype = PromisePrototype;\n\n  // built-in \"brand\" to signal an \"uninitialized\" promise\n  builtInProp(PromisePrototype, \"__NPO__\", 0, /*configurable=*/false);\n  builtInProp(Promise, \"resolve\", function Promise$resolve(msg) {\n    var Constructor = this;\n\n    // spec mandated checks\n    // note: best \"isPromise\" check that's practical for now\n    if (msg && typeof msg == \"object\" && msg.__NPO__ === 1) {\n      return msg;\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      resolve(msg);\n    });\n  });\n  builtInProp(Promise, \"reject\", function Promise$reject(msg) {\n    return new this(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      reject(msg);\n    });\n  });\n  builtInProp(Promise, \"all\", function Promise$all(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    if (arr.length === 0) {\n      return Constructor.resolve([]);\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      var len = arr.length,\n        msgs = Array(len),\n        count = 0;\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        msgs[idx] = msg;\n        if (++count === len) {\n          resolve(msgs);\n        }\n      }, reject);\n    });\n  });\n  builtInProp(Promise, \"race\", function Promise$race(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        resolve(msg);\n      }, reject);\n    });\n  });\n  return Promise;\n});\n});\n\n/**\n * @module lib/callbacks\n */\n\nvar callbackMap = new WeakMap();\n\n/**\n * Store a callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @param {(function(this:Player, *): void|{resolve: function, reject: function})} callback\n *        The callback to call or an object with resolve and reject functions for a promise.\n * @return {void}\n */\nfunction storeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!(name in playerCallbacks)) {\n    playerCallbacks[name] = [];\n  }\n  playerCallbacks[name].push(callback);\n  callbackMap.set(player.element, playerCallbacks);\n}\n\n/**\n * Get the callbacks for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @return {function[]}\n */\nfunction getCallbacks(player, name) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  return playerCallbacks[name] || [];\n}\n\n/**\n * Remove a stored callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @param {function} [callback] The specific callback to remove.\n * @return {boolean} Was this the last callback?\n */\nfunction removeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!playerCallbacks[name]) {\n    return true;\n  }\n\n  // If no callback is passed, remove all callbacks for the event\n  if (!callback) {\n    playerCallbacks[name] = [];\n    callbackMap.set(player.element, playerCallbacks);\n    return true;\n  }\n  var index = playerCallbacks[name].indexOf(callback);\n  if (index !== -1) {\n    playerCallbacks[name].splice(index, 1);\n  }\n  callbackMap.set(player.element, playerCallbacks);\n  return playerCallbacks[name] && playerCallbacks[name].length === 0;\n}\n\n/**\n * Return the first stored callback for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @return {function} The callback, or false if there were none\n */\nfunction shiftCallbacks(player, name) {\n  var playerCallbacks = getCallbacks(player, name);\n  if (playerCallbacks.length < 1) {\n    return false;\n  }\n  var callback = playerCallbacks.shift();\n  removeCallback(player, name, callback);\n  return callback;\n}\n\n/**\n * Move callbacks associated with an element to another element.\n *\n * @param {HTMLElement} oldElement The old element.\n * @param {HTMLElement} newElement The new element.\n * @return {void}\n */\nfunction swapCallbacks(oldElement, newElement) {\n  var playerCallbacks = callbackMap.get(oldElement);\n  callbackMap.set(newElement, playerCallbacks);\n  callbackMap.delete(oldElement);\n}\n\n/**\n * @module lib/postmessage\n */\n\n/**\n * Parse a message received from postMessage.\n *\n * @param {*} data The data received from postMessage.\n * @return {object}\n */\nfunction parseMessageData(data) {\n  if (typeof data === 'string') {\n    try {\n      data = JSON.parse(data);\n    } catch (error) {\n      // If the message cannot be parsed, throw the error as a warning\n      console.warn(error);\n      return {};\n    }\n  }\n  return data;\n}\n\n/**\n * Post a message to the specified target.\n *\n * @param {Player} player The player object to use.\n * @param {string} method The API method to call.\n * @param {string|number|object|Array|undefined} params The parameters to send to the player.\n * @return {void}\n */\nfunction postMessage(player, method, params) {\n  if (!player.element.contentWindow || !player.element.contentWindow.postMessage) {\n    return;\n  }\n  var message = {\n    method: method\n  };\n  if (params !== undefined) {\n    message.value = params;\n  }\n\n  // IE 8 and 9 do not support passing messages, so stringify them\n  var ieVersion = parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\\d+).*$/, '$1'));\n  if (ieVersion >= 8 && ieVersion < 10) {\n    message = JSON.stringify(message);\n  }\n  player.element.contentWindow.postMessage(message, player.origin);\n}\n\n/**\n * Parse the data received from a message event.\n *\n * @param {Player} player The player that received the message.\n * @param {(Object|string)} data The message data. Strings will be parsed into JSON.\n * @return {void}\n */\nfunction processData(player, data) {\n  data = parseMessageData(data);\n  var callbacks = [];\n  var param;\n  if (data.event) {\n    if (data.event === 'error') {\n      var promises = getCallbacks(player, data.data.method);\n      promises.forEach(function (promise) {\n        var error = new Error(data.data.message);\n        error.name = data.data.name;\n        promise.reject(error);\n        removeCallback(player, data.data.method, promise);\n      });\n    }\n    callbacks = getCallbacks(player, \"event:\".concat(data.event));\n    param = data.data;\n  } else if (data.method) {\n    var callback = shiftCallbacks(player, data.method);\n    if (callback) {\n      callbacks.push(callback);\n      param = data.value;\n    }\n  }\n  callbacks.forEach(function (callback) {\n    try {\n      if (typeof callback === 'function') {\n        callback.call(player, param);\n        return;\n      }\n      callback.resolve(param);\n    } catch (e) {\n      // empty\n    }\n  });\n}\n\n/**\n * @module lib/embed\n */\nvar oEmbedParameters = ['airplay', 'audio_tracks', 'audiotrack', 'autopause', 'autoplay', 'background', 'byline', 'cc', 'chapter_id', 'chapters', 'chromecast', 'color', 'colors', 'controls', 'dnt', 'end_time', 'fullscreen', 'height', 'id', 'initial_quality', 'interactive_params', 'keyboard', 'loop', 'maxheight', 'max_quality', 'maxwidth', 'min_quality', 'muted', 'play_button_position', 'playsinline', 'portrait', 'preload', 'progress_bar', 'quality', 'quality_selector', 'responsive', 'skipping_forward', 'speed', 'start_time', 'texttrack', 'thumbnail_id', 'title', 'transcript', 'transparent', 'unmute_button', 'url', 'vimeo_logo', 'volume', 'watch_full_video', 'width'];\n\n/**\n * Get the 'data-vimeo'-prefixed attributes from an element as an object.\n *\n * @param {HTMLElement} element The element.\n * @param {Object} [defaults={}] The default values to use.\n * @return {Object<string, string>}\n */\nfunction getOEmbedParameters(element) {\n  var defaults = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return oEmbedParameters.reduce(function (params, param) {\n    var value = element.getAttribute(\"data-vimeo-\".concat(param));\n    if (value || value === '') {\n      params[param] = value === '' ? 1 : value;\n    }\n    return params;\n  }, defaults);\n}\n\n/**\n * Create an embed from oEmbed data inside an element.\n *\n * @param {object} data The oEmbed data.\n * @param {HTMLElement} element The element to put the iframe in.\n * @return {HTMLIFrameElement} The iframe embed.\n */\nfunction createEmbed(_ref, element) {\n  var html = _ref.html;\n  if (!element) {\n    throw new TypeError('An element must be provided');\n  }\n  if (element.getAttribute('data-vimeo-initialized') !== null) {\n    return element.querySelector('iframe');\n  }\n  var div = document.createElement('div');\n  div.innerHTML = html;\n  element.appendChild(div.firstChild);\n  element.setAttribute('data-vimeo-initialized', 'true');\n  return element.querySelector('iframe');\n}\n\n/**\n * Make an oEmbed call for the specified URL.\n *\n * @param {string} videoUrl The vimeo.com url for the video.\n * @param {Object} [params] Parameters to pass to oEmbed.\n * @param {HTMLElement} element The element.\n * @return {Promise}\n */\nfunction getOEmbedData(videoUrl) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var element = arguments.length > 2 ? arguments[2] : undefined;\n  return new Promise(function (resolve, reject) {\n    if (!isVimeoUrl(videoUrl)) {\n      throw new TypeError(\"\\u201C\".concat(videoUrl, \"\\u201D is not a vimeo.com url.\"));\n    }\n    var domain = getOembedDomain(videoUrl);\n    var url = \"https://\".concat(domain, \"/api/oembed.json?url=\").concat(encodeURIComponent(videoUrl));\n    for (var param in params) {\n      if (params.hasOwnProperty(param)) {\n        url += \"&\".concat(param, \"=\").concat(encodeURIComponent(params[param]));\n      }\n    }\n    var xhr = 'XDomainRequest' in window ? new XDomainRequest() : new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.onload = function () {\n      if (xhr.status === 404) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D was not found.\")));\n        return;\n      }\n      if (xhr.status === 403) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n        return;\n      }\n      try {\n        var json = JSON.parse(xhr.responseText);\n        // Check api response for 403 on oembed\n        if (json.domain_status_code === 403) {\n          // We still want to create the embed to give users visual feedback\n          createEmbed(json, element);\n          reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n          return;\n        }\n        resolve(json);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    xhr.onerror = function () {\n      var status = xhr.status ? \" (\".concat(xhr.status, \")\") : '';\n      reject(new Error(\"There was an error fetching the embed code from Vimeo\".concat(status, \".\")));\n    };\n    xhr.send();\n  });\n}\n\n/**\n * Initialize all embeds within a specific element\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initializeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  var elements = [].slice.call(parent.querySelectorAll('[data-vimeo-id], [data-vimeo-url]'));\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error creating an embed: \".concat(error));\n    }\n  };\n  elements.forEach(function (element) {\n    try {\n      // Skip any that have data-vimeo-defer\n      if (element.getAttribute('data-vimeo-defer') !== null) {\n        return;\n      }\n      var params = getOEmbedParameters(element);\n      var url = getVimeoUrl(params);\n      getOEmbedData(url, params, element).then(function (data) {\n        return createEmbed(data, element);\n      }).catch(handleError);\n    } catch (error) {\n      handleError(error);\n    }\n  });\n}\n\n/**\n * Resize embeds when messaged by the player.\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction resizeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  // Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoPlayerResizeEmbeds_) {\n    return;\n  }\n  window.VimeoPlayerResizeEmbeds_ = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n\n    // 'spacechange' is fired only on embeds with cards\n    if (!event.data || event.data.event !== 'spacechange') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      if (iframes[i].contentWindow !== event.source) {\n        continue;\n      }\n\n      // Change padding-bottom of the enclosing div to accommodate\n      // card carousel without distorting aspect ratio\n      var space = iframes[i].parentElement;\n      space.style.paddingBottom = \"\".concat(event.data.data[0].bottom, \"px\");\n      break;\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Add chapters to existing metadata for Google SEO\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initAppendVideoMetadata() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoSeoMetadataAppended) {\n    return;\n  }\n  window.VimeoSeoMetadataAppended = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      var iframe = iframes[i];\n\n      // Initiate appendVideoMetadata if iframe is a Vimeo embed\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.callMethod('appendVideoMetadata', window.location.href);\n      }\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Seek to time indicated by vimeo_t query parameter if present in URL\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction checkUrlTimeParam() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoCheckedUrlTimeParam) {\n    return;\n  }\n  window.VimeoCheckedUrlTimeParam = true;\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error getting video Id: \".concat(error));\n    }\n  };\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    var _loop = function _loop() {\n      var iframe = iframes[i];\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.getVideoId().then(function (videoId) {\n          var matches = new RegExp(\"[?&]vimeo_t_\".concat(videoId, \"=([^&#]*)\")).exec(window.location.href);\n          if (matches && matches[1]) {\n            var sec = decodeURI(matches[1]);\n            player.setCurrentTime(sec);\n          }\n          return;\n        }).catch(handleError);\n      }\n    };\n    for (var i = 0; i < iframes.length; i++) {\n      _loop();\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/* MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nTerms */\n\nfunction initializeScreenfull() {\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n    // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n    // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n    for (; i < l; i++) {\n      val = fnMap[i];\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n        return ret;\n      }\n    }\n    return false;\n  }();\n  var eventNameMap = {\n    fullscreenchange: fn.fullscreenchange,\n    fullscreenerror: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function request(element) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function onFullScreenEntered() {\n          screenfull.off('fullscreenchange', onFullScreenEntered);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      });\n    },\n    exit: function exit() {\n      return new Promise(function (resolve, reject) {\n        if (!screenfull.isFullscreen) {\n          resolve();\n          return;\n        }\n        var onFullScreenExit = function onFullScreenExit() {\n          screenfull.off('fullscreenchange', onFullScreenExit);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      });\n    },\n    on: function on(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.addEventListener(eventName, callback);\n      }\n    },\n    off: function off(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.removeEventListener(eventName, callback);\n      }\n    }\n  };\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function get() {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function get() {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function get() {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n  return screenfull;\n}\n\n/** @typedef {import('./timing-src-connector.types').PlayerControls} PlayerControls */\n/** @typedef {import('./timing-object.types').TimingObject} TimingObject */\n/** @typedef {import('./timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n/** @typedef {(msg: string) => any} Logger */\n/** @typedef {import('timing-object.types').TConnectionState} TConnectionState */\n\n/**\n * @type {TimingSrcConnectorOptions}\n *\n * For details on these properties and their effects, see the typescript definition referenced above.\n */\nvar defaultOptions = {\n  role: 'viewer',\n  autoPlayMuted: true,\n  allowedDrift: 0.3,\n  maxAllowedDrift: 1,\n  minCheckInterval: 0.1,\n  maxRateAdjustment: 0.2,\n  maxTimeToCatchUp: 1\n};\n\n/**\n * There's a proposed W3C spec for the Timing Object which would introduce a new set of APIs that would simplify time-synchronization tasks for browser applications.\n *\n * Proposed spec: https://webtiming.github.io/timingobject/\n * V3 Spec: https://timingsrc.readthedocs.io/en/latest/\n * Demuxed talk: https://www.youtube.com/watch?v=cZSjDaGDmX8\n *\n * This class makes it easy to connect Vimeo.Player to a provided TimingObject via Vimeo.Player.setTimingSrc(myTimingObject, options) and the synchronization will be handled automatically.\n *\n * There are 5 general responsibilities in TimingSrcConnector:\n *\n * 1. `updatePlayer()` which sets the player's currentTime, playbackRate and pause/play state based on current state of the TimingObject.\n * 2. `updateTimingObject()` which sets the TimingObject's position and velocity from the player's state.\n * 3. `playerUpdater` which listens for change events on the TimingObject and will respond by calling updatePlayer.\n * 4. `timingObjectUpdater` which listens to the player events of seeked, play and pause and will respond by calling `updateTimingObject()`.\n * 5. `maintainPlaybackPosition` this is code that constantly monitors the player to make sure it's always in sync with the TimingObject. This is needed because videos will generally not play with precise time accuracy and there will be some drift which becomes more noticeable over longer periods (as noted in the timing-object spec). More details on this method below.\n */\nvar TimingSrcConnector = /*#__PURE__*/function (_EventTarget) {\n  _inherits(TimingSrcConnector, _EventTarget);\n  var _super = _createSuper(TimingSrcConnector);\n  /**\n   * @param {PlayerControls} player\n   * @param {TimingObject} timingObject\n   * @param {TimingSrcConnectorOptions} options\n   * @param {Logger} logger\n   */\n  function TimingSrcConnector(_player, timingObject) {\n    var _this;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var logger = arguments.length > 3 ? arguments[3] : undefined;\n    _classCallCheck(this, TimingSrcConnector);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"logger\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"speedAdjustment\", 0);\n    /**\n     * @param {PlayerControls} player\n     * @param {number} newAdjustment\n     * @return {Promise<void>}\n     */\n    _defineProperty(_assertThisInitialized(_this), \"adjustSpeed\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(player, newAdjustment) {\n        var newPlaybackRate;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!(_this.speedAdjustment === newAdjustment)) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 2:\n              _context.next = 4;\n              return player.getPlaybackRate();\n            case 4:\n              _context.t0 = _context.sent;\n              _context.t1 = _this.speedAdjustment;\n              _context.t2 = _context.t0 - _context.t1;\n              _context.t3 = newAdjustment;\n              newPlaybackRate = _context.t2 + _context.t3;\n              _this.log(\"New playbackRate:  \".concat(newPlaybackRate));\n              _context.next = 12;\n              return player.setPlaybackRate(newPlaybackRate);\n            case 12:\n              _this.speedAdjustment = newAdjustment;\n            case 13:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _this.logger = logger;\n    _this.init(timingObject, _player, _objectSpread2(_objectSpread2({}, defaultOptions), options));\n    return _this;\n  }\n  _createClass(TimingSrcConnector, [{\n    key: \"disconnect\",\n    value: function disconnect() {\n      this.dispatchEvent(new Event('disconnect'));\n    }\n\n    /**\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"init\",\n    value: function () {\n      var _init = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(timingObject, player, options) {\n        var _this2 = this;\n        var playerUpdater, positionSync, timingObjectUpdater;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return this.waitForTOReadyState(timingObject, 'open');\n            case 2:\n              if (!(options.role === 'viewer')) {\n                _context2.next = 10;\n                break;\n              }\n              _context2.next = 5;\n              return this.updatePlayer(timingObject, player, options);\n            case 5:\n              playerUpdater = subscribe(timingObject, 'change', function () {\n                return _this2.updatePlayer(timingObject, player, options);\n              });\n              positionSync = this.maintainPlaybackPosition(timingObject, player, options);\n              this.addEventListener('disconnect', function () {\n                positionSync.cancel();\n                playerUpdater.cancel();\n              });\n              _context2.next = 14;\n              break;\n            case 10:\n              _context2.next = 12;\n              return this.updateTimingObject(timingObject, player);\n            case 12:\n              timingObjectUpdater = subscribe(player, ['seeked', 'play', 'pause', 'ratechange'], function () {\n                return _this2.updateTimingObject(timingObject, player);\n              }, 'on', 'off');\n              this.addEventListener('disconnect', function () {\n                return timingObjectUpdater.cancel();\n              });\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function init(_x3, _x4, _x5) {\n        return _init.apply(this, arguments);\n      }\n      return init;\n    }()\n    /**\n     * Sets the TimingObject's state to reflect that of the player\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updateTimingObject\",\n    value: function () {\n      var _updateTimingObject = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(timingObject, player) {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.t0 = timingObject;\n              _context3.next = 3;\n              return player.getCurrentTime();\n            case 3:\n              _context3.t1 = _context3.sent;\n              _context3.next = 6;\n              return player.getPaused();\n            case 6:\n              if (!_context3.sent) {\n                _context3.next = 10;\n                break;\n              }\n              _context3.t2 = 0;\n              _context3.next = 13;\n              break;\n            case 10:\n              _context3.next = 12;\n              return player.getPlaybackRate();\n            case 12:\n              _context3.t2 = _context3.sent;\n            case 13:\n              _context3.t3 = _context3.t2;\n              _context3.t4 = {\n                position: _context3.t1,\n                velocity: _context3.t3\n              };\n              _context3.t0.update.call(_context3.t0, _context3.t4);\n            case 16:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      function updateTimingObject(_x6, _x7) {\n        return _updateTimingObject.apply(this, arguments);\n      }\n      return updateTimingObject;\n    }()\n    /**\n     * Sets the player's timing state to reflect that of the TimingObject\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updatePlayer\",\n    value: function () {\n      var _updatePlayer = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(timingObject, player, options) {\n        var _timingObject$query, position, velocity;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _timingObject$query = timingObject.query(), position = _timingObject$query.position, velocity = _timingObject$query.velocity;\n              if (typeof position === 'number') {\n                player.setCurrentTime(position);\n              }\n              if (!(typeof velocity === 'number')) {\n                _context5.next = 25;\n                break;\n              }\n              if (!(velocity === 0)) {\n                _context5.next = 11;\n                break;\n              }\n              _context5.next = 6;\n              return player.getPaused();\n            case 6:\n              _context5.t0 = _context5.sent;\n              if (!(_context5.t0 === false)) {\n                _context5.next = 9;\n                break;\n              }\n              player.pause();\n            case 9:\n              _context5.next = 25;\n              break;\n            case 11:\n              if (!(velocity > 0)) {\n                _context5.next = 25;\n                break;\n              }\n              _context5.next = 14;\n              return player.getPaused();\n            case 14:\n              _context5.t1 = _context5.sent;\n              if (!(_context5.t1 === true)) {\n                _context5.next = 19;\n                break;\n              }\n              _context5.next = 18;\n              return player.play().catch( /*#__PURE__*/function () {\n                var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(err) {\n                  return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n                    while (1) switch (_context4.prev = _context4.next) {\n                      case 0:\n                        if (!(err.name === 'NotAllowedError' && options.autoPlayMuted)) {\n                          _context4.next = 5;\n                          break;\n                        }\n                        _context4.next = 3;\n                        return player.setMuted(true);\n                      case 3:\n                        _context4.next = 5;\n                        return player.play().catch(function (err2) {\n                          return console.error('Couldn\\'t play the video from TimingSrcConnector. Error:', err2);\n                        });\n                      case 5:\n                      case \"end\":\n                        return _context4.stop();\n                    }\n                  }, _callee4);\n                }));\n                return function (_x11) {\n                  return _ref2.apply(this, arguments);\n                };\n              }());\n            case 18:\n              this.updatePlayer(timingObject, player, options);\n            case 19:\n              _context5.next = 21;\n              return player.getPlaybackRate();\n            case 21:\n              _context5.t2 = _context5.sent;\n              _context5.t3 = velocity;\n              if (!(_context5.t2 !== _context5.t3)) {\n                _context5.next = 25;\n                break;\n              }\n              player.setPlaybackRate(velocity);\n            case 25:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, this);\n      }));\n      function updatePlayer(_x8, _x9, _x10) {\n        return _updatePlayer.apply(this, arguments);\n      }\n      return updatePlayer;\n    }()\n    /**\n     * Since video players do not play with 100% time precision, we need to closely monitor\n     * our player to be sure it remains in sync with the TimingObject.\n     *\n     * If out of sync, we use the current conditions and the options provided to determine\n     * whether to re-sync via setting currentTime or adjusting the playbackRate\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {{cancel: (function(): void)}}\n     */\n  }, {\n    key: \"maintainPlaybackPosition\",\n    value: function maintainPlaybackPosition(timingObject, player, options) {\n      var _this3 = this;\n      var allowedDrift = options.allowedDrift,\n        maxAllowedDrift = options.maxAllowedDrift,\n        minCheckInterval = options.minCheckInterval,\n        maxRateAdjustment = options.maxRateAdjustment,\n        maxTimeToCatchUp = options.maxTimeToCatchUp;\n      var syncInterval = Math.min(maxTimeToCatchUp, Math.max(minCheckInterval, maxAllowedDrift)) * 1000;\n      var check = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n          var diff, diffAbs, min, max, adjustment;\n          return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n            while (1) switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.t0 = timingObject.query().velocity === 0;\n                if (_context6.t0) {\n                  _context6.next = 6;\n                  break;\n                }\n                _context6.next = 4;\n                return player.getPaused();\n              case 4:\n                _context6.t1 = _context6.sent;\n                _context6.t0 = _context6.t1 === true;\n              case 6:\n                if (!_context6.t0) {\n                  _context6.next = 8;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 8:\n                _context6.t2 = timingObject.query().position;\n                _context6.next = 11;\n                return player.getCurrentTime();\n              case 11:\n                _context6.t3 = _context6.sent;\n                diff = _context6.t2 - _context6.t3;\n                diffAbs = Math.abs(diff);\n                _this3.log(\"Drift: \".concat(diff));\n                if (!(diffAbs > maxAllowedDrift)) {\n                  _context6.next = 22;\n                  break;\n                }\n                _context6.next = 18;\n                return _this3.adjustSpeed(player, 0);\n              case 18:\n                player.setCurrentTime(timingObject.query().position);\n                _this3.log('Resync by currentTime');\n                _context6.next = 29;\n                break;\n              case 22:\n                if (!(diffAbs > allowedDrift)) {\n                  _context6.next = 29;\n                  break;\n                }\n                min = diffAbs / maxTimeToCatchUp;\n                max = maxRateAdjustment;\n                adjustment = min < max ? (max - min) / 2 : max;\n                _context6.next = 28;\n                return _this3.adjustSpeed(player, adjustment * Math.sign(diff));\n              case 28:\n                _this3.log('Resync by playbackRate');\n              case 29:\n              case \"end\":\n                return _context6.stop();\n            }\n          }, _callee6);\n        }));\n        return function check() {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      var interval = setInterval(function () {\n        return check();\n      }, syncInterval);\n      return {\n        cancel: function cancel() {\n          return clearInterval(interval);\n        }\n      };\n    }\n\n    /**\n     * @param {string} msg\n     */\n  }, {\n    key: \"log\",\n    value: function log(msg) {\n      var _this$logger;\n      (_this$logger = this.logger) === null || _this$logger === void 0 ? void 0 : _this$logger.call(this, \"TimingSrcConnector: \".concat(msg));\n    }\n  }, {\n    key: \"waitForTOReadyState\",\n    value:\n    /**\n     * @param {TimingObject} timingObject\n     * @param {TConnectionState} state\n     * @return {Promise<void>}\n     */\n    function waitForTOReadyState(timingObject, state) {\n      return new Promise(function (resolve) {\n        var check = function check() {\n          if (timingObject.readyState === state) {\n            resolve();\n          } else {\n            timingObject.addEventListener('readystatechange', check, {\n              once: true\n            });\n          }\n        };\n        check();\n      });\n    }\n  }]);\n  return TimingSrcConnector;\n}( /*#__PURE__*/_wrapNativeSuper(EventTarget));\n\nvar playerMap = new WeakMap();\nvar readyMap = new WeakMap();\nvar screenfull = {};\nvar Player = /*#__PURE__*/function () {\n  /**\n   * Create a Player.\n   *\n   * @param {(HTMLIFrameElement|HTMLElement|string|jQuery)} element A reference to the Vimeo\n   *        player iframe, and id, or a jQuery object.\n   * @param {object} [options] oEmbed parameters to use when creating an embed in the element.\n   * @return {Player}\n   */\n  function Player(element) {\n    var _this = this;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Player);\n    /* global jQuery */\n    if (window.jQuery && element instanceof jQuery) {\n      if (element.length > 1 && window.console && console.warn) {\n        console.warn('A jQuery object with multiple elements was passed, using the first element.');\n      }\n      element = element[0];\n    }\n\n    // Find an element by ID\n    if (typeof document !== 'undefined' && typeof element === 'string') {\n      element = document.getElementById(element);\n    }\n\n    // Not an element!\n    if (!isDomElement(element)) {\n      throw new TypeError('You must pass either a valid element or a valid id.');\n    }\n\n    // Already initialized an embed in this div, so grab the iframe\n    if (element.nodeName !== 'IFRAME') {\n      var iframe = element.querySelector('iframe');\n      if (iframe) {\n        element = iframe;\n      }\n    }\n\n    // iframe url is not a Vimeo url\n    if (element.nodeName === 'IFRAME' && !isVimeoUrl(element.getAttribute('src') || '')) {\n      throw new Error('The player element passed isn’t a Vimeo embed.');\n    }\n\n    // If there is already a player object in the map, return that\n    if (playerMap.has(element)) {\n      return playerMap.get(element);\n    }\n    this._window = element.ownerDocument.defaultView;\n    this.element = element;\n    this.origin = '*';\n    var readyPromise = new npo_src(function (resolve, reject) {\n      _this._onMessage = function (event) {\n        if (!isVimeoUrl(event.origin) || _this.element.contentWindow !== event.source) {\n          return;\n        }\n        if (_this.origin === '*') {\n          _this.origin = event.origin;\n        }\n        var data = parseMessageData(event.data);\n        var isError = data && data.event === 'error';\n        var isReadyError = isError && data.data && data.data.method === 'ready';\n        if (isReadyError) {\n          var error = new Error(data.data.message);\n          error.name = data.data.name;\n          reject(error);\n          return;\n        }\n        var isReadyEvent = data && data.event === 'ready';\n        var isPingResponse = data && data.method === 'ping';\n        if (isReadyEvent || isPingResponse) {\n          _this.element.setAttribute('data-ready', 'true');\n          resolve();\n          return;\n        }\n        processData(_this, data);\n      };\n      _this._window.addEventListener('message', _this._onMessage);\n      if (_this.element.nodeName !== 'IFRAME') {\n        var params = getOEmbedParameters(element, options);\n        var url = getVimeoUrl(params);\n        getOEmbedData(url, params, element).then(function (data) {\n          var iframe = createEmbed(data, element);\n          // Overwrite element with the new iframe,\n          // but store reference to the original element\n          _this.element = iframe;\n          _this._originalElement = element;\n          swapCallbacks(element, iframe);\n          playerMap.set(_this.element, _this);\n          return data;\n        }).catch(reject);\n      }\n    });\n\n    // Store a copy of this Player in the map\n    readyMap.set(this, readyPromise);\n    playerMap.set(this.element, this);\n\n    // Send a ping to the iframe so the ready promise will be resolved if\n    // the player is already ready.\n    if (this.element.nodeName === 'IFRAME') {\n      postMessage(this, 'ping');\n    }\n    if (screenfull.isEnabled) {\n      var exitFullscreen = function exitFullscreen() {\n        return screenfull.exit();\n      };\n      this.fullscreenchangeHandler = function () {\n        if (screenfull.isFullscreen) {\n          storeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        } else {\n          removeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        }\n        // eslint-disable-next-line\n        _this.ready().then(function () {\n          postMessage(_this, 'fullscreenchange', screenfull.isFullscreen);\n        });\n      };\n      screenfull.on('fullscreenchange', this.fullscreenchangeHandler);\n    }\n    return this;\n  }\n\n  /**\n   * Get a promise for a method.\n   *\n   * @param {string} name The API method to call.\n   * @param {...(string|number|object|Array)} args Arguments to send via postMessage.\n   * @return {Promise}\n   */\n  _createClass(Player, [{\n    key: \"callMethod\",\n    value: function callMethod(name) {\n      var _this2 = this;\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (name === undefined || name === null) {\n        throw new TypeError('You must pass a method name.');\n      }\n      return new npo_src(function (resolve, reject) {\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this2.ready().then(function () {\n          storeCallback(_this2, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this2, name, args);\n        }).catch(reject);\n      });\n    }\n    /**\n     * Get a promise for the value of a player property.\n     *\n     * @param {string} name The property name\n     * @return {Promise}\n     */\n  }, {\n    key: \"get\",\n    value: function get(name) {\n      var _this3 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'get');\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this3.ready().then(function () {\n          storeCallback(_this3, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this3, name);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Get a promise for setting the value of a player property.\n     *\n     * @param {string} name The API method to call.\n     * @param {mixed} value The value to set.\n     * @return {Promise}\n     */\n  }, {\n    key: \"set\",\n    value: function set(name, value) {\n      var _this4 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'set');\n        if (value === undefined || value === null) {\n          throw new TypeError('There must be a value to set.');\n        }\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this4.ready().then(function () {\n          storeCallback(_this4, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this4, name, value);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Add an event listener for the specified event. Will call the\n     * callback with a single parameter, `data`, that contains the data for\n     * that event.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function(*)} callback The function to call when the event fires.\n     * @return {void}\n     */\n  }, {\n    key: \"on\",\n    value: function on(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (!callback) {\n        throw new TypeError('You must pass a callback function.');\n      }\n      if (typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var callbacks = getCallbacks(this, \"event:\".concat(eventName));\n      if (callbacks.length === 0) {\n        this.callMethod('addEventListener', eventName).catch(function () {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n      storeCallback(this, \"event:\".concat(eventName), callback);\n    }\n\n    /**\n     * Remove an event listener for the specified event. Will remove all\n     * listeners for that event if a `callback` isn’t passed, or only that\n     * specific callback if it is passed.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function} [callback] The specific callback to remove.\n     * @return {void}\n     */\n  }, {\n    key: \"off\",\n    value: function off(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (callback && typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var lastCallback = removeCallback(this, \"event:\".concat(eventName), callback);\n\n      // If there are no callbacks left, remove the listener\n      if (lastCallback) {\n        this.callMethod('removeEventListener', eventName).catch(function (e) {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n    }\n\n    /**\n     * A promise to load a new video.\n     *\n     * @promise LoadVideoPromise\n     * @fulfill {number} The video with this id or url successfully loaded.\n     * @reject {TypeError} The id was not a number.\n     */\n    /**\n     * Load a new video into this embed. The promise will be resolved if\n     * the video is successfully loaded, or it will be rejected if it could\n     * not be loaded.\n     *\n     * @param {number|string|object} options The id of the video, the url of the video, or an object with embed options.\n     * @return {LoadVideoPromise}\n     */\n  }, {\n    key: \"loadVideo\",\n    value: function loadVideo(options) {\n      return this.callMethod('loadVideo', options);\n    }\n\n    /**\n     * A promise to perform an action when the Player is ready.\n     *\n     * @todo document errors\n     * @promise LoadVideoPromise\n     * @fulfill {void}\n     */\n    /**\n     * Trigger a function when the player iframe has initialized. You do not\n     * need to wait for `ready` to trigger to begin adding event listeners\n     * or calling other methods.\n     *\n     * @return {ReadyPromise}\n     */\n  }, {\n    key: \"ready\",\n    value: function ready() {\n      var readyPromise = readyMap.get(this) || new npo_src(function (resolve, reject) {\n        reject(new Error('Unknown player. Probably unloaded.'));\n      });\n      return npo_src.resolve(readyPromise);\n    }\n\n    /**\n     * A promise to add a cue point to the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point to use for removeCuePoint.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Add a cue point to the player.\n     *\n     * @param {number} time The time for the cue point.\n     * @param {object} [data] Arbitrary data to be returned with the cue point.\n     * @return {AddCuePointPromise}\n     */\n  }, {\n    key: \"addCuePoint\",\n    value: function addCuePoint(time) {\n      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return this.callMethod('addCuePoint', {\n        time: time,\n        data: data\n      });\n    }\n\n    /**\n     * A promise to remove a cue point from the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point that was removed.\n     * @reject {InvalidCuePoint} The cue point with the specified id was not\n     *         found.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Remove a cue point from the video.\n     *\n     * @param {string} id The id of the cue point to remove.\n     * @return {RemoveCuePointPromise}\n     */\n  }, {\n    key: \"removeCuePoint\",\n    value: function removeCuePoint(id) {\n      return this.callMethod('removeCuePoint', id);\n    }\n\n    /**\n     * A representation of a text track on a video.\n     *\n     * @typedef {Object} VimeoTextTrack\n     * @property {string} language The ISO language code.\n     * @property {string} kind The kind of track it is (captions or subtitles).\n     * @property {string} label The human‐readable label for the track.\n     */\n    /**\n     * A promise to enable a text track.\n     *\n     * @promise EnableTextTrackPromise\n     * @fulfill {VimeoTextTrack} The text track that was enabled.\n     * @reject {InvalidTrackLanguageError} No track was available with the\n     *         specified language.\n     * @reject {InvalidTrackError} No track was available with the specified\n     *         language and kind.\n     */\n    /**\n     * Enable the text track with the specified language, and optionally the\n     * specified kind (captions or subtitles).\n     *\n     * When set via the API, the track language will not change the viewer’s\n     * stored preference.\n     *\n     * @param {string} language The two‐letter language code.\n     * @param {string} [kind] The kind of track to enable (captions or subtitles).\n     * @return {EnableTextTrackPromise}\n     */\n  }, {\n    key: \"enableTextTrack\",\n    value: function enableTextTrack(language, kind) {\n      if (!language) {\n        throw new TypeError('You must pass a language.');\n      }\n      return this.callMethod('enableTextTrack', {\n        language: language,\n        kind: kind\n      });\n    }\n\n    /**\n     * A promise to disable the active text track.\n     *\n     * @promise DisableTextTrackPromise\n     * @fulfill {void} The track was disabled.\n     */\n    /**\n     * Disable the currently-active text track.\n     *\n     * @return {DisableTextTrackPromise}\n     */\n  }, {\n    key: \"disableTextTrack\",\n    value: function disableTextTrack() {\n      return this.callMethod('disableTextTrack');\n    }\n\n    /**\n     * A promise to pause the video.\n     *\n     * @promise PausePromise\n     * @fulfill {void} The video was paused.\n     */\n    /**\n     * Pause the video if it’s playing.\n     *\n     * @return {PausePromise}\n     */\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      return this.callMethod('pause');\n    }\n\n    /**\n     * A promise to play the video.\n     *\n     * @promise PlayPromise\n     * @fulfill {void} The video was played.\n     */\n    /**\n     * Play the video if it’s paused. **Note:** on iOS and some other\n     * mobile devices, you cannot programmatically trigger play. Once the\n     * viewer has tapped on the play button in the player, however, you\n     * will be able to use this function.\n     *\n     * @return {PlayPromise}\n     */\n  }, {\n    key: \"play\",\n    value: function play() {\n      return this.callMethod('play');\n    }\n\n    /**\n     * Request that the player enters fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestFullscreen\",\n    value: function requestFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.request(this.element);\n      }\n      return this.callMethod('requestFullscreen');\n    }\n\n    /**\n     * Request that the player exits fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitFullscreen\",\n    value: function exitFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.exit();\n      }\n      return this.callMethod('exitFullscreen');\n    }\n\n    /**\n     * Returns true if the player is currently fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getFullscreen\",\n    value: function getFullscreen() {\n      if (screenfull.isEnabled) {\n        return npo_src.resolve(screenfull.isFullscreen);\n      }\n      return this.get('fullscreen');\n    }\n\n    /**\n     * Request that the player enters picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestPictureInPicture\",\n    value: function requestPictureInPicture() {\n      return this.callMethod('requestPictureInPicture');\n    }\n\n    /**\n     * Request that the player exits picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitPictureInPicture\",\n    value: function exitPictureInPicture() {\n      return this.callMethod('exitPictureInPicture');\n    }\n\n    /**\n     * Returns true if the player is currently picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getPictureInPicture\",\n    value: function getPictureInPicture() {\n      return this.get('pictureInPicture');\n    }\n\n    /**\n     * A promise to prompt the viewer to initiate remote playback.\n     *\n     * @promise RemotePlaybackPromptPromise\n     * @fulfill {void}\n     * @reject {NotFoundError} No remote playback device is available.\n     */\n    /**\n     * Request to prompt the user to initiate remote playback.\n     *\n     * @return {RemotePlaybackPromptPromise}\n     */\n  }, {\n    key: \"remotePlaybackPrompt\",\n    value: function remotePlaybackPrompt() {\n      return this.callMethod('remotePlaybackPrompt');\n    }\n\n    /**\n     * A promise to unload the video.\n     *\n     * @promise UnloadPromise\n     * @fulfill {void} The video was unloaded.\n     */\n    /**\n     * Return the player to its initial state.\n     *\n     * @return {UnloadPromise}\n     */\n  }, {\n    key: \"unload\",\n    value: function unload() {\n      return this.callMethod('unload');\n    }\n\n    /**\n     * Cleanup the player and remove it from the DOM\n     *\n     * It won't be usable and a new one should be constructed\n     *  in order to do any operations.\n     *\n     * @return {Promise}\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this5 = this;\n      return new npo_src(function (resolve) {\n        readyMap.delete(_this5);\n        playerMap.delete(_this5.element);\n        if (_this5._originalElement) {\n          playerMap.delete(_this5._originalElement);\n          _this5._originalElement.removeAttribute('data-vimeo-initialized');\n        }\n        if (_this5.element && _this5.element.nodeName === 'IFRAME' && _this5.element.parentNode) {\n          // If we've added an additional wrapper div, remove that from the DOM.\n          // If not, just remove the iframe element.\n          if (_this5.element.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== _this5.element.parentNode) {\n            _this5.element.parentNode.parentNode.removeChild(_this5.element.parentNode);\n          } else {\n            _this5.element.parentNode.removeChild(_this5.element);\n          }\n        }\n\n        // If the clip is private there is a case where the element stays the\n        // div element. Destroy should reset the div and remove the iframe child.\n        if (_this5.element && _this5.element.nodeName === 'DIV' && _this5.element.parentNode) {\n          _this5.element.removeAttribute('data-vimeo-initialized');\n          var iframe = _this5.element.querySelector('iframe');\n          if (iframe && iframe.parentNode) {\n            // If we've added an additional wrapper div, remove that from the DOM.\n            // If not, just remove the iframe element.\n            if (iframe.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== iframe.parentNode) {\n              iframe.parentNode.parentNode.removeChild(iframe.parentNode);\n            } else {\n              iframe.parentNode.removeChild(iframe);\n            }\n          }\n        }\n        _this5._window.removeEventListener('message', _this5._onMessage);\n        if (screenfull.isEnabled) {\n          screenfull.off('fullscreenchange', _this5.fullscreenchangeHandler);\n        }\n        resolve();\n      });\n    }\n\n    /**\n     * A promise to get the autopause behavior of the video.\n     *\n     * @promise GetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get the autopause behavior for this player.\n     *\n     * @return {GetAutopausePromise}\n     */\n  }, {\n    key: \"getAutopause\",\n    value: function getAutopause() {\n      return this.get('autopause');\n    }\n\n    /**\n     * A promise to set the autopause behavior of the video.\n     *\n     * @promise SetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Enable or disable the autopause behavior of this player.\n     *\n     * By default, when another video is played in the same browser, this\n     * player will automatically pause. Unless you have a specific reason\n     * for doing so, we recommend that you leave autopause set to the\n     * default (`true`).\n     *\n     * @param {boolean} autopause\n     * @return {SetAutopausePromise}\n     */\n  }, {\n    key: \"setAutopause\",\n    value: function setAutopause(autopause) {\n      return this.set('autopause', autopause);\n    }\n\n    /**\n     * A promise to get the buffered property of the video.\n     *\n     * @promise GetBufferedPromise\n     * @fulfill {Array} Buffered Timeranges converted to an Array.\n     */\n    /**\n     * Get the buffered property of the video.\n     *\n     * @return {GetBufferedPromise}\n     */\n  }, {\n    key: \"getBuffered\",\n    value: function getBuffered() {\n      return this.get('buffered');\n    }\n\n    /**\n     * @typedef {Object} CameraProperties\n     * @prop {number} props.yaw - Number between 0 and 360.\n     * @prop {number} props.pitch - Number between -90 and 90.\n     * @prop {number} props.roll - Number between -180 and 180.\n     * @prop {number} props.fov - The field of view in degrees.\n     */\n    /**\n     * A promise to get the camera properties of the player.\n     *\n     * @promise GetCameraPromise\n     * @fulfill {CameraProperties} The camera properties.\n     */\n    /**\n     * For 360° videos get the camera properties for this player.\n     *\n     * @return {GetCameraPromise}\n     */\n  }, {\n    key: \"getCameraProps\",\n    value: function getCameraProps() {\n      return this.get('cameraProps');\n    }\n\n    /**\n     * A promise to set the camera properties of the player.\n     *\n     * @promise SetCameraPromise\n     * @fulfill {Object} The camera was successfully set.\n     * @reject {RangeError} The range was out of bounds.\n     */\n    /**\n     * For 360° videos set the camera properties for this player.\n     *\n     * @param {CameraProperties} camera The camera properties\n     * @return {SetCameraPromise}\n     */\n  }, {\n    key: \"setCameraProps\",\n    value: function setCameraProps(camera) {\n      return this.set('cameraProps', camera);\n    }\n\n    /**\n     * A representation of a chapter.\n     *\n     * @typedef {Object} VimeoChapter\n     * @property {number} startTime The start time of the chapter.\n     * @property {object} title The title of the chapter.\n     * @property {number} index The place in the order of Chapters. Starts at 1.\n     */\n    /**\n     * A promise to get chapters for the video.\n     *\n     * @promise GetChaptersPromise\n     * @fulfill {VimeoChapter[]} The chapters for the video.\n     */\n    /**\n     * Get an array of all the chapters for the video.\n     *\n     * @return {GetChaptersPromise}\n     */\n  }, {\n    key: \"getChapters\",\n    value: function getChapters() {\n      return this.get('chapters');\n    }\n\n    /**\n     * A promise to get the currently active chapter.\n     *\n     * @promise GetCurrentChaptersPromise\n     * @fulfill {VimeoChapter|undefined} The current chapter for the video.\n     */\n    /**\n     * Get the currently active chapter for the video.\n     *\n     * @return {GetCurrentChaptersPromise}\n     */\n  }, {\n    key: \"getCurrentChapter\",\n    value: function getCurrentChapter() {\n      return this.get('currentChapter');\n    }\n\n    /**\n     * A promise to get the accent color of the player.\n     *\n     * @promise GetColorPromise\n     * @fulfill {string} The hex color of the player.\n     */\n    /**\n     * Get the accent color for this player. Note this is deprecated in place of `getColorTwo`.\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColor\",\n    value: function getColor() {\n      return this.get('color');\n    }\n\n    /**\n     * A promise to get all colors for the player in an array.\n     *\n     * @promise GetColorsPromise\n     * @fulfill {string[]} The hex colors of the player.\n     */\n    /**\n     * Get all the colors for this player in an array: [colorOne, colorTwo, colorThree, colorFour]\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColors\",\n    value: function getColors() {\n      return npo_src.all([this.get('colorOne'), this.get('colorTwo'), this.get('colorThree'), this.get('colorFour')]);\n    }\n\n    /**\n     * A promise to set the accent color of the player.\n     *\n     * @promise SetColorPromise\n     * @fulfill {string} The color was successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the accent color of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * Note this is deprecated in place of `setColorTwo`.\n     *\n     * @param {string} color The hex or rgb color string to set.\n     * @return {SetColorPromise}\n     */\n  }, {\n    key: \"setColor\",\n    value: function setColor(color) {\n      return this.set('color', color);\n    }\n\n    /**\n     * A promise to set all colors for the player.\n     *\n     * @promise SetColorsPromise\n     * @fulfill {string[]} The colors were successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the colors of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * The colors should be passed in as an array: [colorOne, colorTwo, colorThree, colorFour].\n     * If a color should not be set, the index in the array can be left as null.\n     *\n     * @param {string[]} colors Array of the hex or rgb color strings to set.\n     * @return {SetColorsPromise}\n     */\n  }, {\n    key: \"setColors\",\n    value: function setColors(colors) {\n      if (!Array.isArray(colors)) {\n        return new npo_src(function (resolve, reject) {\n          return reject(new TypeError('Argument must be an array.'));\n        });\n      }\n      var nullPromise = new npo_src(function (resolve) {\n        return resolve(null);\n      });\n      var colorPromises = [colors[0] ? this.set('colorOne', colors[0]) : nullPromise, colors[1] ? this.set('colorTwo', colors[1]) : nullPromise, colors[2] ? this.set('colorThree', colors[2]) : nullPromise, colors[3] ? this.set('colorFour', colors[3]) : nullPromise];\n      return npo_src.all(colorPromises);\n    }\n\n    /**\n     * A representation of a cue point.\n     *\n     * @typedef {Object} VimeoCuePoint\n     * @property {number} time The time of the cue point.\n     * @property {object} data The data passed when adding the cue point.\n     * @property {string} id The unique id for use with removeCuePoint.\n     */\n    /**\n     * A promise to get the cue points of a video.\n     *\n     * @promise GetCuePointsPromise\n     * @fulfill {VimeoCuePoint[]} The cue points added to the video.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get an array of the cue points added to the video.\n     *\n     * @return {GetCuePointsPromise}\n     */\n  }, {\n    key: \"getCuePoints\",\n    value: function getCuePoints() {\n      return this.get('cuePoints');\n    }\n\n    /**\n     * A promise to get the current time of the video.\n     *\n     * @promise GetCurrentTimePromise\n     * @fulfill {number} The current time in seconds.\n     */\n    /**\n     * Get the current playback position in seconds.\n     *\n     * @return {GetCurrentTimePromise}\n     */\n  }, {\n    key: \"getCurrentTime\",\n    value: function getCurrentTime() {\n      return this.get('currentTime');\n    }\n\n    /**\n     * A promise to set the current time of the video.\n     *\n     * @promise SetCurrentTimePromise\n     * @fulfill {number} The actual current time that was set.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     */\n    /**\n     * Set the current playback position in seconds. If the player was\n     * paused, it will remain paused. Likewise, if the player was playing,\n     * it will resume playing once the video has buffered.\n     *\n     * You can provide an accurate time and the player will attempt to seek\n     * to as close to that time as possible. The exact time will be the\n     * fulfilled value of the promise.\n     *\n     * @param {number} currentTime\n     * @return {SetCurrentTimePromise}\n     */\n  }, {\n    key: \"setCurrentTime\",\n    value: function setCurrentTime(currentTime) {\n      return this.set('currentTime', currentTime);\n    }\n\n    /**\n     * A promise to get the duration of the video.\n     *\n     * @promise GetDurationPromise\n     * @fulfill {number} The duration in seconds.\n     */\n    /**\n     * Get the duration of the video in seconds. It will be rounded to the\n     * nearest second before playback begins, and to the nearest thousandth\n     * of a second after playback begins.\n     *\n     * @return {GetDurationPromise}\n     */\n  }, {\n    key: \"getDuration\",\n    value: function getDuration() {\n      return this.get('duration');\n    }\n\n    /**\n     * A promise to get the ended state of the video.\n     *\n     * @promise GetEndedPromise\n     * @fulfill {boolean} Whether or not the video has ended.\n     */\n    /**\n     * Get the ended state of the video. The video has ended if\n     * `currentTime === duration`.\n     *\n     * @return {GetEndedPromise}\n     */\n  }, {\n    key: \"getEnded\",\n    value: function getEnded() {\n      return this.get('ended');\n    }\n\n    /**\n     * A promise to get the loop state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the player is set to loop.\n     */\n    /**\n     * Get the loop state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getLoop\",\n    value: function getLoop() {\n      return this.get('loop');\n    }\n\n    /**\n     * A promise to set the loop state of the player.\n     *\n     * @promise SetLoopPromise\n     * @fulfill {boolean} The loop state that was set.\n     */\n    /**\n     * Set the loop state of the player. When set to `true`, the player\n     * will start over immediately once playback ends.\n     *\n     * @param {boolean} loop\n     * @return {SetLoopPromise}\n     */\n  }, {\n    key: \"setLoop\",\n    value: function setLoop(loop) {\n      return this.set('loop', loop);\n    }\n\n    /**\n     * A promise to set the muted state of the player.\n     *\n     * @promise SetMutedPromise\n     * @fulfill {boolean} The muted state that was set.\n     */\n    /**\n     * Set the muted state of the player. When set to `true`, the player\n     * volume will be muted.\n     *\n     * @param {boolean} muted\n     * @return {SetMutedPromise}\n     */\n  }, {\n    key: \"setMuted\",\n    value: function setMuted(muted) {\n      return this.set('muted', muted);\n    }\n\n    /**\n     * A promise to get the muted state of the player.\n     *\n     * @promise GetMutedPromise\n     * @fulfill {boolean} Whether or not the player is muted.\n     */\n    /**\n     * Get the muted state of the player.\n     *\n     * @return {GetMutedPromise}\n     */\n  }, {\n    key: \"getMuted\",\n    value: function getMuted() {\n      return this.get('muted');\n    }\n\n    /**\n     * A promise to get the paused state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the video is paused.\n     */\n    /**\n     * Get the paused state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getPaused\",\n    value: function getPaused() {\n      return this.get('paused');\n    }\n\n    /**\n     * A promise to get the playback rate of the player.\n     *\n     * @promise GetPlaybackRatePromise\n     * @fulfill {number} The playback rate of the player on a scale from 0 to 2.\n     */\n    /**\n     * Get the playback rate of the player on a scale from `0` to `2`.\n     *\n     * @return {GetPlaybackRatePromise}\n     */\n  }, {\n    key: \"getPlaybackRate\",\n    value: function getPlaybackRate() {\n      return this.get('playbackRate');\n    }\n\n    /**\n     * A promise to set the playbackrate of the player.\n     *\n     * @promise SetPlaybackRatePromise\n     * @fulfill {number} The playback rate was set.\n     * @reject {RangeError} The playback rate was less than 0 or greater than 2.\n     */\n    /**\n     * Set the playback rate of the player on a scale from `0` to `2`. When set\n     * via the API, the playback rate will not be synchronized to other\n     * players or stored as the viewer's preference.\n     *\n     * @param {number} playbackRate\n     * @return {SetPlaybackRatePromise}\n     */\n  }, {\n    key: \"setPlaybackRate\",\n    value: function setPlaybackRate(playbackRate) {\n      return this.set('playbackRate', playbackRate);\n    }\n\n    /**\n     * A promise to get the played property of the video.\n     *\n     * @promise GetPlayedPromise\n     * @fulfill {Array} Played Timeranges converted to an Array.\n     */\n    /**\n     * Get the played property of the video.\n     *\n     * @return {GetPlayedPromise}\n     */\n  }, {\n    key: \"getPlayed\",\n    value: function getPlayed() {\n      return this.get('played');\n    }\n\n    /**\n     * A promise to get the qualities available of the current video.\n     *\n     * @promise GetQualitiesPromise\n     * @fulfill {Array} The qualities of the video.\n     */\n    /**\n     * Get the qualities of the current video.\n     *\n     * @return {GetQualitiesPromise}\n     */\n  }, {\n    key: \"getQualities\",\n    value: function getQualities() {\n      return this.get('qualities');\n    }\n\n    /**\n     * A promise to get the current set quality of the video.\n     *\n     * @promise GetQualityPromise\n     * @fulfill {string} The current set quality.\n     */\n    /**\n     * Get the current set quality of the video.\n     *\n     * @return {GetQualityPromise}\n     */\n  }, {\n    key: \"getQuality\",\n    value: function getQuality() {\n      return this.get('quality');\n    }\n\n    /**\n     * A promise to set the video quality.\n     *\n     * @promise SetQualityPromise\n     * @fulfill {number} The quality was set.\n     * @reject {RangeError} The quality is not available.\n     */\n    /**\n     * Set a video quality.\n     *\n     * @param {string} quality\n     * @return {SetQualityPromise}\n     */\n  }, {\n    key: \"setQuality\",\n    value: function setQuality(quality) {\n      return this.set('quality', quality);\n    }\n\n    /**\n     * A promise to get the remote playback availability.\n     *\n     * @promise RemotePlaybackAvailabilityPromise\n     * @fulfill {boolean} Whether remote playback is available.\n     */\n    /**\n     * Get the availability of remote playback.\n     *\n     * @return {RemotePlaybackAvailabilityPromise}\n     */\n  }, {\n    key: \"getRemotePlaybackAvailability\",\n    value: function getRemotePlaybackAvailability() {\n      return this.get('remotePlaybackAvailability');\n    }\n\n    /**\n     * A promise to get the current remote playback state.\n     *\n     * @promise RemotePlaybackStatePromise\n     * @fulfill {string} The state of the remote playback: connecting, connected, or disconnected.\n     */\n    /**\n     * Get the current remote playback state.\n     *\n     * @return {RemotePlaybackStatePromise}\n     */\n  }, {\n    key: \"getRemotePlaybackState\",\n    value: function getRemotePlaybackState() {\n      return this.get('remotePlaybackState');\n    }\n\n    /**\n     * A promise to get the seekable property of the video.\n     *\n     * @promise GetSeekablePromise\n     * @fulfill {Array} Seekable Timeranges converted to an Array.\n     */\n    /**\n     * Get the seekable property of the video.\n     *\n     * @return {GetSeekablePromise}\n     */\n  }, {\n    key: \"getSeekable\",\n    value: function getSeekable() {\n      return this.get('seekable');\n    }\n\n    /**\n     * A promise to get the seeking property of the player.\n     *\n     * @promise GetSeekingPromise\n     * @fulfill {boolean} Whether or not the player is currently seeking.\n     */\n    /**\n     * Get if the player is currently seeking.\n     *\n     * @return {GetSeekingPromise}\n     */\n  }, {\n    key: \"getSeeking\",\n    value: function getSeeking() {\n      return this.get('seeking');\n    }\n\n    /**\n     * A promise to get the text tracks of a video.\n     *\n     * @promise GetTextTracksPromise\n     * @fulfill {VimeoTextTrack[]} The text tracks associated with the video.\n     */\n    /**\n     * Get an array of the text tracks that exist for the video.\n     *\n     * @return {GetTextTracksPromise}\n     */\n  }, {\n    key: \"getTextTracks\",\n    value: function getTextTracks() {\n      return this.get('textTracks');\n    }\n\n    /**\n     * A promise to get the embed code for the video.\n     *\n     * @promise GetVideoEmbedCodePromise\n     * @fulfill {string} The `<iframe>` embed code for the video.\n     */\n    /**\n     * Get the `<iframe>` embed code for the video.\n     *\n     * @return {GetVideoEmbedCodePromise}\n     */\n  }, {\n    key: \"getVideoEmbedCode\",\n    value: function getVideoEmbedCode() {\n      return this.get('videoEmbedCode');\n    }\n\n    /**\n     * A promise to get the id of the video.\n     *\n     * @promise GetVideoIdPromise\n     * @fulfill {number} The id of the video.\n     */\n    /**\n     * Get the id of the video.\n     *\n     * @return {GetVideoIdPromise}\n     */\n  }, {\n    key: \"getVideoId\",\n    value: function getVideoId() {\n      return this.get('videoId');\n    }\n\n    /**\n     * A promise to get the title of the video.\n     *\n     * @promise GetVideoTitlePromise\n     * @fulfill {number} The title of the video.\n     */\n    /**\n     * Get the title of the video.\n     *\n     * @return {GetVideoTitlePromise}\n     */\n  }, {\n    key: \"getVideoTitle\",\n    value: function getVideoTitle() {\n      return this.get('videoTitle');\n    }\n\n    /**\n     * A promise to get the native width of the video.\n     *\n     * @promise GetVideoWidthPromise\n     * @fulfill {number} The native width of the video.\n     */\n    /**\n     * Get the native width of the currently‐playing video. The width of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoWidthPromise}\n     */\n  }, {\n    key: \"getVideoWidth\",\n    value: function getVideoWidth() {\n      return this.get('videoWidth');\n    }\n\n    /**\n     * A promise to get the native height of the video.\n     *\n     * @promise GetVideoHeightPromise\n     * @fulfill {number} The native height of the video.\n     */\n    /**\n     * Get the native height of the currently‐playing video. The height of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoHeightPromise}\n     */\n  }, {\n    key: \"getVideoHeight\",\n    value: function getVideoHeight() {\n      return this.get('videoHeight');\n    }\n\n    /**\n     * A promise to get the vimeo.com url for the video.\n     *\n     * @promise GetVideoUrlPromise\n     * @fulfill {number} The vimeo.com url for the video.\n     * @reject {PrivacyError} The url isn’t available because of the video’s privacy setting.\n     */\n    /**\n     * Get the vimeo.com url for the video.\n     *\n     * @return {GetVideoUrlPromise}\n     */\n  }, {\n    key: \"getVideoUrl\",\n    value: function getVideoUrl() {\n      return this.get('videoUrl');\n    }\n\n    /**\n     * A promise to get the volume level of the player.\n     *\n     * @promise GetVolumePromise\n     * @fulfill {number} The volume level of the player on a scale from 0 to 1.\n     */\n    /**\n     * Get the current volume level of the player on a scale from `0` to `1`.\n     *\n     * Most mobile devices do not support an independent volume from the\n     * system volume. In those cases, this method will always return `1`.\n     *\n     * @return {GetVolumePromise}\n     */\n  }, {\n    key: \"getVolume\",\n    value: function getVolume() {\n      return this.get('volume');\n    }\n\n    /**\n     * A promise to set the volume level of the player.\n     *\n     * @promise SetVolumePromise\n     * @fulfill {number} The volume was set.\n     * @reject {RangeError} The volume was less than 0 or greater than 1.\n     */\n    /**\n     * Set the volume of the player on a scale from `0` to `1`. When set\n     * via the API, the volume level will not be synchronized to other\n     * players or stored as the viewer’s preference.\n     *\n     * Most mobile devices do not support setting the volume. An error will\n     * *not* be triggered in that situation.\n     *\n     * @param {number} volume\n     * @return {SetVolumePromise}\n     */\n  }, {\n    key: \"setVolume\",\n    value: function setVolume(volume) {\n      return this.set('volume', volume);\n    }\n\n    /** @typedef {import('./lib/timing-object.types').TimingObject} TimingObject */\n    /** @typedef {import('./lib/timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n    /** @typedef {import('./lib/timing-src-connector').TimingSrcConnector} TimingSrcConnector */\n\n    /**\n     * Connects a TimingObject to the video player (https://webtiming.github.io/timingobject/)\n     *\n     * @param {TimingObject} timingObject\n     * @param {TimingSrcConnectorOptions} options\n     *\n     * @return {Promise<TimingSrcConnector>}\n     */\n  }, {\n    key: \"setTimingSrc\",\n    value: function () {\n      var _setTimingSrc = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(timingObject, options) {\n        var _this6 = this;\n        var connector;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (timingObject) {\n                _context.next = 2;\n                break;\n              }\n              throw new TypeError('A Timing Object must be provided.');\n            case 2:\n              _context.next = 4;\n              return this.ready();\n            case 4:\n              connector = new TimingSrcConnector(this, timingObject, options);\n              postMessage(this, 'notifyTimingObjectConnect');\n              connector.addEventListener('disconnect', function () {\n                return postMessage(_this6, 'notifyTimingObjectDisconnect');\n              });\n              return _context.abrupt(\"return\", connector);\n            case 8:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function setTimingSrc(_x, _x2) {\n        return _setTimingSrc.apply(this, arguments);\n      }\n      return setTimingSrc;\n    }()\n  }]);\n  return Player;\n}(); // Setup embed only if this is not a node environment\nif (!isNode) {\n  screenfull = initializeScreenfull();\n  initializeEmbeds();\n  resizeEmbeds();\n  initAppendVideoMetadata();\n  checkUrlTimeParam();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Player);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vimeo/player/dist/player.es.js\n");

/***/ })

};
;