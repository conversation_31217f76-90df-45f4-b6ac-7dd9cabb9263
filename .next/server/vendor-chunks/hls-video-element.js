"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hls-video-element";
exports.ids = ["vendor-chunks/hls-video-element"];
exports.modules = {

/***/ "(ssr)/./node_modules/hls-video-element/dist/hls-video-element.js":
/*!******************************************************************!*\
  !*** ./node_modules/hls-video-element/dist/hls-video-element.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hls: () => (/* reexport safe */ hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   HlsVideoElement: () => (/* binding */ HlsVideoElement),\n/* harmony export */   HlsVideoMixin: () => (/* binding */ HlsVideoMixin),\n/* harmony export */   \"default\": () => (/* binding */ hls_video_element_default)\n/* harmony export */ });\n/* harmony import */ var custom_media_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! custom-media-element */ \"(ssr)/./node_modules/custom-media-element/dist/custom-media-element.js\");\n/* harmony import */ var media_tracks__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! media-tracks */ \"(ssr)/./node_modules/media-tracks/dist/index.js\");\n/* harmony import */ var hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hls.js/dist/hls.mjs */ \"(ssr)/./node_modules/hls.js/dist/hls.mjs\");\n\n\n\nconst HlsVideoMixin = (superclass) => {\n  return class HlsVideo extends superclass {\n    static shadowRootOptions = { ...superclass.shadowRootOptions };\n    static getTemplateHTML = (attrs, props = {}) => {\n      const { src, ...rest } = attrs;\n      return `\n        <script type=\"application/json\" id=\"config\">\n          ${JSON.stringify(props.config || {})}\n        </script>\n        ${superclass.getTemplateHTML(rest)}\n      `;\n    };\n    #airplaySourceEl = null;\n    #config = null;\n    constructor() {\n      super();\n      this.#upgradeProperty(\"config\");\n    }\n    get config() {\n      return this.#config;\n    }\n    set config(value) {\n      this.#config = value;\n    }\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      if (attrName !== \"src\") {\n        super.attributeChangedCallback(attrName, oldValue, newValue);\n      }\n      if (attrName === \"src\" && oldValue != newValue) {\n        this.load();\n      }\n    }\n    #destroy() {\n      var _a, _b;\n      (_a = this.#airplaySourceEl) == null ? void 0 : _a.remove();\n      (_b = this.nativeEl) == null ? void 0 : _b.removeEventListener(\n        \"webkitcurrentplaybacktargetiswirelesschanged\",\n        this.#toggleHlsLoad\n      );\n      if (this.api) {\n        this.api.detachMedia();\n        this.api.destroy();\n        this.api = null;\n      }\n    }\n    async load() {\n      var _a, _b;\n      const isFirstLoad = !this.api;\n      this.#destroy();\n      if (!this.src) {\n        return;\n      }\n      if (isFirstLoad && !this.#config) {\n        this.#config = JSON.parse(((_a = this.shadowRoot.getElementById(\"config\")) == null ? void 0 : _a.textContent) || \"{}\");\n      }\n      if (hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].isSupported()) {\n        this.api = new hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n          // Mimic the media element with an Infinity duration for live streams.\n          liveDurationInfinity: true,\n          // Disable auto quality level/fragment loading.\n          autoStartLoad: false,\n          // Custom configuration for hls.js.\n          ...this.config\n        });\n        await Promise.resolve();\n        this.api.loadSource(this.src);\n        this.api.attachMedia(this.nativeEl);\n        switch (this.nativeEl.preload) {\n          case \"none\": {\n            const loadSourceOnPlay = () => this.api.startLoad();\n            this.nativeEl.addEventListener(\"play\", loadSourceOnPlay, {\n              once: true\n            });\n            this.api.on(hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.DESTROYING, () => {\n              this.nativeEl.removeEventListener(\"play\", loadSourceOnPlay);\n            });\n            break;\n          }\n          case \"metadata\": {\n            const originalLength = this.api.config.maxBufferLength;\n            const originalSize = this.api.config.maxBufferSize;\n            this.api.config.maxBufferLength = 1;\n            this.api.config.maxBufferSize = 1;\n            const increaseBufferOnPlay = () => {\n              this.api.config.maxBufferLength = originalLength;\n              this.api.config.maxBufferSize = originalSize;\n            };\n            this.nativeEl.addEventListener(\"play\", increaseBufferOnPlay, {\n              once: true\n            });\n            this.api.on(hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.DESTROYING, () => {\n              this.nativeEl.removeEventListener(\"play\", increaseBufferOnPlay);\n            });\n            this.api.startLoad();\n            break;\n          }\n          default:\n            this.api.startLoad();\n        }\n        if (this.nativeEl.webkitCurrentPlaybackTargetIsWireless) {\n          this.api.stopLoad();\n        }\n        this.nativeEl.addEventListener(\n          \"webkitcurrentplaybacktargetiswirelesschanged\",\n          this.#toggleHlsLoad\n        );\n        this.#airplaySourceEl = document.createElement(\"source\");\n        this.#airplaySourceEl.setAttribute(\"type\", \"application/x-mpegURL\");\n        this.#airplaySourceEl.setAttribute(\"src\", this.src);\n        this.nativeEl.disableRemotePlayback = false;\n        this.nativeEl.append(this.#airplaySourceEl);\n        const levelIdMap = /* @__PURE__ */ new WeakMap();\n        this.api.on(hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.MANIFEST_PARSED, (event, data) => {\n          if (this.nativeEl.autoplay && this.nativeEl.paused) {\n            this.nativeEl.play().catch((err) => {\n              console.warn(\"Autoplay failed:\", err);\n            });\n          }\n          removeAllMediaTracks();\n          let videoTrack = this.videoTracks.getTrackById(\"main\");\n          if (!videoTrack) {\n            videoTrack = this.addVideoTrack(\"main\");\n            videoTrack.id = \"main\";\n            videoTrack.selected = true;\n          }\n          for (const [id, level] of data.levels.entries()) {\n            const videoRendition = videoTrack.addRendition(\n              level.url[0],\n              level.width,\n              level.height,\n              level.videoCodec,\n              level.bitrate\n            );\n            levelIdMap.set(level, `${id}`);\n            videoRendition.id = `${id}`;\n          }\n          for (let [id, a] of data.audioTracks.entries()) {\n            const kind = a.default ? \"main\" : \"alternative\";\n            const audioTrack = this.addAudioTrack(kind, a.name, a.lang);\n            audioTrack.id = `${id}`;\n            if (a.default) {\n              audioTrack.enabled = true;\n            }\n          }\n        });\n        this.audioTracks.addEventListener(\"change\", () => {\n          var _a2;\n          const audioTrackId = +((_a2 = [...this.audioTracks].find((t) => t.enabled)) == null ? void 0 : _a2.id);\n          const availableIds = this.api.audioTracks.map((t) => t.id);\n          if (audioTrackId != this.api.audioTrack && availableIds.includes(audioTrackId)) {\n            this.api.audioTrack = audioTrackId;\n          }\n        });\n        this.api.on(hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.LEVELS_UPDATED, (event, data) => {\n          const videoTrack = this.videoTracks[this.videoTracks.selectedIndex ?? 0];\n          if (!videoTrack) return;\n          const levelIds = data.levels.map((l) => levelIdMap.get(l));\n          for (const rendition of this.videoRenditions) {\n            if (rendition.id && !levelIds.includes(rendition.id)) {\n              videoTrack.removeRendition(rendition);\n            }\n          }\n        });\n        const switchRendition = (event) => {\n          const level = event.target.selectedIndex;\n          if (level != this.api.nextLevel) {\n            this.api.nextLevel = level;\n          }\n        };\n        (_b = this.videoRenditions) == null ? void 0 : _b.addEventListener(\"change\", switchRendition);\n        const removeAllMediaTracks = () => {\n          for (const videoTrack of this.videoTracks) {\n            this.removeVideoTrack(videoTrack);\n          }\n          for (const audioTrack of this.audioTracks) {\n            this.removeAudioTrack(audioTrack);\n          }\n        };\n        this.api.once(hls_js_dist_hls_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Events.DESTROYING, removeAllMediaTracks);\n        return;\n      }\n      await Promise.resolve();\n      if (this.nativeEl.canPlayType(\"application/vnd.apple.mpegurl\")) {\n        this.nativeEl.src = this.src;\n      }\n    }\n    #toggleHlsLoad = () => {\n      var _a, _b, _c;\n      if ((_a = this.nativeEl) == null ? void 0 : _a.webkitCurrentPlaybackTargetIsWireless) {\n        (_b = this.api) == null ? void 0 : _b.stopLoad();\n      } else {\n        (_c = this.api) == null ? void 0 : _c.startLoad();\n      }\n    };\n    // This is a pattern to update property values that are set before\n    // the custom element is upgraded.\n    // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n    #upgradeProperty(prop) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        delete this[prop];\n        this[prop] = value;\n      }\n    }\n  };\n};\nconst HlsVideoElement = HlsVideoMixin((0,media_tracks__WEBPACK_IMPORTED_MODULE_1__.MediaTracksMixin)(custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement));\nif (globalThis.customElements && !globalThis.customElements.get(\"hls-video\")) {\n  globalThis.customElements.define(\"hls-video\", HlsVideoElement);\n}\nvar hls_video_element_default = HlsVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hls-video-element/dist/hls-video-element.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hls-video-element/dist/react.js":
/*!******************************************************!*\
  !*** ./node_modules/hls-video-element/dist/react.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _hls_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hls-video-element.js */ \"(ssr)/./node_modules/hls-video-element/dist/hls-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent({ react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue }) {\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef((props, ref)=>{\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            const attrName = toAttributeName(reactPropToAttrNameMap[k] ?? k);\n            if (k in elementClass.prototype && !(k in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (false) {}\n        if ( true && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    });\n    ReactComponent.displayName = displayName ?? elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    if (value == null && name in (((_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) ?? {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"hls-video\",\n    elementClass: _hls_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hls-video-element/dist/react.js\n");

/***/ })

};
;