"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/custom-media-element";
exports.ids = ["vendor-chunks/custom-media-element"];
exports.modules = {

/***/ "(ssr)/./node_modules/custom-media-element/dist/custom-media-element.js":
/*!************************************************************************!*\
  !*** ./node_modules/custom-media-element/dist/custom-media-element.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attributes: () => (/* binding */ Attributes),\n/* harmony export */   CustomAudioElement: () => (/* binding */ CustomAudioElement),\n/* harmony export */   CustomMediaMixin: () => (/* binding */ CustomMediaMixin),\n/* harmony export */   CustomVideoElement: () => (/* binding */ CustomVideoElement),\n/* harmony export */   Events: () => (/* binding */ Events)\n/* harmony export */ });\nconst Events = [\n  \"abort\",\n  \"canplay\",\n  \"canplaythrough\",\n  \"durationchange\",\n  \"emptied\",\n  \"encrypted\",\n  \"ended\",\n  \"error\",\n  \"loadeddata\",\n  \"loadedmetadata\",\n  \"loadstart\",\n  \"pause\",\n  \"play\",\n  \"playing\",\n  \"progress\",\n  \"ratechange\",\n  \"seeked\",\n  \"seeking\",\n  \"stalled\",\n  \"suspend\",\n  \"timeupdate\",\n  \"volumechange\",\n  \"waiting\",\n  \"waitingforkey\",\n  \"resize\",\n  \"enterpictureinpicture\",\n  \"leavepictureinpicture\",\n  \"webkitbeginfullscreen\",\n  \"webkitendfullscreen\",\n  \"webkitpresentationmodechanged\"\n];\nconst Attributes = [\n  \"autopictureinpicture\",\n  \"disablepictureinpicture\",\n  \"disableremoteplayback\",\n  \"autoplay\",\n  \"controls\",\n  \"controlslist\",\n  \"crossorigin\",\n  \"loop\",\n  \"muted\",\n  \"playsinline\",\n  \"poster\",\n  \"preload\",\n  \"src\"\n];\nfunction getAudioTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-flex;\n        line-height: 0;\n        flex-direction: column;\n        justify-content: end;\n      }\n\n      audio {\n        width: 100%;\n      }\n    </style>\n    <slot name=\"media\">\n      <audio${serializeAttributes(attrs)}></audio>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction getVideoTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n      }\n\n      video {\n        max-width: 100%;\n        max-height: 100%;\n        min-width: 100%;\n        min-height: 100%;\n        object-fit: var(--media-object-fit, contain);\n        object-position: var(--media-object-position, 50% 50%);\n      }\n\n      video::-webkit-media-text-track-container {\n        transform: var(--media-webkit-text-track-transform);\n        transition: var(--media-webkit-text-track-transition);\n      }\n    </style>\n    <slot name=\"media\">\n      <video${serializeAttributes(attrs)}></video>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction CustomMediaMixin(superclass, { tag, is }) {\n  const nativeElTest = globalThis.document?.createElement?.(tag, { is });\n  const nativeElProps = nativeElTest ? getNativeElProps(nativeElTest) : [];\n  return class CustomMedia extends superclass {\n    static getTemplateHTML = tag.endsWith(\"audio\") ? getAudioTemplateHTML : getVideoTemplateHTML;\n    static shadowRootOptions = { mode: \"open\" };\n    static Events = Events;\n    static #isDefined = false;\n    static get observedAttributes() {\n      CustomMedia.#define();\n      const natAttrs = nativeElTest?.constructor?.observedAttributes ?? [];\n      return [\n        ...natAttrs,\n        ...Attributes\n      ];\n    }\n    static #define() {\n      if (this.#isDefined) return;\n      this.#isDefined = true;\n      const propsToAttrs = new Set(this.observedAttributes);\n      propsToAttrs.delete(\"muted\");\n      for (const prop of nativeElProps) {\n        if (prop in this.prototype) continue;\n        if (typeof nativeElTest[prop] === \"function\") {\n          this.prototype[prop] = function(...args) {\n            this.#init();\n            const fn = () => {\n              if (this.call) return this.call(prop, ...args);\n              const nativeFn = this.nativeEl?.[prop];\n              return nativeFn?.apply(this.nativeEl, args);\n            };\n            return fn();\n          };\n        } else {\n          const config = {\n            get() {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                const val = this.getAttribute(attr);\n                return val === null ? false : val === \"\" ? true : val;\n              }\n              return this.get?.(prop) ?? this.nativeEl?.[prop];\n            }\n          };\n          if (prop !== prop.toUpperCase()) {\n            config.set = function(val) {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                if (val === true || val === false || val == null) {\n                  this.toggleAttribute(attr, Boolean(val));\n                } else {\n                  this.setAttribute(attr, val);\n                }\n                return;\n              }\n              if (this.set) {\n                this.set(prop, val);\n                return;\n              }\n              if (this.nativeEl) {\n                this.nativeEl[prop] = val;\n              }\n            };\n          }\n          Object.defineProperty(this.prototype, prop, config);\n        }\n      }\n    }\n    // Private fields\n    #isInit = false;\n    #nativeEl = null;\n    #childMap = /* @__PURE__ */ new Map();\n    #childObserver;\n    get;\n    set;\n    call;\n    // If the custom element is defined before the custom element's HTML is parsed\n    // no attributes will be available in the constructor (construction process).\n    // Wait until initializing in the attributeChangedCallback or\n    // connectedCallback or accessing any properties.\n    get nativeEl() {\n      this.#init();\n      return this.#nativeEl ?? this.querySelector(\":scope > [slot=media]\") ?? this.querySelector(tag) ?? this.shadowRoot?.querySelector(tag) ?? null;\n    }\n    set nativeEl(val) {\n      this.#nativeEl = val;\n    }\n    get defaultMuted() {\n      return this.hasAttribute(\"muted\");\n    }\n    set defaultMuted(val) {\n      this.toggleAttribute(\"muted\", val);\n    }\n    get src() {\n      return this.getAttribute(\"src\");\n    }\n    set src(val) {\n      this.setAttribute(\"src\", `${val}`);\n    }\n    get preload() {\n      return this.getAttribute(\"preload\") ?? this.nativeEl?.preload;\n    }\n    set preload(val) {\n      this.setAttribute(\"preload\", `${val}`);\n    }\n    #init() {\n      if (this.#isInit) return;\n      this.#isInit = true;\n      this.init();\n    }\n    init() {\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: \"open\" });\n        const attrs = namedNodeMapToObject(this.attributes);\n        if (is) attrs.is = is;\n        if (tag) attrs.part = tag;\n        this.shadowRoot.innerHTML = this.constructor.getTemplateHTML(attrs);\n      }\n      this.nativeEl.muted = this.hasAttribute(\"muted\");\n      for (const prop of nativeElProps) {\n        this.#upgradeProperty(prop);\n      }\n      this.#childObserver = new MutationObserver(this.#syncMediaChildAttribute.bind(this));\n      this.shadowRoot.addEventListener(\"slotchange\", () => this.#syncMediaChildren());\n      this.#syncMediaChildren();\n      for (const type of this.constructor.Events) {\n        this.shadowRoot.addEventListener(type, this, true);\n      }\n    }\n    handleEvent(event) {\n      if (event.target === this.nativeEl) {\n        this.dispatchEvent(new CustomEvent(event.type, { detail: event.detail }));\n      }\n    }\n    #syncMediaChildren() {\n      const removeNativeChildren = new Map(this.#childMap);\n      const defaultSlot = this.shadowRoot?.querySelector(\"slot:not([name])\");\n      const mediaChildren = defaultSlot?.assignedElements({ flatten: true }).filter((el) => [\"track\", \"source\"].includes(el.localName));\n      mediaChildren.forEach((el) => {\n        removeNativeChildren.delete(el);\n        let clone = this.#childMap.get(el);\n        if (!clone) {\n          clone = el.cloneNode();\n          this.#childMap.set(el, clone);\n          this.#childObserver?.observe(el, { attributes: true });\n        }\n        this.nativeEl?.append(clone);\n        this.#enableDefaultTrack(clone);\n      });\n      removeNativeChildren.forEach((clone, el) => {\n        clone.remove();\n        this.#childMap.delete(el);\n      });\n    }\n    #syncMediaChildAttribute(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === \"attributes\") {\n          const { target, attributeName } = mutation;\n          const clone = this.#childMap.get(target);\n          if (clone && attributeName) {\n            clone.setAttribute(attributeName, target.getAttribute(attributeName) ?? \"\");\n            this.#enableDefaultTrack(clone);\n          }\n        }\n      }\n    }\n    #enableDefaultTrack(trackEl) {\n      if (trackEl && trackEl.localName === \"track\" && trackEl.default && (trackEl.kind === \"chapters\" || trackEl.kind === \"metadata\") && trackEl.track.mode === \"disabled\") {\n        trackEl.track.mode = \"hidden\";\n      }\n    }\n    #upgradeProperty(prop) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        delete this[prop];\n        this[prop] = value;\n      }\n    }\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      this.#init();\n      this.#forwardAttribute(attrName, oldValue, newValue);\n    }\n    #forwardAttribute(attrName, _oldValue, newValue) {\n      if ([\"id\", \"class\"].includes(attrName)) return;\n      if (!CustomMedia.observedAttributes.includes(attrName) && this.constructor.observedAttributes.includes(attrName)) {\n        return;\n      }\n      if (newValue === null) {\n        this.nativeEl?.removeAttribute(attrName);\n      } else if (this.nativeEl?.getAttribute(attrName) !== newValue) {\n        this.nativeEl?.setAttribute(attrName, newValue);\n      }\n    }\n    connectedCallback() {\n      this.#init();\n    }\n  };\n}\nfunction getNativeElProps(nativeElTest) {\n  const nativeElProps = [];\n  for (let proto = Object.getPrototypeOf(nativeElTest); proto && proto !== HTMLElement.prototype; proto = Object.getPrototypeOf(proto)) {\n    const props = Object.getOwnPropertyNames(proto);\n    nativeElProps.push(...props);\n  }\n  return nativeElProps;\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    if (!Attributes.includes(key)) continue;\n    const value = attrs[key];\n    if (value === \"\") html += ` ${key}`;\n    else html += ` ${key}=\"${value}\"`;\n  }\n  return html;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  const obj = {};\n  for (const attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst CustomVideoElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"video\"\n});\nconst CustomAudioElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"audio\"\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/custom-media-element/dist/custom-media-element.js\n");

/***/ })

};
;