"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@mux";
exports.ids = ["vendor-chunks/@mux"];
exports.modules = {

/***/ "(ssr)/./node_modules/@mux/mux-player-react/dist/index.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@mux/mux-player-react/dist/index.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaxResolution: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_1__.MaxResolution),\n/* harmony export */   MediaError: () => (/* reexport safe */ _mux_mux_player__WEBPACK_IMPORTED_MODULE_2__.MediaError),\n/* harmony export */   MinResolution: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_1__.MinResolution),\n/* harmony export */   RenditionOrder: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_1__.RenditionOrder),\n/* harmony export */   \"default\": () => (/* binding */ ze),\n/* harmony export */   generatePlayerInitTime: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_1__.generatePlayerInitTime),\n/* harmony export */   playerSoftwareName: () => (/* binding */ fe),\n/* harmony export */   playerSoftwareVersion: () => (/* binding */ de)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _mux_playback_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mux/playback-core */ \"(ssr)/./node_modules/@mux/playback-core/dist/index.mjs\");\n/* harmony import */ var _mux_mux_player__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mux/mux-player */ \"(ssr)/./node_modules/@mux/mux-player/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ MaxResolution,MediaError,MinResolution,RenditionOrder,default,generatePlayerInitTime,playerSoftwareName,playerSoftwareVersion auto */ \n\n\n\nvar M = parseInt(react__WEBPACK_IMPORTED_MODULE_0__.version) >= 19, E = {\n    className: \"class\",\n    classname: \"class\",\n    htmlFor: \"for\",\n    crossOrigin: \"crossorigin\",\n    viewBox: \"viewBox\",\n    playsInline: \"playsinline\",\n    autoPlay: \"autoplay\",\n    playbackRate: \"playbackrate\"\n}, B = (e)=>e == null, ee = (e, t)=>B(t) ? !1 : e in t, te = (e)=>e.replace(/[A-Z]/g, (t)=>`-${t.toLowerCase()}`), ne = (e, t)=>{\n    if (!(!M && typeof t == \"boolean\" && !t)) {\n        if (ee(e, E)) return E[e];\n        if (typeof t != \"undefined\") return /[A-Z]/.test(e) ? te(e) : e;\n    }\n};\nvar ae = (e, t)=>!M && typeof e == \"boolean\" ? \"\" : e, P = (e = {})=>{\n    let { ref: t, ...n } = e;\n    return Object.entries(n).reduce((o, [a, l])=>{\n        let i = ne(a, l);\n        if (!i) return o;\n        let c = ae(l, a);\n        return o[i] = c, o;\n    }, {});\n};\n\nfunction x(e, t) {\n    if (typeof e == \"function\") return e(t);\n    e != null && (e.current = t);\n}\nfunction re(...e) {\n    return (t)=>{\n        let n = !1, o = e.map((a)=>{\n            let l = x(a, t);\n            return !n && typeof l == \"function\" && (n = !0), l;\n        });\n        if (n) return ()=>{\n            for(let a = 0; a < o.length; a++){\n                let l = o[a];\n                typeof l == \"function\" ? l() : x(e[a], null);\n            }\n        };\n    };\n}\nfunction f(...e) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(re(...e), e);\n}\n\nvar oe = Object.prototype.hasOwnProperty, ue = (e, t)=>{\n    if (Object.is(e, t)) return !0;\n    if (typeof e != \"object\" || e === null || typeof t != \"object\" || t === null) return !1;\n    if (Array.isArray(e)) return !Array.isArray(t) || e.length !== t.length ? !1 : e.some((a, l)=>t[l] === a);\n    let n = Object.keys(e), o = Object.keys(t);\n    if (n.length !== o.length) return !1;\n    for(let a = 0; a < n.length; a++)if (!oe.call(t, n[a]) || !Object.is(e[n[a]], t[n[a]])) return !1;\n    return !0;\n}, p = (e, t, n)=>!ue(t, e[n]), se = (e, t, n)=>{\n    e[n] = t;\n}, ie = (e, t, n, o = se, a = p)=>(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let l = n == null ? void 0 : n.current;\n        l && a(l, t, e) && o(l, t, e);\n    }, [\n        n == null ? void 0 : n.current,\n        t\n    ]), u = ie;\nvar ye = ()=>{\n    try {\n        return \"3.5.1\";\n    } catch  {}\n    return \"UNKNOWN\";\n}, me = ye(), g = ()=>me;\n\nvar r = (e, t, n)=>(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let o = t == null ? void 0 : t.current;\n        if (!o || !n) return;\n        let a = e, l = n;\n        return o.addEventListener(a, l), ()=>{\n            o.removeEventListener(a, l);\n        };\n    }, [\n        t == null ? void 0 : t.current,\n        n,\n        e\n    ]);\nvar Pe = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ children: e, ...t }, n)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"mux-player\", {\n        suppressHydrationWarning: !0,\n        ...P(t),\n        ref: n\n    }, e)), xe = (e, t)=>{\n    let { onAbort: n, onCanPlay: o, onCanPlayThrough: a, onEmptied: l, onLoadStart: i, onLoadedData: c, onLoadedMetadata: v, onProgress: R, onDurationChange: T, onVolumeChange: h, onRateChange: b, onResize: C, onWaiting: k, onPlay: O, onPlaying: S, onTimeUpdate: w, onPause: N, onSeeking: L, onSeeked: A, onStalled: I, onSuspend: _, onEnded: K, onError: H, onCuePointChange: D, onChapterChange: V, metadata: W, tokens: U, paused: z, playbackId: F, playbackRates: G, currentTime: Z, themeProps: j, extraSourceParams: q, castCustomData: J, _hlsConfig: Y, ...$ } = t;\n    return u(\"playbackRates\", G, e), u(\"metadata\", W, e), u(\"extraSourceParams\", q, e), u(\"_hlsConfig\", Y, e), u(\"themeProps\", j, e), u(\"tokens\", U, e), u(\"playbackId\", F, e), u(\"castCustomData\", J, e), u(\"paused\", z, e, (s, y)=>{\n        y != null && (y ? s.pause() : s.play());\n    }, (s, y, Q)=>s.hasAttribute(\"autoplay\") && !s.hasPlayed ? !1 : p(s, y, Q)), u(\"currentTime\", Z, e, (s, y)=>{\n        y != null && (s.currentTime = y);\n    }), r(\"abort\", e, n), r(\"canplay\", e, o), r(\"canplaythrough\", e, a), r(\"emptied\", e, l), r(\"loadstart\", e, i), r(\"loadeddata\", e, c), r(\"loadedmetadata\", e, v), r(\"progress\", e, R), r(\"durationchange\", e, T), r(\"volumechange\", e, h), r(\"ratechange\", e, b), r(\"resize\", e, C), r(\"waiting\", e, k), r(\"play\", e, O), r(\"playing\", e, S), r(\"timeupdate\", e, w), r(\"pause\", e, N), r(\"seeking\", e, L), r(\"seeked\", e, A), r(\"stalled\", e, I), r(\"suspend\", e, _), r(\"ended\", e, K), r(\"error\", e, H), r(\"cuepointchange\", e, D), r(\"chapterchange\", e, V), [\n        $\n    ];\n}, de = g(), fe = \"mux-player-react\", ge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((e, t)=>{\n    var i;\n    let n = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null), o = f(n, t), [a] = xe(n, e), [l] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)((i = e.playerInitTime) != null ? i : (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_1__.generatePlayerInitTime)());\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Pe, {\n        ref: o,\n        defaultHiddenCaptions: e.defaultHiddenCaptions,\n        playerSoftwareName: fe,\n        playerSoftwareVersion: de,\n        playerInitTime: l,\n        ...a\n    });\n}), ze = ge;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/mux-player-react/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@mux/mux-player/dist/base.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@mux/mux-player/dist/base.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaError: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.MediaError),\n/* harmony export */   \"default\": () => (/* binding */ Ei),\n/* harmony export */   generatePlayerInitTime: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.generatePlayerInitTime),\n/* harmony export */   getVideoAttribute: () => (/* binding */ U),\n/* harmony export */   playerSoftwareName: () => (/* binding */ xt),\n/* harmony export */   playerSoftwareVersion: () => (/* binding */ Rt)\n/* harmony export */ });\n/* harmony import */ var media_chrome__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! media-chrome */ \"(ssr)/./node_modules/media-chrome/dist/index.js\");\n/* harmony import */ var media_chrome_dist_media_container_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! media-chrome/dist/media-container.js */ \"(ssr)/./node_modules/media-chrome/dist/media-container.js\");\n/* harmony import */ var media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! media-chrome/dist/constants.js */ \"(ssr)/./node_modules/media-chrome/dist/constants.js\");\n/* harmony import */ var media_chrome_dist_experimental_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! media-chrome/dist/experimental/index.js */ \"(ssr)/./node_modules/media-chrome/dist/experimental/index.js\");\n/* harmony import */ var _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mux/mux-video/base */ \"(ssr)/./node_modules/@mux/mux-video/dist/base.mjs\");\n/* harmony import */ var _mux_playback_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mux/playback-core */ \"(ssr)/./node_modules/@mux/playback-core/dist/index.mjs\");\n/* harmony import */ var media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! media-chrome/dist/media-theme-element.js */ \"(ssr)/./node_modules/media-chrome/dist/media-theme-element.js\");\n/* harmony import */ var media_chrome_dist_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! media-chrome/dist/menu */ \"(ssr)/./node_modules/media-chrome/dist/menu/index.js\");\nvar qe=t=>{throw TypeError(t)};var he=(t,a,e)=>a.has(t)||qe(\"Cannot \"+e);var u=(t,a,e)=>(he(t,a,\"read from private field\"),e?e.call(t):a.get(t)),T=(t,a,e)=>a.has(t)?qe(\"Cannot add the same private member more than once\"):a instanceof WeakSet?a.add(t):a.set(t,e),C=(t,a,e,i)=>(he(t,a,\"write to private field\"),i?i.call(t,e):a.set(t,e),e),p=(t,a,e)=>(he(t,a,\"access private method\"),e);var $=class{addEventListener(){}removeEventListener(){}dispatchEvent(a){return!0}};if(typeof DocumentFragment==\"undefined\"){class t extends ${}globalThis.DocumentFragment=t}var G=class extends ${},ge=class extends ${},Pt={get(t){},define(t,a,e){},getName(t){return null},upgrade(t){},whenDefined(t){return Promise.resolve(G)}},j,fe=class{constructor(a,e={}){T(this,j);C(this,j,e==null?void 0:e.detail)}get detail(){return u(this,j)}initCustomEvent(){}};j=new WeakMap;function Dt(t,a){return new G}var Qe={document:{createElement:Dt},DocumentFragment,customElements:Pt,CustomEvent:fe,EventTarget:$,HTMLElement:G,HTMLVideoElement:ge},Je=typeof window==\"undefined\"||typeof globalThis.customElements==\"undefined\",k=Je?Qe:globalThis,Y=Je?Qe.document:globalThis.document;function et(t){let a=\"\";return Object.entries(t).forEach(([e,i])=>{i!=null&&(a+=`${re(e)}: ${i}; `)}),a?a.trim():void 0}function re(t){return t.replace(/([a-z])([A-Z])/g,\"$1-$2\").toLowerCase()}function oe(t){return t.replace(/[-_]([a-z])/g,(a,e)=>e.toUpperCase())}function y(t){if(t==null)return;let a=+t;return Number.isNaN(a)?void 0:a}function ye(t){let a=Ut(t).toString();return a?\"?\"+a:\"\"}function Ut(t){let a={};for(let e in t)t[e]!=null&&(a[e]=t[e]);return new URLSearchParams(a)}var ve=(t,a)=>!t||!a?!1:t.contains(a)?!0:ve(t,a.getRootNode().host);var at=\"mux.com\",Vt=()=>{try{return\"3.5.1\"}catch{}return\"UNKNOWN\"},Bt=Vt(),se=()=>Bt,it=(t,{token:a,customDomain:e=at,thumbnailTime:i,programTime:r}={})=>{var l;let n=a==null?i:void 0,{aud:d}=(l=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.parseJwt)(a))!=null?l:{};if(!(a&&d!==\"t\"))return`https://image.${e}/${t}/thumbnail.webp${ye({token:a,time:n,program_time:r})}`},rt=(t,{token:a,customDomain:e=at,programStartTime:i,programEndTime:r}={})=>{var d;let{aud:n}=(d=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.parseJwt)(a))!=null?d:{};if(!(a&&n!==\"s\"))return`https://image.${e}/${t}/storyboard.vtt${ye({token:a,format:\"webp\",program_start_time:i,program_end_time:r})}`},z=t=>{if(t){if([_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.ON_DEMAND].includes(t))return t;if(t!=null&&t.includes(\"live\"))return _mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE}};var Ht={crossorigin:\"crossOrigin\",playsinline:\"playsInline\"};function ot(t){var a;return(a=Ht[t])!=null?a:oe(t)}var P,D,v,ne=class{constructor(a,e){T(this,P);T(this,D);T(this,v,[]);C(this,P,a),C(this,D,e)}[Symbol.iterator](){return u(this,v).values()}get length(){return u(this,v).length}get value(){var a;return(a=u(this,v).join(\" \"))!=null?a:\"\"}set value(a){var e;a!==this.value&&(C(this,v,[]),this.add(...(e=a==null?void 0:a.split(\" \"))!=null?e:[]))}toString(){return this.value}item(a){return u(this,v)[a]}values(){return u(this,v).values()}keys(){return u(this,v).keys()}forEach(a){u(this,v).forEach(a)}add(...a){var e,i;a.forEach(r=>{this.contains(r)||u(this,v).push(r)}),!(this.value===\"\"&&!((e=u(this,P))!=null&&e.hasAttribute(`${u(this,D)}`)))&&((i=u(this,P))==null||i.setAttribute(`${u(this,D)}`,`${this.value}`))}remove(...a){var e;a.forEach(i=>{u(this,v).splice(u(this,v).indexOf(i),1)}),(e=u(this,P))==null||e.setAttribute(`${u(this,D)}`,`${this.value}`)}contains(a){return u(this,v).includes(a)}toggle(a,e){return typeof e!=\"undefined\"?e?(this.add(a),!0):(this.remove(a),!1):this.contains(a)?(this.remove(a),!1):(this.add(a),!0)}replace(a,e){this.remove(a),this.add(e)}};P=new WeakMap,D=new WeakMap,v=new WeakMap;var nt=`[mux-player ${se()}]`;function x(...t){console.warn(nt,...t)}function E(...t){console.error(nt,...t)}function Ee(t){var e;let a=(e=t.message)!=null?e:\"\";t.context&&(a+=` ${t.context}`),t.file&&(a+=` ${(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Read more: \")}\nhttps://github.com/muxinc/elements/blob/main/errors/${t.file}`),x(a)}var g={AUTOPLAY:\"autoplay\",CROSSORIGIN:\"crossorigin\",LOOP:\"loop\",MUTED:\"muted\",PLAYSINLINE:\"playsinline\",PRELOAD:\"preload\"},N={VOLUME:\"volume\",PLAYBACKRATE:\"playbackrate\",MUTED:\"muted\"},Va={...g,...N},dt=Object.freeze({length:0,start(t){let a=t>>>0;if(a>=this.length)throw new DOMException(`Failed to execute 'start' on 'TimeRanges': The index provided (${a}) is greater than or equal to the maximum bound (${this.length}).`);return 0},end(t){let a=t>>>0;if(a>=this.length)throw new DOMException(`Failed to execute 'end' on 'TimeRanges': The index provided (${a}) is greater than or equal to the maximum bound (${this.length}).`);return 0}}),$t=Object.values(g).filter(t=>g.PLAYSINLINE!==t),Yt=Object.values(N),Ft=[...$t,...Yt],Ae=class extends k.HTMLElement{static get observedAttributes(){return Ft}constructor(){super()}attributeChangedCallback(a,e,i){var r,n;switch(a){case N.MUTED:{this.media&&(this.media.muted=i!=null,this.media.defaultMuted=i!=null);return}case N.VOLUME:{let d=(r=y(i))!=null?r:1;this.media&&(this.media.volume=d);return}case N.PLAYBACKRATE:{let d=(n=y(i))!=null?n:1;this.media&&(this.media.playbackRate=d,this.media.defaultPlaybackRate=d);return}}}play(){var a,e;return(e=(a=this.media)==null?void 0:a.play())!=null?e:Promise.reject()}pause(){var a;(a=this.media)==null||a.pause()}load(){var a;(a=this.media)==null||a.load()}get media(){var a;return(a=this.shadowRoot)==null?void 0:a.querySelector(\"mux-video\")}get audioTracks(){return this.media.audioTracks}get videoTracks(){return this.media.videoTracks}get audioRenditions(){return this.media.audioRenditions}get videoRenditions(){return this.media.videoRenditions}get paused(){var a,e;return(e=(a=this.media)==null?void 0:a.paused)!=null?e:!0}get duration(){var a,e;return(e=(a=this.media)==null?void 0:a.duration)!=null?e:NaN}get ended(){var a,e;return(e=(a=this.media)==null?void 0:a.ended)!=null?e:!1}get buffered(){var a,e;return(e=(a=this.media)==null?void 0:a.buffered)!=null?e:dt}get seekable(){var a,e;return(e=(a=this.media)==null?void 0:a.seekable)!=null?e:dt}get readyState(){var a,e;return(e=(a=this.media)==null?void 0:a.readyState)!=null?e:0}get videoWidth(){var a,e;return(e=(a=this.media)==null?void 0:a.videoWidth)!=null?e:0}get videoHeight(){var a,e;return(e=(a=this.media)==null?void 0:a.videoHeight)!=null?e:0}get currentSrc(){var a,e;return(e=(a=this.media)==null?void 0:a.currentSrc)!=null?e:\"\"}get currentTime(){var a,e;return(e=(a=this.media)==null?void 0:a.currentTime)!=null?e:0}set currentTime(a){this.media&&(this.media.currentTime=Number(a))}get volume(){var a,e;return(e=(a=this.media)==null?void 0:a.volume)!=null?e:1}set volume(a){this.media&&(this.media.volume=Number(a))}get playbackRate(){var a,e;return(e=(a=this.media)==null?void 0:a.playbackRate)!=null?e:1}set playbackRate(a){this.media&&(this.media.playbackRate=Number(a))}get defaultPlaybackRate(){var a;return(a=y(this.getAttribute(N.PLAYBACKRATE)))!=null?a:1}set defaultPlaybackRate(a){a!=null?this.setAttribute(N.PLAYBACKRATE,`${a}`):this.removeAttribute(N.PLAYBACKRATE)}get crossOrigin(){return X(this,g.CROSSORIGIN)}set crossOrigin(a){this.setAttribute(g.CROSSORIGIN,`${a}`)}get autoplay(){return X(this,g.AUTOPLAY)!=null}set autoplay(a){a?this.setAttribute(g.AUTOPLAY,typeof a==\"string\"?a:\"\"):this.removeAttribute(g.AUTOPLAY)}get loop(){return X(this,g.LOOP)!=null}set loop(a){a?this.setAttribute(g.LOOP,\"\"):this.removeAttribute(g.LOOP)}get muted(){var a,e;return(e=(a=this.media)==null?void 0:a.muted)!=null?e:!1}set muted(a){this.media&&(this.media.muted=!!a)}get defaultMuted(){return X(this,g.MUTED)!=null}set defaultMuted(a){a?this.setAttribute(g.MUTED,\"\"):this.removeAttribute(g.MUTED)}get playsInline(){return X(this,g.PLAYSINLINE)!=null}set playsInline(a){E(\"playsInline is set to true by default and is not currently supported as a setter.\")}get preload(){return this.media?this.media.preload:this.getAttribute(\"preload\")}set preload(a){[\"\",\"none\",\"metadata\",\"auto\"].includes(a)?this.setAttribute(g.PRELOAD,a):this.removeAttribute(g.PRELOAD)}};function X(t,a){return t.media?t.media.getAttribute(a):t.getAttribute(a)}var Ce=Ae;var lt=`:host {\n  --media-control-display: var(--controls);\n  --media-loading-indicator-display: var(--loading-indicator);\n  --media-dialog-display: var(--dialog);\n  --media-play-button-display: var(--play-button);\n  --media-live-button-display: var(--live-button);\n  --media-seek-backward-button-display: var(--seek-backward-button);\n  --media-seek-forward-button-display: var(--seek-forward-button);\n  --media-mute-button-display: var(--mute-button);\n  --media-captions-button-display: var(--captions-button);\n  --media-captions-menu-button-display: var(--captions-menu-button, var(--media-captions-button-display));\n  --media-rendition-menu-button-display: var(--rendition-menu-button);\n  --media-audio-track-menu-button-display: var(--audio-track-menu-button);\n  --media-airplay-button-display: var(--airplay-button);\n  --media-pip-button-display: var(--pip-button);\n  --media-fullscreen-button-display: var(--fullscreen-button);\n  --media-cast-button-display: var(--cast-button, var(--_cast-button-drm-display));\n  --media-playback-rate-button-display: var(--playback-rate-button);\n  --media-playback-rate-menu-button-display: var(--playback-rate-menu-button);\n  --media-volume-range-display: var(--volume-range);\n  --media-time-range-display: var(--time-range);\n  --media-time-display-display: var(--time-display);\n  --media-duration-display-display: var(--duration-display);\n  --media-title-display-display: var(--title-display);\n\n  display: inline-block;\n  line-height: 0;\n  width: 100%;\n}\n\na {\n  color: #fff;\n  font-size: 0.9em;\n  text-decoration: underline;\n}\n\nmedia-theme {\n  display: inline-block;\n  line-height: 0;\n  width: 100%;\n  height: 100%;\n  direction: ltr;\n}\n\nmedia-poster-image {\n  display: inline-block;\n  line-height: 0;\n  width: 100%;\n  height: 100%;\n}\n\nmedia-poster-image:not([src]):not([placeholdersrc]) {\n  display: none;\n}\n\n::part(top),\n[part~='top'] {\n  --media-control-display: var(--controls, var(--top-controls));\n  --media-play-button-display: var(--play-button, var(--top-play-button));\n  --media-live-button-display: var(--live-button, var(--top-live-button));\n  --media-seek-backward-button-display: var(--seek-backward-button, var(--top-seek-backward-button));\n  --media-seek-forward-button-display: var(--seek-forward-button, var(--top-seek-forward-button));\n  --media-mute-button-display: var(--mute-button, var(--top-mute-button));\n  --media-captions-button-display: var(--captions-button, var(--top-captions-button));\n  --media-captions-menu-button-display: var(\n    --captions-menu-button,\n    var(--media-captions-button-display, var(--top-captions-menu-button))\n  );\n  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--top-rendition-menu-button));\n  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--top-audio-track-menu-button));\n  --media-airplay-button-display: var(--airplay-button, var(--top-airplay-button));\n  --media-pip-button-display: var(--pip-button, var(--top-pip-button));\n  --media-fullscreen-button-display: var(--fullscreen-button, var(--top-fullscreen-button));\n  --media-cast-button-display: var(--cast-button, var(--top-cast-button, var(--_cast-button-drm-display)));\n  --media-playback-rate-button-display: var(--playback-rate-button, var(--top-playback-rate-button));\n  --media-playback-rate-menu-button-display: var(\n    --captions-menu-button,\n    var(--media-playback-rate-button-display, var(--top-playback-rate-menu-button))\n  );\n  --media-volume-range-display: var(--volume-range, var(--top-volume-range));\n  --media-time-range-display: var(--time-range, var(--top-time-range));\n  --media-time-display-display: var(--time-display, var(--top-time-display));\n  --media-duration-display-display: var(--duration-display, var(--top-duration-display));\n  --media-title-display-display: var(--title-display, var(--top-title-display));\n}\n\n::part(center),\n[part~='center'] {\n  --media-control-display: var(--controls, var(--center-controls));\n  --media-play-button-display: var(--play-button, var(--center-play-button));\n  --media-live-button-display: var(--live-button, var(--center-live-button));\n  --media-seek-backward-button-display: var(--seek-backward-button, var(--center-seek-backward-button));\n  --media-seek-forward-button-display: var(--seek-forward-button, var(--center-seek-forward-button));\n  --media-mute-button-display: var(--mute-button, var(--center-mute-button));\n  --media-captions-button-display: var(--captions-button, var(--center-captions-button));\n  --media-captions-menu-button-display: var(\n    --captions-menu-button,\n    var(--media-captions-button-display, var(--center-captions-menu-button))\n  );\n  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--center-rendition-menu-button));\n  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--center-audio-track-menu-button));\n  --media-airplay-button-display: var(--airplay-button, var(--center-airplay-button));\n  --media-pip-button-display: var(--pip-button, var(--center-pip-button));\n  --media-fullscreen-button-display: var(--fullscreen-button, var(--center-fullscreen-button));\n  --media-cast-button-display: var(--cast-button, var(--center-cast-button, var(--_cast-button-drm-display)));\n  --media-playback-rate-button-display: var(--playback-rate-button, var(--center-playback-rate-button));\n  --media-playback-rate-menu-button-display: var(\n    --playback-rate-menu-button,\n    var(--media-playback-rate-button-display, var(--center-playback-rate-menu-button))\n  );\n  --media-volume-range-display: var(--volume-range, var(--center-volume-range));\n  --media-time-range-display: var(--time-range, var(--center-time-range));\n  --media-time-display-display: var(--time-display, var(--center-time-display));\n  --media-duration-display-display: var(--duration-display, var(--center-duration-display));\n}\n\n::part(bottom),\n[part~='bottom'] {\n  --media-control-display: var(--controls, var(--bottom-controls));\n  --media-play-button-display: var(--play-button, var(--bottom-play-button));\n  --media-live-button-display: var(--live-button, var(--bottom-live-button));\n  --media-seek-backward-button-display: var(--seek-backward-button, var(--bottom-seek-backward-button));\n  --media-seek-forward-button-display: var(--seek-forward-button, var(--bottom-seek-forward-button));\n  --media-mute-button-display: var(--mute-button, var(--bottom-mute-button));\n  --media-captions-button-display: var(--captions-button, var(--bottom-captions-button));\n  --media-captions-menu-button-display: var(\n    --captions-menu-button,\n    var(--media-captions-button-display, var(--bottom-captions-menu-button))\n  );\n  --media-rendition-menu-button-display: var(--rendition-menu-button, var(--bottom-rendition-menu-button));\n  --media-audio-track-menu-button-display: var(--audio-track-menu-button, var(--bottom-audio-track-menu-button));\n  --media-airplay-button-display: var(--airplay-button, var(--bottom-airplay-button));\n  --media-pip-button-display: var(--pip-button, var(--bottom-pip-button));\n  --media-fullscreen-button-display: var(--fullscreen-button, var(--bottom-fullscreen-button));\n  --media-cast-button-display: var(--cast-button, var(--bottom-cast-button, var(--_cast-button-drm-display)));\n  --media-playback-rate-button-display: var(--playback-rate-button, var(--bottom-playback-rate-button));\n  --media-playback-rate-menu-button-display: var(\n    --playback-rate-menu-button,\n    var(--media-playback-rate-button-display, var(--bottom-playback-rate-menu-button))\n  );\n  --media-volume-range-display: var(--volume-range, var(--bottom-volume-range));\n  --media-time-range-display: var(--time-range, var(--bottom-time-range));\n  --media-time-display-display: var(--time-display, var(--bottom-time-display));\n  --media-duration-display-display: var(--duration-display, var(--bottom-duration-display));\n  --media-title-display-display: var(--title-display, var(--bottom-title-display));\n}\n\n:host([no-tooltips]) {\n  --media-tooltip-display: none;\n}\n`;var q=new WeakMap,_e=class t{constructor(a,e){this.element=a;this.type=e;this.element.addEventListener(this.type,this);let i=q.get(this.element);i&&i.set(this.type,this)}set(a){if(typeof a==\"function\")this.handleEvent=a.bind(this.element);else if(typeof a==\"object\"&&typeof a.handleEvent==\"function\")this.handleEvent=a.handleEvent.bind(a);else{this.element.removeEventListener(this.type,this);let e=q.get(this.element);e&&e.delete(this.type)}}static for(a){q.has(a.element)||q.set(a.element,new Map);let e=a.attributeName.slice(2),i=q.get(a.element);return i&&i.has(e)?i.get(e):new t(a.element,e)}};function Gt(t,a){return t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.AttrPart&&t.attributeName.startsWith(\"on\")?(_e.for(t).set(a),t.element.removeAttributeNS(t.attributeNamespace,t.attributeName),!0):!1}function jt(t,a){return a instanceof de&&t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.ChildNodePart?(a.renderInto(t),!0):!1}function zt(t,a){return a instanceof DocumentFragment&&t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.ChildNodePart?(a.childNodes.length&&t.replace(...a.childNodes),!0):!1}function Xt(t,a){if(t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.AttrPart){let e=t.attributeNamespace,i=t.element.getAttributeNS(e,t.attributeName);return String(a)!==i&&(t.value=String(a)),!0}return t.value=String(a),!0}function qt(t,a){if(t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.AttrPart&&a instanceof Element){let e=t.element;return e[t.attributeName]!==a&&(t.element.removeAttributeNS(t.attributeNamespace,t.attributeName),e[t.attributeName]=a),!0}return!1}function Qt(t,a){if(typeof a==\"boolean\"&&t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.AttrPart){let e=t.attributeNamespace,i=t.element.hasAttributeNS(e,t.attributeName);return a!==i&&(t.booleanValue=a),!0}return!1}function Jt(t,a){return a===!1&&t instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.ChildNodePart?(t.replace(\"\"),!0):!1}function ea(t,a){qt(t,a)||Qt(t,a)||Gt(t,a)||Jt(t,a)||jt(t,a)||zt(t,a)||Xt(t,a)}var ke=new Map,ut=new WeakMap,mt=new WeakMap,de=class{constructor(a,e,i){this.strings=a;this.values=e;this.processor=i;this.stringsKey=this.strings.join(\"\u0001\")}get template(){if(ke.has(this.stringsKey))return ke.get(this.stringsKey);{let a=Y.createElement(\"template\"),e=this.strings.length-1;return a.innerHTML=this.strings.reduce((i,r,n)=>i+r+(n<e?`{{ ${n} }}`:\"\"),\"\"),ke.set(this.stringsKey,a),a}}renderInto(a){var r;let e=this.template;if(ut.get(a)!==e){ut.set(a,e);let n=new media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.TemplateInstance(e,this.values,this.processor);mt.set(a,n),a instanceof media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.ChildNodePart?a.replace(...n.children):a.appendChild(n);return}let i=mt.get(a);(r=i==null?void 0:i.update)==null||r.call(i,this.values)}},ta={processCallback(t,a,e){var i;if(e){for(let[r,n]of a)if(r in e){let d=(i=e[r])!=null?i:\"\";ea(n,d)}}}};function Q(t,...a){return new de(t,a,ta)}function ct(t,a){t.renderInto(a)}var ia=t=>{let{tokens:a}=t;return a.drm?\":host(:not([cast-receiver])) { --_cast-button-drm-display: none; }\":\"\"},bt=t=>Q`\n  <style>\n    ${ia(t)}\n    ${lt}\n  </style>\n  ${sa(t)}\n`,ra=t=>{let a=t.hotKeys?`${t.hotKeys}`:\"\";return z(t.streamType)===\"live\"&&(a+=\" noarrowleft noarrowright\"),a},oa={TOP:\"top\",CENTER:\"center\",BOTTOM:\"bottom\",LAYER:\"layer\",MEDIA_LAYER:\"media-layer\",POSTER_LAYER:\"poster-layer\",VERTICAL_LAYER:\"vertical-layer\",CENTERED_LAYER:\"centered-layer\",GESTURE_LAYER:\"gesture-layer\",CONTROLLER_LAYER:\"controller\",BUTTON:\"button\",RANGE:\"range\",DISPLAY:\"display\",CONTROL_BAR:\"control-bar\",MENU_BUTTON:\"menu-button\",MENU:\"menu\",OPTION:\"option\",POSTER:\"poster\",LIVE:\"live\",PLAY:\"play\",PRE_PLAY:\"pre-play\",SEEK_BACKWARD:\"seek-backward\",SEEK_FORWARD:\"seek-forward\",MUTE:\"mute\",CAPTIONS:\"captions\",AIRPLAY:\"airplay\",PIP:\"pip\",FULLSCREEN:\"fullscreen\",CAST:\"cast\",PLAYBACK_RATE:\"playback-rate\",VOLUME:\"volume\",TIME:\"time\",TITLE:\"title\",AUDIO_TRACK:\"audio-track\",RENDITION:\"rendition\"},na=Object.values(oa).join(\", \"),sa=t=>{var a,e,i,r,n,d,l,b,S,F,_,A,R,K,h,ie,W,Z,Ie,Pe,De,Ue,Ve,Be,He,Ke,$e,Ye,Fe,We,Ze,Ge,je,ze,Xe;return Q`\n  <media-theme\n    template=\"${t.themeTemplate||!1}\"\n    defaultstreamtype=\"${(a=t.defaultStreamType)!=null?a:!1}\"\n    hotkeys=\"${ra(t)||!1}\"\n    nohotkeys=\"${t.noHotKeys||!t.hasSrc||!1}\"\n    noautoseektolive=\"${!!((e=t.streamType)!=null&&e.includes(_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE))&&t.targetLiveWindow!==0}\"\n    novolumepref=\"${t.novolumepref||!1}\"\n    disabled=\"${!t.hasSrc||t.isDialogOpen}\"\n    audio=\"${(i=t.audio)!=null?i:!1}\"\n    style=\"${(r=et({\"--media-primary-color\":t.primaryColor,\"--media-secondary-color\":t.secondaryColor,\"--media-accent-color\":t.accentColor}))!=null?r:!1}\"\n    defaultsubtitles=\"${!t.defaultHiddenCaptions}\"\n    forwardseekoffset=\"${(n=t.forwardSeekOffset)!=null?n:!1}\"\n    backwardseekoffset=\"${(d=t.backwardSeekOffset)!=null?d:!1}\"\n    playbackrates=\"${(l=t.playbackRates)!=null?l:!1}\"\n    defaultshowremainingtime=\"${(b=t.defaultShowRemainingTime)!=null?b:!1}\"\n    defaultduration=\"${(S=t.defaultDuration)!=null?S:!1}\"\n    hideduration=\"${(F=t.hideDuration)!=null?F:!1}\"\n    title=\"${(_=t.title)!=null?_:!1}\"\n    videotitle=\"${(A=t.videoTitle)!=null?A:!1}\"\n    proudlydisplaymuxbadge=\"${(R=t.proudlyDisplayMuxBadge)!=null?R:!1}\"\n    exportparts=\"${na}\"\n    onclose=\"${t.onCloseErrorDialog}\"\n    onfocusin=\"${t.onFocusInErrorDialog}\"\n  >\n    <mux-video\n      slot=\"media\"\n      target-live-window=\"${(K=t.targetLiveWindow)!=null?K:!1}\"\n      stream-type=\"${(h=z(t.streamType))!=null?h:!1}\"\n      crossorigin=\"${(ie=t.crossOrigin)!=null?ie:\"\"}\"\n      playsinline\n      autoplay=\"${(W=t.autoplay)!=null?W:!1}\"\n      muted=\"${(Z=t.muted)!=null?Z:!1}\"\n      loop=\"${(Ie=t.loop)!=null?Ie:!1}\"\n      preload=\"${(Pe=t.preload)!=null?Pe:!1}\"\n      debug=\"${(De=t.debug)!=null?De:!1}\"\n      prefer-cmcd=\"${(Ue=t.preferCmcd)!=null?Ue:!1}\"\n      disable-tracking=\"${(Ve=t.disableTracking)!=null?Ve:!1}\"\n      disable-cookies=\"${(Be=t.disableCookies)!=null?Be:!1}\"\n      prefer-playback=\"${(He=t.preferPlayback)!=null?He:!1}\"\n      start-time=\"${t.startTime!=null?t.startTime:!1}\"\n      beacon-collection-domain=\"${(Ke=t.beaconCollectionDomain)!=null?Ke:!1}\"\n      player-init-time=\"${($e=t.playerInitTime)!=null?$e:!1}\"\n      player-software-name=\"${(Ye=t.playerSoftwareName)!=null?Ye:!1}\"\n      player-software-version=\"${(Fe=t.playerSoftwareVersion)!=null?Fe:!1}\"\n      env-key=\"${(We=t.envKey)!=null?We:!1}\"\n      custom-domain=\"${(Ze=t.customDomain)!=null?Ze:!1}\"\n      src=\"${t.src?t.src:t.playbackId?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.toMuxVideoURL)(t):!1}\"\n      cast-src=\"${t.src?t.src:t.playbackId?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.toMuxVideoURL)(t):!1}\"\n      cast-receiver=\"${(Ge=t.castReceiver)!=null?Ge:!1}\"\n      drm-token=\"${(ze=(je=t.tokens)==null?void 0:je.drm)!=null?ze:!1}\"\n      exportparts=\"video\"\n    >\n      ${t.storyboard?Q`<track label=\"thumbnails\" default kind=\"metadata\" src=\"${t.storyboard}\" />`:Q``}\n      <slot></slot>\n    </mux-video>\n    <slot name=\"poster\" slot=\"poster\">\n      <media-poster-image\n        part=\"poster\"\n        exportparts=\"poster, img\"\n        src=\"${t.poster?t.poster:!1}\"\n        placeholdersrc=\"${(Xe=t.placeholder)!=null?Xe:!1}\"\n      ></media-poster-image>\n    </slot>\n  </media-theme>\n`};var ft=t=>t.charAt(0).toUpperCase()+t.slice(1),da=(t,a=!1)=>{var e,i;if(t.muxCode){let r=ft((e=t.errorCategory)!=null?e:\"video\"),n=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.errorCategoryToTokenNameOrPrefix)((i=t.errorCategory)!=null?i:_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCategory.VIDEO);if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_OFFLINE)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Your device appears to be offline\",a);if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_EXPIRED)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"{category} URL has expired\",a).format({category:r});if([_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MALFORMED].includes(t.muxCode))return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"{category} URL is formatted incorrectly\",a).format({category:r});if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MISSING)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Invalid {categoryName} URL\",a).format({categoryName:n});if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_NOT_FOUND)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"{category} does not exist\",a).format({category:r});if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_NOT_READY){let d=t.streamType===\"live\"?\"Live stream\":\"Video\";return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"{mediaType} is not currently available\",a).format({mediaType:d})}}if(t.code){if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_NETWORK)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Network Error\",a);if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_DECODE)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Media Error\",a);if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Source Not Supported\",a)}return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Error\",a)},la=(t,a=!1)=>{var e,i;if(t.muxCode){let r=ft((e=t.errorCategory)!=null?e:\"video\"),n=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.errorCategoryToTokenNameOrPrefix)((i=t.errorCategory)!=null?i:_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCategory.VIDEO);return t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_OFFLINE?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Check your internet connection and try reloading this video.\",a):t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_EXPIRED?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The video\\u2019s secured {tokenNamePrefix}-token has expired.\",a).format({tokenNamePrefix:n}):t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The video\\u2019s playback ID does not match the one encoded in the {tokenNamePrefix}-token.\",a).format({tokenNamePrefix:n}):t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MALFORMED?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"{category} URL is formatted incorrectly\",a).format({category:r}):[_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING].includes(t.muxCode)?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The {tokenNamePrefix}-token is formatted with incorrect information.\",a).format({tokenNamePrefix:n}):[_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MISSING,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_INVALID_URL].includes(t.muxCode)?(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The video URL or {tokenNamePrefix}-token are formatted with incorrect or incomplete information.\",a).format({tokenNamePrefix:n}):t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_NOT_FOUND?\"\":t.message}return t.code&&(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_NETWORK||t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_DECODE||t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED),t.message},yt=(t,a=!1)=>{let e=da(t,a).toString(),i=la(t,a).toString();return{title:e,message:i}},ua=t=>{if(t.muxCode){if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_EXPIRED)return\"403-expired-token.md\";if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MALFORMED)return\"403-malformatted-token.md\";if([_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISMATCH,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_AUD_MISSING].includes(t.muxCode))return\"403-incorrect-aud-value.md\";if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_SUB_MISMATCH)return\"403-playback-id-mismatch.md\";if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_TOKEN_MISSING)return\"missing-signed-tokens.md\";if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_NOT_FOUND)return\"404-not-found.md\";if(t.muxCode===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxErrorCode.NETWORK_NOT_READY)return\"412-not-playable.md\"}if(t.code){if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_NETWORK)return\"\";if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_DECODE)return\"media-decode-error.md\";if(t.code===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED)return\"media-src-not-supported.md\"}return\"\"},Re=(t,a)=>{let e=ua(t);return{message:t.message,context:t.context,file:e}};var vt=`<template id=\"media-theme-gerwig\">\n  <style>\n    @keyframes pre-play-hide {\n      0% {\n        transform: scale(1);\n        opacity: 1;\n      }\n\n      30% {\n        transform: scale(0.7);\n      }\n\n      100% {\n        transform: scale(1.5);\n        opacity: 0;\n      }\n    }\n\n    :host {\n      --_primary-color: var(--media-primary-color, #fff);\n      --_secondary-color: var(--media-secondary-color, transparent);\n      --_accent-color: var(--media-accent-color, #fa50b5);\n      --_text-color: var(--media-text-color, #000);\n\n      --media-icon-color: var(--_primary-color);\n      --media-control-background: var(--_secondary-color);\n      --media-control-hover-background: var(--_accent-color);\n      --media-time-buffered-color: rgba(255, 255, 255, 0.4);\n      --media-preview-time-text-shadow: none;\n      --media-control-height: 14px;\n      --media-control-padding: 6px;\n      --media-tooltip-container-margin: 6px;\n      --media-tooltip-distance: 18px;\n\n      color: var(--_primary-color);\n      display: inline-block;\n      width: 100%;\n      height: 100%;\n    }\n\n    :host([audio]) {\n      --_secondary-color: var(--media-secondary-color, black);\n      --media-preview-time-text-shadow: none;\n    }\n\n    :host([audio]) ::slotted([slot='media']) {\n      height: 0px;\n    }\n\n    :host([audio]) media-loading-indicator {\n      display: none;\n    }\n\n    :host([audio]) media-controller {\n      background: transparent;\n    }\n\n    :host([audio]) media-controller::part(vertical-layer) {\n      background: transparent;\n    }\n\n    :host([audio]) media-control-bar {\n      width: 100%;\n      background-color: var(--media-control-background);\n    }\n\n    /*\n     * 0.433s is the transition duration for VTT Regions.\n     * Borrowed here, so the captions don't move too fast.\n     */\n    media-controller {\n      --media-webkit-text-track-transform: translateY(0) scale(0.98);\n      --media-webkit-text-track-transition: transform 0.433s ease-out 0.3s;\n    }\n    media-controller:is([mediapaused], :not([userinactive])) {\n      --media-webkit-text-track-transform: translateY(-50px) scale(0.98);\n      --media-webkit-text-track-transition: transform 0.15s ease;\n    }\n\n    /*\n     * CSS specific to iOS devices.\n     * See: https://stackoverflow.com/questions/30102792/css-media-query-to-target-only-ios-devices/60220757#60220757\n     */\n    @supports (-webkit-touch-callout: none) {\n      /* Disable subtitle adjusting for iOS Safari */\n      media-controller[mediaisfullscreen] {\n        --media-webkit-text-track-transform: unset;\n        --media-webkit-text-track-transition: unset;\n      }\n    }\n\n    media-time-range {\n      --media-box-padding-left: 6px;\n      --media-box-padding-right: 6px;\n      --media-range-bar-color: var(--_accent-color);\n      --media-time-range-buffered-color: var(--_primary-color);\n      --media-range-track-color: transparent;\n      --media-range-track-background: rgba(255, 255, 255, 0.4);\n      --media-range-thumb-background: radial-gradient(\n        circle,\n        #000 0%,\n        #000 25%,\n        var(--_accent-color) 25%,\n        var(--_accent-color)\n      );\n      --media-range-thumb-width: 12px;\n      --media-range-thumb-height: 12px;\n      --media-range-thumb-transform: scale(0);\n      --media-range-thumb-transition: transform 0.3s;\n      --media-range-thumb-opacity: 1;\n      --media-preview-background: var(--_primary-color);\n      --media-box-arrow-background: var(--_primary-color);\n      --media-preview-thumbnail-border: 5px solid var(--_primary-color);\n      --media-preview-border-radius: 5px;\n      --media-text-color: var(--_text-color);\n      --media-control-hover-background: transparent;\n      --media-preview-chapter-text-shadow: none;\n      color: var(--_accent-color);\n      padding: 0 6px;\n    }\n\n    :host([audio]) media-time-range {\n      --media-preview-time-padding: 1.5px 6px;\n      --media-preview-box-margin: 0 0 -5px;\n    }\n\n    media-time-range:hover {\n      --media-range-thumb-transform: scale(1);\n    }\n\n    media-preview-thumbnail {\n      border-bottom-width: 0;\n    }\n\n    [part~='menu'] {\n      border-radius: 2px;\n      border: 1px solid rgba(0, 0, 0, 0.1);\n      bottom: 50px;\n      padding: 2.5px 10px;\n    }\n\n    [part~='menu']::part(indicator) {\n      fill: var(--_accent-color);\n    }\n\n    [part~='menu']::part(menu-item) {\n      box-sizing: border-box;\n      display: flex;\n      align-items: center;\n      padding: 6px 10px;\n      min-height: 34px;\n    }\n\n    [part~='menu']::part(checked) {\n      font-weight: 700;\n    }\n\n    media-captions-menu,\n    media-rendition-menu,\n    media-audio-track-menu,\n    media-playback-rate-menu {\n      position: absolute; /* ensure they don't take up space in DOM on load */\n      --media-menu-background: var(--_primary-color);\n      --media-menu-item-checked-background: transparent;\n      --media-text-color: var(--_text-color);\n      --media-menu-item-hover-background: transparent;\n      --media-menu-item-hover-outline: var(--_accent-color) solid 1px;\n    }\n\n    media-rendition-menu {\n      min-width: 140px;\n    }\n\n    /* The icon is a circle so make it 16px high instead of 14px for more balance. */\n    media-audio-track-menu-button {\n      --media-control-padding: 5px;\n      --media-control-height: 16px;\n    }\n\n    media-playback-rate-menu-button {\n      --media-control-padding: 6px 3px;\n      min-width: 4.4ch;\n    }\n\n    media-playback-rate-menu {\n      --media-menu-flex-direction: row;\n      --media-menu-item-checked-background: var(--_accent-color);\n      --media-menu-item-checked-indicator-display: none;\n      margin-right: 6px;\n      padding: 0;\n      --media-menu-gap: 0.25em;\n    }\n\n    media-playback-rate-menu[part~='menu']::part(menu-item) {\n      padding: 6px 6px 6px 8px;\n    }\n\n    media-playback-rate-menu[part~='menu']::part(checked) {\n      color: #fff;\n    }\n\n    :host(:not([audio])) media-time-range {\n      /* Adding px is required here for calc() */\n      --media-range-padding: 0px;\n      background: transparent;\n      z-index: 10;\n      height: 10px;\n      bottom: -3px;\n      width: 100%;\n    }\n\n    media-control-bar :is([role='button'], [role='switch'], button) {\n      line-height: 0;\n    }\n\n    media-control-bar :is([part*='button'], [part*='range'], [part*='display']) {\n      border-radius: 3px;\n    }\n\n    .spacer {\n      flex-grow: 1;\n      background-color: var(--media-control-background, rgba(20, 20, 30, 0.7));\n    }\n\n    media-control-bar[slot~='top-chrome'] {\n      min-height: 42px;\n      pointer-events: none;\n    }\n\n    media-control-bar {\n      --gradient-steps:\n        hsl(0 0% 0% / 0) 0%, hsl(0 0% 0% / 0.013) 8.1%, hsl(0 0% 0% / 0.049) 15.5%, hsl(0 0% 0% / 0.104) 22.5%,\n        hsl(0 0% 0% / 0.175) 29%, hsl(0 0% 0% / 0.259) 35.3%, hsl(0 0% 0% / 0.352) 41.2%, hsl(0 0% 0% / 0.45) 47.1%,\n        hsl(0 0% 0% / 0.55) 52.9%, hsl(0 0% 0% / 0.648) 58.8%, hsl(0 0% 0% / 0.741) 64.7%, hsl(0 0% 0% / 0.825) 71%,\n        hsl(0 0% 0% / 0.896) 77.5%, hsl(0 0% 0% / 0.951) 84.5%, hsl(0 0% 0% / 0.987) 91.9%, hsl(0 0% 0%) 100%;\n    }\n\n    :host([title]:not([audio])) media-control-bar[slot='top-chrome']::before {\n      content: '';\n      position: absolute;\n      width: 100%;\n      padding-bottom: min(100px, 25%);\n      background: linear-gradient(to top, var(--gradient-steps));\n      opacity: 0.8;\n      pointer-events: none;\n    }\n\n    :host(:not([audio])) media-control-bar[part~='bottom']::before {\n      content: '';\n      position: absolute;\n      width: 100%;\n      bottom: 0;\n      left: 0;\n      padding-bottom: min(100px, 25%);\n      background: linear-gradient(to bottom, var(--gradient-steps));\n      opacity: 0.8;\n      z-index: 1;\n      pointer-events: none;\n    }\n\n    media-control-bar[part~='bottom'] > * {\n      z-index: 20;\n    }\n\n    media-control-bar[part~='bottom'] {\n      padding: 6px 6px;\n    }\n\n    media-control-bar[slot~='top-chrome'] > * {\n      --media-control-background: transparent;\n      --media-control-hover-background: transparent;\n      position: relative;\n    }\n\n    media-controller::part(vertical-layer) {\n      transition: background-color 1s;\n    }\n\n    media-controller:is([mediapaused], :not([userinactive]))::part(vertical-layer) {\n      background-color: var(--controls-backdrop-color, var(--controls, transparent));\n      transition: background-color 0.25s;\n    }\n\n    .center-controls {\n      --media-button-icon-width: 100%;\n      --media-button-icon-height: auto;\n      --media-tooltip-display: none;\n      pointer-events: none;\n      width: 100%;\n      display: flex;\n      flex-flow: row;\n      align-items: center;\n      justify-content: center;\n      filter: drop-shadow(0 0 2px rgb(0 0 0 / 0.25)) drop-shadow(0 0 6px rgb(0 0 0 / 0.25));\n      paint-order: stroke;\n      stroke: rgba(102, 102, 102, 1);\n      stroke-width: 0.3px;\n      text-shadow:\n        0 0 2px rgb(0 0 0 / 0.25),\n        0 0 6px rgb(0 0 0 / 0.25);\n    }\n\n    .center-controls media-play-button {\n      --media-control-background: transparent;\n      --media-control-hover-background: transparent;\n      --media-control-padding: 0;\n      width: 40px;\n    }\n\n    [breakpointsm] .center-controls media-play-button {\n      width: 90px;\n      height: 90px;\n      border-radius: 50%;\n      transition: background 0.4s;\n      padding: 24px;\n      --media-control-background: #000;\n      --media-control-hover-background: var(--_accent-color);\n    }\n\n    .center-controls media-seek-backward-button,\n    .center-controls media-seek-forward-button {\n      --media-control-background: transparent;\n      --media-control-hover-background: transparent;\n      padding: 0;\n      margin: 0 20px;\n      width: max(33px, min(8%, 40px));\n    }\n\n    [breakpointsm]:not([audio]) .center-controls.pre-playback {\n      display: grid;\n      align-items: initial;\n      justify-content: initial;\n      height: 100%;\n      overflow: hidden;\n    }\n\n    [breakpointsm]:not([audio]) .center-controls.pre-playback media-play-button {\n      place-self: var(--_pre-playback-place, center);\n      grid-area: 1 / 1;\n      margin: 16px;\n    }\n\n    /* Show and hide controls or pre-playback state */\n\n    [breakpointsm]:is([mediahasplayed], :not([mediapaused])):not([audio])\n      .center-controls.pre-playback\n      media-play-button {\n      /* Using \\`forwards\\` would lead to a laggy UI after the animation got in the end state */\n      animation: 0.3s linear pre-play-hide;\n      opacity: 0;\n      pointer-events: none;\n    }\n\n    .autoplay-unmute {\n      --media-control-hover-background: transparent;\n      width: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      filter: drop-shadow(0 0 2px rgb(0 0 0 / 0.25)) drop-shadow(0 0 6px rgb(0 0 0 / 0.25));\n    }\n\n    .autoplay-unmute-btn {\n      --media-control-height: 16px;\n      border-radius: 8px;\n      background: #000;\n      color: var(--_primary-color);\n      display: flex;\n      align-items: center;\n      padding: 8px 16px;\n      font-size: 18px;\n      font-weight: 500;\n      cursor: pointer;\n    }\n\n    .autoplay-unmute-btn:hover {\n      background: var(--_accent-color);\n    }\n\n    [breakpointsm] .autoplay-unmute-btn {\n      --media-control-height: 30px;\n      padding: 14px 24px;\n      font-size: 26px;\n    }\n\n    .autoplay-unmute-btn svg {\n      margin: 0 6px 0 0;\n    }\n\n    [breakpointsm] .autoplay-unmute-btn svg {\n      margin: 0 10px 0 0;\n    }\n\n    media-controller:not([audio]):not([mediahasplayed]) *:is(media-control-bar, media-time-range) {\n      display: none;\n    }\n\n    media-error-dialog:not([mediaerrorcode]) {\n      opacity: 0;\n    }\n\n    media-loading-indicator {\n      --media-loading-icon-width: 100%;\n      --media-button-icon-height: auto;\n      display: var(--media-control-display, var(--media-loading-indicator-display, flex));\n      pointer-events: none;\n      position: absolute;\n      width: min(15%, 150px);\n      flex-flow: row;\n      align-items: center;\n      justify-content: center;\n    }\n\n    /* Intentionally don't target the div for transition but the children\n     of the div. Prevents messing with media-chrome's autohide feature. */\n    media-loading-indicator + div * {\n      transition: opacity 0.15s;\n      opacity: 1;\n    }\n\n    media-loading-indicator[medialoading]:not([mediapaused]) ~ div > * {\n      opacity: 0;\n      transition-delay: 400ms;\n    }\n\n    media-volume-range {\n      width: min(100%, 100px);\n      --media-range-padding-left: 10px;\n      --media-range-padding-right: 10px;\n      --media-range-thumb-width: 12px;\n      --media-range-thumb-height: 12px;\n      --media-range-thumb-background: radial-gradient(\n        circle,\n        #000 0%,\n        #000 25%,\n        var(--_primary-color) 25%,\n        var(--_primary-color)\n      );\n      --media-control-hover-background: none;\n    }\n\n    media-time-display {\n      white-space: nowrap;\n    }\n\n    /* Generic style for explicitly disabled controls */\n    media-control-bar[part~='bottom'] [disabled],\n    media-control-bar[part~='bottom'] [aria-disabled='true'] {\n      opacity: 60%;\n      cursor: not-allowed;\n    }\n\n    media-text-display {\n      --media-font-size: 16px;\n      --media-control-padding: 14px;\n      font-weight: 500;\n    }\n\n    media-play-button.animated *:is(g, path) {\n      transition: all 0.3s;\n    }\n\n    media-play-button.animated[mediapaused] .pause-icon-pt1 {\n      opacity: 0;\n    }\n\n    media-play-button.animated[mediapaused] .pause-icon-pt2 {\n      transform-origin: center center;\n      transform: scaleY(0);\n    }\n\n    media-play-button.animated[mediapaused] .play-icon {\n      clip-path: inset(0 0 0 0);\n    }\n\n    media-play-button.animated:not([mediapaused]) .play-icon {\n      clip-path: inset(0 0 0 100%);\n    }\n\n    media-seek-forward-button,\n    media-seek-backward-button {\n      --media-font-weight: 400;\n    }\n\n    .mute-icon {\n      display: inline-block;\n    }\n\n    .mute-icon :is(path, g) {\n      transition: opacity 0.5s;\n    }\n\n    .muted {\n      opacity: 0;\n    }\n\n    media-mute-button[mediavolumelevel='low'] :is(.volume-medium, .volume-high),\n    media-mute-button[mediavolumelevel='medium'] :is(.volume-high) {\n      opacity: 0;\n    }\n\n    media-mute-button[mediavolumelevel='off'] .unmuted {\n      opacity: 0;\n    }\n\n    media-mute-button[mediavolumelevel='off'] .muted {\n      opacity: 1;\n    }\n\n    /**\n     * Our defaults for these buttons are to hide them at small sizes\n     * users can override this with CSS\n     */\n    media-controller:not([breakpointsm]):not([audio]) {\n      --bottom-play-button: none;\n      --bottom-seek-backward-button: none;\n      --bottom-seek-forward-button: none;\n      --bottom-time-display: none;\n      --bottom-playback-rate-menu-button: none;\n      --bottom-pip-button: none;\n    }\n\n    [part='mux-badge'] {\n      position: absolute;\n      bottom: 10px;\n      right: 10px;\n      z-index: 2;\n      opacity: 0.6;\n      transition:\n        opacity 0.2s ease-in-out,\n        bottom 0.2s ease-in-out;\n    }\n\n    [part='mux-badge']:hover {\n      opacity: 1;\n    }\n\n    [part='mux-badge'] a {\n      font-size: 14px;\n      font-family: var(--_font-family);\n      color: var(--_primary-color);\n      text-decoration: none;\n      display: flex;\n      align-items: center;\n      gap: 5px;\n    }\n\n    [part='mux-badge'] .mux-badge-text {\n      transition: opacity 0.5s ease-in-out;\n      opacity: 0;\n    }\n\n    [part='mux-badge'] .mux-badge-logo {\n      width: 40px;\n      height: auto;\n      display: inline-block;\n    }\n\n    [part='mux-badge'] .mux-badge-logo svg {\n      width: 100%;\n      height: 100%;\n      fill: white;\n    }\n\n    media-controller:not([userinactive]):not([mediahasplayed]) [part='mux-badge'],\n    media-controller:not([userinactive]) [part='mux-badge'],\n    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] {\n      transition: bottom 0.1s ease-in-out;\n    }\n\n    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] {\n      transition: bottom 0.2s ease-in-out 0.62s;\n    }\n\n    media-controller:not([userinactive]) [part='mux-badge'] .mux-badge-text,\n    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] .mux-badge-text {\n      opacity: 1;\n    }\n\n    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] .mux-badge-text {\n      opacity: 0;\n    }\n\n    media-controller[userinactive]:not([mediapaused]) [part='mux-badge'] {\n      bottom: 10px;\n    }\n\n    media-controller:not([userinactive]):not([mediahasplayed]) [part='mux-badge'] {\n      bottom: 10px;\n    }\n\n    media-controller:not([userinactive])[mediahasplayed] [part='mux-badge'],\n    media-controller[mediahasplayed][mediapaused] [part='mux-badge'] {\n      bottom: calc(28px + var(--media-control-height, 0px) + var(--media-control-padding, 0px) * 2);\n    }\n  </style>\n\n  <template partial=\"TitleDisplay\">\n    <template if=\"videotitle\">\n      <template if=\"videotitle != true\">\n        <media-text-display part=\"top title display\" class=\"title-display\">{{videotitle}}</media-text-display>\n      </template>\n    </template>\n    <template if=\"!videotitle\">\n      <template if=\"title\">\n        <media-text-display part=\"top title display\" class=\"title-display\">{{title}}</media-text-display>\n      </template>\n    </template>\n  </template>\n\n  <template partial=\"PlayButton\">\n    <media-play-button\n      part=\"{{section ?? 'bottom'}} play button\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n      class=\"animated\"\n    >\n      <svg aria-hidden=\"true\" viewBox=\"0 0 18 14\" slot=\"icon\">\n        <g class=\"play-icon\">\n          <path\n            d=\"M15.5987 6.2911L3.45577 0.110898C2.83667 -0.204202 2.06287 0.189698 2.06287 0.819798V13.1802C2.06287 13.8103 2.83667 14.2042 3.45577 13.8891L15.5987 7.7089C16.2178 7.3938 16.2178 6.6061 15.5987 6.2911Z\"\n          />\n        </g>\n        <g class=\"pause-icon\">\n          <path\n            class=\"pause-icon-pt1\"\n            d=\"M5.90709 0H2.96889C2.46857 0 2.06299 0.405585 2.06299 0.9059V13.0941C2.06299 13.5944 2.46857 14 2.96889 14H5.90709C6.4074 14 6.81299 13.5944 6.81299 13.0941V0.9059C6.81299 0.405585 6.4074 0 5.90709 0Z\"\n          />\n          <path\n            class=\"pause-icon-pt2\"\n            d=\"M15.1571 0H12.2189C11.7186 0 11.313 0.405585 11.313 0.9059V13.0941C11.313 13.5944 11.7186 14 12.2189 14H15.1571C15.6574 14 16.063 13.5944 16.063 13.0941V0.9059C16.063 0.405585 15.6574 0 15.1571 0Z\"\n          />\n        </g>\n      </svg>\n    </media-play-button>\n  </template>\n\n  <template partial=\"PrePlayButton\">\n    <media-play-button\n      part=\"{{section ?? 'center'}} play button pre-play\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    >\n      <svg aria-hidden=\"true\" viewBox=\"0 0 18 14\" slot=\"icon\" style=\"transform: translate(3px, 0)\">\n        <path\n          d=\"M15.5987 6.2911L3.45577 0.110898C2.83667 -0.204202 2.06287 0.189698 2.06287 0.819798V13.1802C2.06287 13.8103 2.83667 14.2042 3.45577 13.8891L15.5987 7.7089C16.2178 7.3938 16.2178 6.6061 15.5987 6.2911Z\"\n        />\n      </svg>\n    </media-play-button>\n  </template>\n\n  <template partial=\"SeekBackwardButton\">\n    <media-seek-backward-button\n      seekoffset=\"{{backwardseekoffset}}\"\n      part=\"{{section ?? 'bottom'}} seek-backward button\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    >\n      <svg viewBox=\"0 0 22 14\" aria-hidden=\"true\" slot=\"icon\">\n        <path\n          d=\"M3.65 2.07888L0.0864 6.7279C-0.0288 6.87812 -0.0288 7.12188 0.0864 7.2721L3.65 11.9211C3.7792 12.0896 4 11.9703 4 11.7321V2.26787C4 2.02968 3.7792 1.9104 3.65 2.07888Z\"\n        />\n        <text transform=\"translate(6 12)\" style=\"font-size: 14px; font-family: 'ArialMT', 'Arial'\">\n          {{backwardseekoffset}}\n        </text>\n      </svg>\n    </media-seek-backward-button>\n  </template>\n\n  <template partial=\"SeekForwardButton\">\n    <media-seek-forward-button\n      seekoffset=\"{{forwardseekoffset}}\"\n      part=\"{{section ?? 'bottom'}} seek-forward button\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    >\n      <svg viewBox=\"0 0 22 14\" aria-hidden=\"true\" slot=\"icon\">\n        <g>\n          <text transform=\"translate(-1 12)\" style=\"font-size: 14px; font-family: 'ArialMT', 'Arial'\">\n            {{forwardseekoffset}}\n          </text>\n          <path\n            d=\"M18.35 11.9211L21.9136 7.2721C22.0288 7.12188 22.0288 6.87812 21.9136 6.7279L18.35 2.07888C18.2208 1.91041 18 2.02968 18 2.26787V11.7321C18 11.9703 18.2208 12.0896 18.35 11.9211Z\"\n          />\n        </g>\n      </svg>\n    </media-seek-forward-button>\n  </template>\n\n  <template partial=\"MuteButton\">\n    <media-mute-button part=\"bottom mute button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <svg viewBox=\"0 0 18 14\" slot=\"icon\" class=\"mute-icon\" aria-hidden=\"true\">\n        <g class=\"unmuted\">\n          <path\n            d=\"M6.76786 1.21233L3.98606 3.98924H1.19937C0.593146 3.98924 0.101743 4.51375 0.101743 5.1607V6.96412L0 6.99998L0.101743 7.03583V8.83926C0.101743 9.48633 0.593146 10.0108 1.19937 10.0108H3.98606L6.76773 12.7877C7.23561 13.2547 8 12.9007 8 12.2171V1.78301C8 1.09925 7.23574 0.745258 6.76786 1.21233Z\"\n          />\n          <path\n            class=\"volume-low\"\n            d=\"M10 3.54781C10.7452 4.55141 11.1393 5.74511 11.1393 6.99991C11.1393 8.25471 10.7453 9.44791 10 10.4515L10.7988 11.0496C11.6734 9.87201 12.1356 8.47161 12.1356 6.99991C12.1356 5.52821 11.6735 4.12731 10.7988 2.94971L10 3.54781Z\"\n          />\n          <path\n            class=\"volume-medium\"\n            d=\"M12.3778 2.40086C13.2709 3.76756 13.7428 5.35806 13.7428 7.00026C13.7428 8.64246 13.2709 10.233 12.3778 11.5992L13.2106 12.1484C14.2107 10.6185 14.739 8.83796 14.739 7.00016C14.739 5.16236 14.2107 3.38236 13.2106 1.85156L12.3778 2.40086Z\"\n          />\n          <path\n            class=\"volume-high\"\n            d=\"M15.5981 0.75L14.7478 1.2719C15.7937 2.9919 16.3468 4.9723 16.3468 7C16.3468 9.0277 15.7937 11.0082 14.7478 12.7281L15.5981 13.25C16.7398 11.3722 17.343 9.211 17.343 7C17.343 4.789 16.7398 2.6268 15.5981 0.75Z\"\n          />\n        </g>\n        <g class=\"muted\">\n          <path\n            fill-rule=\"evenodd\"\n            clip-rule=\"evenodd\"\n            d=\"M4.39976 4.98924H1.19937C1.19429 4.98924 1.17777 4.98961 1.15296 5.01609C1.1271 5.04369 1.10174 5.09245 1.10174 5.1607V8.83926C1.10174 8.90761 1.12714 8.95641 1.15299 8.984C1.17779 9.01047 1.1943 9.01084 1.19937 9.01084H4.39977L7 11.6066V2.39357L4.39976 4.98924ZM7.47434 1.92006C7.4743 1.9201 7.47439 1.92002 7.47434 1.92006V1.92006ZM6.76773 12.7877L3.98606 10.0108H1.19937C0.593146 10.0108 0.101743 9.48633 0.101743 8.83926V7.03583L0 6.99998L0.101743 6.96412V5.1607C0.101743 4.51375 0.593146 3.98924 1.19937 3.98924H3.98606L6.76786 1.21233C7.23574 0.745258 8 1.09925 8 1.78301V12.2171C8 12.9007 7.23561 13.2547 6.76773 12.7877Z\"\n          />\n          <path\n            fill-rule=\"evenodd\"\n            clip-rule=\"evenodd\"\n            d=\"M15.2677 9.30323C15.463 9.49849 15.7796 9.49849 15.9749 9.30323C16.1701 9.10796 16.1701 8.79138 15.9749 8.59612L14.2071 6.82841L15.9749 5.06066C16.1702 4.8654 16.1702 4.54882 15.9749 4.35355C15.7796 4.15829 15.4631 4.15829 15.2678 4.35355L13.5 6.1213L11.7322 4.35348C11.537 4.15822 11.2204 4.15822 11.0251 4.35348C10.8298 4.54874 10.8298 4.86532 11.0251 5.06058L12.7929 6.82841L11.0251 8.59619C10.8299 8.79146 10.8299 9.10804 11.0251 9.3033C11.2204 9.49856 11.537 9.49856 11.7323 9.3033L13.5 7.53552L15.2677 9.30323Z\"\n          />\n        </g>\n      </svg>\n    </media-mute-button>\n  </template>\n\n  <template partial=\"PipButton\">\n    <media-pip-button part=\"bottom pip button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"icon\">\n        <path\n          d=\"M15.9891 0H2.011C0.9004 0 0 0.9003 0 2.0109V11.989C0 13.0996 0.9004 14 2.011 14H15.9891C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.9891 0ZM17 11.9891C17 12.5465 16.5465 13 15.9891 13H2.011C1.4536 13 1.0001 12.5465 1.0001 11.9891V2.0109C1.0001 1.4535 1.4536 0.9999 2.011 0.9999H15.9891C16.5465 0.9999 17 1.4535 17 2.0109V11.9891Z\"\n        />\n        <path\n          d=\"M15.356 5.67822H8.19523C8.03253 5.67822 7.90063 5.81012 7.90063 5.97282V11.3836C7.90063 11.5463 8.03253 11.6782 8.19523 11.6782H15.356C15.5187 11.6782 15.6506 11.5463 15.6506 11.3836V5.97282C15.6506 5.81012 15.5187 5.67822 15.356 5.67822Z\"\n        />\n      </svg>\n    </media-pip-button>\n  </template>\n\n  <template partial=\"CaptionsMenu\">\n    <media-captions-menu-button part=\"bottom captions button\">\n      <svg aria-hidden=\"true\" viewBox=\"0 0 18 14\" slot=\"on\">\n        <path\n          d=\"M15.989 0H2.011C0.9004 0 0 0.9003 0 2.0109V11.9891C0 13.0997 0.9004 14 2.011 14H15.989C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.989 0ZM4.2292 8.7639C4.5954 9.1902 5.0935 9.4031 5.7233 9.4031C6.1852 9.4031 6.5544 9.301 6.8302 9.0969C7.1061 8.8933 7.2863 8.614 7.3702 8.26H8.4322C8.3062 8.884 8.0093 9.3733 7.5411 9.7273C7.0733 10.0813 6.4703 10.2581 5.732 10.2581C5.108 10.2581 4.5699 10.1219 4.1168 9.8489C3.6637 9.5759 3.3141 9.1946 3.0685 8.7058C2.8224 8.2165 2.6994 7.6511 2.6994 7.009C2.6994 6.3611 2.8224 5.7927 3.0685 5.3034C3.3141 4.8146 3.6637 4.4323 4.1168 4.1559C4.5699 3.88 5.108 3.7418 5.732 3.7418C6.4703 3.7418 7.0733 3.922 7.5411 4.2818C8.0094 4.6422 8.3062 5.1461 8.4322 5.794H7.3702C7.2862 5.4283 7.106 5.1368 6.8302 4.921C6.5544 4.7052 6.1852 4.5968 5.7233 4.5968C5.0934 4.5968 4.5954 4.8116 4.2292 5.2404C3.8635 5.6696 3.6804 6.259 3.6804 7.009C3.6804 7.7531 3.8635 8.3381 4.2292 8.7639ZM11.0974 8.7639C11.4636 9.1902 11.9617 9.4031 12.5915 9.4031C13.0534 9.4031 13.4226 9.301 13.6984 9.0969C13.9743 8.8933 14.1545 8.614 14.2384 8.26H15.3004C15.1744 8.884 14.8775 9.3733 14.4093 9.7273C13.9415 10.0813 13.3385 10.2581 12.6002 10.2581C11.9762 10.2581 11.4381 10.1219 10.985 9.8489C10.5319 9.5759 10.1823 9.1946 9.9367 8.7058C9.6906 8.2165 9.5676 7.6511 9.5676 7.009C9.5676 6.3611 9.6906 5.7927 9.9367 5.3034C10.1823 4.8146 10.5319 4.4323 10.985 4.1559C11.4381 3.88 11.9762 3.7418 12.6002 3.7418C13.3385 3.7418 13.9415 3.922 14.4093 4.2818C14.8776 4.6422 15.1744 5.1461 15.3004 5.794H14.2384C14.1544 5.4283 13.9742 5.1368 13.6984 4.921C13.4226 4.7052 13.0534 4.5968 12.5915 4.5968C11.9616 4.5968 11.4636 4.8116 11.0974 5.2404C10.7317 5.6696 10.5486 6.259 10.5486 7.009C10.5486 7.7531 10.7317 8.3381 11.0974 8.7639Z\"\n        />\n      </svg>\n      <svg aria-hidden=\"true\" viewBox=\"0 0 18 14\" slot=\"off\">\n        <path\n          d=\"M5.73219 10.258C5.10819 10.258 4.57009 10.1218 4.11699 9.8488C3.66389 9.5758 3.31429 9.1945 3.06869 8.7057C2.82259 8.2164 2.69958 7.651 2.69958 7.0089C2.69958 6.361 2.82259 5.7926 3.06869 5.3033C3.31429 4.8145 3.66389 4.4322 4.11699 4.1558C4.57009 3.8799 5.10819 3.7417 5.73219 3.7417C6.47049 3.7417 7.07348 3.9219 7.54128 4.2817C8.00958 4.6421 8.30638 5.146 8.43238 5.7939H7.37039C7.28639 5.4282 7.10618 5.1367 6.83039 4.9209C6.55459 4.7051 6.18538 4.5967 5.72348 4.5967C5.09358 4.5967 4.59559 4.8115 4.22939 5.2403C3.86369 5.6695 3.68058 6.2589 3.68058 7.0089C3.68058 7.753 3.86369 8.338 4.22939 8.7638C4.59559 9.1901 5.09368 9.403 5.72348 9.403C6.18538 9.403 6.55459 9.3009 6.83039 9.0968C7.10629 8.8932 7.28649 8.6139 7.37039 8.2599H8.43238C8.30638 8.8839 8.00948 9.3732 7.54128 9.7272C7.07348 10.0812 6.47049 10.258 5.73219 10.258Z\"\n        />\n        <path\n          d=\"M12.6003 10.258C11.9763 10.258 11.4382 10.1218 10.9851 9.8488C10.532 9.5758 10.1824 9.1945 9.93685 8.7057C9.69075 8.2164 9.56775 7.651 9.56775 7.0089C9.56775 6.361 9.69075 5.7926 9.93685 5.3033C10.1824 4.8145 10.532 4.4322 10.9851 4.1558C11.4382 3.8799 11.9763 3.7417 12.6003 3.7417C13.3386 3.7417 13.9416 3.9219 14.4094 4.2817C14.8777 4.6421 15.1745 5.146 15.3005 5.7939H14.2385C14.1545 5.4282 13.9743 5.1367 13.6985 4.9209C13.4227 4.7051 13.0535 4.5967 12.5916 4.5967C11.9617 4.5967 11.4637 4.8115 11.0975 5.2403C10.7318 5.6695 10.5487 6.2589 10.5487 7.0089C10.5487 7.753 10.7318 8.338 11.0975 8.7638C11.4637 9.1901 11.9618 9.403 12.5916 9.403C13.0535 9.403 13.4227 9.3009 13.6985 9.0968C13.9744 8.8932 14.1546 8.6139 14.2385 8.2599H15.3005C15.1745 8.8839 14.8776 9.3732 14.4094 9.7272C13.9416 10.0812 13.3386 10.258 12.6003 10.258Z\"\n        />\n        <path\n          d=\"M15.9891 1C16.5465 1 17 1.4535 17 2.011V11.9891C17 12.5465 16.5465 13 15.9891 13H2.0109C1.4535 13 1 12.5465 1 11.9891V2.0109C1 1.4535 1.4535 0.9999 2.0109 0.9999L15.9891 1ZM15.9891 0H2.0109C0.9003 0 0 0.9003 0 2.0109V11.9891C0 13.0997 0.9003 14 2.0109 14H15.9891C17.0997 14 18 13.0997 18 11.9891V2.0109C18 0.9003 17.0997 0 15.9891 0Z\"\n        />\n      </svg>\n    </media-captions-menu-button>\n    <media-captions-menu\n      hidden\n      anchor=\"auto\"\n      part=\"bottom captions menu\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n      exportparts=\"menu-item\"\n    >\n      <div slot=\"checked-indicator\">\n        <style>\n          .indicator {\n            position: relative;\n            top: 1px;\n            width: 0.9em;\n            height: auto;\n            fill: var(--_accent-color);\n            margin-right: 5px;\n          }\n\n          [aria-checked='false'] .indicator {\n            display: none;\n          }\n        </style>\n        <svg viewBox=\"0 0 14 18\" class=\"indicator\">\n          <path\n            d=\"M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027\"\n            fill-rule=\"evenodd\"\n          />\n        </svg></div\n    ></media-captions-menu>\n  </template>\n\n  <template partial=\"AirplayButton\">\n    <media-airplay-button part=\"bottom airplay button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"icon\">\n        <path\n          d=\"M16.1383 0H1.8618C0.8335 0 0 0.8335 0 1.8617V10.1382C0 11.1664 0.8335 12 1.8618 12H3.076C3.1204 11.9433 3.1503 11.8785 3.2012 11.826L4.004 11H1.8618C1.3866 11 1 10.6134 1 10.1382V1.8617C1 1.3865 1.3866 0.9999 1.8618 0.9999H16.1383C16.6135 0.9999 17.0001 1.3865 17.0001 1.8617V10.1382C17.0001 10.6134 16.6135 11 16.1383 11H13.9961L14.7989 11.826C14.8499 11.8785 14.8798 11.9432 14.9241 12H16.1383C17.1665 12 18.0001 11.1664 18.0001 10.1382V1.8617C18 0.8335 17.1665 0 16.1383 0Z\"\n        />\n        <path\n          d=\"M9.55061 8.21903C9.39981 8.06383 9.20001 7.98633 9.00011 7.98633C8.80021 7.98633 8.60031 8.06383 8.44951 8.21903L4.09771 12.697C3.62471 13.1838 3.96961 13.9998 4.64831 13.9998H13.3518C14.0304 13.9998 14.3754 13.1838 13.9023 12.697L9.55061 8.21903Z\"\n        />\n      </svg>\n    </media-airplay-button>\n  </template>\n\n  <template partial=\"FullscreenButton\">\n    <media-fullscreen-button part=\"bottom fullscreen button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"enter\">\n        <path\n          d=\"M1.00745 4.39539L1.01445 1.98789C1.01605 1.43049 1.47085 0.978289 2.02835 0.979989L6.39375 0.992589L6.39665 -0.007411L2.03125 -0.020011C0.920646 -0.023211 0.0176463 0.874489 0.0144463 1.98509L0.00744629 4.39539H1.00745Z\"\n        />\n        <path\n          d=\"M17.0144 2.03431L17.0076 4.39541H18.0076L18.0144 2.03721C18.0176 0.926712 17.1199 0.0237125 16.0093 0.0205125L11.6439 0.0078125L11.641 1.00781L16.0064 1.02041C16.5638 1.02201 17.016 1.47681 17.0144 2.03431Z\"\n        />\n        <path\n          d=\"M16.9925 9.60498L16.9855 12.0124C16.9839 12.5698 16.5291 13.022 15.9717 13.0204L11.6063 13.0078L11.6034 14.0078L15.9688 14.0204C17.0794 14.0236 17.9823 13.1259 17.9855 12.0153L17.9925 9.60498H16.9925Z\"\n        />\n        <path\n          d=\"M0.985626 11.9661L0.992426 9.60498H-0.0074737L-0.0142737 11.9632C-0.0174737 13.0738 0.880226 13.9767 1.99083 13.98L6.35623 13.9926L6.35913 12.9926L1.99373 12.98C1.43633 12.9784 0.983926 12.5236 0.985626 11.9661Z\"\n        />\n      </svg>\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"exit\">\n        <path\n          d=\"M5.39655 -0.0200195L5.38955 2.38748C5.38795 2.94488 4.93315 3.39708 4.37565 3.39538L0.0103463 3.38278L0.00744629 4.38278L4.37285 4.39538C5.48345 4.39858 6.38635 3.50088 6.38965 2.39028L6.39665 -0.0200195H5.39655Z\"\n        />\n        <path\n          d=\"M12.6411 2.36891L12.6479 0.0078125H11.6479L11.6411 2.36601C11.6379 3.47651 12.5356 4.37951 13.6462 4.38271L18.0116 4.39531L18.0145 3.39531L13.6491 3.38271C13.0917 3.38111 12.6395 2.92641 12.6411 2.36891Z\"\n        />\n        <path\n          d=\"M12.6034 14.0204L12.6104 11.613C12.612 11.0556 13.0668 10.6034 13.6242 10.605L17.9896 10.6176L17.9925 9.61759L13.6271 9.60499C12.5165 9.60179 11.6136 10.4995 11.6104 11.6101L11.6034 14.0204H12.6034Z\"\n        />\n        <path\n          d=\"M5.359 11.6315L5.3522 13.9926H6.3522L6.359 11.6344C6.3622 10.5238 5.4645 9.62088 4.3539 9.61758L-0.0115043 9.60498L-0.0144043 10.605L4.351 10.6176C4.9084 10.6192 5.3607 11.074 5.359 11.6315Z\"\n        />\n      </svg>\n    </media-fullscreen-button>\n  </template>\n\n  <template partial=\"CastButton\">\n    <media-cast-button part=\"bottom cast button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"enter\">\n        <path\n          d=\"M16.0072 0H2.0291C0.9185 0 0.0181 0.9003 0.0181 2.011V5.5009C0.357 5.5016 0.6895 5.5275 1.0181 5.5669V2.011C1.0181 1.4536 1.4716 1 2.029 1H16.0072C16.5646 1 17.0181 1.4536 17.0181 2.011V11.9891C17.0181 12.5465 16.5646 13 16.0072 13H8.4358C8.4746 13.3286 8.4999 13.6611 8.4999 13.9999H16.0071C17.1177 13.9999 18.018 13.0996 18.018 11.989V2.011C18.0181 0.9003 17.1178 0 16.0072 0ZM0 6.4999V7.4999C3.584 7.4999 6.5 10.4159 6.5 13.9999H7.5C7.5 9.8642 4.1357 6.4999 0 6.4999ZM0 8.7499V9.7499C2.3433 9.7499 4.25 11.6566 4.25 13.9999H5.25C5.25 11.1049 2.895 8.7499 0 8.7499ZM0.0181 11V14H3.0181C3.0181 12.3431 1.675 11 0.0181 11Z\"\n        />\n      </svg>\n      <svg viewBox=\"0 0 18 14\" aria-hidden=\"true\" slot=\"exit\">\n        <path\n          d=\"M15.9891 0H2.01103C0.900434 0 3.35947e-05 0.9003 3.35947e-05 2.011V5.5009C0.338934 5.5016 0.671434 5.5275 1.00003 5.5669V2.011C1.00003 1.4536 1.45353 1 2.01093 1H15.9891C16.5465 1 17 1.4536 17 2.011V11.9891C17 12.5465 16.5465 13 15.9891 13H8.41773C8.45653 13.3286 8.48183 13.6611 8.48183 13.9999H15.989C17.0996 13.9999 17.9999 13.0996 17.9999 11.989V2.011C18 0.9003 17.0997 0 15.9891 0ZM-0.0180664 6.4999V7.4999C3.56593 7.4999 6.48193 10.4159 6.48193 13.9999H7.48193C7.48193 9.8642 4.11763 6.4999 -0.0180664 6.4999ZM-0.0180664 8.7499V9.7499C2.32523 9.7499 4.23193 11.6566 4.23193 13.9999H5.23193C5.23193 11.1049 2.87693 8.7499 -0.0180664 8.7499ZM3.35947e-05 11V14H3.00003C3.00003 12.3431 1.65693 11 3.35947e-05 11Z\"\n        />\n        <path d=\"M2.15002 5.634C5.18352 6.4207 7.57252 8.8151 8.35282 11.8499H15.8501V2.1499H2.15002V5.634Z\" />\n      </svg>\n    </media-cast-button>\n  </template>\n\n  <template partial=\"LiveButton\">\n    <media-live-button part=\"{{section ?? 'top'}} live button\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <span slot=\"text\">Live</span>\n    </media-live-button>\n  </template>\n\n  <template partial=\"PlaybackRateMenu\">\n    <media-playback-rate-menu-button part=\"bottom playback-rate button\"></media-playback-rate-menu-button>\n    <media-playback-rate-menu\n      hidden\n      anchor=\"auto\"\n      rates=\"{{playbackrates}}\"\n      exportparts=\"menu-item\"\n      part=\"bottom playback-rate menu\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    ></media-playback-rate-menu>\n  </template>\n\n  <template partial=\"VolumeRange\">\n    <media-volume-range\n      part=\"bottom volume range\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    ></media-volume-range>\n  </template>\n\n  <template partial=\"TimeDisplay\">\n    <media-time-display\n      remaining=\"{{defaultshowremainingtime}}\"\n      showduration=\"{{!hideduration}}\"\n      part=\"bottom time display\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    ></media-time-display>\n  </template>\n\n  <template partial=\"TimeRange\">\n    <media-time-range part=\"bottom time range\" disabled=\"{{disabled}}\" aria-disabled=\"{{disabled}}\">\n      <media-preview-thumbnail slot=\"preview\"></media-preview-thumbnail>\n      <media-preview-chapter-display slot=\"preview\"></media-preview-chapter-display>\n      <media-preview-time-display slot=\"preview\"></media-preview-time-display>\n      <div slot=\"preview\" part=\"arrow\"></div>\n    </media-time-range>\n  </template>\n\n  <template partial=\"AudioTrackMenu\">\n    <media-audio-track-menu-button part=\"bottom audio-track button\">\n      <svg aria-hidden=\"true\" slot=\"icon\" viewBox=\"0 0 18 16\">\n        <path d=\"M9 15A7 7 0 1 1 9 1a7 7 0 0 1 0 14Zm0 1A8 8 0 1 0 9 0a8 8 0 0 0 0 16Z\" />\n        <path\n          d=\"M5.2 6.3a.5.5 0 0 1 .5.5v2.4a.5.5 0 1 1-1 0V6.8a.5.5 0 0 1 .5-.5Zm2.4-2.4a.5.5 0 0 1 .5.5v7.2a.5.5 0 0 1-1 0V4.4a.5.5 0 0 1 .5-.5ZM10 5.5a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5Zm2.4-.8a.5.5 0 0 1 .5.5v5.6a.5.5 0 0 1-1 0V5.2a.5.5 0 0 1 .5-.5Z\"\n        />\n      </svg>\n    </media-audio-track-menu-button>\n    <media-audio-track-menu\n      hidden\n      anchor=\"auto\"\n      part=\"bottom audio-track menu\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n      exportparts=\"menu-item\"\n    >\n      <div slot=\"checked-indicator\">\n        <style>\n          .indicator {\n            position: relative;\n            top: 1px;\n            width: 0.9em;\n            height: auto;\n            fill: var(--_accent-color);\n            margin-right: 5px;\n          }\n\n          [aria-checked='false'] .indicator {\n            display: none;\n          }\n        </style>\n        <svg viewBox=\"0 0 14 18\" class=\"indicator\">\n          <path\n            d=\"M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027\"\n            fill-rule=\"evenodd\"\n          />\n        </svg>\n      </div>\n    </media-audio-track-menu>\n  </template>\n\n  <template partial=\"RenditionMenu\">\n    <media-rendition-menu-button part=\"bottom rendition button\">\n      <svg aria-hidden=\"true\" slot=\"icon\" viewBox=\"0 0 18 14\">\n        <path\n          d=\"M2.25 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4ZM9 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm6.75 0a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z\"\n        />\n      </svg>\n    </media-rendition-menu-button>\n    <media-rendition-menu\n      hidden\n      anchor=\"auto\"\n      part=\"bottom rendition menu\"\n      disabled=\"{{disabled}}\"\n      aria-disabled=\"{{disabled}}\"\n    >\n      <div slot=\"checked-indicator\">\n        <style>\n          .indicator {\n            position: relative;\n            top: 1px;\n            width: 0.9em;\n            height: auto;\n            fill: var(--_accent-color);\n            margin-right: 5px;\n          }\n\n          [aria-checked='false'] .indicator {\n            opacity: 0;\n          }\n        </style>\n        <svg viewBox=\"0 0 14 18\" class=\"indicator\">\n          <path\n            d=\"M12.252 3.48c-.115.033-.301.161-.425.291-.059.063-1.407 1.815-2.995 3.894s-2.897 3.79-2.908 3.802c-.013.014-.661-.616-1.672-1.624-.908-.905-1.702-1.681-1.765-1.723-.401-.27-.783-.211-1.176.183a1.285 1.285 0 0 0-.261.342.582.582 0 0 0-.082.35c0 .165.01.205.08.35.075.153.213.296 2.182 2.271 1.156 1.159 2.17 2.159 2.253 2.222.189.143.338.196.539.194.203-.003.412-.104.618-.299.205-.193 6.7-8.693 6.804-8.903a.716.716 0 0 0 .085-.345c.01-.179.005-.203-.062-.339-.124-.252-.45-.531-.746-.639a.784.784 0 0 0-.469-.027\"\n            fill-rule=\"evenodd\"\n          />\n        </svg>\n      </div>\n    </media-rendition-menu>\n  </template>\n\n  <template partial=\"MuxBadge\">\n    <div part=\"mux-badge\">\n      <a href=\"https://www.mux.com/player\" target=\"_blank\">\n        <span class=\"mux-badge-text\">Powered by</span>\n        <div class=\"mux-badge-logo\">\n          <svg\n            viewBox=\"0 0 1600 500\"\n            style=\"fill-rule: evenodd; clip-rule: evenodd; stroke-linejoin: round; stroke-miterlimit: 2\"\n          >\n            <g>\n              <path\n                d=\"M994.287,93.486c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m0,-93.486c-34.509,-0 -62.484,27.976 -62.484,62.486l0,187.511c0,68.943 -56.09,125.033 -125.032,125.033c-68.942,-0 -125.03,-56.09 -125.03,-125.033l0,-187.511c0,-34.51 -27.976,-62.486 -62.485,-62.486c-34.509,-0 -62.484,27.976 -62.484,62.486l0,187.511c0,137.853 112.149,250.003 249.999,250.003c137.851,-0 250.001,-112.15 250.001,-250.003l0,-187.511c0,-34.51 -27.976,-62.486 -62.485,-62.486\"\n                style=\"fill-rule: nonzero\"\n              ></path>\n              <path\n                d=\"M1537.51,468.511c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m-275.883,-218.509l-143.33,143.329c-24.402,24.402 -24.402,63.966 0,88.368c24.402,24.402 63.967,24.402 88.369,-0l143.33,-143.329l143.328,143.329c24.402,24.4 63.967,24.402 88.369,-0c24.403,-24.402 24.403,-63.966 0.001,-88.368l-143.33,-143.329l0.001,-0.004l143.329,-143.329c24.402,-24.402 24.402,-63.965 0,-88.367c-24.402,-24.402 -63.967,-24.402 -88.369,-0l-143.329,143.328l-143.329,-143.328c-24.402,-24.401 -63.967,-24.402 -88.369,-0c-24.402,24.402 -24.402,63.965 0,88.367l143.329,143.329l0,0.004Z\"\n                style=\"fill-rule: nonzero\"\n              ></path>\n              <path\n                d=\"M437.511,468.521c-17.121,-0 -31,-13.879 -31,-31c0,-17.121 13.879,-31 31,-31c17.121,-0 31,13.879 31,31c0,17.121 -13.879,31 -31,31m23.915,-463.762c-23.348,-9.672 -50.226,-4.327 -68.096,13.544l-143.331,143.329l-143.33,-143.329c-17.871,-17.871 -44.747,-23.216 -68.096,-13.544c-23.349,9.671 -38.574,32.455 -38.574,57.729l0,375.026c0,34.51 27.977,62.486 62.487,62.486c34.51,-0 62.486,-27.976 62.486,-62.486l0,-224.173l80.843,80.844c24.404,24.402 63.965,24.402 88.369,-0l80.843,-80.844l0,224.173c0,34.51 27.976,62.486 62.486,62.486c34.51,-0 62.486,-27.976 62.486,-62.486l0,-375.026c0,-25.274 -15.224,-48.058 -38.573,-57.729\"\n                style=\"fill-rule: nonzero\"\n              ></path>\n            </g>\n          </svg>\n        </div>\n      </a>\n    </div>\n  </template>\n\n  <media-controller\n    part=\"controller\"\n    defaultstreamtype=\"{{defaultstreamtype ?? 'on-demand'}}\"\n    breakpoints=\"sm:470\"\n    gesturesdisabled=\"{{disabled}}\"\n    hotkeys=\"{{hotkeys}}\"\n    nohotkeys=\"{{nohotkeys}}\"\n    novolumepref=\"{{novolumepref}}\"\n    audio=\"{{audio}}\"\n    noautoseektolive=\"{{noautoseektolive}}\"\n    defaultsubtitles=\"{{defaultsubtitles}}\"\n    defaultduration=\"{{defaultduration ?? false}}\"\n    keyboardforwardseekoffset=\"{{forwardseekoffset}}\"\n    keyboardbackwardseekoffset=\"{{backwardseekoffset}}\"\n    exportparts=\"layer, media-layer, poster-layer, vertical-layer, centered-layer, gesture-layer\"\n    style=\"--_pre-playback-place:{{preplaybackplace ?? 'center'}}\"\n  >\n    <slot name=\"media\" slot=\"media\"></slot>\n    <slot name=\"poster\" slot=\"poster\"></slot>\n\n    <media-loading-indicator slot=\"centered-chrome\" noautohide></media-loading-indicator>\n    <media-error-dialog slot=\"dialog\" noautohide></media-error-dialog>\n\n    <template if=\"!audio\">\n      <!-- Pre-playback UI -->\n      <!-- same for both on-demand and live -->\n      <div slot=\"centered-chrome\" class=\"center-controls pre-playback\">\n        <template if=\"!breakpointsm\">{{>PlayButton section=\"center\"}}</template>\n        <template if=\"breakpointsm\">{{>PrePlayButton section=\"center\"}}</template>\n      </div>\n\n      <!-- Mux Badge -->\n      <template if=\"proudlydisplaymuxbadge\"> {{>MuxBadge}} </template>\n\n      <!-- Autoplay centered unmute button -->\n      <!--\n        todo: figure out how show this with available state variables\n        needs to show when:\n        - autoplay is enabled\n        - playback has been successful\n        - audio is muted\n        - in place / instead of the pre-plaback play button\n        - not to show again after user has interacted with this button\n          - OR user has interacted with the mute button in the control bar\n      -->\n      <!--\n        There should be a >MuteButton to the left of the \"Unmute\" text, but a templating bug\n        makes it appear even if commented out in the markup, add it back when code is un-commented\n      -->\n      <!-- <div slot=\"centered-chrome\" class=\"autoplay-unmute\">\n        <div role=\"button\" class=\"autoplay-unmute-btn\">Unmute</div>\n      </div> -->\n\n      <template if=\"streamtype == 'on-demand'\">\n        <template if=\"breakpointsm\">\n          <media-control-bar part=\"control-bar top\" slot=\"top-chrome\">{{>TitleDisplay}} </media-control-bar>\n        </template>\n        {{>TimeRange}}\n        <media-control-bar part=\"control-bar bottom\">\n          {{>PlayButton}} {{>SeekBackwardButton}} {{>SeekForwardButton}} {{>TimeDisplay}} {{>MuteButton}}\n          {{>VolumeRange}}\n          <div class=\"spacer\"></div>\n          {{>RenditionMenu}} {{>PlaybackRateMenu}} {{>AudioTrackMenu}} {{>CaptionsMenu}} {{>AirplayButton}}\n          {{>CastButton}} {{>PipButton}} {{>FullscreenButton}}\n        </media-control-bar>\n      </template>\n\n      <template if=\"streamtype == 'live'\">\n        <media-control-bar part=\"control-bar top\" slot=\"top-chrome\">\n          {{>LiveButton}}\n          <template if=\"breakpointsm\"> {{>TitleDisplay}} </template>\n        </media-control-bar>\n        <template if=\"targetlivewindow > 0\">{{>TimeRange}}</template>\n        <media-control-bar part=\"control-bar bottom\">\n          {{>PlayButton}}\n          <template if=\"targetlivewindow > 0\">{{>SeekBackwardButton}} {{>SeekForwardButton}}</template>\n          {{>MuteButton}} {{>VolumeRange}}\n          <div class=\"spacer\"></div>\n          {{>RenditionMenu}} {{>AudioTrackMenu}} {{>CaptionsMenu}} {{>AirplayButton}} {{>CastButton}} {{>PipButton}}\n          {{>FullscreenButton}}\n        </media-control-bar>\n      </template>\n    </template>\n\n    <template if=\"audio\">\n      <template if=\"streamtype == 'on-demand'\">\n        <template if=\"title\">\n          <media-control-bar part=\"control-bar top\">{{>TitleDisplay}}</media-control-bar>\n        </template>\n        <media-control-bar part=\"control-bar bottom\">\n          {{>PlayButton}}\n          <template if=\"breakpointsm\"> {{>SeekBackwardButton}} {{>SeekForwardButton}} </template>\n          {{>MuteButton}}\n          <template if=\"breakpointsm\">{{>VolumeRange}}</template>\n          {{>TimeDisplay}} {{>TimeRange}}\n          <template if=\"breakpointsm\">{{>PlaybackRateMenu}}</template>\n          {{>AirplayButton}} {{>CastButton}}\n        </media-control-bar>\n      </template>\n\n      <template if=\"streamtype == 'live'\">\n        <template if=\"title\">\n          <media-control-bar part=\"control-bar top\">{{>TitleDisplay}}</media-control-bar>\n        </template>\n        <media-control-bar part=\"control-bar bottom\">\n          {{>PlayButton}} {{>LiveButton section=\"bottom\"}} {{>MuteButton}}\n          <template if=\"breakpointsm\">\n            {{>VolumeRange}}\n            <template if=\"targetlivewindow > 0\"> {{>SeekBackwardButton}} {{>SeekForwardButton}} </template>\n          </template>\n          <template if=\"targetlivewindow > 0\"> {{>TimeDisplay}} {{>TimeRange}} </template>\n          <template if=\"!targetlivewindow\"><div class=\"spacer\"></div></template>\n          {{>AirplayButton}} {{>CastButton}}\n        </media-control-bar>\n      </template>\n    </template>\n\n    <slot></slot>\n  </media-controller>\n</template>\n`;var xe=Y.createElement(\"template\");\"innerHTML\"in xe&&(xe.innerHTML=vt);var Tt,Et,me=class extends media_chrome_dist_media_theme_element_js__WEBPACK_IMPORTED_MODULE_6__.MediaThemeElement{};me.template=(Et=(Tt=xe.content)==null?void 0:Tt.children)==null?void 0:Et[0];k.customElements.get(\"media-theme-gerwig\")||k.customElements.define(\"media-theme-gerwig\",me);var fa=\"gerwig\",M={SRC:\"src\",POSTER:\"poster\"},o={STYLE:\"style\",DEFAULT_HIDDEN_CAPTIONS:\"default-hidden-captions\",PRIMARY_COLOR:\"primary-color\",SECONDARY_COLOR:\"secondary-color\",ACCENT_COLOR:\"accent-color\",FORWARD_SEEK_OFFSET:\"forward-seek-offset\",BACKWARD_SEEK_OFFSET:\"backward-seek-offset\",PLAYBACK_TOKEN:\"playback-token\",THUMBNAIL_TOKEN:\"thumbnail-token\",STORYBOARD_TOKEN:\"storyboard-token\",DRM_TOKEN:\"drm-token\",STORYBOARD_SRC:\"storyboard-src\",THUMBNAIL_TIME:\"thumbnail-time\",AUDIO:\"audio\",NOHOTKEYS:\"nohotkeys\",HOTKEYS:\"hotkeys\",PLAYBACK_RATES:\"playbackrates\",DEFAULT_SHOW_REMAINING_TIME:\"default-show-remaining-time\",DEFAULT_DURATION:\"default-duration\",TITLE:\"title\",VIDEO_TITLE:\"video-title\",PLACEHOLDER:\"placeholder\",THEME:\"theme\",DEFAULT_STREAM_TYPE:\"default-stream-type\",TARGET_LIVE_WINDOW:\"target-live-window\",EXTRA_SOURCE_PARAMS:\"extra-source-params\",NO_VOLUME_PREF:\"no-volume-pref\",CAST_RECEIVER:\"cast-receiver\",NO_TOOLTIPS:\"no-tooltips\",PROUDLY_DISPLAY_MUX_BADGE:\"proudly-display-mux-badge\"},Se=[\"audio\",\"backwardseekoffset\",\"defaultduration\",\"defaultshowremainingtime\",\"defaultsubtitles\",\"noautoseektolive\",\"disabled\",\"exportparts\",\"forwardseekoffset\",\"hideduration\",\"hotkeys\",\"nohotkeys\",\"playbackrates\",\"defaultstreamtype\",\"streamtype\",\"style\",\"targetlivewindow\",\"template\",\"title\",\"videotitle\",\"novolumepref\",\"proudlydisplaymuxbadge\"];function ya(t,a){var i,r;return{src:!t.playbackId&&t.src,playbackId:t.playbackId,hasSrc:!!t.playbackId||!!t.src||!!t.currentSrc,poster:t.poster,storyboard:t.storyboard,storyboardSrc:t.getAttribute(o.STORYBOARD_SRC),placeholder:t.getAttribute(\"placeholder\"),themeTemplate:Ta(t),thumbnailTime:!t.tokens.thumbnail&&t.thumbnailTime,autoplay:t.autoplay,crossOrigin:t.crossOrigin,loop:t.loop,noHotKeys:t.hasAttribute(o.NOHOTKEYS),hotKeys:t.getAttribute(o.HOTKEYS),muted:t.muted,paused:t.paused,preload:t.preload,envKey:t.envKey,preferCmcd:t.preferCmcd,debug:t.debug,disableTracking:t.disableTracking,disableCookies:t.disableCookies,tokens:t.tokens,beaconCollectionDomain:t.beaconCollectionDomain,maxResolution:t.maxResolution,minResolution:t.minResolution,programStartTime:t.programStartTime,programEndTime:t.programEndTime,assetStartTime:t.assetStartTime,assetEndTime:t.assetEndTime,renditionOrder:t.renditionOrder,metadata:t.metadata,playerInitTime:t.playerInitTime,playerSoftwareName:t.playerSoftwareName,playerSoftwareVersion:t.playerSoftwareVersion,startTime:t.startTime,preferPlayback:t.preferPlayback,audio:t.audio,defaultStreamType:t.defaultStreamType,targetLiveWindow:t.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.TARGET_LIVE_WINDOW),streamType:z(t.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.STREAM_TYPE)),primaryColor:t.getAttribute(o.PRIMARY_COLOR),secondaryColor:t.getAttribute(o.SECONDARY_COLOR),accentColor:t.getAttribute(o.ACCENT_COLOR),forwardSeekOffset:t.forwardSeekOffset,backwardSeekOffset:t.backwardSeekOffset,defaultHiddenCaptions:t.defaultHiddenCaptions,defaultDuration:t.defaultDuration,defaultShowRemainingTime:t.defaultShowRemainingTime,hideDuration:Ea(t),playbackRates:t.getAttribute(o.PLAYBACK_RATES),customDomain:(i=t.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.CUSTOM_DOMAIN))!=null?i:void 0,title:t.getAttribute(o.TITLE),videoTitle:(r=t.getAttribute(o.VIDEO_TITLE))!=null?r:t.getAttribute(o.TITLE),novolumepref:t.hasAttribute(o.NO_VOLUME_PREF),proudlyDisplayMuxBadge:t.hasAttribute(o.PROUDLY_DISPLAY_MUX_BADGE),castReceiver:t.castReceiver,...a,extraSourceParams:t.extraSourceParams}}var va=media_chrome__WEBPACK_IMPORTED_MODULE_0__.MediaErrorDialog.formatErrorMessage;media_chrome__WEBPACK_IMPORTED_MODULE_0__.MediaErrorDialog.formatErrorMessage=t=>{var a,e;if(t instanceof _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.MediaError){let i=yt(t,!1);return`\n      ${i!=null&&i.title?`<h3>${i.title}</h3>`:\"\"}\n      ${i!=null&&i.message||i!=null&&i.linkUrl?`<p>\n        ${i==null?void 0:i.message}\n        ${i!=null&&i.linkUrl?`<a\n              href=\"${i.linkUrl}\"\n              target=\"_blank\"\n              rel=\"external noopener\"\n              aria-label=\"${(a=i.linkText)!=null?a:\"\"} ${(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"(opens in a new window)\")}\"\n              >${(e=i.linkText)!=null?e:i.linkUrl}</a\n            >`:\"\"}\n      </p>`:\"\"}\n    `}return va(t)};function Ta(t){var e,i;let a=t.theme;if(a){let r=(i=(e=t.getRootNode())==null?void 0:e.getElementById)==null?void 0:i.call(e,a);if(r&&r instanceof HTMLTemplateElement)return r;a.startsWith(\"media-theme-\")||(a=`media-theme-${a}`);let n=k.customElements.get(a);if(n!=null&&n.template)return n.template}}function Ea(t){var e;let a=(e=t.mediaController)==null?void 0:e.querySelector(\"media-time-display\");return a&&getComputedStyle(a).getPropertyValue(\"--media-duration-display-display\").trim()===\"none\"}function _t(t){let a=t.videoTitle?{video_title:t.videoTitle}:{};return t.getAttributeNames().filter(e=>e.startsWith(\"metadata-\")).reduce((e,i)=>{let r=t.getAttribute(i);return r!==null&&(e[i.replace(/^metadata-/,\"\").replace(/-/g,\"_\")]=r),e},a)}var Aa=Object.values(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes),Ca=Object.values(M),ka=Object.values(o),Rt=se(),xt=\"mux-player\",Ot={isDialogOpen:!1},_a={redundant_streams:!0},J,ee,te,I,ae,H,m,w,Mt,we,B,St,Nt,wt,It,Ne=class extends Ce{constructor(){super();T(this,m);T(this,J);T(this,ee,!1);T(this,te,{});T(this,I,!0);T(this,ae,new ne(this,\"hotkeys\"));T(this,H,{...Ot,onCloseErrorDialog:e=>{var r;((r=e.composedPath()[0])==null?void 0:r.localName)===\"media-error-dialog\"&&p(this,m,we).call(this,{isDialogOpen:!1})},onFocusInErrorDialog:e=>{var n;if(((n=e.composedPath()[0])==null?void 0:n.localName)!==\"media-error-dialog\")return;ve(this,Y.activeElement)||e.preventDefault()}});C(this,J,(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.generatePlayerInitTime)()),this.attachShadow({mode:\"open\"}),p(this,m,Mt).call(this),this.isConnected&&p(this,m,w).call(this)}static get NAME(){return xt}static get VERSION(){return Rt}static get observedAttributes(){var e;return[...(e=Ce.observedAttributes)!=null?e:[],...Ca,...Aa,...ka]}get mediaTheme(){var e;return(e=this.shadowRoot)==null?void 0:e.querySelector(\"media-theme\")}get mediaController(){var e,i;return(i=(e=this.mediaTheme)==null?void 0:e.shadowRoot)==null?void 0:i.querySelector(\"media-controller\")}connectedCallback(){let e=this.media;e&&(e.metadata=_t(this))}attributeChangedCallback(e,i,r){switch(p(this,m,w).call(this),super.attributeChangedCallback(e,i,r),e){case o.HOTKEYS:u(this,ae).value=r;break;case o.THUMBNAIL_TIME:{r!=null&&this.tokens.thumbnail&&x((0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Use of thumbnail-time with thumbnail-token is currently unsupported. Ignore thumbnail-time.\").toString());break}case o.THUMBNAIL_TOKEN:{if(r){let d=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.parseJwt)(r);if(d){let{aud:l}=d,b=_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxJWTAud.THUMBNAIL;l!==b&&x((0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.\").format({aud:l,expectedAud:b,tokenNamePrefix:\"thumbnail\"}))}}break}case o.STORYBOARD_TOKEN:{if(r){let d=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.parseJwt)(r);if(d){let{aud:l}=d,b=_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxJWTAud.STORYBOARD;l!==b&&x((0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.\").format({aud:l,expectedAud:b,tokenNamePrefix:\"storyboard\"}))}}break}case o.DRM_TOKEN:{if(r){let d=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.parseJwt)(r);if(d){let{aud:l}=d,b=_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.MuxJWTAud.DRM;l!==b&&x((0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.\").format({aud:l,expectedAud:b,tokenNamePrefix:\"drm\"}))}}break}case _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYBACK_ID:{r!=null&&r.includes(\"?token\")&&E((0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"The specificed playback ID {playbackId} contains a token which must be provided via the playback-token attribute.\").format({playbackId:r}));break}case _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.STREAM_TYPE:r&&![_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.ON_DEMAND,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.UNKNOWN].includes(r)?[\"ll-live\",\"live:dvr\",\"ll-live:dvr\"].includes(this.streamType)?this.targetLiveWindow=r.includes(\"dvr\")?Number.POSITIVE_INFINITY:0:Ee({file:\"invalid-stream-type.md\",message:(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.i18n)(\"Invalid stream-type value supplied: `{streamType}`. Please provide stream-type as either: `on-demand` or `live`\").format({streamType:this.streamType})}):r===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE?this.getAttribute(o.TARGET_LIVE_WINDOW)==null&&(this.targetLiveWindow=0):this.targetLiveWindow=Number.NaN}[_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYBACK_ID,M.SRC,o.PLAYBACK_TOKEN].includes(e)&&i!==r&&C(this,H,{...u(this,H),...Ot}),p(this,m,B).call(this,{[ot(e)]:r})}async requestFullscreen(e){var i;if(!(!this.mediaController||this.mediaController.hasAttribute(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIAttributes.MEDIA_IS_FULLSCREEN)))return(i=this.mediaController)==null||i.dispatchEvent(new k.CustomEvent(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIEvents.MEDIA_ENTER_FULLSCREEN_REQUEST,{composed:!0,bubbles:!0})),new Promise((r,n)=>{var d;(d=this.mediaController)==null||d.addEventListener(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaStateChangeEvents.MEDIA_IS_FULLSCREEN,()=>r(),{once:!0})})}async exitFullscreen(){var e;if(!(!this.mediaController||!this.mediaController.hasAttribute(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIAttributes.MEDIA_IS_FULLSCREEN)))return(e=this.mediaController)==null||e.dispatchEvent(new k.CustomEvent(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIEvents.MEDIA_EXIT_FULLSCREEN_REQUEST,{composed:!0,bubbles:!0})),new Promise((i,r)=>{var n;(n=this.mediaController)==null||n.addEventListener(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaStateChangeEvents.MEDIA_IS_FULLSCREEN,()=>i(),{once:!0})})}get preferCmcd(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_CMCD))!=null?e:void 0}set preferCmcd(e){e!==this.preferCmcd&&(e?_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.CmcdTypeValues.includes(e)?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_CMCD,e):x(`Invalid value for preferCmcd. Must be one of ${_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.CmcdTypeValues.join()}`):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_CMCD))}get hasPlayed(){var e,i;return(i=(e=this.mediaController)==null?void 0:e.hasAttribute(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIAttributes.MEDIA_HAS_PLAYED))!=null?i:!1}get inLiveWindow(){var e;return(e=this.mediaController)==null?void 0:e.hasAttribute(media_chrome_dist_constants_js__WEBPACK_IMPORTED_MODULE_2__.MediaUIAttributes.MEDIA_TIME_IS_LIVE)}get _hls(){var e;return(e=this.media)==null?void 0:e._hls}get mux(){var e;return(e=this.media)==null?void 0:e.mux}get theme(){var e;return(e=this.getAttribute(o.THEME))!=null?e:fa}set theme(e){this.setAttribute(o.THEME,`${e}`)}get themeProps(){let e=this.mediaTheme;if(!e)return;let i={};for(let r of e.getAttributeNames()){if(Se.includes(r))continue;let n=e.getAttribute(r);i[oe(r)]=n===\"\"?!0:n}return i}set themeProps(e){var r,n;p(this,m,w).call(this);let i={...this.themeProps,...e};for(let d in i){if(Se.includes(d))continue;let l=e==null?void 0:e[d];typeof l==\"boolean\"||l==null?(r=this.mediaTheme)==null||r.toggleAttribute(re(d),!!l):(n=this.mediaTheme)==null||n.setAttribute(re(d),l)}}get playbackId(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYBACK_ID))!=null?e:void 0}set playbackId(e){e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYBACK_ID,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYBACK_ID)}get src(){var e,i;return this.playbackId?(e=U(this,M.SRC))!=null?e:void 0:(i=this.getAttribute(M.SRC))!=null?i:void 0}set src(e){e?this.setAttribute(M.SRC,e):this.removeAttribute(M.SRC)}get poster(){var r;let e=this.getAttribute(M.POSTER);if(e!=null)return e;let{tokens:i}=this;if(i.playback&&!i.thumbnail){x(\"Missing expected thumbnail token. No poster image will be shown\");return}if(this.playbackId&&!this.audio)return it(this.playbackId,{customDomain:this.customDomain,thumbnailTime:(r=this.thumbnailTime)!=null?r:this.startTime,programTime:this.programStartTime,token:i.thumbnail})}set poster(e){e||e===\"\"?this.setAttribute(M.POSTER,e):this.removeAttribute(M.POSTER)}get storyboardSrc(){var e;return(e=this.getAttribute(o.STORYBOARD_SRC))!=null?e:void 0}set storyboardSrc(e){e?this.setAttribute(o.STORYBOARD_SRC,e):this.removeAttribute(o.STORYBOARD_SRC)}get storyboard(){let{tokens:e}=this;if(this.storyboardSrc&&!e.storyboard)return this.storyboardSrc;if(!(this.audio||!this.playbackId||!this.streamType||[_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.UNKNOWN].includes(this.streamType)||e.playback&&!e.storyboard))return rt(this.playbackId,{customDomain:this.customDomain,token:e.storyboard,programStartTime:this.programStartTime,programEndTime:this.programEndTime})}get audio(){return this.hasAttribute(o.AUDIO)}set audio(e){if(!e){this.removeAttribute(o.AUDIO);return}this.setAttribute(o.AUDIO,\"\")}get hotkeys(){return u(this,ae)}get nohotkeys(){return this.hasAttribute(o.NOHOTKEYS)}set nohotkeys(e){if(!e){this.removeAttribute(o.NOHOTKEYS);return}this.setAttribute(o.NOHOTKEYS,\"\")}get thumbnailTime(){return y(this.getAttribute(o.THUMBNAIL_TIME))}set thumbnailTime(e){this.setAttribute(o.THUMBNAIL_TIME,`${e}`)}get videoTitle(){var e,i;return(i=(e=this.getAttribute(o.VIDEO_TITLE))!=null?e:this.getAttribute(o.TITLE))!=null?i:\"\"}set videoTitle(e){e!==this.videoTitle&&(e?this.setAttribute(o.VIDEO_TITLE,e):this.removeAttribute(o.VIDEO_TITLE))}get placeholder(){var e;return(e=U(this,o.PLACEHOLDER))!=null?e:\"\"}set placeholder(e){this.setAttribute(o.PLACEHOLDER,`${e}`)}get primaryColor(){var i,r;let e=this.getAttribute(o.PRIMARY_COLOR);if(e!=null||this.mediaTheme&&(e=(r=(i=k.getComputedStyle(this.mediaTheme))==null?void 0:i.getPropertyValue(\"--_primary-color\"))==null?void 0:r.trim(),e))return e}set primaryColor(e){this.setAttribute(o.PRIMARY_COLOR,`${e}`)}get secondaryColor(){var i,r;let e=this.getAttribute(o.SECONDARY_COLOR);if(e!=null||this.mediaTheme&&(e=(r=(i=k.getComputedStyle(this.mediaTheme))==null?void 0:i.getPropertyValue(\"--_secondary-color\"))==null?void 0:r.trim(),e))return e}set secondaryColor(e){this.setAttribute(o.SECONDARY_COLOR,`${e}`)}get accentColor(){var i,r;let e=this.getAttribute(o.ACCENT_COLOR);if(e!=null||this.mediaTheme&&(e=(r=(i=k.getComputedStyle(this.mediaTheme))==null?void 0:i.getPropertyValue(\"--_accent-color\"))==null?void 0:r.trim(),e))return e}set accentColor(e){this.setAttribute(o.ACCENT_COLOR,`${e}`)}get defaultShowRemainingTime(){return this.hasAttribute(o.DEFAULT_SHOW_REMAINING_TIME)}set defaultShowRemainingTime(e){e?this.setAttribute(o.DEFAULT_SHOW_REMAINING_TIME,\"\"):this.removeAttribute(o.DEFAULT_SHOW_REMAINING_TIME)}get playbackRates(){if(this.hasAttribute(o.PLAYBACK_RATES))return this.getAttribute(o.PLAYBACK_RATES).trim().split(/\\s*,?\\s+/).map(e=>Number(e)).filter(e=>!Number.isNaN(e)).sort((e,i)=>e-i)}set playbackRates(e){if(!e){this.removeAttribute(o.PLAYBACK_RATES);return}this.setAttribute(o.PLAYBACK_RATES,e.join(\" \"))}get forwardSeekOffset(){var e;return(e=y(this.getAttribute(o.FORWARD_SEEK_OFFSET)))!=null?e:10}set forwardSeekOffset(e){this.setAttribute(o.FORWARD_SEEK_OFFSET,`${e}`)}get backwardSeekOffset(){var e;return(e=y(this.getAttribute(o.BACKWARD_SEEK_OFFSET)))!=null?e:10}set backwardSeekOffset(e){this.setAttribute(o.BACKWARD_SEEK_OFFSET,`${e}`)}get defaultHiddenCaptions(){return this.hasAttribute(o.DEFAULT_HIDDEN_CAPTIONS)}set defaultHiddenCaptions(e){e?this.setAttribute(o.DEFAULT_HIDDEN_CAPTIONS,\"\"):this.removeAttribute(o.DEFAULT_HIDDEN_CAPTIONS)}get defaultDuration(){return y(this.getAttribute(o.DEFAULT_DURATION))}set defaultDuration(e){e==null?this.removeAttribute(o.DEFAULT_DURATION):this.setAttribute(o.DEFAULT_DURATION,`${e}`)}get playerInitTime(){return this.hasAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_INIT_TIME)?y(this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_INIT_TIME)):u(this,J)}set playerInitTime(e){e!=this.playerInitTime&&(e==null?this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_INIT_TIME):this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_INIT_TIME,`${+e}`))}get playerSoftwareName(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_SOFTWARE_NAME))!=null?e:xt}get playerSoftwareVersion(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PLAYER_SOFTWARE_VERSION))!=null?e:Rt}get beaconCollectionDomain(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.BEACON_COLLECTION_DOMAIN))!=null?e:void 0}set beaconCollectionDomain(e){e!==this.beaconCollectionDomain&&(e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.BEACON_COLLECTION_DOMAIN,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.BEACON_COLLECTION_DOMAIN))}get maxResolution(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MAX_RESOLUTION))!=null?e:void 0}set maxResolution(e){e!==this.maxResolution&&(e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MAX_RESOLUTION,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MAX_RESOLUTION))}get minResolution(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MIN_RESOLUTION))!=null?e:void 0}set minResolution(e){e!==this.minResolution&&(e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MIN_RESOLUTION,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.MIN_RESOLUTION))}get renditionOrder(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.RENDITION_ORDER))!=null?e:void 0}set renditionOrder(e){e!==this.renditionOrder&&(e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.RENDITION_ORDER,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.RENDITION_ORDER))}get programStartTime(){return y(this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_START_TIME))}set programStartTime(e){e==null?this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_START_TIME):this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_START_TIME,`${e}`)}get programEndTime(){return y(this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_END_TIME))}set programEndTime(e){e==null?this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_END_TIME):this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PROGRAM_END_TIME,`${e}`)}get assetStartTime(){return y(this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_START_TIME))}set assetStartTime(e){e==null?this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_START_TIME):this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_START_TIME,`${e}`)}get assetEndTime(){return y(this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_END_TIME))}set assetEndTime(e){e==null?this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_END_TIME):this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ASSET_END_TIME,`${e}`)}get extraSourceParams(){return this.hasAttribute(o.EXTRA_SOURCE_PARAMS)?[...new URLSearchParams(this.getAttribute(o.EXTRA_SOURCE_PARAMS)).entries()].reduce((e,[i,r])=>(e[i]=r,e),{}):_a}set extraSourceParams(e){e==null?this.removeAttribute(o.EXTRA_SOURCE_PARAMS):this.setAttribute(o.EXTRA_SOURCE_PARAMS,new URLSearchParams(e).toString())}get customDomain(){var e;return(e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.CUSTOM_DOMAIN))!=null?e:void 0}set customDomain(e){e!==this.customDomain&&(e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.CUSTOM_DOMAIN,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.CUSTOM_DOMAIN))}get envKey(){var e;return(e=U(this,_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ENV_KEY))!=null?e:void 0}set envKey(e){this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.ENV_KEY,`${e}`)}get noVolumePref(){return this.hasAttribute(o.NO_VOLUME_PREF)}set noVolumePref(e){e?this.setAttribute(o.NO_VOLUME_PREF,\"\"):this.removeAttribute(o.NO_VOLUME_PREF)}get debug(){return U(this,_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DEBUG)!=null}set debug(e){e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DEBUG,\"\"):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DEBUG)}get disableTracking(){return U(this,_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DISABLE_TRACKING)!=null}set disableTracking(e){this.toggleAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DISABLE_TRACKING,!!e)}get disableCookies(){return U(this,_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DISABLE_COOKIES)!=null}set disableCookies(e){e?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DISABLE_COOKIES,\"\"):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.DISABLE_COOKIES)}get streamType(){var e,i,r;return(r=(i=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.STREAM_TYPE))!=null?i:(e=this.media)==null?void 0:e.streamType)!=null?r:_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.UNKNOWN}set streamType(e){this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.STREAM_TYPE,`${e}`)}get defaultStreamType(){var e,i,r;return(r=(i=this.getAttribute(o.DEFAULT_STREAM_TYPE))!=null?i:(e=this.mediaController)==null?void 0:e.getAttribute(o.DEFAULT_STREAM_TYPE))!=null?r:_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.ON_DEMAND}set defaultStreamType(e){e?this.setAttribute(o.DEFAULT_STREAM_TYPE,e):this.removeAttribute(o.DEFAULT_STREAM_TYPE)}get targetLiveWindow(){var e,i;return this.hasAttribute(o.TARGET_LIVE_WINDOW)?+this.getAttribute(o.TARGET_LIVE_WINDOW):(i=(e=this.media)==null?void 0:e.targetLiveWindow)!=null?i:Number.NaN}set targetLiveWindow(e){e==this.targetLiveWindow||Number.isNaN(e)&&Number.isNaN(this.targetLiveWindow)||(e==null?this.removeAttribute(o.TARGET_LIVE_WINDOW):this.setAttribute(o.TARGET_LIVE_WINDOW,`${+e}`))}get liveEdgeStart(){var e;return(e=this.media)==null?void 0:e.liveEdgeStart}get startTime(){return y(U(this,_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.START_TIME))}set startTime(e){this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.START_TIME,`${e}`)}get preferPlayback(){let e=this.getAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_PLAYBACK);if(e===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.PlaybackTypes.MSE||e===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.PlaybackTypes.NATIVE)return e}set preferPlayback(e){e!==this.preferPlayback&&(e===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.PlaybackTypes.MSE||e===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.PlaybackTypes.NATIVE?this.setAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_PLAYBACK,e):this.removeAttribute(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.Attributes.PREFER_PLAYBACK))}get metadata(){var e;return(e=this.media)==null?void 0:e.metadata}set metadata(e){if(p(this,m,w).call(this),!this.media){E(\"underlying media element missing when trying to set metadata. metadata will not be set.\");return}this.media.metadata={..._t(this),...e}}get _hlsConfig(){var e;return(e=this.media)==null?void 0:e._hlsConfig}set _hlsConfig(e){if(p(this,m,w).call(this),!this.media){E(\"underlying media element missing when trying to set _hlsConfig. _hlsConfig will not be set.\");return}this.media._hlsConfig=e}async addCuePoints(e){var i;if(p(this,m,w).call(this),!this.media){E(\"underlying media element missing when trying to addCuePoints. cuePoints will not be added.\");return}return(i=this.media)==null?void 0:i.addCuePoints(e)}get activeCuePoint(){var e;return(e=this.media)==null?void 0:e.activeCuePoint}get cuePoints(){var e,i;return(i=(e=this.media)==null?void 0:e.cuePoints)!=null?i:[]}addChapters(e){var i;if(p(this,m,w).call(this),!this.media){E(\"underlying media element missing when trying to addChapters. chapters will not be added.\");return}return(i=this.media)==null?void 0:i.addChapters(e)}get activeChapter(){var e;return(e=this.media)==null?void 0:e.activeChapter}get chapters(){var e,i;return(i=(e=this.media)==null?void 0:e.chapters)!=null?i:[]}getStartDate(){var e;return(e=this.media)==null?void 0:e.getStartDate()}get currentPdt(){var e;return(e=this.media)==null?void 0:e.currentPdt}get tokens(){let e=this.getAttribute(o.PLAYBACK_TOKEN),i=this.getAttribute(o.DRM_TOKEN),r=this.getAttribute(o.THUMBNAIL_TOKEN),n=this.getAttribute(o.STORYBOARD_TOKEN);return{...u(this,te),...e!=null?{playback:e}:{},...i!=null?{drm:i}:{},...r!=null?{thumbnail:r}:{},...n!=null?{storyboard:n}:{}}}set tokens(e){C(this,te,e!=null?e:{})}get playbackToken(){var e;return(e=this.getAttribute(o.PLAYBACK_TOKEN))!=null?e:void 0}set playbackToken(e){this.setAttribute(o.PLAYBACK_TOKEN,`${e}`)}get drmToken(){var e;return(e=this.getAttribute(o.DRM_TOKEN))!=null?e:void 0}set drmToken(e){this.setAttribute(o.DRM_TOKEN,`${e}`)}get thumbnailToken(){var e;return(e=this.getAttribute(o.THUMBNAIL_TOKEN))!=null?e:void 0}set thumbnailToken(e){this.setAttribute(o.THUMBNAIL_TOKEN,`${e}`)}get storyboardToken(){var e;return(e=this.getAttribute(o.STORYBOARD_TOKEN))!=null?e:void 0}set storyboardToken(e){this.setAttribute(o.STORYBOARD_TOKEN,`${e}`)}addTextTrack(e,i,r,n){var l;let d=(l=this.media)==null?void 0:l.nativeEl;if(d)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.addTextTrack)(d,e,i,r,n)}removeTextTrack(e){var r;let i=(r=this.media)==null?void 0:r.nativeEl;if(i)return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.removeTextTrack)(i,e)}get textTracks(){var e;return(e=this.media)==null?void 0:e.textTracks}get castReceiver(){var e;return(e=this.getAttribute(o.CAST_RECEIVER))!=null?e:void 0}set castReceiver(e){e!==this.castReceiver&&(e?this.setAttribute(o.CAST_RECEIVER,e):this.removeAttribute(o.CAST_RECEIVER))}get castCustomData(){var e;return(e=this.media)==null?void 0:e.castCustomData}set castCustomData(e){if(!this.media){E(\"underlying media element missing when trying to set castCustomData. castCustomData will not be set.\");return}this.media.castCustomData=e}get noTooltips(){return this.hasAttribute(o.NO_TOOLTIPS)}set noTooltips(e){if(!e){this.removeAttribute(o.NO_TOOLTIPS);return}this.setAttribute(o.NO_TOOLTIPS,\"\")}get proudlyDisplayMuxBadge(){return this.hasAttribute(o.PROUDLY_DISPLAY_MUX_BADGE)}set proudlyDisplayMuxBadge(e){e?this.setAttribute(o.PROUDLY_DISPLAY_MUX_BADGE,\"\"):this.removeAttribute(o.PROUDLY_DISPLAY_MUX_BADGE)}};J=new WeakMap,ee=new WeakMap,te=new WeakMap,I=new WeakMap,ae=new WeakMap,H=new WeakMap,m=new WeakSet,w=function(){var e,i,r,n;if(!u(this,ee)){C(this,ee,!0),p(this,m,B).call(this);try{if(customElements.upgrade(this.mediaTheme),!(this.mediaTheme instanceof k.HTMLElement))throw\"\"}catch{E(\"<media-theme> failed to upgrade!\")}try{customElements.upgrade(this.media)}catch{E(\"underlying media element failed to upgrade!\")}try{if(customElements.upgrade(this.mediaController),!(this.mediaController instanceof media_chrome__WEBPACK_IMPORTED_MODULE_0__.MediaController))throw\"\"}catch{E(\"<media-controller> failed to upgrade!\")}p(this,m,St).call(this),p(this,m,Nt).call(this),p(this,m,wt).call(this),C(this,I,(i=(e=this.mediaController)==null?void 0:e.hasAttribute(media_chrome_dist_media_container_js__WEBPACK_IMPORTED_MODULE_1__.Attributes.USER_INACTIVE))!=null?i:!0),p(this,m,It).call(this),(r=this.media)==null||r.addEventListener(\"streamtypechange\",()=>p(this,m,B).call(this)),(n=this.media)==null||n.addEventListener(\"loadstart\",()=>p(this,m,B).call(this))}},Mt=function(){var e,i;try{(e=window==null?void 0:window.CSS)==null||e.registerProperty({name:\"--media-primary-color\",syntax:\"<color>\",inherits:!0}),(i=window==null?void 0:window.CSS)==null||i.registerProperty({name:\"--media-secondary-color\",syntax:\"<color>\",inherits:!0})}catch{}},we=function(e){Object.assign(u(this,H),e),p(this,m,B).call(this)},B=function(e={}){ct(bt(ya(this,{...u(this,H),...e})),this.shadowRoot)},St=function(){let e=r=>{var l,b;if(!(r!=null&&r.startsWith(\"theme-\")))return;let n=r.replace(/^theme-/,\"\");if(Se.includes(n))return;let d=this.getAttribute(r);d!=null?(l=this.mediaTheme)==null||l.setAttribute(n,d):(b=this.mediaTheme)==null||b.removeAttribute(n)};new MutationObserver(r=>{for(let{attributeName:n}of r)e(n)}).observe(this,{attributes:!0}),this.getAttributeNames().forEach(e)},Nt=function(){let e=i=>{var d;let r=(d=this.media)==null?void 0:d.error;if(!(r instanceof _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.MediaError)){let{message:l,code:b}=r!=null?r:{};r=new _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.MediaError(l,b)}if(!(r!=null&&r.fatal)){x(r),r.data&&x(`${r.name} data:`,r.data);return}let n=Re(r,!1);n.message&&Ee(n),E(r),r.data&&E(`${r.name} data:`,r.data),p(this,m,we).call(this,{isDialogOpen:!0})};this.addEventListener(\"error\",e),this.media&&(this.media.errorTranslator=(i={})=>{var n,d,l;if(!(((n=this.media)==null?void 0:n.error)instanceof _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_4__.MediaError))return i;let r=Re((d=this.media)==null?void 0:d.error,!1);return{player_error_code:(l=this.media)==null?void 0:l.error.code,player_error_message:r.message?String(r.message):i.player_error_message,player_error_context:r.context?String(r.context):i.player_error_context}})},wt=function(){var i,r,n,d;let e=()=>p(this,m,B).call(this);(r=(i=this.media)==null?void 0:i.textTracks)==null||r.addEventListener(\"addtrack\",e),(d=(n=this.media)==null?void 0:n.textTracks)==null||d.addEventListener(\"removetrack\",e)},It=function(){var S,F;if(!/Firefox/i.test(navigator.userAgent))return;let i,r=new WeakMap,n=()=>this.streamType===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE&&!this.secondaryColor&&this.offsetWidth>=800,d=(_,A,R=!1)=>{if(n())return;Array.from(_&&_.activeCues||[]).forEach(h=>{if(!(!h.snapToLines||h.line<-5||h.line>=0&&h.line<10))if(!A||this.paused){let ie=h.text.split(`\n`).length,W=-3;this.streamType===_mux_playback_core__WEBPACK_IMPORTED_MODULE_5__.StreamTypes.LIVE&&(W=-2);let Z=W-ie;if(h.line===Z&&!R)return;r.has(h)||r.set(h,h.line),h.line=Z}else setTimeout(()=>{h.line=r.get(h)||\"auto\"},500)})},l=()=>{var _,A;d(i,(A=(_=this.mediaController)==null?void 0:_.hasAttribute(media_chrome_dist_media_container_js__WEBPACK_IMPORTED_MODULE_1__.Attributes.USER_INACTIVE))!=null?A:!1)},b=()=>{var R,K;let A=Array.from(((K=(R=this.mediaController)==null?void 0:R.media)==null?void 0:K.textTracks)||[]).filter(h=>[\"subtitles\",\"captions\"].includes(h.kind)&&h.mode===\"showing\")[0];A!==i&&(i==null||i.removeEventListener(\"cuechange\",l)),i=A,i==null||i.addEventListener(\"cuechange\",l),d(i,u(this,I))};b(),(S=this.textTracks)==null||S.addEventListener(\"change\",b),(F=this.textTracks)==null||F.addEventListener(\"addtrack\",b),this.addEventListener(\"userinactivechange\",()=>{var A,R;let _=(R=(A=this.mediaController)==null?void 0:A.hasAttribute(media_chrome_dist_media_container_js__WEBPACK_IMPORTED_MODULE_1__.Attributes.USER_INACTIVE))!=null?R:!0;u(this,I)!==_&&(C(this,I,_),d(i,u(this,I)))})};function U(t,a){return t.media?t.media.getAttribute(a):t.getAttribute(a)}var Ei=Ne;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/mux-player/dist/base.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@mux/mux-player/dist/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/@mux/mux-player/dist/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MediaError: () => (/* reexport safe */ _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__.MediaError),\n/* harmony export */   \"default\": () => (/* binding */ F),\n/* harmony export */   generatePlayerInitTime: () => (/* reexport safe */ _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__.generatePlayerInitTime),\n/* harmony export */   getVideoAttribute: () => (/* reexport safe */ _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__.getVideoAttribute),\n/* harmony export */   playerSoftwareName: () => (/* reexport safe */ _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__.playerSoftwareName),\n/* harmony export */   playerSoftwareVersion: () => (/* reexport safe */ _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__.playerSoftwareVersion)\n/* harmony export */ });\n/* harmony import */ var _mux_mux_video__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mux/mux-video */ \"(ssr)/./node_modules/@mux/mux-video/dist/index.mjs\");\n/* harmony import */ var _mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mux/mux-player/base */ \"(ssr)/./node_modules/@mux/mux-player/dist/base.mjs\");\nvar c=e=>{throw TypeError(e)};var d=(e,t,n)=>t.has(e)||c(\"Cannot \"+n);var g=(e,t,n)=>(d(e,t,\"read from private field\"),n?n.call(e):t.get(e)),p=(e,t,n)=>t.has(e)?c(\"Cannot add the same private member more than once\"):t instanceof WeakSet?t.add(e):t.set(e,n),f=(e,t,n,i)=>(d(e,t,\"write to private field\"),i?i.call(e,n):t.set(e,n),n);var o=class{addEventListener(){}removeEventListener(){}dispatchEvent(t){return!0}};if(typeof DocumentFragment==\"undefined\"){class e extends o{}globalThis.DocumentFragment=e}var s=class extends o{},a=class extends o{},b={get(e){},define(e,t,n){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(s)}},r,m=class{constructor(t,n={}){p(this,r);f(this,r,n==null?void 0:n.detail)}get detail(){return g(this,r)}initCustomEvent(){}};r=new WeakMap;function y(e,t){return new s}var h={document:{createElement:y},DocumentFragment,customElements:b,CustomEvent:m,EventTarget:o,HTMLElement:s,HTMLVideoElement:a},E=typeof window==\"undefined\"||typeof globalThis.customElements==\"undefined\",l=E?h:globalThis,x=E?h.document:globalThis.document;l.customElements.get(\"mux-player\")||(l.customElements.define(\"mux-player\",_mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),l.MuxPlayerElement=_mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);var F=_mux_mux_player_base__WEBPACK_IMPORTED_MODULE_1__[\"default\"];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQG11eC9tdXgtcGxheWVyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEsVUFBVSxvQkFBb0Isd0NBQXdDLHFRQUFxUSxZQUFZLG9CQUFvQix1QkFBdUIsaUJBQWlCLFdBQVcseUNBQXlDLG1CQUFtQiw4QkFBOEIsdUJBQXVCLG9CQUFvQixJQUFJLFFBQVEsZ0JBQWdCLFlBQVksWUFBWSxhQUFhLGdCQUFnQiwyQkFBMkIsV0FBVyxrQkFBa0IsRUFBRSxVQUFVLGtDQUFrQyxhQUFhLGlCQUFpQixxQkFBcUIsY0FBYyxnQkFBZ0IsYUFBYSxPQUFPLFVBQVUsZ0JBQWdCLGdHQUFnRyxpSUFBOE4sMEVBQTBFLDREQUFDLHFCQUFxQiw0REFBQyxFQUFFLE1BQU0sNERBQUMsQ0FBc0IiLCJzb3VyY2VzIjpbIi9Vc2Vycy9FdGhhbkxlZS9EZXNrdG9wL0FkdlgvT3Blbi1MTE0tVlR1YmVyLVdlYi9ub2RlX21vZHVsZXMvQG11eC9tdXgtcGxheWVyL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBjPWU9Pnt0aHJvdyBUeXBlRXJyb3IoZSl9O3ZhciBkPShlLHQsbik9PnQuaGFzKGUpfHxjKFwiQ2Fubm90IFwiK24pO3ZhciBnPShlLHQsbik9PihkKGUsdCxcInJlYWQgZnJvbSBwcml2YXRlIGZpZWxkXCIpLG4/bi5jYWxsKGUpOnQuZ2V0KGUpKSxwPShlLHQsbik9PnQuaGFzKGUpP2MoXCJDYW5ub3QgYWRkIHRoZSBzYW1lIHByaXZhdGUgbWVtYmVyIG1vcmUgdGhhbiBvbmNlXCIpOnQgaW5zdGFuY2VvZiBXZWFrU2V0P3QuYWRkKGUpOnQuc2V0KGUsbiksZj0oZSx0LG4saSk9PihkKGUsdCxcIndyaXRlIHRvIHByaXZhdGUgZmllbGRcIiksaT9pLmNhbGwoZSxuKTp0LnNldChlLG4pLG4pO3ZhciBvPWNsYXNze2FkZEV2ZW50TGlzdGVuZXIoKXt9cmVtb3ZlRXZlbnRMaXN0ZW5lcigpe31kaXNwYXRjaEV2ZW50KHQpe3JldHVybiEwfX07aWYodHlwZW9mIERvY3VtZW50RnJhZ21lbnQ9PVwidW5kZWZpbmVkXCIpe2NsYXNzIGUgZXh0ZW5kcyBve31nbG9iYWxUaGlzLkRvY3VtZW50RnJhZ21lbnQ9ZX12YXIgcz1jbGFzcyBleHRlbmRzIG97fSxhPWNsYXNzIGV4dGVuZHMgb3t9LGI9e2dldChlKXt9LGRlZmluZShlLHQsbil7fSxnZXROYW1lKGUpe3JldHVybiBudWxsfSx1cGdyYWRlKGUpe30sd2hlbkRlZmluZWQoZSl7cmV0dXJuIFByb21pc2UucmVzb2x2ZShzKX19LHIsbT1jbGFzc3tjb25zdHJ1Y3Rvcih0LG49e30pe3AodGhpcyxyKTtmKHRoaXMscixuPT1udWxsP3ZvaWQgMDpuLmRldGFpbCl9Z2V0IGRldGFpbCgpe3JldHVybiBnKHRoaXMscil9aW5pdEN1c3RvbUV2ZW50KCl7fX07cj1uZXcgV2Vha01hcDtmdW5jdGlvbiB5KGUsdCl7cmV0dXJuIG5ldyBzfXZhciBoPXtkb2N1bWVudDp7Y3JlYXRlRWxlbWVudDp5fSxEb2N1bWVudEZyYWdtZW50LGN1c3RvbUVsZW1lbnRzOmIsQ3VzdG9tRXZlbnQ6bSxFdmVudFRhcmdldDpvLEhUTUxFbGVtZW50OnMsSFRNTFZpZGVvRWxlbWVudDphfSxFPXR5cGVvZiB3aW5kb3c9PVwidW5kZWZpbmVkXCJ8fHR5cGVvZiBnbG9iYWxUaGlzLmN1c3RvbUVsZW1lbnRzPT1cInVuZGVmaW5lZFwiLGw9RT9oOmdsb2JhbFRoaXMseD1FP2guZG9jdW1lbnQ6Z2xvYmFsVGhpcy5kb2N1bWVudDtpbXBvcnRcIkBtdXgvbXV4LXZpZGVvXCI7aW1wb3J0IHUgZnJvbVwiQG11eC9tdXgtcGxheWVyL2Jhc2VcIjtleHBvcnQqZnJvbVwiQG11eC9tdXgtcGxheWVyL2Jhc2VcIjtsLmN1c3RvbUVsZW1lbnRzLmdldChcIm11eC1wbGF5ZXJcIil8fChsLmN1c3RvbUVsZW1lbnRzLmRlZmluZShcIm11eC1wbGF5ZXJcIix1KSxsLk11eFBsYXllckVsZW1lbnQ9dSk7dmFyIEY9dTtleHBvcnR7RiBhcyBkZWZhdWx0fTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/mux-player/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@mux/mux-video/dist/base.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@mux/mux-video/dist/base.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attributes: () => (/* binding */ e),\n/* harmony export */   Events: () => (/* reexport safe */ custom_media_element__WEBPACK_IMPORTED_MODULE_1__.Events),\n/* harmony export */   MediaError: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.MediaError),\n/* harmony export */   MuxVideoBaseElement: () => (/* binding */ K),\n/* harmony export */   generatePlayerInitTime: () => (/* reexport safe */ _mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.generatePlayerInitTime),\n/* harmony export */   playerSoftwareName: () => (/* binding */ x),\n/* harmony export */   playerSoftwareVersion: () => (/* binding */ v)\n/* harmony export */ });\n/* harmony import */ var _mux_playback_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mux/playback-core */ \"(ssr)/./node_modules/@mux/playback-core/dist/index.mjs\");\n/* harmony import */ var custom_media_element__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! custom-media-element */ \"(ssr)/./node_modules/custom-media-element/dist/custom-media-element.js\");\nvar C=s=>{throw TypeError(s)};var S=(s,a,t)=>a.has(s)||C(\"Cannot \"+t);var n=(s,a,t)=>(S(s,a,\"read from private field\"),t?t.call(s):a.get(s)),u=(s,a,t)=>a.has(s)?C(\"Cannot add the same private member more than once\"):a instanceof WeakSet?a.add(s):a.set(s,t),o=(s,a,t,i)=>(S(s,a,\"write to private field\"),i?i.call(s,t):a.set(s,t),t),M=(s,a,t)=>(S(s,a,\"access private method\"),t);var Y=()=>{try{return\"0.26.1\"}catch{}return\"UNKNOWN\"},B=Y(),P=()=>B;var k=`\n<svg xmlns=\"http://www.w3.org/2000/svg\" xml:space=\"preserve\" part=\"logo\" style=\"fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2\" viewBox=\"0 0 1600 500\"><g fill=\"#fff\"><path d=\"M994.287 93.486c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m0-93.486c-34.509 0-62.484 27.976-62.484 62.486v187.511c0 68.943-56.09 125.033-125.032 125.033s-125.03-56.09-125.03-125.033V62.486C681.741 27.976 653.765 0 619.256 0s-62.484 27.976-62.484 62.486v187.511C556.772 387.85 668.921 500 806.771 500c137.851 0 250.001-112.15 250.001-250.003V62.486c0-34.51-27.976-62.486-62.485-62.486M1537.51 468.511c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31m-275.883-218.509-143.33 143.329c-24.402 24.402-24.402 63.966 0 88.368 24.402 24.402 63.967 24.402 88.369 0l143.33-143.329 143.328 143.329c24.402 24.4 63.967 24.402 88.369 0 24.403-24.402 24.403-63.966.001-88.368l-143.33-143.329.001-.004 143.329-143.329c24.402-24.402 24.402-63.965 0-88.367s-63.967-24.402-88.369 0L1349.996 161.63 1206.667 18.302c-24.402-24.401-63.967-24.402-88.369 0s-24.402 63.965 0 88.367l143.329 143.329v.004ZM437.511 468.521c-17.121 0-31-13.879-31-31 0-17.121 13.879-31 31-31 17.121 0 31 13.879 31 31 0 17.121-13.879 31-31 31M461.426 4.759C438.078-4.913 411.2.432 393.33 18.303L249.999 161.632 106.669 18.303C88.798.432 61.922-4.913 38.573 4.759 15.224 14.43-.001 37.214-.001 62.488v375.026c0 34.51 27.977 62.486 62.487 62.486 34.51 0 62.486-27.976 62.486-62.486V213.341l80.843 80.844c24.404 24.402 63.965 24.402 88.369 0l80.843-80.844v224.173c0 34.51 27.976 62.486 62.486 62.486s62.486-27.976 62.486-62.486V62.488c0-25.274-15.224-48.058-38.573-57.729\" style=\"fill-rule:nonzero\"/></g></svg>`;var e={BEACON_COLLECTION_DOMAIN:\"beacon-collection-domain\",CUSTOM_DOMAIN:\"custom-domain\",DEBUG:\"debug\",DISABLE_TRACKING:\"disable-tracking\",DISABLE_COOKIES:\"disable-cookies\",DRM_TOKEN:\"drm-token\",PLAYBACK_TOKEN:\"playback-token\",ENV_KEY:\"env-key\",MAX_RESOLUTION:\"max-resolution\",MIN_RESOLUTION:\"min-resolution\",RENDITION_ORDER:\"rendition-order\",PROGRAM_START_TIME:\"program-start-time\",PROGRAM_END_TIME:\"program-end-time\",ASSET_START_TIME:\"asset-start-time\",ASSET_END_TIME:\"asset-end-time\",METADATA_URL:\"metadata-url\",PLAYBACK_ID:\"playback-id\",PLAYER_SOFTWARE_NAME:\"player-software-name\",PLAYER_SOFTWARE_VERSION:\"player-software-version\",PLAYER_INIT_TIME:\"player-init-time\",PREFER_CMCD:\"prefer-cmcd\",PREFER_PLAYBACK:\"prefer-playback\",START_TIME:\"start-time\",STREAM_TYPE:\"stream-type\",TARGET_LIVE_WINDOW:\"target-live-window\",LIVE_EDGE_OFFSET:\"live-edge-offset\",TYPE:\"type\",LOGO:\"logo\"},at=Object.values(e),v=P(),x=\"mux-video\",l,f,c,A,b,T,p,_,O,g,m,y,K=class extends custom_media_element__WEBPACK_IMPORTED_MODULE_1__.CustomVideoElement{constructor(){super();u(this,m);u(this,l);u(this,f);u(this,c);u(this,A,{});u(this,b,{});u(this,T);u(this,p);u(this,_);u(this,O);u(this,g,\"\");o(this,c,(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.generatePlayerInitTime)()),this.nativeEl.addEventListener(\"muxmetadata\",t=>{var d;let i=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getMetadata)(this.nativeEl),r=(d=this.metadata)!=null?d:{};this.metadata={...i,...r},(i==null?void 0:i[\"com.mux.video.branding\"])===\"mux-free-plan\"&&(o(this,g,\"default\"),this.updateLogo())})}static get NAME(){return x}static get VERSION(){return v}static get observedAttributes(){var t;return[...at,...(t=custom_media_element__WEBPACK_IMPORTED_MODULE_1__.CustomVideoElement.observedAttributes)!=null?t:[]]}static getLogoHTML(t){return!t||t===\"false\"?\"\":t===\"default\"?k:`<img part=\"logo\" src=\"${t}\" />`}static getTemplateHTML(t={}){var i;return`\n      ${custom_media_element__WEBPACK_IMPORTED_MODULE_1__.CustomVideoElement.getTemplateHTML(t)}\n      <style>\n        :host {\n          position: relative;\n        }\n        slot[name=\"logo\"] {\n          display: flex;\n          justify-content: end;\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          opacity: 0;\n          transition: opacity 0.25s ease-in-out;\n          z-index: 1;\n        }\n        slot[name=\"logo\"]:has([part=\"logo\"]) {\n          opacity: 1;\n        }\n        slot[name=\"logo\"] [part=\"logo\"] {\n          width: 5rem;\n          pointer-events: none;\n          user-select: none;\n        }\n      </style>\n      <slot name=\"logo\">\n        ${this.getLogoHTML((i=t[e.LOGO])!=null?i:\"\")}\n      </slot>\n    `}get preferCmcd(){var t;return(t=this.getAttribute(e.PREFER_CMCD))!=null?t:void 0}set preferCmcd(t){t!==this.preferCmcd&&(t?_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.CmcdTypeValues.includes(t)?this.setAttribute(e.PREFER_CMCD,t):console.warn(`Invalid value for preferCmcd. Must be one of ${_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.CmcdTypeValues.join()}`):this.removeAttribute(e.PREFER_CMCD))}get playerInitTime(){return this.hasAttribute(e.PLAYER_INIT_TIME)?+this.getAttribute(e.PLAYER_INIT_TIME):n(this,c)}set playerInitTime(t){t!=this.playerInitTime&&(t==null?this.removeAttribute(e.PLAYER_INIT_TIME):this.setAttribute(e.PLAYER_INIT_TIME,`${+t}`))}get playerSoftwareName(){var t;return(t=n(this,_))!=null?t:x}set playerSoftwareName(t){o(this,_,t)}get playerSoftwareVersion(){var t;return(t=n(this,p))!=null?t:v}set playerSoftwareVersion(t){o(this,p,t)}get _hls(){var t;return(t=n(this,l))==null?void 0:t.engine}get mux(){var t;return(t=this.nativeEl)==null?void 0:t.mux}get error(){var t;return(t=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getError)(this.nativeEl))!=null?t:null}get errorTranslator(){return n(this,O)}set errorTranslator(t){o(this,O,t)}get src(){return this.getAttribute(\"src\")}set src(t){t!==this.src&&(t==null?this.removeAttribute(\"src\"):this.setAttribute(\"src\",t))}get type(){var t;return(t=this.getAttribute(e.TYPE))!=null?t:void 0}set type(t){t!==this.type&&(t?this.setAttribute(e.TYPE,t):this.removeAttribute(e.TYPE))}get preload(){let t=this.getAttribute(\"preload\");return t===\"\"?\"auto\":[\"none\",\"metadata\",\"auto\"].includes(t)?t:super.preload}set preload(t){t!=this.getAttribute(\"preload\")&&([\"\",\"none\",\"metadata\",\"auto\"].includes(t)?this.setAttribute(\"preload\",t):this.removeAttribute(\"preload\"))}get debug(){return this.getAttribute(e.DEBUG)!=null}set debug(t){t!==this.debug&&(t?this.setAttribute(e.DEBUG,\"\"):this.removeAttribute(e.DEBUG))}get disableTracking(){return this.hasAttribute(e.DISABLE_TRACKING)}set disableTracking(t){t!==this.disableTracking&&this.toggleAttribute(e.DISABLE_TRACKING,!!t)}get disableCookies(){return this.hasAttribute(e.DISABLE_COOKIES)}set disableCookies(t){t!==this.disableCookies&&(t?this.setAttribute(e.DISABLE_COOKIES,\"\"):this.removeAttribute(e.DISABLE_COOKIES))}get startTime(){let t=this.getAttribute(e.START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set startTime(t){t!==this.startTime&&(t==null?this.removeAttribute(e.START_TIME):this.setAttribute(e.START_TIME,`${t}`))}get playbackId(){var t;return this.hasAttribute(e.PLAYBACK_ID)?this.getAttribute(e.PLAYBACK_ID):(t=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.toPlaybackIdFromSrc)(this.src))!=null?t:void 0}set playbackId(t){t!==this.playbackId&&(t?this.setAttribute(e.PLAYBACK_ID,t):this.removeAttribute(e.PLAYBACK_ID))}get maxResolution(){var t;return(t=this.getAttribute(e.MAX_RESOLUTION))!=null?t:void 0}set maxResolution(t){t!==this.maxResolution&&(t?this.setAttribute(e.MAX_RESOLUTION,t):this.removeAttribute(e.MAX_RESOLUTION))}get minResolution(){var t;return(t=this.getAttribute(e.MIN_RESOLUTION))!=null?t:void 0}set minResolution(t){t!==this.minResolution&&(t?this.setAttribute(e.MIN_RESOLUTION,t):this.removeAttribute(e.MIN_RESOLUTION))}get renditionOrder(){var t;return(t=this.getAttribute(e.RENDITION_ORDER))!=null?t:void 0}set renditionOrder(t){t!==this.renditionOrder&&(t?this.setAttribute(e.RENDITION_ORDER,t):this.removeAttribute(e.RENDITION_ORDER))}get programStartTime(){let t=this.getAttribute(e.PROGRAM_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programStartTime(t){t==null?this.removeAttribute(e.PROGRAM_START_TIME):this.setAttribute(e.PROGRAM_START_TIME,`${t}`)}get programEndTime(){let t=this.getAttribute(e.PROGRAM_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set programEndTime(t){t==null?this.removeAttribute(e.PROGRAM_END_TIME):this.setAttribute(e.PROGRAM_END_TIME,`${t}`)}get assetStartTime(){let t=this.getAttribute(e.ASSET_START_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetStartTime(t){t==null?this.removeAttribute(e.ASSET_START_TIME):this.setAttribute(e.ASSET_START_TIME,`${t}`)}get assetEndTime(){let t=this.getAttribute(e.ASSET_END_TIME);if(t==null)return;let i=+t;return Number.isNaN(i)?void 0:i}set assetEndTime(t){t==null?this.removeAttribute(e.ASSET_END_TIME):this.setAttribute(e.ASSET_END_TIME,`${t}`)}get customDomain(){var t;return(t=this.getAttribute(e.CUSTOM_DOMAIN))!=null?t:void 0}set customDomain(t){t!==this.customDomain&&(t?this.setAttribute(e.CUSTOM_DOMAIN,t):this.removeAttribute(e.CUSTOM_DOMAIN))}get drmToken(){var t;return(t=this.getAttribute(e.DRM_TOKEN))!=null?t:void 0}set drmToken(t){t!==this.drmToken&&(t?this.setAttribute(e.DRM_TOKEN,t):this.removeAttribute(e.DRM_TOKEN))}get playbackToken(){var t,i,r,d;if(this.hasAttribute(e.PLAYBACK_TOKEN))return(t=this.getAttribute(e.PLAYBACK_TOKEN))!=null?t:void 0;if(this.hasAttribute(e.PLAYBACK_ID)){let[,E]=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.toPlaybackIdParts)((i=this.playbackId)!=null?i:\"\");return(r=new URLSearchParams(E).get(\"token\"))!=null?r:void 0}if(this.src)return(d=new URLSearchParams(this.src).get(\"token\"))!=null?d:void 0}set playbackToken(t){t!==this.playbackToken&&(t?this.setAttribute(e.PLAYBACK_TOKEN,t):this.removeAttribute(e.PLAYBACK_TOKEN))}get tokens(){let t=this.getAttribute(e.PLAYBACK_TOKEN),i=this.getAttribute(e.DRM_TOKEN);return{...n(this,b),...t!=null?{playback:t}:{},...i!=null?{drm:i}:{}}}set tokens(t){o(this,b,t!=null?t:{})}get ended(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getEnded)(this.nativeEl,this._hls)}get envKey(){var t;return(t=this.getAttribute(e.ENV_KEY))!=null?t:void 0}set envKey(t){t!==this.envKey&&(t?this.setAttribute(e.ENV_KEY,t):this.removeAttribute(e.ENV_KEY))}get beaconCollectionDomain(){var t;return(t=this.getAttribute(e.BEACON_COLLECTION_DOMAIN))!=null?t:void 0}set beaconCollectionDomain(t){t!==this.beaconCollectionDomain&&(t?this.setAttribute(e.BEACON_COLLECTION_DOMAIN,t):this.removeAttribute(e.BEACON_COLLECTION_DOMAIN))}get streamType(){var t;return(t=this.getAttribute(e.STREAM_TYPE))!=null?t:(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getStreamType)(this.nativeEl)}set streamType(t){t!==this.streamType&&(t?this.setAttribute(e.STREAM_TYPE,t):this.removeAttribute(e.STREAM_TYPE))}get targetLiveWindow(){return this.hasAttribute(e.TARGET_LIVE_WINDOW)?+this.getAttribute(e.TARGET_LIVE_WINDOW):(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getTargetLiveWindow)(this.nativeEl)}set targetLiveWindow(t){t!=this.targetLiveWindow&&(t==null?this.removeAttribute(e.TARGET_LIVE_WINDOW):this.setAttribute(e.TARGET_LIVE_WINDOW,`${+t}`))}get liveEdgeStart(){var t,i;if(this.hasAttribute(e.LIVE_EDGE_OFFSET)){let{liveEdgeOffset:r}=this,d=(t=this.nativeEl.seekable.end(0))!=null?t:0,E=(i=this.nativeEl.seekable.start(0))!=null?i:0;return Math.max(E,d-r)}return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getLiveEdgeStart)(this.nativeEl)}get liveEdgeOffset(){if(this.hasAttribute(e.LIVE_EDGE_OFFSET))return+this.getAttribute(e.LIVE_EDGE_OFFSET)}set liveEdgeOffset(t){t!=this.liveEdgeOffset&&(t==null?this.removeAttribute(e.LIVE_EDGE_OFFSET):this.setAttribute(e.LIVE_EDGE_OFFSET,`${+t}`))}get seekable(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getSeekable)(this.nativeEl)}async addCuePoints(t){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.addCuePoints)(this.nativeEl,t)}get activeCuePoint(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getActiveCuePoint)(this.nativeEl)}get cuePoints(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getCuePoints)(this.nativeEl)}async addChapters(t){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.addChapters)(this.nativeEl,t)}get activeChapter(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getActiveChapter)(this.nativeEl)}get chapters(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getChapters)(this.nativeEl)}getStartDate(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getStartDate)(this.nativeEl,this._hls)}get currentPdt(){return (0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.getCurrentPdt)(this.nativeEl,this._hls)}get preferPlayback(){let t=this.getAttribute(e.PREFER_PLAYBACK);if(t===_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.PlaybackTypes.MSE||t===_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.PlaybackTypes.NATIVE)return t}set preferPlayback(t){t!==this.preferPlayback&&(t===_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.PlaybackTypes.MSE||t===_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.PlaybackTypes.NATIVE?this.setAttribute(e.PREFER_PLAYBACK,t):this.removeAttribute(e.PREFER_PLAYBACK))}get metadata(){return{...this.getAttributeNames().filter(i=>i.startsWith(\"metadata-\")&&![e.METADATA_URL].includes(i)).reduce((i,r)=>{let d=this.getAttribute(r);return d!=null&&(i[r.replace(/^metadata-/,\"\").replace(/-/g,\"_\")]=d),i},{}),...n(this,A)}}set metadata(t){o(this,A,t!=null?t:{}),this.mux&&this.mux.emit(\"hb\",n(this,A))}get _hlsConfig(){return n(this,T)}set _hlsConfig(t){o(this,T,t)}get logo(){var t;return(t=this.getAttribute(e.LOGO))!=null?t:n(this,g)}set logo(t){t?this.setAttribute(e.LOGO,t):this.removeAttribute(e.LOGO)}load(){o(this,l,(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.initialize)(this,this.nativeEl,n(this,l)))}unload(){(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.teardown)(this.nativeEl,n(this,l),this),o(this,l,void 0)}attributeChangedCallback(t,i,r){var E,L;switch(custom_media_element__WEBPACK_IMPORTED_MODULE_1__.CustomVideoElement.observedAttributes.includes(t)&&![\"src\",\"autoplay\",\"preload\"].includes(t)&&super.attributeChangedCallback(t,i,r),t){case e.PLAYER_SOFTWARE_NAME:this.playerSoftwareName=r!=null?r:void 0;break;case e.PLAYER_SOFTWARE_VERSION:this.playerSoftwareVersion=r!=null?r:void 0;break;case\"src\":{let h=!!i,N=!!r;!h&&N?M(this,m,y).call(this):h&&!N?this.unload():h&&N&&(this.unload(),M(this,m,y).call(this));break}case\"autoplay\":if(r===i)break;(E=n(this,l))==null||E.setAutoplay(this.autoplay);break;case\"preload\":if(r===i)break;(L=n(this,l))==null||L.setPreload(r);break;case e.PLAYBACK_ID:this.src=(0,_mux_playback_core__WEBPACK_IMPORTED_MODULE_0__.toMuxVideoURL)(this);break;case e.DEBUG:{let h=this.debug;this.mux&&console.info(\"Cannot toggle debug mode of mux data after initialization. Make sure you set all metadata to override before setting the src.\"),this._hls&&(this._hls.config.debug=h);break}case e.METADATA_URL:r&&fetch(r).then(h=>h.json()).then(h=>this.metadata=h).catch(()=>console.error(`Unable to load or parse metadata JSON from metadata-url ${r}!`));break;case e.STREAM_TYPE:(r==null||r!==i)&&this.dispatchEvent(new CustomEvent(\"streamtypechange\",{composed:!0,bubbles:!0}));break;case e.TARGET_LIVE_WINDOW:(r==null||r!==i)&&this.dispatchEvent(new CustomEvent(\"targetlivewindowchange\",{composed:!0,bubbles:!0,detail:this.targetLiveWindow}));break;case e.LOGO:(r==null||r!==i)&&this.updateLogo();break}}updateLogo(){if(!this.shadowRoot)return;let t=this.shadowRoot.querySelector('slot[name=\"logo\"]');if(!t)return;let i=this.constructor.getLogoHTML(n(this,g)||this.logo);t.innerHTML=i}connectedCallback(){var t;(t=super.connectedCallback)==null||t.call(this),this.nativeEl&&this.src&&!n(this,l)&&M(this,m,y).call(this)}disconnectedCallback(){this.unload()}handleEvent(t){t.target===this.nativeEl&&this.dispatchEvent(new CustomEvent(t.type,{composed:!0,detail:t.detail}))}};l=new WeakMap,f=new WeakMap,c=new WeakMap,A=new WeakMap,b=new WeakMap,T=new WeakMap,p=new WeakMap,_=new WeakMap,O=new WeakMap,g=new WeakMap,m=new WeakSet,y=async function(){n(this,f)||(await o(this,f,Promise.resolve()),o(this,f,null),this.load())};\n//# sourceMappingURL=base.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/mux-video/dist/base.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@mux/mux-video/dist/index.mjs":
/*!****************************************************!*\
  !*** ./node_modules/@mux/mux-video/dist/index.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attributes: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.Attributes),\n/* harmony export */   Events: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.Events),\n/* harmony export */   MediaError: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.MediaError),\n/* harmony export */   MuxVideoBaseElement: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.MuxVideoBaseElement),\n/* harmony export */   \"default\": () => (/* binding */ F),\n/* harmony export */   generatePlayerInitTime: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.generatePlayerInitTime),\n/* harmony export */   playerSoftwareName: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.playerSoftwareName),\n/* harmony export */   playerSoftwareVersion: () => (/* reexport safe */ _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.playerSoftwareVersion)\n/* harmony export */ });\n/* harmony import */ var _mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mux/mux-video/base */ \"(ssr)/./node_modules/@mux/mux-video/dist/base.mjs\");\n/* harmony import */ var castable_video_castable_mixin_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! castable-video/castable-mixin.js */ \"(ssr)/./node_modules/castable-video/castable-mixin.js\");\n/* harmony import */ var media_tracks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! media-tracks */ \"(ssr)/./node_modules/media-tracks/dist/index.js\");\nvar f=e=>{throw TypeError(e)};var g=(e,o,t)=>o.has(e)||f(\"Cannot \"+t);var u=(e,o,t)=>(g(e,o,\"read from private field\"),t?t.call(e):o.get(e)),m=(e,o,t)=>o.has(e)?f(\"Cannot add the same private member more than once\"):o instanceof WeakSet?o.add(e):o.set(e,t),d=(e,o,t,l)=>(g(e,o,\"write to private field\"),l?l.call(e,t):o.set(e,t),t);var s=class{addEventListener(){}removeEventListener(){}dispatchEvent(o){return!0}};if(typeof DocumentFragment==\"undefined\"){class e extends s{}globalThis.DocumentFragment=e}var n=class extends s{},p=class extends s{},x={get(e){},define(e,o,t){},getName(e){return null},upgrade(e){},whenDefined(e){return Promise.resolve(n)}},a,h=class{constructor(o,t={}){m(this,a);d(this,a,t==null?void 0:t.detail)}get detail(){return u(this,a)}initCustomEvent(){}};a=new WeakMap;function C(e,o){return new n}var y={document:{createElement:C},DocumentFragment,customElements:x,CustomEvent:h,EventTarget:s,HTMLElement:n,HTMLVideoElement:p},b=typeof window==\"undefined\"||typeof globalThis.customElements==\"undefined\",c=b?y:globalThis,k=b?y.document:globalThis.document;var r,i=class extends (0,castable_video_castable_mixin_js__WEBPACK_IMPORTED_MODULE_1__.CastableMediaMixin)((0,media_tracks__WEBPACK_IMPORTED_MODULE_2__.MediaTracksMixin)(_mux_mux_video_base__WEBPACK_IMPORTED_MODULE_0__.MuxVideoBaseElement)){constructor(){super(...arguments);m(this,r)}get autoplay(){let t=this.getAttribute(\"autoplay\");return t===null?!1:t===\"\"?!0:t}set autoplay(t){let l=this.autoplay;t!==l&&(t?this.setAttribute(\"autoplay\",typeof t==\"string\"?t:\"\"):this.removeAttribute(\"autoplay\"))}get muxCastCustomData(){return{mux:{playbackId:this.playbackId,minResolution:this.minResolution,maxResolution:this.maxResolution,renditionOrder:this.renditionOrder,customDomain:this.customDomain,tokens:{drm:this.drmToken},envKey:this.envKey,metadata:this.metadata,disableCookies:this.disableCookies,disableTracking:this.disableTracking,beaconCollectionDomain:this.beaconCollectionDomain,startTime:this.startTime,preferCmcd:this.preferCmcd}}}get castCustomData(){var t;return(t=u(this,r))!=null?t:this.muxCastCustomData}set castCustomData(t){d(this,r,t)}};r=new WeakMap;c.customElements.get(\"mux-video\")||(c.customElements.define(\"mux-video\",i),c.MuxVideoElement=i);var F=i;\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/mux-video/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@mux/playback-core/dist/index.mjs":
/*!********************************************************!*\
  !*** ./node_modules/@mux/playback-core/dist/index.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoplayTypes: () => (/* binding */ K),\n/* harmony export */   CmcdTypeValues: () => (/* binding */ jt),\n/* harmony export */   CmcdTypes: () => (/* binding */ S),\n/* harmony export */   ExtensionMimeTypeMap: () => (/* binding */ A),\n/* harmony export */   Hls: () => (/* binding */ g),\n/* harmony export */   MaxResolution: () => (/* binding */ Gt),\n/* harmony export */   MediaError: () => (/* binding */ f),\n/* harmony export */   MimeTypeShorthandMap: () => (/* binding */ W),\n/* harmony export */   MinResolution: () => (/* binding */ Xt),\n/* harmony export */   MuxErrorCategory: () => (/* binding */ C),\n/* harmony export */   MuxErrorCode: () => (/* binding */ D),\n/* harmony export */   MuxJWTAud: () => (/* binding */ se),\n/* harmony export */   PlaybackTypes: () => (/* binding */ X),\n/* harmony export */   RenditionOrder: () => (/* binding */ zt),\n/* harmony export */   StreamTypes: () => (/* binding */ _),\n/* harmony export */   addChapters: () => (/* binding */ Le),\n/* harmony export */   addCuePoints: () => (/* binding */ Pe),\n/* harmony export */   addTextTrack: () => (/* binding */ ne),\n/* harmony export */   allMediaTypes: () => (/* binding */ qt),\n/* harmony export */   errorCategoryToTokenNameOrPrefix: () => (/* binding */ V),\n/* harmony export */   fetchAndDispatchMuxMetadata: () => (/* binding */ de),\n/* harmony export */   generatePlayerInitTime: () => (/* binding */ Wr),\n/* harmony export */   generateUUID: () => (/* binding */ _t),\n/* harmony export */   getActiveChapter: () => (/* binding */ Ne),\n/* harmony export */   getActiveCuePoint: () => (/* binding */ _e),\n/* harmony export */   getAppCertificate: () => (/* binding */ Ut),\n/* harmony export */   getChapters: () => (/* binding */ ct),\n/* harmony export */   getCuePoints: () => (/* binding */ it),\n/* harmony export */   getCurrentPdt: () => (/* binding */ dt),\n/* harmony export */   getDRMConfig: () => (/* binding */ Ot),\n/* harmony export */   getEnded: () => (/* binding */ At),\n/* harmony export */   getError: () => (/* binding */ ht),\n/* harmony export */   getLicenseKey: () => (/* binding */ Ht),\n/* harmony export */   getLiveEdgeStart: () => (/* binding */ Br),\n/* harmony export */   getMediaPlaylistFromMultivariantPlaylist: () => (/* binding */ Tt),\n/* harmony export */   getMetadata: () => (/* binding */ Fr),\n/* harmony export */   getMultivariantPlaylistSessionData: () => (/* binding */ yt),\n/* harmony export */   getSeekable: () => (/* binding */ Be),\n/* harmony export */   getStartDate: () => (/* binding */ ut),\n/* harmony export */   getStreamInfoFromHlsjsLevelDetails: () => (/* binding */ Rt),\n/* harmony export */   getStreamInfoFromPlaylist: () => (/* binding */ gt),\n/* harmony export */   getStreamInfoFromSrcAndType: () => (/* binding */ Mt),\n/* harmony export */   getStreamType: () => (/* binding */ we),\n/* harmony export */   getStreamTypeConfig: () => (/* binding */ wt),\n/* harmony export */   getTargetLiveWindow: () => (/* binding */ $r),\n/* harmony export */   getTextTrack: () => (/* binding */ w),\n/* harmony export */   i18n: () => (/* binding */ x),\n/* harmony export */   initialize: () => (/* binding */ jr),\n/* harmony export */   isKeyOf: () => (/* binding */ O),\n/* harmony export */   isMuxVideoSrc: () => (/* binding */ Xe),\n/* harmony export */   isPseudoEnded: () => (/* binding */ Nt),\n/* harmony export */   isStuckOnLastFragment: () => (/* binding */ Je),\n/* harmony export */   loadMedia: () => (/* binding */ Wt),\n/* harmony export */   mux: () => (/* reexport safe */ mux_embed__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   muxMediaState: () => (/* binding */ P),\n/* harmony export */   parseJwt: () => (/* binding */ ee),\n/* harmony export */   parseTagAttributes: () => (/* binding */ Et),\n/* harmony export */   removeTextTrack: () => (/* binding */ st),\n/* harmony export */   setupChapters: () => (/* binding */ Ae),\n/* harmony export */   setupCuePoints: () => (/* binding */ ke),\n/* harmony export */   setupHls: () => (/* binding */ St),\n/* harmony export */   setupMux: () => (/* binding */ Kt),\n/* harmony export */   setupNativeFairplayDRM: () => (/* binding */ Vt),\n/* harmony export */   shorthandKeys: () => (/* binding */ Jt),\n/* harmony export */   teardown: () => (/* binding */ It),\n/* harmony export */   toAppCertURL: () => (/* binding */ Ge),\n/* harmony export */   toDRMTypeFromKeySystem: () => (/* binding */ ft),\n/* harmony export */   toLicenseKeyURL: () => (/* binding */ q),\n/* harmony export */   toMuxVideoURL: () => (/* binding */ Yr),\n/* harmony export */   toPlaybackIdFromSrc: () => (/* binding */ $e),\n/* harmony export */   toPlaybackIdParts: () => (/* binding */ F),\n/* harmony export */   updateStreamInfoFromHlsjsLevelDetails: () => (/* binding */ Dt),\n/* harmony export */   updateStreamInfoFromSrc: () => (/* binding */ xt)\n/* harmony export */ });\n/* harmony import */ var mux_embed__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mux-embed */ \"(ssr)/./node_modules/mux-embed/dist/mux.mjs\");\n/* harmony import */ var hls_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hls.js */ \"(ssr)/./node_modules/hls.js/dist/hls.mjs\");\nvar g=hls_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"];var C={VIDEO:\"video\",THUMBNAIL:\"thumbnail\",STORYBOARD:\"storyboard\",DRM:\"drm\"},D={NOT_AN_ERROR:0,NETWORK_OFFLINE:2000002,NETWORK_UNKNOWN_ERROR:2e6,NETWORK_NO_STATUS:2000001,NETWORK_INVALID_URL:24e5,NETWORK_NOT_FOUND:2404e3,NETWORK_NOT_READY:2412e3,NETWORK_GENERIC_SERVER_FAIL:25e5,NETWORK_TOKEN_MISSING:2403201,NETWORK_TOKEN_MALFORMED:2412202,NETWORK_TOKEN_EXPIRED:2403210,NETWORK_TOKEN_AUD_MISSING:2403221,NETWORK_TOKEN_AUD_MISMATCH:2403222,NETWORK_TOKEN_SUB_MISMATCH:2403232,ENCRYPTED_ERROR:5e6,ENCRYPTED_UNSUPPORTED_KEY_SYSTEM:5000001,ENCRYPTED_GENERATE_REQUEST_FAILED:5000002,ENCRYPTED_UPDATE_LICENSE_FAILED:5000003,ENCRYPTED_UPDATE_SERVER_CERT_FAILED:5000004,ENCRYPTED_CDM_ERROR:5000005,ENCRYPTED_OUTPUT_RESTRICTED:5000006,ENCRYPTED_MISSING_TOKEN:5000002},V=e=>e===C.VIDEO?\"playback\":e,L=class L extends Error{constructor(t,r=L.MEDIA_ERR_CUSTOM,n,o){var a;super(t),this.name=\"MediaError\",this.code=r,this.context=o,this.fatal=n!=null?n:r>=L.MEDIA_ERR_NETWORK&&r<=L.MEDIA_ERR_ENCRYPTED,this.message||(this.message=(a=L.defaultMessages[this.code])!=null?a:\"\")}};L.MEDIA_ERR_ABORTED=1,L.MEDIA_ERR_NETWORK=2,L.MEDIA_ERR_DECODE=3,L.MEDIA_ERR_SRC_NOT_SUPPORTED=4,L.MEDIA_ERR_ENCRYPTED=5,L.MEDIA_ERR_CUSTOM=100,L.defaultMessages={1:\"You aborted the media playback\",2:\"A network error caused the media download to fail.\",3:\"A media error caused playback to be aborted. The media could be corrupt or your browser does not support this format.\",4:\"An unsupported error occurred. The server or network failed, or your browser does not support this format.\",5:\"The media is encrypted and there are no keys to decrypt it.\"};var f=L;var et=e=>e==null,O=(e,t)=>et(t)?!1:e in t,K={ANY:\"any\",MUTED:\"muted\"},_={ON_DEMAND:\"on-demand\",LIVE:\"live\",UNKNOWN:\"unknown\"},X={MSE:\"mse\",NATIVE:\"native\"},S={HEADER:\"header\",QUERY:\"query\",NONE:\"none\"},jt=Object.values(S),A={M3U8:\"application/vnd.apple.mpegurl\",MP4:\"video/mp4\"},W={HLS:A.M3U8},Jt=Object.keys(W),qt=[...Object.values(A),\"hls\",\"HLS\"],Gt={upTo720p:\"720p\",upTo1080p:\"1080p\",upTo1440p:\"1440p\",upTo2160p:\"2160p\"},Xt={noLessThan480p:\"480p\",noLessThan540p:\"540p\",noLessThan720p:\"720p\",noLessThan1080p:\"1080p\",noLessThan1440p:\"1440p\",noLessThan2160p:\"2160p\"},zt={DESCENDING:\"desc\"};var tt=\"en\",Y={code:tt};var v=(e,t,r,n,o=e)=>{o.addEventListener(t,r,n),e.addEventListener(\"teardown\",()=>{o.removeEventListener(t,r)},{once:!0})};function fe(e,t,r){t&&r>t&&(r=t);for(let n=0;n<e.length;n++)if(e.start(n)<=r&&e.end(n)>=r)return!0;return!1}var F=e=>{let t=e.indexOf(\"?\");if(t<0)return[e];let r=e.slice(0,t),n=e.slice(t);return[r,n]},U=e=>{let{type:t}=e;if(t){let r=t.toUpperCase();return O(r,W)?W[r]:t}return rt(e)},Q=e=>e===\"VOD\"?_.ON_DEMAND:_.LIVE,Z=e=>e===\"EVENT\"?Number.POSITIVE_INFINITY:e===\"VOD\"?Number.NaN:0,rt=e=>{let{src:t}=e;if(!t)return\"\";let r=\"\";try{r=new URL(t).pathname}catch{console.error(\"invalid url\")}let n=r.lastIndexOf(\".\");if(n<0)return ot(e)?A.M3U8:\"\";let a=r.slice(n+1).toUpperCase();return O(a,A)?A[a]:\"\"},nt=\"mux.com\",ot=({src:e,customDomain:t=nt})=>{let r;try{r=new URL(`${e}`)}catch{return!1}let n=r.protocol===\"https:\",o=r.hostname===`stream.${t}`.toLowerCase(),a=r.pathname.split(\"/\"),i=a.length===2,c=!(a!=null&&a[1].includes(\".\"));return n&&o&&i&&c},ee=e=>{let t=(e!=null?e:\"\").split(\".\")[1];if(t)try{let r=t.replace(/-/g,\"+\").replace(/_/g,\"/\"),n=decodeURIComponent(atob(r).split(\"\").map(function(o){return\"%\"+(\"00\"+o.charCodeAt(0).toString(16)).slice(-2)}).join(\"\"));return JSON.parse(n)}catch{return}},Te=({exp:e},t=Date.now())=>!e||e*1e3<t,ye=({sub:e},t)=>e!==t,me=({aud:e},t)=>!e,Ee=({aud:e},t)=>e!==t,ge=\"en\";function x(e,t=!0){var o,a;let r=t&&(a=(o=Y)==null?void 0:o[e])!=null?a:e,n=t?Y.code:ge;return new z(r,n)}var z=class{constructor(t,r=(n=>(n=Y)!=null?n:ge)()){this.message=t,this.locale=r}format(t){return this.message.replace(/\\{(\\w+)\\}/g,(r,n)=>{var o;return(o=t[n])!=null?o:\"\"})}toString(){return this.message}};var at=Object.values(K),Me=e=>typeof e==\"boolean\"||typeof e==\"string\"&&at.includes(e),xe=(e,t,r)=>{let{autoplay:n}=e,o=!1,a=!1,i=Me(n)?n:!!n,c=()=>{o||v(t,\"playing\",()=>{o=!0},{once:!0})};if(c(),v(t,\"loadstart\",()=>{o=!1,c(),te(t,i)},{once:!0}),v(t,\"loadstart\",()=>{r||(e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=!Number.isFinite(t.duration)),te(t,i)},{once:!0}),r&&r.once(g.Events.LEVEL_LOADED,(u,s)=>{var p;e.streamType&&e.streamType!==_.UNKNOWN?a=e.streamType===_.LIVE:a=(p=s.details.live)!=null?p:!1}),!i){let u=()=>{!a||Number.isFinite(e.startTime)||(r!=null&&r.liveSyncPosition?t.currentTime=r.liveSyncPosition:Number.isFinite(t.seekable.end(0))&&(t.currentTime=t.seekable.end(0)))};r&&v(t,\"play\",()=>{t.preload===\"metadata\"?r.once(g.Events.LEVEL_UPDATED,u):u()},{once:!0})}return u=>{o||(i=Me(u)?u:!!u,te(t,i))}},te=(e,t)=>{if(!t)return;let r=e.muted,n=()=>e.muted=r;switch(t){case K.ANY:e.play().catch(()=>{e.muted=!0,e.play().catch(n)});break;case K.MUTED:e.muted=!0,e.play().catch(n);break;default:e.play().catch(()=>{});break}};var Re=({preload:e,src:t},r,n)=>{let o=p=>{p!=null&&[\"\",\"none\",\"metadata\",\"auto\"].includes(p)?r.setAttribute(\"preload\",p):r.removeAttribute(\"preload\")};if(!n)return o(e),o;let a=!1,i=!1,c=n.config.maxBufferLength,d=n.config.maxBufferSize,u=p=>{o(p);let l=p!=null?p:r.preload;i||l===\"none\"||(l===\"metadata\"?(n.config.maxBufferLength=1,n.config.maxBufferSize=1):(n.config.maxBufferLength=c,n.config.maxBufferSize=d),s())},s=()=>{!a&&t&&(a=!0,n.loadSource(t))};return v(r,\"play\",()=>{i=!0,n.config.maxBufferLength=c,n.config.maxBufferSize=d,s()},{once:!0}),u(e),u};function De(e,t){var c;if(!(\"videoTracks\"in e))return;let r=new WeakMap;t.on(g.Events.MANIFEST_PARSED,function(d,u){i();let s=e.addVideoTrack(\"main\");s.selected=!0;for(let[p,l]of u.levels.entries()){let T=s.addRendition(l.url[0],l.width,l.height,l.videoCodec,l.bitrate);r.set(l,`${p}`),T.id=`${p}`}}),t.on(g.Events.AUDIO_TRACKS_UPDATED,function(d,u){a();for(let s of u.audioTracks){let p=s.default?\"main\":\"alternative\",l=e.addAudioTrack(p,s.name,s.lang);l.id=`${s.id}`,s.default&&(l.enabled=!0)}}),e.audioTracks.addEventListener(\"change\",()=>{var s;let d=+((s=[...e.audioTracks].find(p=>p.enabled))==null?void 0:s.id),u=t.audioTracks.map(p=>p.id);d!=t.audioTrack&&u.includes(d)&&(t.audioTrack=d)}),t.on(g.Events.LEVELS_UPDATED,function(d,u){var l;let s=e.videoTracks[(l=e.videoTracks.selectedIndex)!=null?l:0];if(!s)return;let p=u.levels.map(T=>r.get(T));for(let T of e.videoRenditions)T.id&&!p.includes(T.id)&&s.removeRendition(T)});let n=d=>{let u=d.target.selectedIndex;u!=t.nextLevel&&(t.nextLevel=u)};(c=e.videoRenditions)==null||c.addEventListener(\"change\",n);let o=()=>{for(let d of e.videoTracks)e.removeVideoTrack(d)},a=()=>{for(let d of e.audioTracks)e.removeAudioTrack(d)},i=()=>{o(),a()};t.once(g.Events.DESTROYING,i)}var re=e=>\"time\"in e?e.time:e.startTime;function be(e,t){t.on(g.Events.NON_NATIVE_TEXT_TRACKS_FOUND,(o,{tracks:a})=>{a.forEach(i=>{var s,p;let c=(s=i.subtitleTrack)!=null?s:i.closedCaptions,d=t.subtitleTracks.findIndex(({lang:l,name:T,type:m})=>l==(c==null?void 0:c.lang)&&T===i.label&&m.toLowerCase()===i.kind),u=((p=i._id)!=null?p:i.default)?\"default\":`${i.kind}${d}`;ne(e,i.kind,i.label,c==null?void 0:c.lang,u,i.default)})});let r=()=>{if(!t.subtitleTracks.length)return;let o=Array.from(e.textTracks).find(c=>c.id&&c.mode===\"showing\"&&[\"subtitles\",\"captions\"].includes(c.kind));if(!o)return;let a=t.subtitleTracks[t.subtitleTrack],i=a?a.default?\"default\":`${t.subtitleTracks[t.subtitleTrack].type.toLowerCase()}${t.subtitleTrack}`:void 0;if(t.subtitleTrack<0||(o==null?void 0:o.id)!==i){let c=t.subtitleTracks.findIndex(({lang:d,name:u,type:s,default:p})=>o.id===\"default\"&&p||d==o.language&&u===o.label&&s.toLowerCase()===o.kind);t.subtitleTrack=c}(o==null?void 0:o.id)===i&&o.cues&&Array.from(o.cues).forEach(c=>{o.addCue(c)})};e.textTracks.addEventListener(\"change\",r),t.on(g.Events.CUES_PARSED,(o,{track:a,cues:i})=>{let c=e.textTracks.getTrackById(a);if(!c)return;let d=c.mode===\"disabled\";d&&(c.mode=\"hidden\"),i.forEach(u=>{var s;(s=c.cues)!=null&&s.getCueById(u.id)||c.addCue(u)}),d&&(c.mode=\"disabled\")}),t.once(g.Events.DESTROYING,()=>{e.textTracks.removeEventListener(\"change\",r),e.querySelectorAll(\"track[data-removeondestroy]\").forEach(a=>{a.remove()})});let n=()=>{Array.from(e.textTracks).forEach(o=>{var a,i;if(![\"subtitles\",\"caption\"].includes(o.kind)&&(o.label===\"thumbnails\"||o.kind===\"chapters\")){if(!((a=o.cues)!=null&&a.length)){let c=\"track\";o.kind&&(c+=`[kind=\"${o.kind}\"]`),o.label&&(c+=`[label=\"${o.label}\"]`);let d=e.querySelector(c),u=(i=d==null?void 0:d.getAttribute(\"src\"))!=null?i:\"\";d==null||d.removeAttribute(\"src\"),setTimeout(()=>{d==null||d.setAttribute(\"src\",u)},0)}o.mode!==\"hidden\"&&(o.mode=\"hidden\")}})};t.once(g.Events.MANIFEST_LOADED,n),t.once(g.Events.MEDIA_ATTACHED,n)}function ne(e,t,r,n,o,a){let i=document.createElement(\"track\");return i.kind=t,i.label=r,n&&(i.srclang=n),o&&(i.id=o),a&&(i.default=!0),i.track.mode=[\"subtitles\",\"captions\"].includes(t)?\"disabled\":\"hidden\",i.setAttribute(\"data-removeondestroy\",\"\"),e.append(i),i.track}function st(e,t){let r=Array.prototype.find.call(e.querySelectorAll(\"track\"),n=>n.track===t);r==null||r.remove()}function w(e,t,r){var n;return(n=Array.from(e.querySelectorAll(\"track\")).find(o=>o.track.label===t&&o.track.kind===r))==null?void 0:n.track}async function Ce(e,t,r,n){let o=w(e,r,n);return o||(o=ne(e,n,r),o.mode=\"hidden\",await new Promise(a=>setTimeout(()=>a(void 0),0))),o.mode!==\"hidden\"&&(o.mode=\"hidden\"),[...t].sort((a,i)=>re(i)-re(a)).forEach(a=>{var d,u;let i=a.value,c=re(a);if(\"endTime\"in a&&a.endTime!=null)o==null||o.addCue(new VTTCue(c,a.endTime,n===\"chapters\"?i:JSON.stringify(i!=null?i:null)));else{let s=Array.prototype.findIndex.call(o==null?void 0:o.cues,m=>m.startTime>=c),p=(d=o==null?void 0:o.cues)==null?void 0:d[s],l=p?p.startTime:Number.isFinite(e.duration)?e.duration:Number.MAX_SAFE_INTEGER,T=(u=o==null?void 0:o.cues)==null?void 0:u[s-1];T&&(T.endTime=c),o==null||o.addCue(new VTTCue(c,l,n===\"chapters\"?i:JSON.stringify(i!=null?i:null)))}}),e.textTracks.dispatchEvent(new Event(\"change\",{bubbles:!0,composed:!0})),o}var oe=\"cuepoints\",ve=Object.freeze({label:oe});async function Pe(e,t,r=ve){return Ce(e,t,r.label,\"metadata\")}var $=e=>({time:e.startTime,value:JSON.parse(e.text)});function it(e,t={label:oe}){let r=w(e,t.label,\"metadata\");return r!=null&&r.cues?Array.from(r.cues,n=>$(n)):[]}function _e(e,t={label:oe}){var a,i;let r=w(e,t.label,\"metadata\");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return $(r.activeCues[0]);let{currentTime:n}=e,o=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=n&&d>n);return $(o||r.activeCues[0])}async function ke(e,t=ve){return new Promise(r=>{v(e,\"loadstart\",async()=>{let n=await Pe(e,[],t);v(e,\"cuechange\",()=>{let o=_e(e);if(o){let a=new CustomEvent(\"cuepointchange\",{composed:!0,bubbles:!0,detail:o});e.dispatchEvent(a)}},{},n),r(n)})})}var ae=\"chapters\",he=Object.freeze({label:ae}),B=e=>({startTime:e.startTime,endTime:e.endTime,value:e.text});async function Le(e,t,r=he){return Ce(e,t,r.label,\"chapters\")}function ct(e,t={label:ae}){var n;let r=w(e,t.label,\"chapters\");return(n=r==null?void 0:r.cues)!=null&&n.length?Array.from(r.cues,o=>B(o)):[]}function Ne(e,t={label:ae}){var a,i;let r=w(e,t.label,\"chapters\");if(!((a=r==null?void 0:r.activeCues)!=null&&a.length))return;if(r.activeCues.length===1)return B(r.activeCues[0]);let{currentTime:n}=e,o=Array.prototype.find.call((i=r.activeCues)!=null?i:[],({startTime:c,endTime:d})=>c<=n&&d>n);return B(o||r.activeCues[0])}async function Ae(e,t=he){return new Promise(r=>{v(e,\"loadstart\",async()=>{let n=await Le(e,[],t);v(e,\"cuechange\",()=>{let o=Ne(e);if(o){let a=new CustomEvent(\"chapterchange\",{composed:!0,bubbles:!0,detail:o});e.dispatchEvent(a)}},{},n),r(n)})})}function ut(e,t){if(t){let r=t.playingDate;if(r!=null)return new Date(r.getTime()-e.currentTime*1e3)}return typeof e.getStartDate==\"function\"?e.getStartDate():new Date(NaN)}function dt(e,t){if(t&&t.playingDate)return t.playingDate;if(typeof e.getStartDate==\"function\"){let r=e.getStartDate();return new Date(r.getTime()+e.currentTime*1e3)}return new Date(NaN)}var se={VIDEO:\"v\",THUMBNAIL:\"t\",STORYBOARD:\"s\",DRM:\"d\"},lt=e=>{if(e===C.VIDEO)return se.VIDEO;if(e===C.DRM)return se.DRM},pt=(e,t)=>{var o,a;let r=V(e),n=`${r}Token`;return(o=t.tokens)!=null&&o[r]?(a=t.tokens)==null?void 0:a[r]:O(n,t)?t[n]:void 0},H=(e,t,r,n,o=!1,a=!(i=>(i=globalThis.navigator)==null?void 0:i.onLine)())=>{var M,h;if(a){let E=x(\"Your device appears to be offline\",o),b=void 0,y=f.MEDIA_ERR_NETWORK,k=new f(E,y,!1,b);return k.errorCategory=t,k.muxCode=D.NETWORK_OFFLINE,k.data=e,k}let c=\"status\"in e?e.status:e.code,d=Date.now(),u=f.MEDIA_ERR_NETWORK;if(c===200)return;let s=V(t),p=pt(t,r),l=lt(t),[T]=F((M=r.playbackId)!=null?M:\"\");if(!c||!T)return;let m=ee(p);if(p&&!m){let E=x(\"The {tokenNamePrefix}-token provided is invalid or malformed.\",o).format({tokenNamePrefix:s}),b=x(\"Compact JWT string: {token}\",o).format({token:p}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MALFORMED,y.data=e,y}if(c>=500){let E=new f(\"\",u,n!=null?n:!0);return E.errorCategory=t,E.muxCode=D.NETWORK_UNKNOWN_ERROR,E}if(c===403)if(m){if(Te(m,d)){let E={timeStyle:\"medium\",dateStyle:\"medium\"},b=x(\"The video\\u2019s secured {tokenNamePrefix}-token has expired.\",o).format({tokenNamePrefix:s}),y=x(\"Expired at: {expiredDate}. Current time: {currentDate}.\",o).format({expiredDate:new Intl.DateTimeFormat(\"en\",E).format((h=m.exp)!=null?h:0*1e3),currentDate:new Intl.DateTimeFormat(\"en\",E).format(d)}),k=new f(b,u,!0,y);return k.errorCategory=t,k.muxCode=D.NETWORK_TOKEN_EXPIRED,k.data=e,k}if(ye(m,T)){let E=x(\"The video\\u2019s playback ID does not match the one encoded in the {tokenNamePrefix}-token.\",o).format({tokenNamePrefix:s}),b=x(\"Specified playback ID: {playbackId} and the playback ID encoded in the {tokenNamePrefix}-token: {tokenPlaybackId}\",o).format({tokenNamePrefix:s,playbackId:T,tokenPlaybackId:m.sub}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_SUB_MISMATCH,y.data=e,y}if(me(m,l)){let E=x(\"The {tokenNamePrefix}-token is formatted with incorrect information.\",o).format({tokenNamePrefix:s}),b=x(\"The {tokenNamePrefix}-token has no aud value. aud value should be {expectedAud}.\",o).format({tokenNamePrefix:s,expectedAud:l}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISSING,y.data=e,y}if(Ee(m,l)){let E=x(\"The {tokenNamePrefix}-token is formatted with incorrect information.\",o).format({tokenNamePrefix:s}),b=x(\"The {tokenNamePrefix}-token has an incorrect aud value: {aud}. aud value should be {expectedAud}.\",o).format({tokenNamePrefix:s,expectedAud:l,aud:m.aud}),y=new f(E,u,!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_AUD_MISMATCH,y.data=e,y}}else{let E=x(\"Authorization error trying to access this {category} URL. If this is a signed URL, you might need to provide a {tokenNamePrefix}-token.\",o).format({tokenNamePrefix:s,category:t}),b=x(\"Specified playback ID: {playbackId}\",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_TOKEN_MISSING,y.data=e,y}if(c===412){let E=x(\"This playback-id may belong to a live stream that is not currently active or an asset that is not ready.\",o),b=x(\"Specified playback ID: {playbackId}\",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_READY,y.streamType=r.streamType===_.LIVE?\"live\":r.streamType===_.ON_DEMAND?\"on-demand\":\"unknown\",y.data=e,y}if(c===404){let E=x(\"This URL or playback-id does not exist. You may have used an Asset ID or an ID from a different resource.\",o),b=x(\"Specified playback ID: {playbackId}\",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_NOT_FOUND,y.data=e,y}if(c===400){let E=x(\"The URL or playback-id was invalid. You may have used an invalid value as a playback-id.\"),b=x(\"Specified playback ID: {playbackId}\",o).format({playbackId:T}),y=new f(E,u,n!=null?n:!0,b);return y.errorCategory=t,y.muxCode=D.NETWORK_INVALID_URL,y.data=e,y}let R=new f(\"\",u,n!=null?n:!0);return R.errorCategory=t,R.muxCode=D.NETWORK_UNKNOWN_ERROR,R.data=e,R};var Ie=g.DefaultConfig.capLevelController,j=class j extends Ie{constructor(t){super(t)}get levels(){var t;return(t=this.hls.levels)!=null?t:[]}getValidLevels(t){return this.levels.filter((r,n)=>this.isLevelAllowed(r)&&n<=t)}getMaxLevel(t){let r=super.getMaxLevel(t),n=this.getValidLevels(t);if(!n[r])return r;let o=Math.min(n[r].width,n[r].height),a=j.minMaxResolution;return o>=a?r:Ie.getMaxLevelByMediaSize(n,a*(16/9),a)}};j.minMaxResolution=720;var ie=j,Se=ie;var J={FAIRPLAY:\"fairplay\",PLAYREADY:\"playready\",WIDEVINE:\"widevine\"},ft=e=>{if(e.includes(\"fps\"))return J.FAIRPLAY;if(e.includes(\"playready\"))return J.PLAYREADY;if(e.includes(\"widevine\"))return J.WIDEVINE},Tt=e=>{let t=e.split(`\n`).find((r,n,o)=>n&&o[n-1].startsWith(\"#EXT-X-STREAM-INF\"));return fetch(t).then(r=>r.status!==200?Promise.reject(r):r.text())},yt=e=>{let t=e.split(`\n`).filter(n=>n.startsWith(\"#EXT-X-SESSION-DATA\"));if(!t.length)return{};let r={};for(let n of t){let o=Et(n),a=o[\"DATA-ID\"];a&&(r[a]={...o})}return{sessionData:r}},mt=/([A-Z0-9-]+)=\"?(.*?)\"?(?:,|$)/g;function Et(e){let t=[...e.matchAll(mt)];return Object.fromEntries(t.map(([,r,n])=>[r,n]))}var gt=e=>{var c,d,u;let t=e.split(`\n`),n=(d=((c=t.find(s=>s.startsWith(\"#EXT-X-PLAYLIST-TYPE\")))!=null?c:\"\").split(\":\")[1])==null?void 0:d.trim(),o=Q(n),a=Z(n),i;if(o===_.LIVE){let s=t.find(l=>l.startsWith(\"#EXT-X-PART-INF\"));if(!!s)i=+s.split(\":\")[1].split(\"=\")[1]*2;else{let l=t.find(R=>R.startsWith(\"#EXT-X-TARGETDURATION\")),T=(u=l==null?void 0:l.split(\":\"))==null?void 0:u[1];i=+(T!=null?T:6)*3}}return{streamType:o,targetLiveWindow:a,liveEdgeStartOffset:i}},Mt=async(e,t)=>{if(t===A.MP4)return{streamType:_.ON_DEMAND,targetLiveWindow:Number.NaN,liveEdgeStartOffset:void 0,sessionData:void 0};if(t===A.M3U8){let r=await fetch(e);if(!r.ok)return Promise.reject(r);let n=await r.text(),o=await Tt(n);return{...yt(n),...gt(o)}}return console.error(`Media type ${t} is an unrecognized or unsupported type for src ${e}.`),{streamType:void 0,targetLiveWindow:void 0,liveEdgeStartOffset:void 0,sessionData:void 0}},xt=async(e,t,r=U({src:e}))=>{var d,u,s,p;let{streamType:n,targetLiveWindow:o,liveEdgeStartOffset:a,sessionData:i}=await Mt(e,r),c=i==null?void 0:i[\"com.apple.hls.chapters\"];(c!=null&&c.URI||c!=null&&c.VALUE.toLocaleLowerCase().startsWith(\"http\"))&&de((d=c.URI)!=null?d:c.VALUE,t),((u=P.get(t))!=null?u:{}).liveEdgeStartOffset=a,((s=P.get(t))!=null?s:{}).targetLiveWindow=o,t.dispatchEvent(new CustomEvent(\"targetlivewindowchange\",{composed:!0,bubbles:!0})),((p=P.get(t))!=null?p:{}).streamType=n,t.dispatchEvent(new CustomEvent(\"streamtypechange\",{composed:!0,bubbles:!0}))},de=async(e,t)=>{var r,n;try{let o=await fetch(e);if(!o.ok)throw new Error(`Failed to fetch Mux metadata: ${o.status} ${o.statusText}`);let a=await o.json(),i={};if(!((r=a==null?void 0:a[0])!=null&&r.metadata))return;for(let d of a[0].metadata)d.key&&d.value&&(i[d.key]=d.value);((n=P.get(t))!=null?n:{}).metadata=i;let c=new CustomEvent(\"muxmetadata\");t.dispatchEvent(c)}catch(o){console.error(o)}},Rt=e=>{var i;let t=e.type,r=Q(t),n=Z(t),o,a=!!((i=e.partList)!=null&&i.length);return r===_.LIVE&&(o=a?e.partTarget*2:e.targetduration*3),{streamType:r,targetLiveWindow:n,liveEdgeStartOffset:o,lowLatency:a}},Dt=(e,t,r)=>{var c,d,u,s,p,l,T,m;let{streamType:n,targetLiveWindow:o,liveEdgeStartOffset:a,lowLatency:i}=Rt(e);if(n===_.LIVE){i?(r.config.backBufferLength=(c=r.userConfig.backBufferLength)!=null?c:4,r.config.maxFragLookUpTolerance=(d=r.userConfig.maxFragLookUpTolerance)!=null?d:.001,r.config.abrBandWidthUpFactor=(u=r.userConfig.abrBandWidthUpFactor)!=null?u:r.config.abrBandWidthFactor):r.config.backBufferLength=(s=r.userConfig.backBufferLength)!=null?s:8;let R=Object.freeze({get length(){return t.seekable.length},start(M){return t.seekable.start(M)},end(M){var h;return M>this.length||M<0||Number.isFinite(t.duration)?t.seekable.end(M):(h=r.liveSyncPosition)!=null?h:t.seekable.end(M)}});((p=P.get(t))!=null?p:{}).seekable=R}((l=P.get(t))!=null?l:{}).liveEdgeStartOffset=a,((T=P.get(t))!=null?T:{}).targetLiveWindow=o,t.dispatchEvent(new CustomEvent(\"targetlivewindowchange\",{composed:!0,bubbles:!0})),((m=P.get(t))!=null?m:{}).streamType=n,t.dispatchEvent(new CustomEvent(\"streamtypechange\",{composed:!0,bubbles:!0}))},Oe,Ue,bt=(Ue=(Oe=globalThis==null?void 0:globalThis.navigator)==null?void 0:Oe.userAgent)!=null?Ue:\"\",He,Ve,Ke,Ct=(Ke=(Ve=(He=globalThis==null?void 0:globalThis.navigator)==null?void 0:He.userAgentData)==null?void 0:Ve.platform)!=null?Ke:\"\",vt=bt.toLowerCase().includes(\"android\")||[\"x11\",\"android\"].some(e=>Ct.toLowerCase().includes(e)),P=new WeakMap,I=\"mux.com\",We,Ye,Fe=(Ye=(We=g).isSupported)==null?void 0:Ye.call(We),Pt=vt,Wr=()=>mux_embed__WEBPACK_IMPORTED_MODULE_0__[\"default\"].utils.now(),_t=mux_embed__WEBPACK_IMPORTED_MODULE_0__[\"default\"].utils.generateUUID,Yr=({playbackId:e,customDomain:t=I,maxResolution:r,minResolution:n,renditionOrder:o,programStartTime:a,programEndTime:i,assetStartTime:c,assetEndTime:d,playbackToken:u,tokens:{playback:s=u}={},extraSourceParams:p={}}={})=>{if(!e)return;let[l,T=\"\"]=F(e),m=new URL(`https://stream.${t}/${l}.m3u8${T}`);return s||m.searchParams.has(\"token\")?(m.searchParams.forEach((R,M)=>{M!=\"token\"&&m.searchParams.delete(M)}),s&&m.searchParams.set(\"token\",s)):(r&&m.searchParams.set(\"max_resolution\",r),n&&(m.searchParams.set(\"min_resolution\",n),r&&+r.slice(0,-1)<+n.slice(0,-1)&&console.error(\"minResolution must be <= maxResolution\",\"minResolution\",n,\"maxResolution\",r)),o&&m.searchParams.set(\"rendition_order\",o),a&&m.searchParams.set(\"program_start_time\",`${a}`),i&&m.searchParams.set(\"program_end_time\",`${i}`),c&&m.searchParams.set(\"asset_start_time\",`${c}`),d&&m.searchParams.set(\"asset_end_time\",`${d}`),Object.entries(p).forEach(([R,M])=>{M!=null&&m.searchParams.set(R,M)})),m.toString()},G=e=>{if(!e)return;let[t]=e.split(\"?\");return t||void 0},$e=e=>{if(!e||!e.startsWith(\"https://stream.\"))return;let[t]=new URL(e).pathname.slice(1).split(/\\.m3u8|\\//);return t||void 0},kt=e=>{var t,r,n;return(t=e==null?void 0:e.metadata)!=null&&t.video_id?e.metadata.video_id:Xe(e)&&(n=(r=G(e.playbackId))!=null?r:$e(e.src))!=null?n:e.src},ht=e=>{var t;return(t=P.get(e))==null?void 0:t.error},Fr=e=>{var t;return(t=P.get(e))==null?void 0:t.metadata},we=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.streamType)!=null?r:_.UNKNOWN},$r=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.targetLiveWindow)!=null?r:Number.NaN},Be=e=>{var t,r;return(r=(t=P.get(e))==null?void 0:t.seekable)!=null?r:e.seekable},Br=e=>{var n;let t=(n=P.get(e))==null?void 0:n.liveEdgeStartOffset;if(typeof t!=\"number\")return Number.NaN;let r=Be(e);return r.length?r.end(r.length-1)-t:Number.NaN},le=.034,Lt=(e,t,r=le)=>Math.abs(e-t)<=r,je=(e,t,r=le)=>e>t||Lt(e,t,r),Nt=(e,t=le)=>e.paused&&je(e.currentTime,e.duration,t),Je=(e,t)=>{var u,s,p;if(!t||!e.buffered.length)return;if(e.readyState>2)return!1;let r=t.currentLevel>=0?(s=(u=t.levels)==null?void 0:u[t.currentLevel])==null?void 0:s.details:(p=t.levels.find(l=>!!l.details))==null?void 0:p.details;if(!r||r.live)return;let{fragments:n}=r;if(!(n!=null&&n.length))return;if(e.currentTime<e.duration-(r.targetduration+.5))return!1;let o=n[n.length-1];if(e.currentTime<=o.start)return!1;let a=o.start+o.duration/2,i=e.buffered.start(e.buffered.length-1),c=e.buffered.end(e.buffered.length-1);return a>i&&a<c},At=(e,t)=>e.ended||e.loop?e.ended:t&&Je(e,t)?!0:Nt(e),jr=(e,t,r)=>{It(t,r,e);let{metadata:n={}}=e,{view_session_id:o=_t()}=n,a=kt(e);n.view_session_id=o,n.video_id=a,e.metadata=n;let i=s=>{var p;(p=t.mux)==null||p.emit(\"hb\",{view_drm_type:s})};e.drmTypeCb=i,P.set(t,{retryCount:0});let c=St(e,t),d=Re(e,t,c);e!=null&&e.muxDataKeepSession&&(t!=null&&t.mux)&&!t.mux.deleted?c&&t.mux.addHLSJS({hlsjs:c,Hls:c?g:void 0}):Kt(e,t,c),Wt(e,t,c),ke(t),Ae(t);let u=xe(e,t,c);return{engine:c,setAutoplay:u,setPreload:d}},It=(e,t,r)=>{let n=t==null?void 0:t.engine;e!=null&&e.mux&&!e.mux.deleted&&(r!=null&&r.muxDataKeepSession?n&&e.mux.removeHLSJS():(e.mux.destroy(),delete e.mux)),n&&(n.detachMedia(),n.destroy()),e&&(e.hasAttribute(\"src\")&&(e.removeAttribute(\"src\"),e.load()),e.removeEventListener(\"error\",Qe),e.removeEventListener(\"error\",ce),e.removeEventListener(\"durationchange\",ze),P.delete(e),e.dispatchEvent(new Event(\"teardown\")))};function qe(e,t){var u;let r=U(e);if(!(r===A.M3U8))return!0;let o=!r||((u=t.canPlayType(r))!=null?u:!0),{preferPlayback:a}=e,i=a===X.MSE,c=a===X.NATIVE;return o&&(c||!(Fe&&(i||Pt)))}var St=(e,t)=>{let{debug:r,streamType:n,startTime:o=-1,metadata:a,preferCmcd:i,_hlsConfig:c={}}=e,u=U(e)===A.M3U8,s=qe(e,t);if(u&&!s&&Fe){let p={backBufferLength:30,renderTextTracksNatively:!1,liveDurationInfinity:!0,capLevelToPlayerSize:!0,capLevelOnFPSDrop:!0},l=wt(n),T=Ot(e),m=[S.QUERY,S.HEADER].includes(i)?{useHeaders:i===S.HEADER,sessionId:a==null?void 0:a.view_session_id,contentId:a==null?void 0:a.video_id}:void 0,R=new g({debug:r,startPosition:o,cmcd:m,xhrSetup:(M,h)=>{var y,k;if(i&&i!==S.QUERY)return;let E=new URL(h);if(!E.searchParams.has(\"CMCD\"))return;let b=((k=(y=E.searchParams.get(\"CMCD\"))==null?void 0:y.split(\",\"))!=null?k:[]).filter(pe=>pe.startsWith(\"sid\")||pe.startsWith(\"cid\")).join(\",\");E.searchParams.set(\"CMCD\",b),M.open(\"GET\",E)},capLevelController:Se,...p,...l,...T,...c});return R.on(g.Events.MANIFEST_PARSED,async function(M,h){var b,y;let E=(b=h.sessionData)==null?void 0:b[\"com.apple.hls.chapters\"];(E!=null&&E.URI||E!=null&&E.VALUE.toLocaleLowerCase().startsWith(\"http\"))&&de((y=E==null?void 0:E.URI)!=null?y:E==null?void 0:E.VALUE,t)}),R}},wt=e=>e===_.LIVE?{backBufferLength:8}:{},Ot=e=>{let{tokens:{drm:t}={},playbackId:r,drmTypeCb:n}=e,o=G(r);return!t||!o?{}:{emeEnabled:!0,drmSystems:{\"com.apple.fps\":{licenseUrl:q(e,\"fairplay\"),serverCertificateUrl:Ge(e,\"fairplay\")},\"com.widevine.alpha\":{licenseUrl:q(e,\"widevine\")},\"com.microsoft.playready\":{licenseUrl:q(e,\"playready\")}},requestMediaKeySystemAccessFunc:(a,i)=>(a===\"com.widevine.alpha\"&&(i=[...i.map(c=>{var u;let d=(u=c.videoCapabilities)==null?void 0:u.map(s=>({...s,robustness:\"HW_SECURE_ALL\"}));return{...c,videoCapabilities:d}}),...i]),navigator.requestMediaKeySystemAccess(a,i).then(c=>{let d=ft(a);return n==null||n(d),c}))}},Ut=async e=>{let t=await fetch(e);return t.status!==200?Promise.reject(t):await t.arrayBuffer()},Ht=async(e,t)=>{let r=await fetch(t,{method:\"POST\",headers:{\"Content-type\":\"application/octet-stream\"},body:e});if(r.status!==200)return Promise.reject(r);let n=await r.arrayBuffer();return new Uint8Array(n)},Vt=(e,t)=>{v(t,\"encrypted\",async n=>{try{let o=n.initDataType;if(o!==\"skd\"){console.error(`Received unexpected initialization data type \"${o}\"`);return}if(!t.mediaKeys){let u=await navigator.requestMediaKeySystemAccess(\"com.apple.fps\",[{initDataTypes:[o],videoCapabilities:[{contentType:\"application/vnd.apple.mpegurl\",robustness:\"\"}],distinctiveIdentifier:\"not-allowed\",persistentState:\"not-allowed\",sessionTypes:[\"temporary\"]}]).then(p=>{var l;return(l=e.drmTypeCb)==null||l.call(e,J.FAIRPLAY),p}).catch(()=>{let p=x(\"Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser.\"),l=new f(p,f.MEDIA_ERR_ENCRYPTED,!0);l.errorCategory=C.DRM,l.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM,N(t,l)});if(!u)return;let s=await u.createMediaKeys();try{let p=await Ut(Ge(e,\"fairplay\")).catch(l=>{if(l instanceof Response){let T=H(l,C.DRM,e);return console.error(\"mediaError\",T==null?void 0:T.message,T==null?void 0:T.context),T?Promise.reject(T):Promise.reject(new Error(\"Unexpected error in app cert request\"))}return Promise.reject(l)});await s.setServerCertificate(p).catch(()=>{let l=x(\"Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate.\"),T=new f(l,f.MEDIA_ERR_ENCRYPTED,!0);return T.errorCategory=C.DRM,T.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED,Promise.reject(T)})}catch(p){N(t,p);return}await t.setMediaKeys(s)}let a=n.initData;if(a==null){console.error(`Could not start encrypted playback due to missing initData in ${n.type} event`);return}let i=t.mediaKeys.createSession();i.addEventListener(\"keystatuseschange\",()=>{i.keyStatuses.forEach(u=>{let s;if(u===\"internal-error\"){let p=x(\"The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.\");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!0),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_CDM_ERROR}else if(u===\"output-restricted\"||u===\"output-downscaled\"){let p=x(\"DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.\");s=new f(p,f.MEDIA_ERR_ENCRYPTED,!1),s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}s&&N(t,s)})});let c=await Promise.all([i.generateRequest(o,a).catch(()=>{let u=x(\"Failed to generate a DRM license request. This may be an issue with the player or your protected content.\"),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED,N(t,s)}),new Promise(u=>{i.addEventListener(\"message\",s=>{u(s.message)},{once:!0})})]).then(([,u])=>u),d=await Ht(c,q(e,\"fairplay\")).catch(u=>{if(u instanceof Response){let s=H(u,C.DRM,e);return console.error(\"mediaError\",s==null?void 0:s.message,s==null?void 0:s.context),s?Promise.reject(s):Promise.reject(new Error(\"Unexpected error in license key request\"))}return Promise.reject(u)});await i.update(d).catch(()=>{let u=x(\"Failed to update DRM license. This may be an issue with the player or your protected content.\"),s=new f(u,f.MEDIA_ERR_ENCRYPTED,!0);return s.errorCategory=C.DRM,s.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED,Promise.reject(s)})}catch(o){N(t,o);return}})},q=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},n)=>{let o=G(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/license/${n}/${o}?token=${t}`},Ge=({playbackId:e,tokens:{drm:t}={},customDomain:r=I},n)=>{let o=G(e);return`https://license.${r.toLocaleLowerCase().endsWith(I)?r:I}/appcert/${n}/${o}?token=${t}`},Xe=({playbackId:e,src:t,customDomain:r})=>{if(e)return!0;if(typeof t!=\"string\")return!1;let n=window==null?void 0:window.location.href,o=new URL(t,n).hostname.toLocaleLowerCase();return o.includes(I)||!!r&&o.includes(r.toLocaleLowerCase())},Kt=(e,t,r)=>{var d;let{envKey:n,disableTracking:o,muxDataSDK:a=mux_embed__WEBPACK_IMPORTED_MODULE_0__[\"default\"],muxDataSDKOptions:i={}}=e,c=Xe(e);if(!o&&(n||c)){let{playerInitTime:u,playerSoftwareName:s,playerSoftwareVersion:p,beaconCollectionDomain:l,debug:T,disableCookies:m}=e,R={...e.metadata,video_title:((d=e==null?void 0:e.metadata)==null?void 0:d.video_title)||void 0},M=h=>typeof h.player_error_code==\"string\"?!1:typeof e.errorTranslator==\"function\"?e.errorTranslator(h):h;a.monitor(t,{debug:T,beaconCollectionDomain:l,hlsjs:r,Hls:r?g:void 0,automaticErrorTracking:!1,errorTranslator:M,disableCookies:m,...i,data:{...n?{env_key:n}:{},player_software_name:s,player_software:s,player_software_version:p,player_init_time:u,...R}})}},Wt=(e,t,r)=>{var s,p;let n=qe(e,t),{src:o,customDomain:a=I}=e,i=()=>{t.ended||!At(t,r)||(Je(t,r)?t.currentTime=t.buffered.end(t.buffered.length-1):t.dispatchEvent(new Event(\"ended\")))},c,d,u=()=>{let l=Be(t),T,m;l.length>0&&(T=l.start(0),m=l.end(0)),(d!==m||c!==T)&&t.dispatchEvent(new CustomEvent(\"seekablechange\",{composed:!0})),c=T,d=m};if(v(t,\"durationchange\",u),t&&n){let l=U(e);if(typeof o==\"string\"){if(o.endsWith(\".mp4\")&&o.includes(a)){let R=$e(o),M=new URL(`https://stream.${a}/${R}/metadata.json`);de(M.toString(),t)}let T=()=>{if(we(t)!==_.LIVE||Number.isFinite(t.duration))return;let R=setInterval(u,1e3);t.addEventListener(\"teardown\",()=>{clearInterval(R)},{once:!0}),v(t,\"durationchange\",()=>{Number.isFinite(t.duration)&&clearInterval(R)})},m=async()=>xt(o,t,l).then(T).catch(R=>{if(R instanceof Response){let M=H(R,C.VIDEO,e);if(M){N(t,M);return}}else R instanceof Error});if(t.preload===\"none\"){let R=()=>{m(),t.removeEventListener(\"loadedmetadata\",M)},M=()=>{m(),t.removeEventListener(\"play\",R)};v(t,\"play\",R,{once:!0}),v(t,\"loadedmetadata\",M,{once:!0})}else m();(s=e.tokens)!=null&&s.drm?Vt(e,t):v(t,\"encrypted\",()=>{let R=x(\"Attempting to play DRM-protected content without providing a DRM token.\"),M=new f(R,f.MEDIA_ERR_ENCRYPTED,!0);M.errorCategory=C.DRM,M.muxCode=D.ENCRYPTED_MISSING_TOKEN,N(t,M)},{once:!0}),t.setAttribute(\"src\",o),e.startTime&&(((p=P.get(t))!=null?p:{}).startTime=e.startTime,t.addEventListener(\"durationchange\",ze,{once:!0}))}else t.removeAttribute(\"src\");t.addEventListener(\"error\",Qe),t.addEventListener(\"error\",ce),t.addEventListener(\"emptied\",()=>{t.querySelectorAll(\"track[data-removeondestroy]\").forEach(m=>{m.remove()})},{once:!0}),v(t,\"pause\",i),v(t,\"seeked\",i),v(t,\"play\",()=>{t.ended||je(t.currentTime,t.duration)&&(t.currentTime=t.seekable.length?t.seekable.start(0):0)})}else r&&o?(r.once(g.Events.LEVEL_LOADED,(l,T)=>{Dt(T.details,t,r),u(),we(t)===_.LIVE&&!Number.isFinite(t.duration)&&(r.on(g.Events.LEVEL_UPDATED,u),v(t,\"durationchange\",()=>{Number.isFinite(t.duration)&&r.off(g.Events.LEVELS_UPDATED,u)}))}),r.on(g.Events.ERROR,(l,T)=>{var R,M;let m=Yt(T,e);if(m.muxCode===D.NETWORK_NOT_READY){let E=(R=P.get(t))!=null?R:{},b=(M=E.retryCount)!=null?M:0;if(b<6){let y=b===0?5e3:6e4,k=new f(`Retrying in ${y/1e3} seconds...`,m.code,m.fatal);Object.assign(k,m),N(t,k),setTimeout(()=>{E.retryCount=b+1,T.details===\"manifestLoadError\"&&T.url&&r.loadSource(T.url)},y);return}else{E.retryCount=0;let y=new f('Try again later or <a href=\"#\" onclick=\"window.location.reload(); return false;\" style=\"color: #4a90e2;\">click here to retry</a>',m.code,m.fatal);Object.assign(y,m),N(t,y);return}}N(t,m)}),r.on(g.Events.MANIFEST_LOADED,()=>{let l=P.get(t);l&&l.error&&(l.error=null,l.retryCount=0,t.dispatchEvent(new Event(\"emptied\")),t.dispatchEvent(new Event(\"loadstart\")))}),t.addEventListener(\"error\",ce),v(t,\"waiting\",i),De(e,r),be(t,r),r.attachMedia(t)):console.error(\"It looks like the video you're trying to play will not work on this system! If possible, try upgrading to the newest versions of your browser or software.\")};function ze(e){var n;let t=e.target,r=(n=P.get(t))==null?void 0:n.startTime;if(r&&fe(t.seekable,t.duration,r)){let o=t.preload===\"auto\";o&&(t.preload=\"none\"),t.currentTime=r,o&&(t.preload=\"auto\")}}async function Qe(e){if(!e.isTrusted)return;e.stopImmediatePropagation();let t=e.target;if(!(t!=null&&t.error))return;let{message:r,code:n}=t.error,o=new f(r,n);if(t.src&&n===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&t.readyState===HTMLMediaElement.HAVE_NOTHING){setTimeout(()=>{var i;let a=(i=ht(t))!=null?i:t.error;(a==null?void 0:a.code)===f.MEDIA_ERR_SRC_NOT_SUPPORTED&&N(t,o)},500);return}if(t.src&&(n!==f.MEDIA_ERR_DECODE||n!==void 0))try{let{status:a}=await fetch(t.src);o.data={response:{code:a}}}catch{}N(t,o)}function N(e,t){var r;t.fatal&&(((r=P.get(e))!=null?r:{}).error=t,e.dispatchEvent(new CustomEvent(\"error\",{detail:t})))}function ce(e){var n,o;if(!(e instanceof CustomEvent)||!(e.detail instanceof f))return;let t=e.target,r=e.detail;!r||!r.fatal||(((n=P.get(t))!=null?n:{}).error=r,(o=t.mux)==null||o.emit(\"error\",{player_error_code:r.code,player_error_message:r.message,player_error_context:r.context}))}var Yt=(e,t)=>{var c,d,u;console.error(\"getErrorFromHlsErrorData()\",e);let r={[g.ErrorTypes.NETWORK_ERROR]:f.MEDIA_ERR_NETWORK,[g.ErrorTypes.MEDIA_ERROR]:f.MEDIA_ERR_DECODE,[g.ErrorTypes.KEY_SYSTEM_ERROR]:f.MEDIA_ERR_ENCRYPTED},n=s=>[g.ErrorDetails.KEY_SYSTEM_LICENSE_REQUEST_FAILED,g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED].includes(s.details)?f.MEDIA_ERR_NETWORK:r[s.type],o=s=>{if(s.type===g.ErrorTypes.KEY_SYSTEM_ERROR)return C.DRM;if(s.type===g.ErrorTypes.NETWORK_ERROR)return C.VIDEO},a,i=n(e);if(i===f.MEDIA_ERR_NETWORK&&e.response){let s=(c=o(e))!=null?c:C.VIDEO;a=(d=H(e.response,s,t,e.fatal))!=null?d:new f(\"\",i,e.fatal)}else if(i===f.MEDIA_ERR_ENCRYPTED)if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_CONFIGURED_LICENSE){let s=x(\"Attempting to play DRM-protected content without providing a DRM token.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_MISSING_TOKEN}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_ACCESS){let s=x(\"Cannot play DRM-protected content with current security configuration on this browser. Try playing in another browser.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UNSUPPORTED_KEY_SYSTEM}else if(e.details===g.ErrorDetails.KEY_SYSTEM_NO_SESSION){let s=x(\"Failed to generate a DRM license request. This may be an issue with the player or your protected content.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!0),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_GENERATE_REQUEST_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SESSION_UPDATE_FAILED){let s=x(\"Failed to update DRM license. This may be an issue with the player or your protected content.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_LICENSE_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED){let s=x(\"Your server certificate failed when attempting to set it. This may be an issue with a no longer valid certificate.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_UPDATE_SERVER_CERT_FAILED}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_INTERNAL_ERROR){let s=x(\"The DRM Content Decryption Module system had an internal failure. Try reloading the page, upading your browser, or playing in another browser.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_CDM_ERROR}else if(e.details===g.ErrorDetails.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED){let s=x(\"DRM playback is being attempted in an environment that is not sufficiently secure. User may see black screen.\");a=new f(s,f.MEDIA_ERR_ENCRYPTED,!1),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_OUTPUT_RESTRICTED}else a=new f(e.error.message,f.MEDIA_ERR_ENCRYPTED,e.fatal),a.errorCategory=C.DRM,a.muxCode=D.ENCRYPTED_ERROR;else a=new f(\"\",i,e.fatal);return a.context||(a.context=`${e.url?`url: ${e.url}\n`:\"\"}${e.response&&(e.response.code||e.response.text)?`response: ${e.response.code}, ${e.response.text}\n`:\"\"}${e.reason?`failure reason: ${e.reason}\n`:\"\"}${e.level?`level: ${e.level}\n`:\"\"}${e.parent?`parent stream controller: ${e.parent}\n`:\"\"}${e.buffer?`buffer length: ${e.buffer}\n`:\"\"}${e.error?`error: ${e.error}\n`:\"\"}${e.event?`event: ${e.event}\n`:\"\"}${e.err?`error message: ${(u=e.err)==null?void 0:u.message}\n`:\"\"}`),a.data=e,a};\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@mux/playback-core/dist/index.mjs\n");

/***/ })

};
;