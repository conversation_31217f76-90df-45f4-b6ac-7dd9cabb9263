"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerYouTube"],{

/***/ "(app-pages-browser)/./node_modules/youtube-video-element/dist/react.js":
/*!**********************************************************!*\
  !*** ./node_modules/youtube-video-element/dist/react.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _youtube_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./youtube-video-element.js */ \"(app-pages-browser)/./node_modules/youtube-video-element/dist/youtube-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"youtube-video\",\n    elementClass: _youtube_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/youtube-video-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/youtube-video-element/dist/youtube-video-element.js":
/*!**************************************************************************!*\
  !*** ./node_modules/youtube-video-element/dist/youtube-video-element.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ youtube_video_element_default)\n/* harmony export */ });\nconst EMBED_BASE = \"https://www.youtube.com/embed\";\nconst EMBED_BASE_NOCOOKIE = \"https://www.youtube-nocookie.com/embed\";\nconst API_URL = \"https://www.youtube.com/iframe_api\";\nconst API_GLOBAL = \"YT\";\nconst API_GLOBAL_READY = \"onYouTubeIframeAPIReady\";\nconst VIDEO_MATCH_SRC = /(?:youtu\\.be\\/|youtube(?:-nocookie)?\\.com\\/(?:embed\\/|v\\/|watch\\?v=|watch\\?.+&v=|shorts\\/|live\\/))((\\w|-){11})/;\nconst PLAYLIST_MATCH_SRC = /(?:youtu\\.be\\/|youtube(?:-nocookie)?\\.com\\/.*?[?&]list=)([\\w_-]+)/;\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n        position: relative;\n        min-width: 300px;\n        min-height: 150px;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  if (!attrs.src) return;\n  const embedBase = attrs.src.includes(\"-nocookie\") ? EMBED_BASE_NOCOOKIE : EMBED_BASE;\n  const params = {\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    loop: attrs.loop,\n    mute: attrs.muted,\n    playsinline: attrs.playsinline,\n    preload: attrs.preload ?? \"metadata\",\n    // https://developers.google.com/youtube/player_parameters#Parameters\n    // origin: globalThis.location?.origin,\n    enablejsapi: 1,\n    showinfo: 0,\n    rel: 0,\n    iv_load_policy: 3,\n    modestbranding: 1,\n    ...props.config\n  };\n  if (VIDEO_MATCH_SRC.test(attrs.src)) {\n    const matches2 = attrs.src.match(VIDEO_MATCH_SRC);\n    const srcId = matches2 && matches2[1];\n    return `${embedBase}/${srcId}?${serialize(params)}`;\n  }\n  const matches = attrs.src.match(PLAYLIST_MATCH_SRC);\n  const playlistId = matches && matches[1];\n  const extendedParams = {\n    listType: \"playlist\",\n    list: playlistId,\n    ...params\n  };\n  return `${embedBase}?${serialize(extendedParams)}`;\n}\nclass YoutubeVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"autoplay\",\n    \"controls\",\n    \"crossorigin\",\n    \"loop\",\n    \"muted\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #readyState = 0;\n  #seeking = false;\n  #seekComplete;\n  isLoaded = false;\n  #error = null;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n      this.isLoaded = false;\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#readyState = 0;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      oldApi == null ? void 0 : oldApi.destroy();\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    let attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    const YT = await loadScript(API_URL, API_GLOBAL, API_GLOBAL_READY);\n    this.api = new YT.Player(iframe, {\n      events: {\n        onReady: () => {\n          this.#readyState = 1;\n          this.dispatchEvent(new Event(\"loadedmetadata\"));\n          this.dispatchEvent(new Event(\"durationchange\"));\n          this.dispatchEvent(new Event(\"volumechange\"));\n          this.dispatchEvent(new Event(\"loadcomplete\"));\n          this.isLoaded = true;\n          this.loadComplete.resolve();\n        },\n        onError: (error) => {\n          console.error(error);\n          this.#error = {\n            code: error.data,\n            message: `YouTube iframe player error #${error.data}; visit https://developers.google.com/youtube/iframe_api_reference#onError for the full error message.`\n          };\n          this.dispatchEvent(new Event(\"error\"));\n        }\n      }\n    });\n    let playFired = false;\n    this.api.addEventListener(\"onStateChange\", (event) => {\n      var _a;\n      const state = event.data;\n      if (state === YT.PlayerState.PLAYING || state === YT.PlayerState.BUFFERING) {\n        if (!playFired) {\n          playFired = true;\n          this.dispatchEvent(new Event(\"play\"));\n        }\n      }\n      if (state === YT.PlayerState.PLAYING) {\n        if (this.seeking) {\n          this.#seeking = false;\n          (_a = this.#seekComplete) == null ? void 0 : _a.resolve();\n          this.dispatchEvent(new Event(\"seeked\"));\n        }\n        this.#readyState = 3;\n        this.dispatchEvent(new Event(\"playing\"));\n      } else if (state === YT.PlayerState.PAUSED) {\n        const diff = Math.abs(this.currentTime - lastCurrentTime);\n        if (!this.seeking && diff > 0.1) {\n          this.#seeking = true;\n          this.dispatchEvent(new Event(\"seeking\"));\n        }\n        playFired = false;\n        this.dispatchEvent(new Event(\"pause\"));\n      }\n      if (state === YT.PlayerState.ENDED) {\n        playFired = false;\n        this.dispatchEvent(new Event(\"pause\"));\n        this.dispatchEvent(new Event(\"ended\"));\n        if (this.loop) {\n          this.play();\n        }\n      }\n    });\n    this.api.addEventListener(\"onPlaybackRateChange\", () => {\n      this.dispatchEvent(new Event(\"ratechange\"));\n    });\n    this.api.addEventListener(\"onVolumeChange\", () => {\n      this.dispatchEvent(new Event(\"volumechange\"));\n    });\n    this.api.addEventListener(\"onVideoProgress\", () => {\n      this.dispatchEvent(new Event(\"timeupdate\"));\n    });\n    await this.loadComplete;\n    let lastCurrentTime = 0;\n    setInterval(() => {\n      var _a;\n      const diff = Math.abs(this.currentTime - lastCurrentTime);\n      const bufferedEnd = this.buffered.end(this.buffered.length - 1);\n      if (this.seeking && bufferedEnd > 0.1) {\n        this.#seeking = false;\n        (_a = this.#seekComplete) == null ? void 0 : _a.resolve();\n        this.dispatchEvent(new Event(\"seeked\"));\n      } else if (!this.seeking && diff > 0.1) {\n        this.#seeking = true;\n        this.dispatchEvent(new Event(\"seeking\"));\n      }\n      lastCurrentTime = this.currentTime;\n    }, 50);\n    let lastBufferedEnd;\n    const progressInterval = setInterval(() => {\n      const bufferedEnd = this.buffered.end(this.buffered.length - 1);\n      if (bufferedEnd >= this.duration) {\n        clearInterval(progressInterval);\n        this.#readyState = 4;\n      }\n      if (lastBufferedEnd != bufferedEnd) {\n        lastBufferedEnd = bufferedEnd;\n        this.dispatchEvent(new Event(\"progress\"));\n      }\n    }, 100);\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\":\n      case \"autoplay\":\n      case \"controls\":\n      case \"loop\":\n      case \"playsinline\": {\n        this.load();\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#seekComplete = null;\n    await this.loadComplete;\n    (_a = this.api) == null ? void 0 : _a.playVideo();\n    return createPlayPromise(this);\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pauseVideo();\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    if (this.src == val) return;\n    this.setAttribute(\"src\", val);\n  }\n  get error() {\n    return this.#error;\n  }\n  /* onStateChange\n    -1 (unstarted)\n    0 (ended)\n    1 (playing)\n    2 (paused)\n    3 (buffering)\n    5 (video cued).\n  */\n  get paused() {\n    var _a, _b;\n    if (!this.isLoaded) return !this.autoplay;\n    return [-1, 0, 2, 5].includes((_b = (_a = this.api) == null ? void 0 : _a.getPlayerState) == null ? void 0 : _b.call(_a));\n  }\n  get duration() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getDuration) == null ? void 0 : _b.call(_a)) ?? NaN;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get buffered() {\n    var _a, _b;\n    if (!this.isLoaded) return createTimeRanges();\n    const progress = ((_a = this.api) == null ? void 0 : _a.getVideoLoadedFraction()) * ((_b = this.api) == null ? void 0 : _b.getDuration());\n    if (progress > 0) {\n      return createTimeRanges(0, progress);\n    }\n    return createTimeRanges();\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getCurrentTime) == null ? void 0 : _b.call(_a)) ?? 0;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#seekComplete = new PublicPromise();\n    this.loadComplete.then(() => {\n      var _a, _b;\n      (_a = this.api) == null ? void 0 : _a.seekTo(val, true);\n      if (this.paused) {\n        (_b = this.#seekComplete) == null ? void 0 : _b.then(() => {\n          var _a2;\n          if (!this.#seekComplete) return;\n          (_a2 = this.api) == null ? void 0 : _a2.pauseVideo();\n        });\n      }\n    });\n  }\n  set defaultMuted(val) {\n    if (this.defaultMuted == val) return;\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  set muted(val) {\n    if (this.muted == val) return;\n    this.loadComplete.then(() => {\n      var _a, _b;\n      val ? (_a = this.api) == null ? void 0 : _a.mute() : (_b = this.api) == null ? void 0 : _b.unMute();\n    });\n  }\n  get muted() {\n    var _a, _b;\n    if (!this.isLoaded) return this.defaultMuted;\n    return (_b = (_a = this.api) == null ? void 0 : _a.isMuted) == null ? void 0 : _b.call(_a);\n  }\n  get playbackRate() {\n    var _a, _b;\n    return ((_b = (_a = this.api) == null ? void 0 : _a.getPlaybackRate) == null ? void 0 : _b.call(_a)) ?? 1;\n  }\n  set playbackRate(val) {\n    if (this.playbackRate == val) return;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setPlaybackRate(val);\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    if (this.playsInline == val) return;\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  get poster() {\n    return this.getAttribute(\"poster\");\n  }\n  set poster(val) {\n    if (this.poster == val) return;\n    this.setAttribute(\"poster\", `${val}`);\n  }\n  set volume(val) {\n    if (this.volume == val) return;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setVolume(val * 100);\n    });\n  }\n  get volume() {\n    var _a;\n    if (!this.isLoaded) return 1;\n    return ((_a = this.api) == null ? void 0 : _a.getVolume()) / 100;\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName, readyFnName) {\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (globalName && self[globalName]) {\n    await delay(0);\n    return self[globalName];\n  }\n  return loadScriptCache[src] = new Promise(function(resolve, reject) {\n    const script = document.createElement(\"script\");\n    script.src = src;\n    const ready = () => resolve(self[globalName]);\n    if (readyFnName) self[readyFnName] = ready;\n    script.onload = () => !readyFnName && ready();\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nconst delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nfunction promisify(fn) {\n  return (...args) => new Promise((resolve) => {\n    fn(...args, (...res) => {\n      if (res.length > 1) resolve(res);\n      else resolve(res[0]);\n    });\n  });\n}\nfunction createPlayPromise(player) {\n  return promisify((event, cb) => {\n    let fn;\n    player.addEventListener(\n      event,\n      fn = () => {\n        player.removeEventListener(event, fn);\n        cb();\n      }\n    );\n  })(\"playing\");\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"youtube-video\")) {\n  globalThis.customElements.define(\"youtube-video\", YoutubeVideoElement);\n}\nvar youtube_video_element_default = YoutubeVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy95b3V0dWJlLXZpZGVvLWVsZW1lbnQvZGlzdC95b3V0dWJlLXZpZGVvLWVsZW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtSUFBbUksR0FBRztBQUN0STtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLFlBQVksVUFBVSxpQkFBaUIsV0FBVztBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlDQUFpQztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxVQUFVLEdBQUcsTUFBTSxHQUFHLGtCQUFrQjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxVQUFVLEdBQUcsMEJBQTBCO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLGNBQWM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUVBQXlFO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELGFBQWE7QUFDbEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLElBQUk7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxnQkFBZ0I7QUFDbEQscUJBQXFCLGdCQUFnQixJQUFJLGNBQWMsTUFBTSxHQUFHO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDLHNCQUFzQixzQkFBc0Isd0JBQXdCLHdCQUF3Qix3QkFBd0I7QUFDcko7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFIiwic291cmNlcyI6WyIvVXNlcnMvRXRoYW5MZWUvRGVza3RvcC9BZHZYL09wZW4tTExNLVZUdWJlci1XZWIvbm9kZV9tb2R1bGVzL3lvdXR1YmUtdmlkZW8tZWxlbWVudC9kaXN0L3lvdXR1YmUtdmlkZW8tZWxlbWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBFTUJFRF9CQVNFID0gXCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZFwiO1xuY29uc3QgRU1CRURfQkFTRV9OT0NPT0tJRSA9IFwiaHR0cHM6Ly93d3cueW91dHViZS1ub2Nvb2tpZS5jb20vZW1iZWRcIjtcbmNvbnN0IEFQSV9VUkwgPSBcImh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL2lmcmFtZV9hcGlcIjtcbmNvbnN0IEFQSV9HTE9CQUwgPSBcIllUXCI7XG5jb25zdCBBUElfR0xPQkFMX1JFQURZID0gXCJvbllvdVR1YmVJZnJhbWVBUElSZWFkeVwiO1xuY29uc3QgVklERU9fTUFUQ0hfU1JDID0gLyg/OnlvdXR1XFwuYmVcXC98eW91dHViZSg/Oi1ub2Nvb2tpZSk/XFwuY29tXFwvKD86ZW1iZWRcXC98dlxcL3x3YXRjaFxcP3Y9fHdhdGNoXFw/Lismdj18c2hvcnRzXFwvfGxpdmVcXC8pKSgoXFx3fC0pezExfSkvO1xuY29uc3QgUExBWUxJU1RfTUFUQ0hfU1JDID0gLyg/OnlvdXR1XFwuYmVcXC98eW91dHViZSg/Oi1ub2Nvb2tpZSk/XFwuY29tXFwvLio/Wz8mXWxpc3Q9KShbXFx3Xy1dKykvO1xuZnVuY3Rpb24gZ2V0VGVtcGxhdGVIVE1MKGF0dHJzLCBwcm9wcyA9IHt9KSB7XG4gIGNvbnN0IGlmcmFtZUF0dHJzID0ge1xuICAgIHNyYzogc2VyaWFsaXplSWZyYW1lVXJsKGF0dHJzLCBwcm9wcyksXG4gICAgZnJhbWVib3JkZXI6IDAsXG4gICAgd2lkdGg6IFwiMTAwJVwiLFxuICAgIGhlaWdodDogXCIxMDAlXCIsXG4gICAgYWxsb3c6IFwiYWNjZWxlcm9tZXRlcjsgZnVsbHNjcmVlbjsgYXV0b3BsYXk7IGVuY3J5cHRlZC1tZWRpYTsgZ3lyb3Njb3BlOyBwaWN0dXJlLWluLXBpY3R1cmVcIlxuICB9O1xuICBpZiAocHJvcHMuY29uZmlnKSB7XG4gICAgaWZyYW1lQXR0cnNbXCJkYXRhLWNvbmZpZ1wiXSA9IEpTT04uc3RyaW5naWZ5KHByb3BzLmNvbmZpZyk7XG4gIH1cbiAgcmV0dXJuIChcbiAgICAvKmh0bWwqL1xuICAgIGBcbiAgICA8c3R5bGU+XG4gICAgICA6aG9zdCB7XG4gICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgICAgbGluZS1oZWlnaHQ6IDA7XG4gICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgICAgbWluLXdpZHRoOiAzMDBweDtcbiAgICAgICAgbWluLWhlaWdodDogMTUwcHg7XG4gICAgICB9XG4gICAgICBpZnJhbWUge1xuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgIHRvcDogMDtcbiAgICAgICAgbGVmdDogMDtcbiAgICAgIH1cbiAgICA8L3N0eWxlPlxuICAgIDxpZnJhbWUke3NlcmlhbGl6ZUF0dHJpYnV0ZXMoaWZyYW1lQXR0cnMpfT48L2lmcmFtZT5cbiAgYFxuICApO1xufVxuZnVuY3Rpb24gc2VyaWFsaXplSWZyYW1lVXJsKGF0dHJzLCBwcm9wcykge1xuICBpZiAoIWF0dHJzLnNyYykgcmV0dXJuO1xuICBjb25zdCBlbWJlZEJhc2UgPSBhdHRycy5zcmMuaW5jbHVkZXMoXCItbm9jb29raWVcIikgPyBFTUJFRF9CQVNFX05PQ09PS0lFIDogRU1CRURfQkFTRTtcbiAgY29uc3QgcGFyYW1zID0ge1xuICAgIC8vID9jb250cm9scz10cnVlIGlzIGVuYWJsZWQgYnkgZGVmYXVsdCBpbiB0aGUgaWZyYW1lXG4gICAgY29udHJvbHM6IGF0dHJzLmNvbnRyb2xzID09PSBcIlwiID8gbnVsbCA6IDAsXG4gICAgYXV0b3BsYXk6IGF0dHJzLmF1dG9wbGF5LFxuICAgIGxvb3A6IGF0dHJzLmxvb3AsXG4gICAgbXV0ZTogYXR0cnMubXV0ZWQsXG4gICAgcGxheXNpbmxpbmU6IGF0dHJzLnBsYXlzaW5saW5lLFxuICAgIHByZWxvYWQ6IGF0dHJzLnByZWxvYWQgPz8gXCJtZXRhZGF0YVwiLFxuICAgIC8vIGh0dHBzOi8vZGV2ZWxvcGVycy5nb29nbGUuY29tL3lvdXR1YmUvcGxheWVyX3BhcmFtZXRlcnMjUGFyYW1ldGVyc1xuICAgIC8vIG9yaWdpbjogZ2xvYmFsVGhpcy5sb2NhdGlvbj8ub3JpZ2luLFxuICAgIGVuYWJsZWpzYXBpOiAxLFxuICAgIHNob3dpbmZvOiAwLFxuICAgIHJlbDogMCxcbiAgICBpdl9sb2FkX3BvbGljeTogMyxcbiAgICBtb2Rlc3RicmFuZGluZzogMSxcbiAgICAuLi5wcm9wcy5jb25maWdcbiAgfTtcbiAgaWYgKFZJREVPX01BVENIX1NSQy50ZXN0KGF0dHJzLnNyYykpIHtcbiAgICBjb25zdCBtYXRjaGVzMiA9IGF0dHJzLnNyYy5tYXRjaChWSURFT19NQVRDSF9TUkMpO1xuICAgIGNvbnN0IHNyY0lkID0gbWF0Y2hlczIgJiYgbWF0Y2hlczJbMV07XG4gICAgcmV0dXJuIGAke2VtYmVkQmFzZX0vJHtzcmNJZH0/JHtzZXJpYWxpemUocGFyYW1zKX1gO1xuICB9XG4gIGNvbnN0IG1hdGNoZXMgPSBhdHRycy5zcmMubWF0Y2goUExBWUxJU1RfTUFUQ0hfU1JDKTtcbiAgY29uc3QgcGxheWxpc3RJZCA9IG1hdGNoZXMgJiYgbWF0Y2hlc1sxXTtcbiAgY29uc3QgZXh0ZW5kZWRQYXJhbXMgPSB7XG4gICAgbGlzdFR5cGU6IFwicGxheWxpc3RcIixcbiAgICBsaXN0OiBwbGF5bGlzdElkLFxuICAgIC4uLnBhcmFtc1xuICB9O1xuICByZXR1cm4gYCR7ZW1iZWRCYXNlfT8ke3NlcmlhbGl6ZShleHRlbmRlZFBhcmFtcyl9YDtcbn1cbmNsYXNzIFlvdXR1YmVWaWRlb0VsZW1lbnQgZXh0ZW5kcyAoZ2xvYmFsVGhpcy5IVE1MRWxlbWVudCA/PyBjbGFzcyB7XG59KSB7XG4gIHN0YXRpYyBnZXRUZW1wbGF0ZUhUTUwgPSBnZXRUZW1wbGF0ZUhUTUw7XG4gIHN0YXRpYyBzaGFkb3dSb290T3B0aW9ucyA9IHsgbW9kZTogXCJvcGVuXCIgfTtcbiAgc3RhdGljIG9ic2VydmVkQXR0cmlidXRlcyA9IFtcbiAgICBcImF1dG9wbGF5XCIsXG4gICAgXCJjb250cm9sc1wiLFxuICAgIFwiY3Jvc3NvcmlnaW5cIixcbiAgICBcImxvb3BcIixcbiAgICBcIm11dGVkXCIsXG4gICAgXCJwbGF5c2lubGluZVwiLFxuICAgIFwicG9zdGVyXCIsXG4gICAgXCJwcmVsb2FkXCIsXG4gICAgXCJzcmNcIlxuICBdO1xuICBsb2FkQ29tcGxldGUgPSBuZXcgUHVibGljUHJvbWlzZSgpO1xuICAjbG9hZFJlcXVlc3RlZDtcbiAgI2hhc0xvYWRlZDtcbiAgI3JlYWR5U3RhdGUgPSAwO1xuICAjc2Vla2luZyA9IGZhbHNlO1xuICAjc2Vla0NvbXBsZXRlO1xuICBpc0xvYWRlZCA9IGZhbHNlO1xuICAjZXJyb3IgPSBudWxsO1xuICAjY29uZmlnID0gbnVsbDtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgc3VwZXIoKTtcbiAgICB0aGlzLiN1cGdyYWRlUHJvcGVydHkoXCJjb25maWdcIik7XG4gIH1cbiAgZ2V0IGNvbmZpZygpIHtcbiAgICByZXR1cm4gdGhpcy4jY29uZmlnO1xuICB9XG4gIHNldCBjb25maWcodmFsdWUpIHtcbiAgICB0aGlzLiNjb25maWcgPSB2YWx1ZTtcbiAgfVxuICBhc3luYyBsb2FkKCkge1xuICAgIGlmICh0aGlzLiNsb2FkUmVxdWVzdGVkKSByZXR1cm47XG4gICAgaWYgKCF0aGlzLnNoYWRvd1Jvb3QpIHtcbiAgICAgIHRoaXMuYXR0YWNoU2hhZG93KHsgbW9kZTogXCJvcGVuXCIgfSk7XG4gICAgfVxuICAgIGNvbnN0IGlzRmlyc3RMb2FkID0gIXRoaXMuI2hhc0xvYWRlZDtcbiAgICBpZiAodGhpcy4jaGFzTG9hZGVkKSB7XG4gICAgICB0aGlzLmxvYWRDb21wbGV0ZSA9IG5ldyBQdWJsaWNQcm9taXNlKCk7XG4gICAgICB0aGlzLmlzTG9hZGVkID0gZmFsc2U7XG4gICAgfVxuICAgIHRoaXMuI2hhc0xvYWRlZCA9IHRydWU7XG4gICAgYXdhaXQgKHRoaXMuI2xvYWRSZXF1ZXN0ZWQgPSBQcm9taXNlLnJlc29sdmUoKSk7XG4gICAgdGhpcy4jbG9hZFJlcXVlc3RlZCA9IG51bGw7XG4gICAgdGhpcy4jcmVhZHlTdGF0ZSA9IDA7XG4gICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcImVtcHRpZWRcIikpO1xuICAgIGxldCBvbGRBcGkgPSB0aGlzLmFwaTtcbiAgICB0aGlzLmFwaSA9IG51bGw7XG4gICAgaWYgKCF0aGlzLnNyYykge1xuICAgICAgb2xkQXBpID09IG51bGwgPyB2b2lkIDAgOiBvbGRBcGkuZGVzdHJveSgpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwibG9hZHN0YXJ0XCIpKTtcbiAgICBsZXQgaWZyYW1lID0gdGhpcy5zaGFkb3dSb290LnF1ZXJ5U2VsZWN0b3IoXCJpZnJhbWVcIik7XG4gICAgbGV0IGF0dHJzID0gbmFtZWROb2RlTWFwVG9PYmplY3QodGhpcy5hdHRyaWJ1dGVzKTtcbiAgICBpZiAoaXNGaXJzdExvYWQgJiYgaWZyYW1lKSB7XG4gICAgICB0aGlzLiNjb25maWcgPSBKU09OLnBhcnNlKGlmcmFtZS5nZXRBdHRyaWJ1dGUoXCJkYXRhLWNvbmZpZ1wiKSB8fCBcInt9XCIpO1xuICAgIH1cbiAgICBpZiAoIShpZnJhbWUgPT0gbnVsbCA/IHZvaWQgMCA6IGlmcmFtZS5zcmMpIHx8IGlmcmFtZS5zcmMgIT09IHNlcmlhbGl6ZUlmcmFtZVVybChhdHRycywgdGhpcykpIHtcbiAgICAgIHRoaXMuc2hhZG93Um9vdC5pbm5lckhUTUwgPSBnZXRUZW1wbGF0ZUhUTUwoYXR0cnMsIHRoaXMpO1xuICAgICAgaWZyYW1lID0gdGhpcy5zaGFkb3dSb290LnF1ZXJ5U2VsZWN0b3IoXCJpZnJhbWVcIik7XG4gICAgfVxuICAgIGNvbnN0IFlUID0gYXdhaXQgbG9hZFNjcmlwdChBUElfVVJMLCBBUElfR0xPQkFMLCBBUElfR0xPQkFMX1JFQURZKTtcbiAgICB0aGlzLmFwaSA9IG5ldyBZVC5QbGF5ZXIoaWZyYW1lLCB7XG4gICAgICBldmVudHM6IHtcbiAgICAgICAgb25SZWFkeTogKCkgPT4ge1xuICAgICAgICAgIHRoaXMuI3JlYWR5U3RhdGUgPSAxO1xuICAgICAgICAgIHRoaXMuZGlzcGF0Y2hFdmVudChuZXcgRXZlbnQoXCJsb2FkZWRtZXRhZGF0YVwiKSk7XG4gICAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcImR1cmF0aW9uY2hhbmdlXCIpKTtcbiAgICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwidm9sdW1lY2hhbmdlXCIpKTtcbiAgICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwibG9hZGNvbXBsZXRlXCIpKTtcbiAgICAgICAgICB0aGlzLmlzTG9hZGVkID0gdHJ1ZTtcbiAgICAgICAgICB0aGlzLmxvYWRDb21wbGV0ZS5yZXNvbHZlKCk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4ge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuICAgICAgICAgIHRoaXMuI2Vycm9yID0ge1xuICAgICAgICAgICAgY29kZTogZXJyb3IuZGF0YSxcbiAgICAgICAgICAgIG1lc3NhZ2U6IGBZb3VUdWJlIGlmcmFtZSBwbGF5ZXIgZXJyb3IgIyR7ZXJyb3IuZGF0YX07IHZpc2l0IGh0dHBzOi8vZGV2ZWxvcGVycy5nb29nbGUuY29tL3lvdXR1YmUvaWZyYW1lX2FwaV9yZWZlcmVuY2Ujb25FcnJvciBmb3IgdGhlIGZ1bGwgZXJyb3IgbWVzc2FnZS5gXG4gICAgICAgICAgfTtcbiAgICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwiZXJyb3JcIikpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG4gICAgbGV0IHBsYXlGaXJlZCA9IGZhbHNlO1xuICAgIHRoaXMuYXBpLmFkZEV2ZW50TGlzdGVuZXIoXCJvblN0YXRlQ2hhbmdlXCIsIChldmVudCkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgY29uc3Qgc3RhdGUgPSBldmVudC5kYXRhO1xuICAgICAgaWYgKHN0YXRlID09PSBZVC5QbGF5ZXJTdGF0ZS5QTEFZSU5HIHx8IHN0YXRlID09PSBZVC5QbGF5ZXJTdGF0ZS5CVUZGRVJJTkcpIHtcbiAgICAgICAgaWYgKCFwbGF5RmlyZWQpIHtcbiAgICAgICAgICBwbGF5RmlyZWQgPSB0cnVlO1xuICAgICAgICAgIHRoaXMuZGlzcGF0Y2hFdmVudChuZXcgRXZlbnQoXCJwbGF5XCIpKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKHN0YXRlID09PSBZVC5QbGF5ZXJTdGF0ZS5QTEFZSU5HKSB7XG4gICAgICAgIGlmICh0aGlzLnNlZWtpbmcpIHtcbiAgICAgICAgICB0aGlzLiNzZWVraW5nID0gZmFsc2U7XG4gICAgICAgICAgKF9hID0gdGhpcy4jc2Vla0NvbXBsZXRlKSA9PSBudWxsID8gdm9pZCAwIDogX2EucmVzb2x2ZSgpO1xuICAgICAgICAgIHRoaXMuZGlzcGF0Y2hFdmVudChuZXcgRXZlbnQoXCJzZWVrZWRcIikpO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuI3JlYWR5U3RhdGUgPSAzO1xuICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwicGxheWluZ1wiKSk7XG4gICAgICB9IGVsc2UgaWYgKHN0YXRlID09PSBZVC5QbGF5ZXJTdGF0ZS5QQVVTRUQpIHtcbiAgICAgICAgY29uc3QgZGlmZiA9IE1hdGguYWJzKHRoaXMuY3VycmVudFRpbWUgLSBsYXN0Q3VycmVudFRpbWUpO1xuICAgICAgICBpZiAoIXRoaXMuc2Vla2luZyAmJiBkaWZmID4gMC4xKSB7XG4gICAgICAgICAgdGhpcy4jc2Vla2luZyA9IHRydWU7XG4gICAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcInNlZWtpbmdcIikpO1xuICAgICAgICB9XG4gICAgICAgIHBsYXlGaXJlZCA9IGZhbHNlO1xuICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwicGF1c2VcIikpO1xuICAgICAgfVxuICAgICAgaWYgKHN0YXRlID09PSBZVC5QbGF5ZXJTdGF0ZS5FTkRFRCkge1xuICAgICAgICBwbGF5RmlyZWQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcInBhdXNlXCIpKTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcImVuZGVkXCIpKTtcbiAgICAgICAgaWYgKHRoaXMubG9vcCkge1xuICAgICAgICAgIHRoaXMucGxheSgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG4gICAgdGhpcy5hcGkuYWRkRXZlbnRMaXN0ZW5lcihcIm9uUGxheWJhY2tSYXRlQ2hhbmdlXCIsICgpID0+IHtcbiAgICAgIHRoaXMuZGlzcGF0Y2hFdmVudChuZXcgRXZlbnQoXCJyYXRlY2hhbmdlXCIpKTtcbiAgICB9KTtcbiAgICB0aGlzLmFwaS5hZGRFdmVudExpc3RlbmVyKFwib25Wb2x1bWVDaGFuZ2VcIiwgKCkgPT4ge1xuICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcInZvbHVtZWNoYW5nZVwiKSk7XG4gICAgfSk7XG4gICAgdGhpcy5hcGkuYWRkRXZlbnRMaXN0ZW5lcihcIm9uVmlkZW9Qcm9ncmVzc1wiLCAoKSA9PiB7XG4gICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwidGltZXVwZGF0ZVwiKSk7XG4gICAgfSk7XG4gICAgYXdhaXQgdGhpcy5sb2FkQ29tcGxldGU7XG4gICAgbGV0IGxhc3RDdXJyZW50VGltZSA9IDA7XG4gICAgc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgY29uc3QgZGlmZiA9IE1hdGguYWJzKHRoaXMuY3VycmVudFRpbWUgLSBsYXN0Q3VycmVudFRpbWUpO1xuICAgICAgY29uc3QgYnVmZmVyZWRFbmQgPSB0aGlzLmJ1ZmZlcmVkLmVuZCh0aGlzLmJ1ZmZlcmVkLmxlbmd0aCAtIDEpO1xuICAgICAgaWYgKHRoaXMuc2Vla2luZyAmJiBidWZmZXJlZEVuZCA+IDAuMSkge1xuICAgICAgICB0aGlzLiNzZWVraW5nID0gZmFsc2U7XG4gICAgICAgIChfYSA9IHRoaXMuI3NlZWtDb21wbGV0ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnJlc29sdmUoKTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcInNlZWtlZFwiKSk7XG4gICAgICB9IGVsc2UgaWYgKCF0aGlzLnNlZWtpbmcgJiYgZGlmZiA+IDAuMSkge1xuICAgICAgICB0aGlzLiNzZWVraW5nID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudChcInNlZWtpbmdcIikpO1xuICAgICAgfVxuICAgICAgbGFzdEN1cnJlbnRUaW1lID0gdGhpcy5jdXJyZW50VGltZTtcbiAgICB9LCA1MCk7XG4gICAgbGV0IGxhc3RCdWZmZXJlZEVuZDtcbiAgICBjb25zdCBwcm9ncmVzc0ludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgY29uc3QgYnVmZmVyZWRFbmQgPSB0aGlzLmJ1ZmZlcmVkLmVuZCh0aGlzLmJ1ZmZlcmVkLmxlbmd0aCAtIDEpO1xuICAgICAgaWYgKGJ1ZmZlcmVkRW5kID49IHRoaXMuZHVyYXRpb24pIHtcbiAgICAgICAgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKTtcbiAgICAgICAgdGhpcy4jcmVhZHlTdGF0ZSA9IDQ7XG4gICAgICB9XG4gICAgICBpZiAobGFzdEJ1ZmZlcmVkRW5kICE9IGJ1ZmZlcmVkRW5kKSB7XG4gICAgICAgIGxhc3RCdWZmZXJlZEVuZCA9IGJ1ZmZlcmVkRW5kO1xuICAgICAgICB0aGlzLmRpc3BhdGNoRXZlbnQobmV3IEV2ZW50KFwicHJvZ3Jlc3NcIikpO1xuICAgICAgfVxuICAgIH0sIDEwMCk7XG4gIH1cbiAgYXN5bmMgYXR0cmlidXRlQ2hhbmdlZENhbGxiYWNrKGF0dHJOYW1lLCBvbGRWYWx1ZSwgbmV3VmFsdWUpIHtcbiAgICBpZiAob2xkVmFsdWUgPT09IG5ld1ZhbHVlKSByZXR1cm47XG4gICAgc3dpdGNoIChhdHRyTmFtZSkge1xuICAgICAgY2FzZSBcInNyY1wiOlxuICAgICAgY2FzZSBcImF1dG9wbGF5XCI6XG4gICAgICBjYXNlIFwiY29udHJvbHNcIjpcbiAgICAgIGNhc2UgXCJsb29wXCI6XG4gICAgICBjYXNlIFwicGxheXNpbmxpbmVcIjoge1xuICAgICAgICB0aGlzLmxvYWQoKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgYXN5bmMgcGxheSgpIHtcbiAgICB2YXIgX2E7XG4gICAgdGhpcy4jc2Vla0NvbXBsZXRlID0gbnVsbDtcbiAgICBhd2FpdCB0aGlzLmxvYWRDb21wbGV0ZTtcbiAgICAoX2EgPSB0aGlzLmFwaSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnBsYXlWaWRlbygpO1xuICAgIHJldHVybiBjcmVhdGVQbGF5UHJvbWlzZSh0aGlzKTtcbiAgfVxuICBhc3luYyBwYXVzZSgpIHtcbiAgICB2YXIgX2E7XG4gICAgYXdhaXQgdGhpcy5sb2FkQ29tcGxldGU7XG4gICAgcmV0dXJuIChfYSA9IHRoaXMuYXBpKSA9PSBudWxsID8gdm9pZCAwIDogX2EucGF1c2VWaWRlbygpO1xuICB9XG4gIGdldCBzZWVraW5nKCkge1xuICAgIHJldHVybiB0aGlzLiNzZWVraW5nO1xuICB9XG4gIGdldCByZWFkeVN0YXRlKCkge1xuICAgIHJldHVybiB0aGlzLiNyZWFkeVN0YXRlO1xuICB9XG4gIGdldCBzcmMoKSB7XG4gICAgcmV0dXJuIHRoaXMuZ2V0QXR0cmlidXRlKFwic3JjXCIpO1xuICB9XG4gIHNldCBzcmModmFsKSB7XG4gICAgaWYgKHRoaXMuc3JjID09IHZhbCkgcmV0dXJuO1xuICAgIHRoaXMuc2V0QXR0cmlidXRlKFwic3JjXCIsIHZhbCk7XG4gIH1cbiAgZ2V0IGVycm9yKCkge1xuICAgIHJldHVybiB0aGlzLiNlcnJvcjtcbiAgfVxuICAvKiBvblN0YXRlQ2hhbmdlXG4gICAgLTEgKHVuc3RhcnRlZClcbiAgICAwIChlbmRlZClcbiAgICAxIChwbGF5aW5nKVxuICAgIDIgKHBhdXNlZClcbiAgICAzIChidWZmZXJpbmcpXG4gICAgNSAodmlkZW8gY3VlZCkuXG4gICovXG4gIGdldCBwYXVzZWQoKSB7XG4gICAgdmFyIF9hLCBfYjtcbiAgICBpZiAoIXRoaXMuaXNMb2FkZWQpIHJldHVybiAhdGhpcy5hdXRvcGxheTtcbiAgICByZXR1cm4gWy0xLCAwLCAyLCA1XS5pbmNsdWRlcygoX2IgPSAoX2EgPSB0aGlzLmFwaSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmdldFBsYXllclN0YXRlKSA9PSBudWxsID8gdm9pZCAwIDogX2IuY2FsbChfYSkpO1xuICB9XG4gIGdldCBkdXJhdGlvbigpIHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIHJldHVybiAoKF9iID0gKF9hID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5nZXREdXJhdGlvbikgPT0gbnVsbCA/IHZvaWQgMCA6IF9iLmNhbGwoX2EpKSA/PyBOYU47XG4gIH1cbiAgZ2V0IGF1dG9wbGF5KCkge1xuICAgIHJldHVybiB0aGlzLmhhc0F0dHJpYnV0ZShcImF1dG9wbGF5XCIpO1xuICB9XG4gIHNldCBhdXRvcGxheSh2YWwpIHtcbiAgICBpZiAodGhpcy5hdXRvcGxheSA9PSB2YWwpIHJldHVybjtcbiAgICB0aGlzLnRvZ2dsZUF0dHJpYnV0ZShcImF1dG9wbGF5XCIsIEJvb2xlYW4odmFsKSk7XG4gIH1cbiAgZ2V0IGJ1ZmZlcmVkKCkge1xuICAgIHZhciBfYSwgX2I7XG4gICAgaWYgKCF0aGlzLmlzTG9hZGVkKSByZXR1cm4gY3JlYXRlVGltZVJhbmdlcygpO1xuICAgIGNvbnN0IHByb2dyZXNzID0gKChfYSA9IHRoaXMuYXBpKSA9PSBudWxsID8gdm9pZCAwIDogX2EuZ2V0VmlkZW9Mb2FkZWRGcmFjdGlvbigpKSAqICgoX2IgPSB0aGlzLmFwaSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9iLmdldER1cmF0aW9uKCkpO1xuICAgIGlmIChwcm9ncmVzcyA+IDApIHtcbiAgICAgIHJldHVybiBjcmVhdGVUaW1lUmFuZ2VzKDAsIHByb2dyZXNzKTtcbiAgICB9XG4gICAgcmV0dXJuIGNyZWF0ZVRpbWVSYW5nZXMoKTtcbiAgfVxuICBnZXQgY29udHJvbHMoKSB7XG4gICAgcmV0dXJuIHRoaXMuaGFzQXR0cmlidXRlKFwiY29udHJvbHNcIik7XG4gIH1cbiAgc2V0IGNvbnRyb2xzKHZhbCkge1xuICAgIGlmICh0aGlzLmNvbnRyb2xzID09IHZhbCkgcmV0dXJuO1xuICAgIHRoaXMudG9nZ2xlQXR0cmlidXRlKFwiY29udHJvbHNcIiwgQm9vbGVhbih2YWwpKTtcbiAgfVxuICBnZXQgY3VycmVudFRpbWUoKSB7XG4gICAgdmFyIF9hLCBfYjtcbiAgICByZXR1cm4gKChfYiA9IChfYSA9IHRoaXMuYXBpKSA9PSBudWxsID8gdm9pZCAwIDogX2EuZ2V0Q3VycmVudFRpbWUpID09IG51bGwgPyB2b2lkIDAgOiBfYi5jYWxsKF9hKSkgPz8gMDtcbiAgfVxuICBzZXQgY3VycmVudFRpbWUodmFsKSB7XG4gICAgaWYgKHRoaXMuY3VycmVudFRpbWUgPT0gdmFsKSByZXR1cm47XG4gICAgdGhpcy4jc2Vla0NvbXBsZXRlID0gbmV3IFB1YmxpY1Byb21pc2UoKTtcbiAgICB0aGlzLmxvYWRDb21wbGV0ZS50aGVuKCgpID0+IHtcbiAgICAgIHZhciBfYSwgX2I7XG4gICAgICAoX2EgPSB0aGlzLmFwaSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLnNlZWtUbyh2YWwsIHRydWUpO1xuICAgICAgaWYgKHRoaXMucGF1c2VkKSB7XG4gICAgICAgIChfYiA9IHRoaXMuI3NlZWtDb21wbGV0ZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9iLnRoZW4oKCkgPT4ge1xuICAgICAgICAgIHZhciBfYTI7XG4gICAgICAgICAgaWYgKCF0aGlzLiNzZWVrQ29tcGxldGUpIHJldHVybjtcbiAgICAgICAgICAoX2EyID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYTIucGF1c2VWaWRlbygpO1xuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuICBzZXQgZGVmYXVsdE11dGVkKHZhbCkge1xuICAgIGlmICh0aGlzLmRlZmF1bHRNdXRlZCA9PSB2YWwpIHJldHVybjtcbiAgICB0aGlzLnRvZ2dsZUF0dHJpYnV0ZShcIm11dGVkXCIsIEJvb2xlYW4odmFsKSk7XG4gIH1cbiAgZ2V0IGRlZmF1bHRNdXRlZCgpIHtcbiAgICByZXR1cm4gdGhpcy5oYXNBdHRyaWJ1dGUoXCJtdXRlZFwiKTtcbiAgfVxuICBnZXQgbG9vcCgpIHtcbiAgICByZXR1cm4gdGhpcy5oYXNBdHRyaWJ1dGUoXCJsb29wXCIpO1xuICB9XG4gIHNldCBsb29wKHZhbCkge1xuICAgIGlmICh0aGlzLmxvb3AgPT0gdmFsKSByZXR1cm47XG4gICAgdGhpcy50b2dnbGVBdHRyaWJ1dGUoXCJsb29wXCIsIEJvb2xlYW4odmFsKSk7XG4gIH1cbiAgc2V0IG11dGVkKHZhbCkge1xuICAgIGlmICh0aGlzLm11dGVkID09IHZhbCkgcmV0dXJuO1xuICAgIHRoaXMubG9hZENvbXBsZXRlLnRoZW4oKCkgPT4ge1xuICAgICAgdmFyIF9hLCBfYjtcbiAgICAgIHZhbCA/IChfYSA9IHRoaXMuYXBpKSA9PSBudWxsID8gdm9pZCAwIDogX2EubXV0ZSgpIDogKF9iID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYi51bk11dGUoKTtcbiAgICB9KTtcbiAgfVxuICBnZXQgbXV0ZWQoKSB7XG4gICAgdmFyIF9hLCBfYjtcbiAgICBpZiAoIXRoaXMuaXNMb2FkZWQpIHJldHVybiB0aGlzLmRlZmF1bHRNdXRlZDtcbiAgICByZXR1cm4gKF9iID0gKF9hID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5pc011dGVkKSA9PSBudWxsID8gdm9pZCAwIDogX2IuY2FsbChfYSk7XG4gIH1cbiAgZ2V0IHBsYXliYWNrUmF0ZSgpIHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIHJldHVybiAoKF9iID0gKF9hID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5nZXRQbGF5YmFja1JhdGUpID09IG51bGwgPyB2b2lkIDAgOiBfYi5jYWxsKF9hKSkgPz8gMTtcbiAgfVxuICBzZXQgcGxheWJhY2tSYXRlKHZhbCkge1xuICAgIGlmICh0aGlzLnBsYXliYWNrUmF0ZSA9PSB2YWwpIHJldHVybjtcbiAgICB0aGlzLmxvYWRDb21wbGV0ZS50aGVuKCgpID0+IHtcbiAgICAgIHZhciBfYTtcbiAgICAgIChfYSA9IHRoaXMuYXBpKSA9PSBudWxsID8gdm9pZCAwIDogX2Euc2V0UGxheWJhY2tSYXRlKHZhbCk7XG4gICAgfSk7XG4gIH1cbiAgZ2V0IHBsYXlzSW5saW5lKCkge1xuICAgIHJldHVybiB0aGlzLmhhc0F0dHJpYnV0ZShcInBsYXlzaW5saW5lXCIpO1xuICB9XG4gIHNldCBwbGF5c0lubGluZSh2YWwpIHtcbiAgICBpZiAodGhpcy5wbGF5c0lubGluZSA9PSB2YWwpIHJldHVybjtcbiAgICB0aGlzLnRvZ2dsZUF0dHJpYnV0ZShcInBsYXlzaW5saW5lXCIsIEJvb2xlYW4odmFsKSk7XG4gIH1cbiAgZ2V0IHBvc3RlcigpIHtcbiAgICByZXR1cm4gdGhpcy5nZXRBdHRyaWJ1dGUoXCJwb3N0ZXJcIik7XG4gIH1cbiAgc2V0IHBvc3Rlcih2YWwpIHtcbiAgICBpZiAodGhpcy5wb3N0ZXIgPT0gdmFsKSByZXR1cm47XG4gICAgdGhpcy5zZXRBdHRyaWJ1dGUoXCJwb3N0ZXJcIiwgYCR7dmFsfWApO1xuICB9XG4gIHNldCB2b2x1bWUodmFsKSB7XG4gICAgaWYgKHRoaXMudm9sdW1lID09IHZhbCkgcmV0dXJuO1xuICAgIHRoaXMubG9hZENvbXBsZXRlLnRoZW4oKCkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgKF9hID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5zZXRWb2x1bWUodmFsICogMTAwKTtcbiAgICB9KTtcbiAgfVxuICBnZXQgdm9sdW1lKCkge1xuICAgIHZhciBfYTtcbiAgICBpZiAoIXRoaXMuaXNMb2FkZWQpIHJldHVybiAxO1xuICAgIHJldHVybiAoKF9hID0gdGhpcy5hcGkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5nZXRWb2x1bWUoKSkgLyAxMDA7XG4gIH1cbiAgLy8gVGhpcyBpcyBhIHBhdHRlcm4gdG8gdXBkYXRlIHByb3BlcnR5IHZhbHVlcyB0aGF0IGFyZSBzZXQgYmVmb3JlXG4gIC8vIHRoZSBjdXN0b20gZWxlbWVudCBpcyB1cGdyYWRlZC5cbiAgLy8gaHR0cHM6Ly93ZWIuZGV2L2N1c3RvbS1lbGVtZW50cy1iZXN0LXByYWN0aWNlcy8jbWFrZS1wcm9wZXJ0aWVzLWxhenlcbiAgI3VwZ3JhZGVQcm9wZXJ0eShwcm9wKSB7XG4gICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbCh0aGlzLCBwcm9wKSkge1xuICAgICAgY29uc3QgdmFsdWUgPSB0aGlzW3Byb3BdO1xuICAgICAgZGVsZXRlIHRoaXNbcHJvcF07XG4gICAgICB0aGlzW3Byb3BdID0gdmFsdWU7XG4gICAgfVxuICB9XG59XG5mdW5jdGlvbiBzZXJpYWxpemVBdHRyaWJ1dGVzKGF0dHJzKSB7XG4gIGxldCBodG1sID0gXCJcIjtcbiAgZm9yIChjb25zdCBrZXkgaW4gYXR0cnMpIHtcbiAgICBjb25zdCB2YWx1ZSA9IGF0dHJzW2tleV07XG4gICAgaWYgKHZhbHVlID09PSBcIlwiKSBodG1sICs9IGAgJHtlc2NhcGVIdG1sKGtleSl9YDtcbiAgICBlbHNlIGh0bWwgKz0gYCAke2VzY2FwZUh0bWwoa2V5KX09XCIke2VzY2FwZUh0bWwoYCR7dmFsdWV9YCl9XCJgO1xuICB9XG4gIHJldHVybiBodG1sO1xufVxuZnVuY3Rpb24gZXNjYXBlSHRtbChzdHIpIHtcbiAgcmV0dXJuIHN0ci5yZXBsYWNlKC8mL2csIFwiJmFtcDtcIikucmVwbGFjZSgvPC9nLCBcIiZsdDtcIikucmVwbGFjZSgvPi9nLCBcIiZndDtcIikucmVwbGFjZSgvXCIvZywgXCImcXVvdDtcIikucmVwbGFjZSgvJy9nLCBcIiZhcG9zO1wiKS5yZXBsYWNlKC9gL2csIFwiJiN4NjA7XCIpO1xufVxuZnVuY3Rpb24gc2VyaWFsaXplKHByb3BzKSB7XG4gIHJldHVybiBTdHJpbmcobmV3IFVSTFNlYXJjaFBhcmFtcyhib29sVG9CaW5hcnkocHJvcHMpKSk7XG59XG5mdW5jdGlvbiBib29sVG9CaW5hcnkocHJvcHMpIHtcbiAgbGV0IHAgPSB7fTtcbiAgZm9yIChsZXQga2V5IGluIHByb3BzKSB7XG4gICAgbGV0IHZhbCA9IHByb3BzW2tleV07XG4gICAgaWYgKHZhbCA9PT0gdHJ1ZSB8fCB2YWwgPT09IFwiXCIpIHBba2V5XSA9IDE7XG4gICAgZWxzZSBpZiAodmFsID09PSBmYWxzZSkgcFtrZXldID0gMDtcbiAgICBlbHNlIGlmICh2YWwgIT0gbnVsbCkgcFtrZXldID0gdmFsO1xuICB9XG4gIHJldHVybiBwO1xufVxuZnVuY3Rpb24gbmFtZWROb2RlTWFwVG9PYmplY3QobmFtZWROb2RlTWFwKSB7XG4gIGxldCBvYmogPSB7fTtcbiAgZm9yIChsZXQgYXR0ciBvZiBuYW1lZE5vZGVNYXApIHtcbiAgICBvYmpbYXR0ci5uYW1lXSA9IGF0dHIudmFsdWU7XG4gIH1cbiAgcmV0dXJuIG9iajtcbn1cbmNvbnN0IGxvYWRTY3JpcHRDYWNoZSA9IHt9O1xuYXN5bmMgZnVuY3Rpb24gbG9hZFNjcmlwdChzcmMsIGdsb2JhbE5hbWUsIHJlYWR5Rm5OYW1lKSB7XG4gIGlmIChsb2FkU2NyaXB0Q2FjaGVbc3JjXSkgcmV0dXJuIGxvYWRTY3JpcHRDYWNoZVtzcmNdO1xuICBpZiAoZ2xvYmFsTmFtZSAmJiBzZWxmW2dsb2JhbE5hbWVdKSB7XG4gICAgYXdhaXQgZGVsYXkoMCk7XG4gICAgcmV0dXJuIHNlbGZbZ2xvYmFsTmFtZV07XG4gIH1cbiAgcmV0dXJuIGxvYWRTY3JpcHRDYWNoZVtzcmNdID0gbmV3IFByb21pc2UoZnVuY3Rpb24ocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgY29uc3Qgc2NyaXB0ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiKTtcbiAgICBzY3JpcHQuc3JjID0gc3JjO1xuICAgIGNvbnN0IHJlYWR5ID0gKCkgPT4gcmVzb2x2ZShzZWxmW2dsb2JhbE5hbWVdKTtcbiAgICBpZiAocmVhZHlGbk5hbWUpIHNlbGZbcmVhZHlGbk5hbWVdID0gcmVhZHk7XG4gICAgc2NyaXB0Lm9ubG9hZCA9ICgpID0+ICFyZWFkeUZuTmFtZSAmJiByZWFkeSgpO1xuICAgIHNjcmlwdC5vbmVycm9yID0gcmVqZWN0O1xuICAgIGRvY3VtZW50LmhlYWQuYXBwZW5kKHNjcmlwdCk7XG4gIH0pO1xufVxuY29uc3QgZGVsYXkgPSAobXMpID0+IG5ldyBQcm9taXNlKChyZXNvbHZlKSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIG1zKSk7XG5mdW5jdGlvbiBwcm9taXNpZnkoZm4pIHtcbiAgcmV0dXJuICguLi5hcmdzKSA9PiBuZXcgUHJvbWlzZSgocmVzb2x2ZSkgPT4ge1xuICAgIGZuKC4uLmFyZ3MsICguLi5yZXMpID0+IHtcbiAgICAgIGlmIChyZXMubGVuZ3RoID4gMSkgcmVzb2x2ZShyZXMpO1xuICAgICAgZWxzZSByZXNvbHZlKHJlc1swXSk7XG4gICAgfSk7XG4gIH0pO1xufVxuZnVuY3Rpb24gY3JlYXRlUGxheVByb21pc2UocGxheWVyKSB7XG4gIHJldHVybiBwcm9taXNpZnkoKGV2ZW50LCBjYikgPT4ge1xuICAgIGxldCBmbjtcbiAgICBwbGF5ZXIuYWRkRXZlbnRMaXN0ZW5lcihcbiAgICAgIGV2ZW50LFxuICAgICAgZm4gPSAoKSA9PiB7XG4gICAgICAgIHBsYXllci5yZW1vdmVFdmVudExpc3RlbmVyKGV2ZW50LCBmbik7XG4gICAgICAgIGNiKCk7XG4gICAgICB9XG4gICAgKTtcbiAgfSkoXCJwbGF5aW5nXCIpO1xufVxuY2xhc3MgUHVibGljUHJvbWlzZSBleHRlbmRzIFByb21pc2Uge1xuICBjb25zdHJ1Y3RvcihleGVjdXRvciA9ICgpID0+IHtcbiAgfSkge1xuICAgIGxldCByZXMsIHJlajtcbiAgICBzdXBlcigocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICBleGVjdXRvcihyZXNvbHZlLCByZWplY3QpO1xuICAgICAgcmVzID0gcmVzb2x2ZTtcbiAgICAgIHJlaiA9IHJlamVjdDtcbiAgICB9KTtcbiAgICB0aGlzLnJlc29sdmUgPSByZXM7XG4gICAgdGhpcy5yZWplY3QgPSByZWo7XG4gIH1cbn1cbmZ1bmN0aW9uIGNyZWF0ZVRpbWVSYW5nZXMoc3RhcnQsIGVuZCkge1xuICBpZiAoQXJyYXkuaXNBcnJheShzdGFydCkpIHtcbiAgICByZXR1cm4gY3JlYXRlVGltZVJhbmdlc09iaihzdGFydCk7XG4gIH0gZWxzZSBpZiAoc3RhcnQgPT0gbnVsbCB8fCBlbmQgPT0gbnVsbCB8fCBzdGFydCA9PT0gMCAmJiBlbmQgPT09IDApIHtcbiAgICByZXR1cm4gY3JlYXRlVGltZVJhbmdlc09iaihbWzAsIDBdXSk7XG4gIH1cbiAgcmV0dXJuIGNyZWF0ZVRpbWVSYW5nZXNPYmooW1tzdGFydCwgZW5kXV0pO1xufVxuZnVuY3Rpb24gY3JlYXRlVGltZVJhbmdlc09iaihyYW5nZXMpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMocmFuZ2VzLCB7XG4gICAgc3RhcnQ6IHtcbiAgICAgIHZhbHVlOiAoaSkgPT4gcmFuZ2VzW2ldWzBdXG4gICAgfSxcbiAgICBlbmQ6IHtcbiAgICAgIHZhbHVlOiAoaSkgPT4gcmFuZ2VzW2ldWzFdXG4gICAgfVxuICB9KTtcbiAgcmV0dXJuIHJhbmdlcztcbn1cbmlmIChnbG9iYWxUaGlzLmN1c3RvbUVsZW1lbnRzICYmICFnbG9iYWxUaGlzLmN1c3RvbUVsZW1lbnRzLmdldChcInlvdXR1YmUtdmlkZW9cIikpIHtcbiAgZ2xvYmFsVGhpcy5jdXN0b21FbGVtZW50cy5kZWZpbmUoXCJ5b3V0dWJlLXZpZGVvXCIsIFlvdXR1YmVWaWRlb0VsZW1lbnQpO1xufVxudmFyIHlvdXR1YmVfdmlkZW9fZWxlbWVudF9kZWZhdWx0ID0gWW91dHViZVZpZGVvRWxlbWVudDtcbmV4cG9ydCB7XG4gIHlvdXR1YmVfdmlkZW9fZWxlbWVudF9kZWZhdWx0IGFzIGRlZmF1bHRcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/youtube-video-element/dist/youtube-video-element.js\n"));

/***/ })

}]);