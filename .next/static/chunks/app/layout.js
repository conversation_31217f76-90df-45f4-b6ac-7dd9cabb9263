/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Flive2d-config-context.tsx%22%2C%22ids%22%3A%5B%22Live2DConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Fsettings-context.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Flive2d-config-context.tsx%22%2C%22ids%22%3A%5B%22Live2DConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Fsettings-context.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/live2d-config-context.tsx */ \"(app-pages-browser)/./src/context/live2d-config-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/context/settings-context.tsx */ \"(app-pages-browser)/./src/context/settings-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/index.css */ \"(app-pages-browser)/./src/index.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZFdGhhbkxlZSUyRkRlc2t0b3AlMkZBZHZYJTJGT3Blbi1MTE0tVlR1YmVyLVdlYiUyRnNyYyUyRmNvbnRleHQlMkZsaXZlMmQtY29uZmlnLWNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGl2ZTJEQ29uZmlnUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZFdGhhbkxlZSUyRkRlc2t0b3AlMkZBZHZYJTJGT3Blbi1MTE0tVlR1YmVyLVdlYiUyRnNyYyUyRmNvbnRleHQlMkZzZXR0aW5ncy1jb250ZXh0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlNldHRpbmdzUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZFdGhhbkxlZSUyRkRlc2t0b3AlMkZBZHZYJTJGT3Blbi1MTE0tVlR1YmVyLVdlYiUyRnNyYyUyRmluZGV4LmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUFxSztBQUNySztBQUNBLDhMQUE0SjtBQUM1SjtBQUNBLHdKQUFtRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTGl2ZTJEQ29uZmlnUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvRXRoYW5MZWUvRGVza3RvcC9BZHZYL09wZW4tTExNLVZUdWJlci1XZWIvc3JjL2NvbnRleHQvbGl2ZTJkLWNvbmZpZy1jb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2V0dGluZ3NQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9FdGhhbkxlZS9EZXNrdG9wL0FkdlgvT3Blbi1MTE0tVlR1YmVyLVdlYi9zcmMvY29udGV4dC9zZXR0aW5ncy1jb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL0V0aGFuTGVlL0Rlc2t0b3AvQWR2WC9PcGVuLUxMTS1WVHViZXItV2ViL3NyYy9pbmRleC5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Flive2d-config-context.tsx%22%2C%22ids%22%3A%5B%22Live2DConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Fsettings-context.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9FdGhhbkxlZS9EZXNrdG9wL0FkdlgvT3Blbi1MTE0tVlR1YmVyLVdlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3JlYWN0L2pzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/context/live2d-config-context.tsx":
/*!***********************************************!*\
  !*** ./src/context/live2d-config-context.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Live2DConfigContext: () => (/* binding */ Live2DConfigContext),\n/* harmony export */   Live2DConfigProvider: () => (/* binding */ Live2DConfigProvider),\n/* harmony export */   useLive2DConfig: () => (/* binding */ useLive2DConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Live2DConfigContext,Live2DConfigProvider,useLive2DConfig auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n/**\n * Default model configuration with specified URL\n * Optimized for right-side companion area display\n */ const DEFAULT_MODEL_INFO = {\n    name: \"Elaina\",\n    description: \"Default Live2D model - AI Companion\",\n    url: \"http://127.0.0.1:12393/live2d-models/elaina/LSS.model3.json\",\n    kScale: 0.8,\n    initialXshift: 20,\n    initialYshift: -50,\n    idleMotionGroupName: \"Idle\",\n    defaultEmotion: 0,\n    emotionMap: {},\n    pointerInteractive: true,\n    scrollToResize: true,\n    initialScale: 0.8\n};\n/**\n * Default values and constants\n */ const DEFAULT_CONFIG = {\n    modelInfo: DEFAULT_MODEL_INFO,\n    isLoading: false\n};\n/**\n * Create the Live2D configuration context\n */ const Live2DConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\n/**\n * Live2D Configuration Provider Component\n * @param {Object} props - Provider props\n * @param {React.ReactNode} props.children - Child components\n */ function Live2DConfigProvider(param) {\n    let { children } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_CONFIG.isLoading);\n    const [modelInfo, setModelInfoState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_CONFIG.modelInfo);\n    const setModelInfo = (info)=>{\n        if (!(info === null || info === void 0 ? void 0 : info.url)) {\n            setModelInfoState(undefined); // Clear state if no URL\n            return;\n        }\n        // Always use the scale defined in the incoming info object (from config)\n        const finalScale = Number(info.kScale || 0.5) * 2; // Use default scale if kScale is missing\n        console.log(\"Setting model info with default scale:\", finalScale);\n        var _modelInfo_pointerInteractive, _modelInfo_scrollToResize;\n        setModelInfoState({\n            ...info,\n            kScale: finalScale,\n            // Preserve other potentially user-modified settings if needed, otherwise use defaults from info\n            pointerInteractive: \"pointerInteractive\" in info ? info.pointerInteractive : (_modelInfo_pointerInteractive = modelInfo === null || modelInfo === void 0 ? void 0 : modelInfo.pointerInteractive) !== null && _modelInfo_pointerInteractive !== void 0 ? _modelInfo_pointerInteractive : true,\n            scrollToResize: \"scrollToResize\" in info ? info.scrollToResize : (_modelInfo_scrollToResize = modelInfo === null || modelInfo === void 0 ? void 0 : modelInfo.scrollToResize) !== null && _modelInfo_scrollToResize !== void 0 ? _modelInfo_scrollToResize : true\n        });\n    };\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Live2DConfigProvider.useMemo[contextValue]\": ()=>({\n                modelInfo,\n                setModelInfo,\n                isLoading,\n                setIsLoading\n            })\n    }[\"Live2DConfigProvider.useMemo[contextValue]\"], [\n        modelInfo,\n        isLoading,\n        setIsLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Live2DConfigContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/AdvX/Open-LLM-VTuber-Web/src/context/live2d-config-context.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, this);\n}\n_s(Live2DConfigProvider, \"PM4+UP/HctcCqEWgaxOh+ZxESHw=\");\n_c = Live2DConfigProvider;\n/**\n * Custom hook to use the Live2D configuration context\n * @throws {Error} If used outside of Live2DConfigProvider\n */ function useLive2DConfig() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(Live2DConfigContext);\n    if (!context) {\n        throw new Error('useLive2DConfig must be used within a Live2DConfigProvider');\n    }\n    return context;\n}\n_s1(useLive2DConfig, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"Live2DConfigProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/live2d-config-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/context/settings-context.tsx":
/*!******************************************!*\
  !*** ./src/context/settings-context.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst defaultGeneralSettings = {\n    baseUrl: 'http://127.0.0.1:8000',\n    websocketBaseUrl: 'ws://127.0.0.1:8000/ws'\n};\nconst defaultBackgroundSettings = {\n    type: 'image',\n    imageUrl: '',\n    imageFile: null,\n    imageScale: 1,\n    imagePositionX: 50,\n    imagePositionY: 50,\n    imageMode: 'cover'\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useSettings = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\n_s(useSettings, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst SettingsProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultGeneralSettings);\n    const [backgroundSettings, setBackgroundSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultBackgroundSettings);\n    // Load settings from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            loadSettings();\n        }\n    }[\"SettingsProvider.useEffect\"], []);\n    const updateGeneralSettings = (settings)=>{\n        setGeneralSettings((prev)=>({\n                ...prev,\n                ...settings\n            }));\n    };\n    const updateBackgroundSettings = (settings)=>{\n        setBackgroundSettings(settings);\n    };\n    const saveSettings = ()=>{\n        try {\n            // Save general settings\n            localStorage.setItem('generalSettings', JSON.stringify(generalSettings));\n            // Save background settings (excluding imageFile as it can't be serialized)\n            const backgroundSettingsToSave = {\n                ...backgroundSettings,\n                imageFile: null\n            };\n            localStorage.setItem('backgroundSettings', JSON.stringify(backgroundSettingsToSave));\n            console.log('Settings saved successfully');\n        } catch (error) {\n            console.error('Failed to save settings:', error);\n        }\n    };\n    const loadSettings = ()=>{\n        try {\n            // Load general settings\n            const savedGeneralSettings = localStorage.getItem('generalSettings');\n            if (savedGeneralSettings) {\n                const parsed = JSON.parse(savedGeneralSettings);\n                setGeneralSettings({\n                    ...defaultGeneralSettings,\n                    ...parsed\n                });\n            }\n            // Load background settings\n            const savedBackgroundSettings = localStorage.getItem('backgroundSettings');\n            if (savedBackgroundSettings) {\n                const parsed = JSON.parse(savedBackgroundSettings);\n                setBackgroundSettings({\n                    ...defaultBackgroundSettings,\n                    ...parsed,\n                    imageFile: null // Always reset imageFile as it can't be serialized\n                });\n            }\n            console.log('Settings loaded successfully');\n        } catch (error) {\n            console.error('Failed to load settings:', error);\n        }\n    };\n    const value = {\n        generalSettings,\n        backgroundSettings,\n        updateGeneralSettings,\n        updateBackgroundSettings,\n        saveSettings,\n        loadSettings\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/AdvX/Open-LLM-VTuber-Web/src/context/settings-context.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SettingsProvider, \"+87MlnEu+ZHqCPCnFJGa+hoawsE=\");\n_c = SettingsProvider;\nvar _c;\n$RefreshReg$(_c, \"SettingsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/settings-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e94355b54f61\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvRXRoYW5MZWUvRGVza3RvcC9BZHZYL09wZW4tTExNLVZUdWJlci1XZWIvc3JjL2luZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU5NDM1NWI1NGY2MVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/index.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Flive2d-config-context.tsx%22%2C%22ids%22%3A%5B%22Live2DConfigProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Fcontext%2Fsettings-context.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2FEthanLee%2FDesktop%2FAdvX%2FOpen-LLM-VTuber-Web%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);