"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerTiktok"],{

/***/ "(app-pages-browser)/./node_modules/tiktok-video-element/dist/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/tiktok-video-element/dist/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _tiktok_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tiktok-video-element.js */ \"(app-pages-browser)/./node_modules/tiktok-video-element/dist/tiktok-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"tiktok-video\",\n    elementClass: _tiktok_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tiktok-video-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tiktok-video-element/dist/tiktok-video-element.js":
/*!************************************************************************!*\
  !*** ./node_modules/tiktok-video-element/dist/tiktok-video-element.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tiktok_video_element_default)\n/* harmony export */ });\nconst EMBED_BASE = \"https://www.tiktok.com/player/v1\";\nconst MATCH_SRC = /tiktok\\.com\\/(?:@[^/]+\\/video\\/)?(\\d+)(?:\\/([\\w-]+))?/;\nconst PlayerState = { INIT: -1, ENDED: 0, PLAYING: 1, PAUSED: 2, BUFFERING: 3 };\nconst EventMap = {\n  [PlayerState.INIT]: \"emptied\",\n  [PlayerState.PAUSED]: \"pause\",\n  [PlayerState.ENDED]: \"ended\",\n  [PlayerState.PLAYING]: \"play\",\n  [PlayerState.BUFFERING]: \"waiting\"\n};\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; autoplay; fullscreen; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display:inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position:absolute;\n        top:0;\n        left:0;\n        width:100%;\n        height:100%;\n        border:0;\n      }\n    </style>\n    <iframe ${serializeAttributes(iframeAttrs)} title=\"TikTok video\"></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props = {}) {\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const srcId = matches && matches[1];\n  const params = {\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    muted: attrs.muted,\n    loop: attrs.loop,\n    rel: 0,\n    ...props.config\n  };\n  return `${EMBED_BASE}/${srcId}?${serialize(params)}`;\n}\nclass TikTokVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static get observedAttributes() {\n    return [\"src\", \"controls\", \"loop\", \"autoplay\", \"muted\"];\n  }\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #muted = false;\n  #currentTime = 0;\n  #paused = true;\n  #config = null;\n  #volume = 100;\n  #duration = 0;\n  #iframe;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow(TikTokVideoElement.shadowRootOptions);\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#currentTime = 0;\n    this.#muted = false;\n    this.#paused = true;\n    if (!this.src) {\n      this.shadowRoot.innerHTML = \"\";\n      globalThis.removeEventListener(\"message\", this.#onMessage);\n      return;\n    }\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    const attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.#iframe = iframe;\n    globalThis.addEventListener(\"message\", this.#onMessage);\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"muted\": {\n        await this.loadComplete;\n        this.muted = newValue != null;\n        break;\n      }\n      case \"autoplay\":\n      case \"controls\":\n      case \"loop\":\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  #onMessage = (event) => {\n    const msg = event.data;\n    if (!(msg == null ? void 0 : msg[\"x-tiktok-player\"])) return;\n    switch (msg.type) {\n      case \"onPlayerReady\":\n        this.loadComplete.resolve();\n        break;\n      case \"onStateChange\": {\n        this.#paused = [PlayerState.INIT, PlayerState.PAUSED, PlayerState.ENDED].includes(msg.value);\n        const eventType = EventMap[msg.value];\n        if (eventType) this.dispatchEvent(new Event(eventType));\n        break;\n      }\n      case \"onCurrentTime\":\n        this.#currentTime = msg.value.currentTime;\n        this.#duration = msg.value.duration;\n        this.dispatchEvent(new Event(\"durationchange\"));\n        this.dispatchEvent(new Event(\"timeupdate\"));\n        break;\n      case \"onVolumeChange\":\n        this.#volume = msg.value;\n        this.dispatchEvent(new Event(\"volumechange\"));\n        break;\n      case \"onMute\":\n        this.#muted = msg.value ? true : false;\n        this.#volume = msg.value ? 0 : this.#volume;\n        this.dispatchEvent(new Event(\"volumechange\"));\n        break;\n      case \"onError\":\n        this.dispatchEvent(new Event(\"error\"));\n        break;\n      default:\n        console.warn(\"Unhandled TikTok player message:\", msg);\n        break;\n    }\n  };\n  #post(type, value) {\n    var _a;\n    if (!((_a = this.#iframe) == null ? void 0 : _a.contentWindow)) return;\n    const message = { \"x-tiktok-player\": true, type, ...value !== void 0 ? { value } : {} };\n    this.#iframe.contentWindow.postMessage(message, \"*\");\n  }\n  async play() {\n    await this.loadComplete;\n    this.#post(\"play\");\n  }\n  async pause() {\n    await this.loadComplete;\n    this.#post(\"pause\");\n  }\n  async #seekTo(sec) {\n    await this.loadComplete;\n    this.#post(\"seekTo\", Number(sec));\n  }\n  async #mute() {\n    await this.loadComplete;\n    this.#post(\"mute\");\n  }\n  async #unMute() {\n    await this.loadComplete;\n    this.#post(\"unMute\");\n  }\n  get volume() {\n    return this.#volume / 100;\n  }\n  set volume(_val) {\n    console.warn(\"Volume control is not supported for TikTok videos.\");\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    this.#seekTo(val);\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    this.#muted = val;\n    val ? this.#mute() : this.#unMute();\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    this.toggleAttribute(\"muted\", !!val);\n  }\n  get paused() {\n    return this.#paused;\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    this.setAttribute(\"src\", val ?? \"\");\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"tiktok-video\")) {\n  globalThis.customElements.define(\"tiktok-video\", TikTokVideoElement);\n}\nvar tiktok_video_element_default = TikTokVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tiktok-video-element/dist/tiktok-video-element.js\n"));

/***/ })

}]);