"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerSpotify"],{

/***/ "(app-pages-browser)/./node_modules/spotify-audio-element/dist/react.js":
/*!**********************************************************!*\
  !*** ./node_modules/spotify-audio-element/dist/react.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _spotify_audio_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./spotify-audio-element.js */ \"(app-pages-browser)/./node_modules/spotify-audio-element/dist/spotify-audio-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"spotify-audio\",\n    elementClass: _spotify_audio_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/spotify-audio-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/spotify-audio-element/dist/spotify-audio-element.js":
/*!**************************************************************************!*\
  !*** ./node_modules/spotify-audio-element/dist/spotify-audio-element.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ spotify_audio_element_default)\n/* harmony export */ });\nconst EMBED_BASE = \"https://open.spotify.com\";\nconst MATCH_SRC = /open\\.spotify\\.com\\/(\\w+)\\/(\\w+)/i;\nconst API_URL = \"https://open.spotify.com/embed-podcast/iframe-api/v1\";\nconst API_GLOBAL = \"SpotifyIframeApi\";\nconst API_GLOBAL_READY = \"onSpotifyIframeApiReady\";\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    scrolling: \"no\",\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 160px;\n        min-height: 80px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        overflow: hidden;\n      }\n      :host(:not([controls])) {\n        display: none !important;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  var _a, _b, _c;\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const type = matches && matches[1];\n  const metaId = matches && matches[2];\n  const params = {\n    t: (_a = props.config) == null ? void 0 : _a.startAt,\n    theme: ((_b = props.config) == null ? void 0 : _b.theme) === \"dark\" ? \"0\" : null\n  };\n  const videoPath = ((_c = props.config) == null ? void 0 : _c.preferVideo) ? \"/video\" : \"\";\n  return `${EMBED_BASE}/embed/${type}/${metaId}${videoPath}?${serialize(params)}`;\n}\nclass SpotifyAudioElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"controls\",\n    \"loop\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #isInit;\n  #isWaiting = false;\n  #closeToEnded = false;\n  #paused = true;\n  #currentTime = 0;\n  #duration = NaN;\n  #seeking = false;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  async load() {\n    var _a, _b, _c;\n    if (this.#loadRequested) return;\n    if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#isWaiting = false;\n    this.#closeToEnded = false;\n    this.#currentTime = 0;\n    this.#duration = NaN;\n    this.#seeking = false;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    const options = {\n      t: (_a = this.config) == null ? void 0 : _a.startAt,\n      theme: ((_b = this.config) == null ? void 0 : _b.theme) === \"dark\" ? \"0\" : null,\n      preferVideo: (_c = this.config) == null ? void 0 : _c.preferVideo\n    };\n    if (this.#isInit) {\n      this.api = oldApi;\n      this.api.iframeElement.src = serializeIframeUrl(namedNodeMapToObject(this.attributes), this);\n    } else {\n      this.#isInit = true;\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: \"open\" });\n        this.shadowRoot.innerHTML = getTemplateHTML(namedNodeMapToObject(this.attributes), this);\n      }\n      let iframe = this.shadowRoot.querySelector(\"iframe\");\n      const Spotify = await loadScript(API_URL, API_GLOBAL, API_GLOBAL_READY);\n      this.api = await new Promise((resolve) => Spotify.createController(iframe, options, resolve));\n      this.api.iframeElement = iframe;\n      this.api.addListener(\"ready\", () => {\n        this.dispatchEvent(new Event(\"loadedmetadata\"));\n        this.dispatchEvent(new Event(\"durationchange\"));\n        this.dispatchEvent(new Event(\"volumechange\"));\n      });\n      this.api.addListener(\"playback_update\", (event) => {\n        if (this.#closeToEnded && this.#paused && (event.data.isBuffering || !event.data.isPaused)) {\n          this.#closeToEnded = false;\n          this.currentTime = 1;\n          return;\n        }\n        if (event.data.duration / 1e3 !== this.#duration) {\n          this.#closeToEnded = false;\n          this.#duration = event.data.duration / 1e3;\n          this.dispatchEvent(new Event(\"durationchange\"));\n        }\n        if (event.data.position / 1e3 !== this.#currentTime) {\n          this.#seeking = false;\n          this.#closeToEnded = false;\n          this.#currentTime = event.data.position / 1e3;\n          this.dispatchEvent(new Event(\"timeupdate\"));\n        }\n        if (!this.#isWaiting && !this.#paused && event.data.isPaused) {\n          this.#paused = true;\n          this.dispatchEvent(new Event(\"pause\"));\n          return;\n        }\n        if (this.#paused && (event.data.isBuffering || !event.data.isPaused)) {\n          this.#paused = false;\n          this.dispatchEvent(new Event(\"play\"));\n          this.#isWaiting = event.data.isBuffering;\n          if (this.#isWaiting) {\n            this.dispatchEvent(new Event(\"waiting\"));\n          } else {\n            this.dispatchEvent(new Event(\"playing\"));\n          }\n          return;\n        }\n        if (this.#isWaiting && !event.data.isPaused) {\n          this.#isWaiting = false;\n          this.dispatchEvent(new Event(\"playing\"));\n          return;\n        }\n        if (!this.paused && !this.seeking && !this.#closeToEnded && Math.ceil(this.currentTime) >= this.duration) {\n          this.#closeToEnded = true;\n          if (this.loop) {\n            this.currentTime = 1;\n            return;\n          }\n          if (!this.continuous) {\n            this.pause();\n            this.dispatchEvent(new Event(\"ended\"));\n          }\n          return;\n        }\n      });\n    }\n    this.loadComplete.resolve();\n    await this.loadComplete;\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#paused = false;\n    this.#isWaiting = true;\n    this.dispatchEvent(new Event(\"play\"));\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.resume();\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pause();\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  get paused() {\n    return this.#paused ?? true;\n  }\n  get muted() {\n    return false;\n  }\n  set muted(val) {\n  }\n  get volume() {\n    return 1;\n  }\n  set volume(val) {\n  }\n  get ended() {\n    return Math.ceil(this.currentTime) >= this.duration;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#seeking = true;\n    let oldTime = this.#currentTime;\n    this.#currentTime = val;\n    this.dispatchEvent(new Event(\"timeupdate\"));\n    this.#currentTime = oldTime;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.seek(val);\n    });\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    this.setAttribute(\"src\", `${val}`);\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${key}`;\n    else html += ` ${key}=\"${value}\"`;\n  }\n  return html;\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName, readyFnName) {\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (globalName && self[globalName]) {\n    return Promise.resolve(self[globalName]);\n  }\n  return loadScriptCache[src] = new Promise(function(resolve, reject) {\n    const script = document.createElement(\"script\");\n    script.src = src;\n    const ready = (api) => resolve(api);\n    if (readyFnName) self[readyFnName] = ready;\n    script.onload = () => !readyFnName && ready();\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"spotify-audio\")) {\n  globalThis.customElements.define(\"spotify-audio\", SpotifyAudioElement);\n}\nvar spotify_audio_element_default = SpotifyAudioElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/spotify-audio-element/dist/spotify-audio-element.js\n"));

/***/ })

}]);