"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerVimeo"],{

/***/ "(app-pages-browser)/./node_modules/@vimeo/player/dist/player.es.js":
/*!******************************************************!*\
  !*** ./node_modules/@vimeo/player/dist/player.es.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*! @vimeo/player v2.29.0 | (c) 2025 Vimeo | MIT License | https://github.com/vimeo/player.js */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _regeneratorRuntime() {\n  _regeneratorRuntime = function () {\n    return exports;\n  };\n  var exports = {},\n    Op = Object.prototype,\n    hasOwn = Op.hasOwnProperty,\n    defineProperty = Object.defineProperty || function (obj, key, desc) {\n      obj[key] = desc.value;\n    },\n    $Symbol = \"function\" == typeof Symbol ? Symbol : {},\n    iteratorSymbol = $Symbol.iterator || \"@@iterator\",\n    asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\",\n    toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n  function define(obj, key, value) {\n    return Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), obj[key];\n  }\n  try {\n    define({}, \"\");\n  } catch (err) {\n    define = function (obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator,\n      generator = Object.create(protoGenerator.prototype),\n      context = new Context(tryLocsList || []);\n    return defineProperty(generator, \"_invoke\", {\n      value: makeInvokeMethod(innerFn, self, context)\n    }), generator;\n  }\n  function tryCatch(fn, obj, arg) {\n    try {\n      return {\n        type: \"normal\",\n        arg: fn.call(obj, arg)\n      };\n    } catch (err) {\n      return {\n        type: \"throw\",\n        arg: err\n      };\n    }\n  }\n  exports.wrap = wrap;\n  var ContinueSentinel = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n  var getProto = Object.getPrototypeOf,\n    NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype);\n  var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype);\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function (method) {\n      define(prototype, method, function (arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (\"throw\" !== record.type) {\n        var result = record.arg,\n          value = result.value;\n        return value && \"object\" == typeof value && hasOwn.call(value, \"__await\") ? PromiseImpl.resolve(value.__await).then(function (value) {\n          invoke(\"next\", value, resolve, reject);\n        }, function (err) {\n          invoke(\"throw\", err, resolve, reject);\n        }) : PromiseImpl.resolve(value).then(function (unwrapped) {\n          result.value = unwrapped, resolve(result);\n        }, function (error) {\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n      reject(record.arg);\n    }\n    var previousPromise;\n    defineProperty(this, \"_invoke\", {\n      value: function (method, arg) {\n        function callInvokeWithMethodAndArg() {\n          return new PromiseImpl(function (resolve, reject) {\n            invoke(method, arg, resolve, reject);\n          });\n        }\n        return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = \"suspendedStart\";\n    return function (method, arg) {\n      if (\"executing\" === state) throw new Error(\"Generator is already running\");\n      if (\"completed\" === state) {\n        if (\"throw\" === method) throw arg;\n        return doneResult();\n      }\n      for (context.method = method, context.arg = arg;;) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n        if (\"next\" === context.method) context.sent = context._sent = context.arg;else if (\"throw\" === context.method) {\n          if (\"suspendedStart\" === state) throw state = \"completed\", context.arg;\n          context.dispatchException(context.arg);\n        } else \"return\" === context.method && context.abrupt(\"return\", context.arg);\n        state = \"executing\";\n        var record = tryCatch(innerFn, self, context);\n        if (\"normal\" === record.type) {\n          if (state = context.done ? \"completed\" : \"suspendedYield\", record.arg === ContinueSentinel) continue;\n          return {\n            value: record.arg,\n            done: context.done\n          };\n        }\n        \"throw\" === record.type && (state = \"completed\", context.method = \"throw\", context.arg = record.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method,\n      method = delegate.iterator[methodName];\n    if (undefined === method) return context.delegate = null, \"throw\" === methodName && delegate.iterator.return && (context.method = \"return\", context.arg = undefined, maybeInvokeDelegate(delegate, context), \"throw\" === context.method) || \"return\" !== methodName && (context.method = \"throw\", context.arg = new TypeError(\"The iterator does not provide a '\" + methodName + \"' method\")), ContinueSentinel;\n    var record = tryCatch(method, delegate.iterator, context.arg);\n    if (\"throw\" === record.type) return context.method = \"throw\", context.arg = record.arg, context.delegate = null, ContinueSentinel;\n    var info = record.arg;\n    return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, \"return\" !== context.method && (context.method = \"next\", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = \"throw\", context.arg = new TypeError(\"iterator result is not an object\"), context.delegate = null, ContinueSentinel);\n  }\n  function pushTryEntry(locs) {\n    var entry = {\n      tryLoc: locs[0]\n    };\n    1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry);\n  }\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\", delete record.arg, entry.completion = record;\n  }\n  function Context(tryLocsList) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) return iteratorMethod.call(iterable);\n      if (\"function\" == typeof iterable.next) return iterable;\n      if (!isNaN(iterable.length)) {\n        var i = -1,\n          next = function next() {\n            for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next;\n            return next.value = undefined, next.done = !0, next;\n          };\n        return next.next = next;\n      }\n    }\n    return {\n      next: doneResult\n    };\n  }\n  function doneResult() {\n    return {\n      value: undefined,\n      done: !0\n    };\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), defineProperty(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, \"GeneratorFunction\"), exports.isGeneratorFunction = function (genFun) {\n    var ctor = \"function\" == typeof genFun && genFun.constructor;\n    return !!ctor && (ctor === GeneratorFunction || \"GeneratorFunction\" === (ctor.displayName || ctor.name));\n  }, exports.mark = function (genFun) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, \"GeneratorFunction\")), genFun.prototype = Object.create(Gp), genFun;\n  }, exports.awrap = function (arg) {\n    return {\n      __await: arg\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    void 0 === PromiseImpl && (PromiseImpl = Promise);\n    var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl);\n    return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) {\n      return result.done ? result.value : iter.next();\n    });\n  }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, \"Generator\"), define(Gp, iteratorSymbol, function () {\n    return this;\n  }), define(Gp, \"toString\", function () {\n    return \"[object Generator]\";\n  }), exports.keys = function (val) {\n    var object = Object(val),\n      keys = [];\n    for (var key in object) keys.push(key);\n    return keys.reverse(), function next() {\n      for (; keys.length;) {\n        var key = keys.pop();\n        if (key in object) return next.value = key, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, exports.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function (skipTempReset) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) \"t\" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined);\n    },\n    stop: function () {\n      this.done = !0;\n      var rootRecord = this.tryEntries[0].completion;\n      if (\"throw\" === rootRecord.type) throw rootRecord.arg;\n      return this.rval;\n    },\n    dispatchException: function (exception) {\n      if (this.done) throw exception;\n      var context = this;\n      function handle(loc, caught) {\n        return record.type = \"throw\", record.arg = exception, context.next = loc, caught && (context.method = \"next\", context.arg = undefined), !!caught;\n      }\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i],\n          record = entry.completion;\n        if (\"root\" === entry.tryLoc) return handle(\"end\");\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\"),\n            hasFinally = hasOwn.call(entry, \"finallyLoc\");\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0);\n          } else {\n            if (!hasFinally) throw new Error(\"try statement without catch or finally\");\n            if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function (type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev && hasOwn.call(entry, \"finallyLoc\") && this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n      finallyEntry && (\"break\" === type || \"continue\" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null);\n      var record = finallyEntry ? finallyEntry.completion : {};\n      return record.type = type, record.arg = arg, finallyEntry ? (this.method = \"next\", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record);\n    },\n    complete: function (record, afterLoc) {\n      if (\"throw\" === record.type) throw record.arg;\n      return \"break\" === record.type || \"continue\" === record.type ? this.next = record.arg : \"return\" === record.type ? (this.rval = this.arg = record.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel;\n    },\n    finish: function (finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel;\n      }\n    },\n    catch: function (tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (\"throw\" === record.type) {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n      throw new Error(\"illegal catch attempt\");\n    },\n    delegateYield: function (iterable, resultName, nextLoc) {\n      return this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      }, \"next\" === this.method && (this.arg = undefined), ContinueSentinel;\n    }\n  }, exports;\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n      args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n      _next(undefined);\n    });\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _construct(Parent, args, Class) {\n  if (_isNativeReflectConstruct()) {\n    _construct = Reflect.construct.bind();\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) _setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n  return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !_isNativeFunction(Class)) return Class;\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n      _cache.set(Class, Wrapper);\n    }\n    function Wrapper() {\n      return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n    }\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return _setPrototypeOf(Wrapper, Class);\n  };\n  return _wrapNativeSuper(Class);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\n/**\n * @module lib/functions\n */\n\n/**\n * Check to see this is a node environment.\n * @type {Boolean}\n */\n/* global global */\nvar isNode = typeof __webpack_require__.g !== 'undefined' && {}.toString.call(__webpack_require__.g) === '[object global]';\n\n/**\n * Get the name of the method for a given getter or setter.\n *\n * @param {string} prop The name of the property.\n * @param {string} type Either “get” or “set”.\n * @return {string}\n */\nfunction getMethodName(prop, type) {\n  if (prop.indexOf(type.toLowerCase()) === 0) {\n    return prop;\n  }\n  return \"\".concat(type.toLowerCase()).concat(prop.substr(0, 1).toUpperCase()).concat(prop.substr(1));\n}\n\n/**\n * Check to see if the object is a DOM Element.\n *\n * @param {*} element The object to check.\n * @return {boolean}\n */\nfunction isDomElement(element) {\n  return Boolean(element && element.nodeType === 1 && 'nodeName' in element && element.ownerDocument && element.ownerDocument.defaultView);\n}\n\n/**\n * Check to see whether the value is a number.\n *\n * @see http://dl.dropboxusercontent.com/u/35146/js/tests/isNumber.html\n * @param {*} value The value to check.\n * @param {boolean} integer Check if the value is an integer.\n * @return {boolean}\n */\nfunction isInteger(value) {\n  // eslint-disable-next-line eqeqeq\n  return !isNaN(parseFloat(value)) && isFinite(value) && Math.floor(value) == value;\n}\n\n/**\n * Check to see if the URL is a Vimeo url.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoUrl(url) {\n  return /^(https?:)?\\/\\/((((player|www)\\.)?vimeo\\.com)|((player\\.)?[a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))(?=$|\\/)/.test(url);\n}\n\n/**\n * Check to see if the URL is for a Vimeo embed.\n *\n * @param {string} url The url string.\n * @return {boolean}\n */\nfunction isVimeoEmbed(url) {\n  var expr = /^https:\\/\\/player\\.((vimeo\\.com)|([a-zA-Z0-9-]+\\.(videoji\\.(hk|cn)|vimeo\\.work)))\\/video\\/\\d+/;\n  return expr.test(url);\n}\nfunction getOembedDomain(url) {\n  var match = (url || '').match(/^(?:https?:)?(?:\\/\\/)?([^/?]+)/);\n  var domain = (match && match[1] || '').replace('player.', '');\n  var customDomains = ['.videoji.hk', '.vimeo.work', '.videoji.cn'];\n  for (var _i = 0, _customDomains = customDomains; _i < _customDomains.length; _i++) {\n    var customDomain = _customDomains[_i];\n    if (domain.endsWith(customDomain)) {\n      return domain;\n    }\n  }\n  return 'vimeo.com';\n}\n\n/**\n * Get the Vimeo URL from an element.\n * The element must have either a data-vimeo-id or data-vimeo-url attribute.\n *\n * @param {object} oEmbedParameters The oEmbed parameters.\n * @return {string}\n */\nfunction getVimeoUrl() {\n  var oEmbedParameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var id = oEmbedParameters.id;\n  var url = oEmbedParameters.url;\n  var idOrUrl = id || url;\n  if (!idOrUrl) {\n    throw new Error('An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.');\n  }\n  if (isInteger(idOrUrl)) {\n    return \"https://vimeo.com/\".concat(idOrUrl);\n  }\n  if (isVimeoUrl(idOrUrl)) {\n    return idOrUrl.replace('http:', 'https:');\n  }\n  if (id) {\n    throw new TypeError(\"\\u201C\".concat(id, \"\\u201D is not a valid video id.\"));\n  }\n  throw new TypeError(\"\\u201C\".concat(idOrUrl, \"\\u201D is not a vimeo.com url.\"));\n}\n\n/* eslint-disable max-params */\n/**\n * A utility method for attaching and detaching event handlers\n *\n * @param {EventTarget} target\n * @param {string | string[]} eventName\n * @param {function} callback\n * @param {'addEventListener' | 'on'} onName\n * @param {'removeEventListener' | 'off'} offName\n * @return {{cancel: (function(): void)}}\n */\nvar subscribe = function subscribe(target, eventName, callback) {\n  var onName = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'addEventListener';\n  var offName = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 'removeEventListener';\n  var eventNames = typeof eventName === 'string' ? [eventName] : eventName;\n  eventNames.forEach(function (evName) {\n    target[onName](evName, callback);\n  });\n  return {\n    cancel: function cancel() {\n      return eventNames.forEach(function (evName) {\n        return target[offName](evName, callback);\n      });\n    }\n  };\n};\n\nvar arrayIndexOfSupport = typeof Array.prototype.indexOf !== 'undefined';\nvar postMessageSupport = typeof window !== 'undefined' && typeof window.postMessage !== 'undefined';\nif (!isNode && (!arrayIndexOfSupport || !postMessageSupport)) {\n  throw new Error('Sorry, the Vimeo Player API is not available in this browser.');\n}\n\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof __webpack_require__.g !== 'undefined' ? __webpack_require__.g : typeof self !== 'undefined' ? self : {};\n\nfunction createCommonjsModule(fn, module) {\n\treturn module = { exports: {} }, fn(module, module.exports), module.exports;\n}\n\n/*!\n * weakmap-polyfill v2.0.4 - ECMAScript6 WeakMap polyfill\n * https://github.com/polygonplanet/weakmap-polyfill\n * Copyright (c) 2015-2021 polygonplanet <<EMAIL>>\n * @license MIT\n */\n\n(function (self) {\n\n  if (self.WeakMap) {\n    return;\n  }\n  var hasOwnProperty = Object.prototype.hasOwnProperty;\n  var hasDefine = Object.defineProperty && function () {\n    try {\n      // Avoid IE8's broken Object.defineProperty\n      return Object.defineProperty({}, 'x', {\n        value: 1\n      }).x === 1;\n    } catch (e) {}\n  }();\n  var defineProperty = function (object, name, value) {\n    if (hasDefine) {\n      Object.defineProperty(object, name, {\n        configurable: true,\n        writable: true,\n        value: value\n      });\n    } else {\n      object[name] = value;\n    }\n  };\n  self.WeakMap = function () {\n    // ECMA-262 23.3 WeakMap Objects\n    function WeakMap() {\n      if (this === void 0) {\n        throw new TypeError(\"Constructor WeakMap requires 'new'\");\n      }\n      defineProperty(this, '_id', genId('_WeakMap'));\n\n      // ECMA-262 23.3.1.1 WeakMap([iterable])\n      if (arguments.length > 0) {\n        // Currently, WeakMap `iterable` argument is not supported\n        throw new TypeError('WeakMap iterable is not supported');\n      }\n    }\n\n    // ECMA-262 23.3.3.2 WeakMap.prototype.delete(key)\n    defineProperty(WeakMap.prototype, 'delete', function (key) {\n      checkInstance(this, 'delete');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        delete key[this._id];\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.3 WeakMap.prototype.get(key)\n    defineProperty(WeakMap.prototype, 'get', function (key) {\n      checkInstance(this, 'get');\n      if (!isObject(key)) {\n        return void 0;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return entry[1];\n      }\n      return void 0;\n    });\n\n    // ECMA-262 23.3.3.4 WeakMap.prototype.has(key)\n    defineProperty(WeakMap.prototype, 'has', function (key) {\n      checkInstance(this, 'has');\n      if (!isObject(key)) {\n        return false;\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        return true;\n      }\n      return false;\n    });\n\n    // ECMA-262 23.3.3.5 WeakMap.prototype.set(key, value)\n    defineProperty(WeakMap.prototype, 'set', function (key, value) {\n      checkInstance(this, 'set');\n      if (!isObject(key)) {\n        throw new TypeError('Invalid value used as weak map key');\n      }\n      var entry = key[this._id];\n      if (entry && entry[0] === key) {\n        entry[1] = value;\n        return this;\n      }\n      defineProperty(key, this._id, [key, value]);\n      return this;\n    });\n    function checkInstance(x, methodName) {\n      if (!isObject(x) || !hasOwnProperty.call(x, '_id')) {\n        throw new TypeError(methodName + ' method called on incompatible receiver ' + typeof x);\n      }\n    }\n    function genId(prefix) {\n      return prefix + '_' + rand() + '.' + rand();\n    }\n    function rand() {\n      return Math.random().toString().substring(2);\n    }\n    defineProperty(WeakMap, '_polyfill', true);\n    return WeakMap;\n  }();\n  function isObject(x) {\n    return Object(x) === x;\n  }\n})(typeof globalThis !== 'undefined' ? globalThis : typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof commonjsGlobal !== 'undefined' ? commonjsGlobal : commonjsGlobal);\n\nvar npo_src = createCommonjsModule(function (module) {\n/*! Native Promise Only\n    v0.8.1 (c) Kyle Simpson\n    MIT License: http://getify.mit-license.org\n*/\n\n(function UMD(name, context, definition) {\n  // special form of UMD for polyfilling across evironments\n  context[name] = context[name] || definition();\n  if ( module.exports) {\n    module.exports = context[name];\n  }\n})(\"Promise\", typeof commonjsGlobal != \"undefined\" ? commonjsGlobal : commonjsGlobal, function DEF() {\n\n  var builtInProp,\n    cycle,\n    scheduling_queue,\n    ToString = Object.prototype.toString,\n    timer = typeof setImmediate != \"undefined\" ? function timer(fn) {\n      return setImmediate(fn);\n    } : setTimeout;\n\n  // dammit, IE8.\n  try {\n    Object.defineProperty({}, \"x\", {});\n    builtInProp = function builtInProp(obj, name, val, config) {\n      return Object.defineProperty(obj, name, {\n        value: val,\n        writable: true,\n        configurable: config !== false\n      });\n    };\n  } catch (err) {\n    builtInProp = function builtInProp(obj, name, val) {\n      obj[name] = val;\n      return obj;\n    };\n  }\n\n  // Note: using a queue instead of array for efficiency\n  scheduling_queue = function Queue() {\n    var first, last, item;\n    function Item(fn, self) {\n      this.fn = fn;\n      this.self = self;\n      this.next = void 0;\n    }\n    return {\n      add: function add(fn, self) {\n        item = new Item(fn, self);\n        if (last) {\n          last.next = item;\n        } else {\n          first = item;\n        }\n        last = item;\n        item = void 0;\n      },\n      drain: function drain() {\n        var f = first;\n        first = last = cycle = void 0;\n        while (f) {\n          f.fn.call(f.self);\n          f = f.next;\n        }\n      }\n    };\n  }();\n  function schedule(fn, self) {\n    scheduling_queue.add(fn, self);\n    if (!cycle) {\n      cycle = timer(scheduling_queue.drain);\n    }\n  }\n\n  // promise duck typing\n  function isThenable(o) {\n    var _then,\n      o_type = typeof o;\n    if (o != null && (o_type == \"object\" || o_type == \"function\")) {\n      _then = o.then;\n    }\n    return typeof _then == \"function\" ? _then : false;\n  }\n  function notify() {\n    for (var i = 0; i < this.chain.length; i++) {\n      notifyIsolated(this, this.state === 1 ? this.chain[i].success : this.chain[i].failure, this.chain[i]);\n    }\n    this.chain.length = 0;\n  }\n\n  // NOTE: This is a separate function to isolate\n  // the `try..catch` so that other code can be\n  // optimized better\n  function notifyIsolated(self, cb, chain) {\n    var ret, _then;\n    try {\n      if (cb === false) {\n        chain.reject(self.msg);\n      } else {\n        if (cb === true) {\n          ret = self.msg;\n        } else {\n          ret = cb.call(void 0, self.msg);\n        }\n        if (ret === chain.promise) {\n          chain.reject(TypeError(\"Promise-chain cycle\"));\n        } else if (_then = isThenable(ret)) {\n          _then.call(ret, chain.resolve, chain.reject);\n        } else {\n          chain.resolve(ret);\n        }\n      }\n    } catch (err) {\n      chain.reject(err);\n    }\n  }\n  function resolve(msg) {\n    var _then,\n      self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    try {\n      if (_then = isThenable(msg)) {\n        schedule(function () {\n          var def_wrapper = new MakeDefWrapper(self);\n          try {\n            _then.call(msg, function $resolve$() {\n              resolve.apply(def_wrapper, arguments);\n            }, function $reject$() {\n              reject.apply(def_wrapper, arguments);\n            });\n          } catch (err) {\n            reject.call(def_wrapper, err);\n          }\n        });\n      } else {\n        self.msg = msg;\n        self.state = 1;\n        if (self.chain.length > 0) {\n          schedule(notify, self);\n        }\n      }\n    } catch (err) {\n      reject.call(new MakeDefWrapper(self), err);\n    }\n  }\n  function reject(msg) {\n    var self = this;\n\n    // already triggered?\n    if (self.triggered) {\n      return;\n    }\n    self.triggered = true;\n\n    // unwrap\n    if (self.def) {\n      self = self.def;\n    }\n    self.msg = msg;\n    self.state = 2;\n    if (self.chain.length > 0) {\n      schedule(notify, self);\n    }\n  }\n  function iteratePromises(Constructor, arr, resolver, rejecter) {\n    for (var idx = 0; idx < arr.length; idx++) {\n      (function IIFE(idx) {\n        Constructor.resolve(arr[idx]).then(function $resolver$(msg) {\n          resolver(idx, msg);\n        }, rejecter);\n      })(idx);\n    }\n  }\n  function MakeDefWrapper(self) {\n    this.def = self;\n    this.triggered = false;\n  }\n  function MakeDef(self) {\n    this.promise = self;\n    this.state = 0;\n    this.triggered = false;\n    this.chain = [];\n    this.msg = void 0;\n  }\n  function Promise(executor) {\n    if (typeof executor != \"function\") {\n      throw TypeError(\"Not a function\");\n    }\n    if (this.__NPO__ !== 0) {\n      throw TypeError(\"Not a promise\");\n    }\n\n    // instance shadowing the inherited \"brand\"\n    // to signal an already \"initialized\" promise\n    this.__NPO__ = 1;\n    var def = new MakeDef(this);\n    this[\"then\"] = function then(success, failure) {\n      var o = {\n        success: typeof success == \"function\" ? success : true,\n        failure: typeof failure == \"function\" ? failure : false\n      };\n      // Note: `then(..)` itself can be borrowed to be used against\n      // a different promise constructor for making the chained promise,\n      // by substituting a different `this` binding.\n      o.promise = new this.constructor(function extractChain(resolve, reject) {\n        if (typeof resolve != \"function\" || typeof reject != \"function\") {\n          throw TypeError(\"Not a function\");\n        }\n        o.resolve = resolve;\n        o.reject = reject;\n      });\n      def.chain.push(o);\n      if (def.state !== 0) {\n        schedule(notify, def);\n      }\n      return o.promise;\n    };\n    this[\"catch\"] = function $catch$(failure) {\n      return this.then(void 0, failure);\n    };\n    try {\n      executor.call(void 0, function publicResolve(msg) {\n        resolve.call(def, msg);\n      }, function publicReject(msg) {\n        reject.call(def, msg);\n      });\n    } catch (err) {\n      reject.call(def, err);\n    }\n  }\n  var PromisePrototype = builtInProp({}, \"constructor\", Promise, /*configurable=*/false);\n\n  // Note: Android 4 cannot use `Object.defineProperty(..)` here\n  Promise.prototype = PromisePrototype;\n\n  // built-in \"brand\" to signal an \"uninitialized\" promise\n  builtInProp(PromisePrototype, \"__NPO__\", 0, /*configurable=*/false);\n  builtInProp(Promise, \"resolve\", function Promise$resolve(msg) {\n    var Constructor = this;\n\n    // spec mandated checks\n    // note: best \"isPromise\" check that's practical for now\n    if (msg && typeof msg == \"object\" && msg.__NPO__ === 1) {\n      return msg;\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      resolve(msg);\n    });\n  });\n  builtInProp(Promise, \"reject\", function Promise$reject(msg) {\n    return new this(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      reject(msg);\n    });\n  });\n  builtInProp(Promise, \"all\", function Promise$all(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    if (arr.length === 0) {\n      return Constructor.resolve([]);\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      var len = arr.length,\n        msgs = Array(len),\n        count = 0;\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        msgs[idx] = msg;\n        if (++count === len) {\n          resolve(msgs);\n        }\n      }, reject);\n    });\n  });\n  builtInProp(Promise, \"race\", function Promise$race(arr) {\n    var Constructor = this;\n\n    // spec mandated checks\n    if (ToString.call(arr) != \"[object Array]\") {\n      return Constructor.reject(TypeError(\"Not an array\"));\n    }\n    return new Constructor(function executor(resolve, reject) {\n      if (typeof resolve != \"function\" || typeof reject != \"function\") {\n        throw TypeError(\"Not a function\");\n      }\n      iteratePromises(Constructor, arr, function resolver(idx, msg) {\n        resolve(msg);\n      }, reject);\n    });\n  });\n  return Promise;\n});\n});\n\n/**\n * @module lib/callbacks\n */\n\nvar callbackMap = new WeakMap();\n\n/**\n * Store a callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @param {(function(this:Player, *): void|{resolve: function, reject: function})} callback\n *        The callback to call or an object with resolve and reject functions for a promise.\n * @return {void}\n */\nfunction storeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!(name in playerCallbacks)) {\n    playerCallbacks[name] = [];\n  }\n  playerCallbacks[name].push(callback);\n  callbackMap.set(player.element, playerCallbacks);\n}\n\n/**\n * Get the callbacks for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @return {function[]}\n */\nfunction getCallbacks(player, name) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  return playerCallbacks[name] || [];\n}\n\n/**\n * Remove a stored callback for a method or event for a player.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name\n * @param {function} [callback] The specific callback to remove.\n * @return {boolean} Was this the last callback?\n */\nfunction removeCallback(player, name, callback) {\n  var playerCallbacks = callbackMap.get(player.element) || {};\n  if (!playerCallbacks[name]) {\n    return true;\n  }\n\n  // If no callback is passed, remove all callbacks for the event\n  if (!callback) {\n    playerCallbacks[name] = [];\n    callbackMap.set(player.element, playerCallbacks);\n    return true;\n  }\n  var index = playerCallbacks[name].indexOf(callback);\n  if (index !== -1) {\n    playerCallbacks[name].splice(index, 1);\n  }\n  callbackMap.set(player.element, playerCallbacks);\n  return playerCallbacks[name] && playerCallbacks[name].length === 0;\n}\n\n/**\n * Return the first stored callback for a player and event or method.\n *\n * @param {Player} player The player object.\n * @param {string} name The method or event name.\n * @return {function} The callback, or false if there were none\n */\nfunction shiftCallbacks(player, name) {\n  var playerCallbacks = getCallbacks(player, name);\n  if (playerCallbacks.length < 1) {\n    return false;\n  }\n  var callback = playerCallbacks.shift();\n  removeCallback(player, name, callback);\n  return callback;\n}\n\n/**\n * Move callbacks associated with an element to another element.\n *\n * @param {HTMLElement} oldElement The old element.\n * @param {HTMLElement} newElement The new element.\n * @return {void}\n */\nfunction swapCallbacks(oldElement, newElement) {\n  var playerCallbacks = callbackMap.get(oldElement);\n  callbackMap.set(newElement, playerCallbacks);\n  callbackMap.delete(oldElement);\n}\n\n/**\n * @module lib/postmessage\n */\n\n/**\n * Parse a message received from postMessage.\n *\n * @param {*} data The data received from postMessage.\n * @return {object}\n */\nfunction parseMessageData(data) {\n  if (typeof data === 'string') {\n    try {\n      data = JSON.parse(data);\n    } catch (error) {\n      // If the message cannot be parsed, throw the error as a warning\n      console.warn(error);\n      return {};\n    }\n  }\n  return data;\n}\n\n/**\n * Post a message to the specified target.\n *\n * @param {Player} player The player object to use.\n * @param {string} method The API method to call.\n * @param {string|number|object|Array|undefined} params The parameters to send to the player.\n * @return {void}\n */\nfunction postMessage(player, method, params) {\n  if (!player.element.contentWindow || !player.element.contentWindow.postMessage) {\n    return;\n  }\n  var message = {\n    method: method\n  };\n  if (params !== undefined) {\n    message.value = params;\n  }\n\n  // IE 8 and 9 do not support passing messages, so stringify them\n  var ieVersion = parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\\d+).*$/, '$1'));\n  if (ieVersion >= 8 && ieVersion < 10) {\n    message = JSON.stringify(message);\n  }\n  player.element.contentWindow.postMessage(message, player.origin);\n}\n\n/**\n * Parse the data received from a message event.\n *\n * @param {Player} player The player that received the message.\n * @param {(Object|string)} data The message data. Strings will be parsed into JSON.\n * @return {void}\n */\nfunction processData(player, data) {\n  data = parseMessageData(data);\n  var callbacks = [];\n  var param;\n  if (data.event) {\n    if (data.event === 'error') {\n      var promises = getCallbacks(player, data.data.method);\n      promises.forEach(function (promise) {\n        var error = new Error(data.data.message);\n        error.name = data.data.name;\n        promise.reject(error);\n        removeCallback(player, data.data.method, promise);\n      });\n    }\n    callbacks = getCallbacks(player, \"event:\".concat(data.event));\n    param = data.data;\n  } else if (data.method) {\n    var callback = shiftCallbacks(player, data.method);\n    if (callback) {\n      callbacks.push(callback);\n      param = data.value;\n    }\n  }\n  callbacks.forEach(function (callback) {\n    try {\n      if (typeof callback === 'function') {\n        callback.call(player, param);\n        return;\n      }\n      callback.resolve(param);\n    } catch (e) {\n      // empty\n    }\n  });\n}\n\n/**\n * @module lib/embed\n */\nvar oEmbedParameters = ['airplay', 'audio_tracks', 'audiotrack', 'autopause', 'autoplay', 'background', 'byline', 'cc', 'chapter_id', 'chapters', 'chromecast', 'color', 'colors', 'controls', 'dnt', 'end_time', 'fullscreen', 'height', 'id', 'initial_quality', 'interactive_params', 'keyboard', 'loop', 'maxheight', 'max_quality', 'maxwidth', 'min_quality', 'muted', 'play_button_position', 'playsinline', 'portrait', 'preload', 'progress_bar', 'quality', 'quality_selector', 'responsive', 'skipping_forward', 'speed', 'start_time', 'texttrack', 'thumbnail_id', 'title', 'transcript', 'transparent', 'unmute_button', 'url', 'vimeo_logo', 'volume', 'watch_full_video', 'width'];\n\n/**\n * Get the 'data-vimeo'-prefixed attributes from an element as an object.\n *\n * @param {HTMLElement} element The element.\n * @param {Object} [defaults={}] The default values to use.\n * @return {Object<string, string>}\n */\nfunction getOEmbedParameters(element) {\n  var defaults = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return oEmbedParameters.reduce(function (params, param) {\n    var value = element.getAttribute(\"data-vimeo-\".concat(param));\n    if (value || value === '') {\n      params[param] = value === '' ? 1 : value;\n    }\n    return params;\n  }, defaults);\n}\n\n/**\n * Create an embed from oEmbed data inside an element.\n *\n * @param {object} data The oEmbed data.\n * @param {HTMLElement} element The element to put the iframe in.\n * @return {HTMLIFrameElement} The iframe embed.\n */\nfunction createEmbed(_ref, element) {\n  var html = _ref.html;\n  if (!element) {\n    throw new TypeError('An element must be provided');\n  }\n  if (element.getAttribute('data-vimeo-initialized') !== null) {\n    return element.querySelector('iframe');\n  }\n  var div = document.createElement('div');\n  div.innerHTML = html;\n  element.appendChild(div.firstChild);\n  element.setAttribute('data-vimeo-initialized', 'true');\n  return element.querySelector('iframe');\n}\n\n/**\n * Make an oEmbed call for the specified URL.\n *\n * @param {string} videoUrl The vimeo.com url for the video.\n * @param {Object} [params] Parameters to pass to oEmbed.\n * @param {HTMLElement} element The element.\n * @return {Promise}\n */\nfunction getOEmbedData(videoUrl) {\n  var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var element = arguments.length > 2 ? arguments[2] : undefined;\n  return new Promise(function (resolve, reject) {\n    if (!isVimeoUrl(videoUrl)) {\n      throw new TypeError(\"\\u201C\".concat(videoUrl, \"\\u201D is not a vimeo.com url.\"));\n    }\n    var domain = getOembedDomain(videoUrl);\n    var url = \"https://\".concat(domain, \"/api/oembed.json?url=\").concat(encodeURIComponent(videoUrl));\n    for (var param in params) {\n      if (params.hasOwnProperty(param)) {\n        url += \"&\".concat(param, \"=\").concat(encodeURIComponent(params[param]));\n      }\n    }\n    var xhr = 'XDomainRequest' in window ? new XDomainRequest() : new XMLHttpRequest();\n    xhr.open('GET', url, true);\n    xhr.onload = function () {\n      if (xhr.status === 404) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D was not found.\")));\n        return;\n      }\n      if (xhr.status === 403) {\n        reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n        return;\n      }\n      try {\n        var json = JSON.parse(xhr.responseText);\n        // Check api response for 403 on oembed\n        if (json.domain_status_code === 403) {\n          // We still want to create the embed to give users visual feedback\n          createEmbed(json, element);\n          reject(new Error(\"\\u201C\".concat(videoUrl, \"\\u201D is not embeddable.\")));\n          return;\n        }\n        resolve(json);\n      } catch (error) {\n        reject(error);\n      }\n    };\n    xhr.onerror = function () {\n      var status = xhr.status ? \" (\".concat(xhr.status, \")\") : '';\n      reject(new Error(\"There was an error fetching the embed code from Vimeo\".concat(status, \".\")));\n    };\n    xhr.send();\n  });\n}\n\n/**\n * Initialize all embeds within a specific element\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initializeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  var elements = [].slice.call(parent.querySelectorAll('[data-vimeo-id], [data-vimeo-url]'));\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error creating an embed: \".concat(error));\n    }\n  };\n  elements.forEach(function (element) {\n    try {\n      // Skip any that have data-vimeo-defer\n      if (element.getAttribute('data-vimeo-defer') !== null) {\n        return;\n      }\n      var params = getOEmbedParameters(element);\n      var url = getVimeoUrl(params);\n      getOEmbedData(url, params, element).then(function (data) {\n        return createEmbed(data, element);\n      }).catch(handleError);\n    } catch (error) {\n      handleError(error);\n    }\n  });\n}\n\n/**\n * Resize embeds when messaged by the player.\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction resizeEmbeds() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  // Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoPlayerResizeEmbeds_) {\n    return;\n  }\n  window.VimeoPlayerResizeEmbeds_ = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n\n    // 'spacechange' is fired only on embeds with cards\n    if (!event.data || event.data.event !== 'spacechange') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      if (iframes[i].contentWindow !== event.source) {\n        continue;\n      }\n\n      // Change padding-bottom of the enclosing div to accommodate\n      // card carousel without distorting aspect ratio\n      var space = iframes[i].parentElement;\n      space.style.paddingBottom = \"\".concat(event.data.data[0].bottom, \"px\");\n      break;\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Add chapters to existing metadata for Google SEO\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction initAppendVideoMetadata() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoSeoMetadataAppended) {\n    return;\n  }\n  window.VimeoSeoMetadataAppended = true;\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    for (var i = 0; i < iframes.length; i++) {\n      var iframe = iframes[i];\n\n      // Initiate appendVideoMetadata if iframe is a Vimeo embed\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.callMethod('appendVideoMetadata', window.location.href);\n      }\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/**\n * Seek to time indicated by vimeo_t query parameter if present in URL\n *\n * @param {HTMLElement} [parent=document] The parent element.\n * @return {void}\n */\nfunction checkUrlTimeParam() {\n  var parent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : document;\n  //  Prevent execution if users include the player.js script multiple times.\n  if (window.VimeoCheckedUrlTimeParam) {\n    return;\n  }\n  window.VimeoCheckedUrlTimeParam = true;\n  var handleError = function handleError(error) {\n    if ('console' in window && console.error) {\n      console.error(\"There was an error getting video Id: \".concat(error));\n    }\n  };\n  var onMessage = function onMessage(event) {\n    if (!isVimeoUrl(event.origin)) {\n      return;\n    }\n    var data = parseMessageData(event.data);\n    if (!data || data.event !== 'ready') {\n      return;\n    }\n    var iframes = parent.querySelectorAll('iframe');\n    var _loop = function _loop() {\n      var iframe = iframes[i];\n      var isValidMessageSource = iframe.contentWindow === event.source;\n      if (isVimeoEmbed(iframe.src) && isValidMessageSource) {\n        var player = new Player(iframe);\n        player.getVideoId().then(function (videoId) {\n          var matches = new RegExp(\"[?&]vimeo_t_\".concat(videoId, \"=([^&#]*)\")).exec(window.location.href);\n          if (matches && matches[1]) {\n            var sec = decodeURI(matches[1]);\n            player.setCurrentTime(sec);\n          }\n          return;\n        }).catch(handleError);\n      }\n    };\n    for (var i = 0; i < iframes.length; i++) {\n      _loop();\n    }\n  };\n  window.addEventListener('message', onMessage);\n}\n\n/* MIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\nTerms */\n\nfunction initializeScreenfull() {\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n    // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n    // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n    for (; i < l; i++) {\n      val = fnMap[i];\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n        return ret;\n      }\n    }\n    return false;\n  }();\n  var eventNameMap = {\n    fullscreenchange: fn.fullscreenchange,\n    fullscreenerror: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function request(element) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function onFullScreenEntered() {\n          screenfull.off('fullscreenchange', onFullScreenEntered);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      });\n    },\n    exit: function exit() {\n      return new Promise(function (resolve, reject) {\n        if (!screenfull.isFullscreen) {\n          resolve();\n          return;\n        }\n        var onFullScreenExit = function onFullScreenExit() {\n          screenfull.off('fullscreenchange', onFullScreenExit);\n          resolve();\n        };\n        screenfull.on('fullscreenchange', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      });\n    },\n    on: function on(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.addEventListener(eventName, callback);\n      }\n    },\n    off: function off(event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.removeEventListener(eventName, callback);\n      }\n    }\n  };\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function get() {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function get() {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function get() {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n  return screenfull;\n}\n\n/** @typedef {import('./timing-src-connector.types').PlayerControls} PlayerControls */\n/** @typedef {import('./timing-object.types').TimingObject} TimingObject */\n/** @typedef {import('./timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n/** @typedef {(msg: string) => any} Logger */\n/** @typedef {import('timing-object.types').TConnectionState} TConnectionState */\n\n/**\n * @type {TimingSrcConnectorOptions}\n *\n * For details on these properties and their effects, see the typescript definition referenced above.\n */\nvar defaultOptions = {\n  role: 'viewer',\n  autoPlayMuted: true,\n  allowedDrift: 0.3,\n  maxAllowedDrift: 1,\n  minCheckInterval: 0.1,\n  maxRateAdjustment: 0.2,\n  maxTimeToCatchUp: 1\n};\n\n/**\n * There's a proposed W3C spec for the Timing Object which would introduce a new set of APIs that would simplify time-synchronization tasks for browser applications.\n *\n * Proposed spec: https://webtiming.github.io/timingobject/\n * V3 Spec: https://timingsrc.readthedocs.io/en/latest/\n * Demuxed talk: https://www.youtube.com/watch?v=cZSjDaGDmX8\n *\n * This class makes it easy to connect Vimeo.Player to a provided TimingObject via Vimeo.Player.setTimingSrc(myTimingObject, options) and the synchronization will be handled automatically.\n *\n * There are 5 general responsibilities in TimingSrcConnector:\n *\n * 1. `updatePlayer()` which sets the player's currentTime, playbackRate and pause/play state based on current state of the TimingObject.\n * 2. `updateTimingObject()` which sets the TimingObject's position and velocity from the player's state.\n * 3. `playerUpdater` which listens for change events on the TimingObject and will respond by calling updatePlayer.\n * 4. `timingObjectUpdater` which listens to the player events of seeked, play and pause and will respond by calling `updateTimingObject()`.\n * 5. `maintainPlaybackPosition` this is code that constantly monitors the player to make sure it's always in sync with the TimingObject. This is needed because videos will generally not play with precise time accuracy and there will be some drift which becomes more noticeable over longer periods (as noted in the timing-object spec). More details on this method below.\n */\nvar TimingSrcConnector = /*#__PURE__*/function (_EventTarget) {\n  _inherits(TimingSrcConnector, _EventTarget);\n  var _super = _createSuper(TimingSrcConnector);\n  /**\n   * @param {PlayerControls} player\n   * @param {TimingObject} timingObject\n   * @param {TimingSrcConnectorOptions} options\n   * @param {Logger} logger\n   */\n  function TimingSrcConnector(_player, timingObject) {\n    var _this;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var logger = arguments.length > 3 ? arguments[3] : undefined;\n    _classCallCheck(this, TimingSrcConnector);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"logger\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"speedAdjustment\", 0);\n    /**\n     * @param {PlayerControls} player\n     * @param {number} newAdjustment\n     * @return {Promise<void>}\n     */\n    _defineProperty(_assertThisInitialized(_this), \"adjustSpeed\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(player, newAdjustment) {\n        var newPlaybackRate;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (!(_this.speedAdjustment === newAdjustment)) {\n                _context.next = 2;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 2:\n              _context.next = 4;\n              return player.getPlaybackRate();\n            case 4:\n              _context.t0 = _context.sent;\n              _context.t1 = _this.speedAdjustment;\n              _context.t2 = _context.t0 - _context.t1;\n              _context.t3 = newAdjustment;\n              newPlaybackRate = _context.t2 + _context.t3;\n              _this.log(\"New playbackRate:  \".concat(newPlaybackRate));\n              _context.next = 12;\n              return player.setPlaybackRate(newPlaybackRate);\n            case 12:\n              _this.speedAdjustment = newAdjustment;\n            case 13:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _this.logger = logger;\n    _this.init(timingObject, _player, _objectSpread2(_objectSpread2({}, defaultOptions), options));\n    return _this;\n  }\n  _createClass(TimingSrcConnector, [{\n    key: \"disconnect\",\n    value: function disconnect() {\n      this.dispatchEvent(new Event('disconnect'));\n    }\n\n    /**\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"init\",\n    value: function () {\n      var _init = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(timingObject, player, options) {\n        var _this2 = this;\n        var playerUpdater, positionSync, timingObjectUpdater;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              _context2.next = 2;\n              return this.waitForTOReadyState(timingObject, 'open');\n            case 2:\n              if (!(options.role === 'viewer')) {\n                _context2.next = 10;\n                break;\n              }\n              _context2.next = 5;\n              return this.updatePlayer(timingObject, player, options);\n            case 5:\n              playerUpdater = subscribe(timingObject, 'change', function () {\n                return _this2.updatePlayer(timingObject, player, options);\n              });\n              positionSync = this.maintainPlaybackPosition(timingObject, player, options);\n              this.addEventListener('disconnect', function () {\n                positionSync.cancel();\n                playerUpdater.cancel();\n              });\n              _context2.next = 14;\n              break;\n            case 10:\n              _context2.next = 12;\n              return this.updateTimingObject(timingObject, player);\n            case 12:\n              timingObjectUpdater = subscribe(player, ['seeked', 'play', 'pause', 'ratechange'], function () {\n                return _this2.updateTimingObject(timingObject, player);\n              }, 'on', 'off');\n              this.addEventListener('disconnect', function () {\n                return timingObjectUpdater.cancel();\n              });\n            case 14:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2, this);\n      }));\n      function init(_x3, _x4, _x5) {\n        return _init.apply(this, arguments);\n      }\n      return init;\n    }()\n    /**\n     * Sets the TimingObject's state to reflect that of the player\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updateTimingObject\",\n    value: function () {\n      var _updateTimingObject = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(timingObject, player) {\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.t0 = timingObject;\n              _context3.next = 3;\n              return player.getCurrentTime();\n            case 3:\n              _context3.t1 = _context3.sent;\n              _context3.next = 6;\n              return player.getPaused();\n            case 6:\n              if (!_context3.sent) {\n                _context3.next = 10;\n                break;\n              }\n              _context3.t2 = 0;\n              _context3.next = 13;\n              break;\n            case 10:\n              _context3.next = 12;\n              return player.getPlaybackRate();\n            case 12:\n              _context3.t2 = _context3.sent;\n            case 13:\n              _context3.t3 = _context3.t2;\n              _context3.t4 = {\n                position: _context3.t1,\n                velocity: _context3.t3\n              };\n              _context3.t0.update.call(_context3.t0, _context3.t4);\n            case 16:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      function updateTimingObject(_x6, _x7) {\n        return _updateTimingObject.apply(this, arguments);\n      }\n      return updateTimingObject;\n    }()\n    /**\n     * Sets the player's timing state to reflect that of the TimingObject\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {Promise<void>}\n     */\n  }, {\n    key: \"updatePlayer\",\n    value: function () {\n      var _updatePlayer = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee5(timingObject, player, options) {\n        var _timingObject$query, position, velocity;\n        return _regeneratorRuntime().wrap(function _callee5$(_context5) {\n          while (1) switch (_context5.prev = _context5.next) {\n            case 0:\n              _timingObject$query = timingObject.query(), position = _timingObject$query.position, velocity = _timingObject$query.velocity;\n              if (typeof position === 'number') {\n                player.setCurrentTime(position);\n              }\n              if (!(typeof velocity === 'number')) {\n                _context5.next = 25;\n                break;\n              }\n              if (!(velocity === 0)) {\n                _context5.next = 11;\n                break;\n              }\n              _context5.next = 6;\n              return player.getPaused();\n            case 6:\n              _context5.t0 = _context5.sent;\n              if (!(_context5.t0 === false)) {\n                _context5.next = 9;\n                break;\n              }\n              player.pause();\n            case 9:\n              _context5.next = 25;\n              break;\n            case 11:\n              if (!(velocity > 0)) {\n                _context5.next = 25;\n                break;\n              }\n              _context5.next = 14;\n              return player.getPaused();\n            case 14:\n              _context5.t1 = _context5.sent;\n              if (!(_context5.t1 === true)) {\n                _context5.next = 19;\n                break;\n              }\n              _context5.next = 18;\n              return player.play().catch( /*#__PURE__*/function () {\n                var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(err) {\n                  return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n                    while (1) switch (_context4.prev = _context4.next) {\n                      case 0:\n                        if (!(err.name === 'NotAllowedError' && options.autoPlayMuted)) {\n                          _context4.next = 5;\n                          break;\n                        }\n                        _context4.next = 3;\n                        return player.setMuted(true);\n                      case 3:\n                        _context4.next = 5;\n                        return player.play().catch(function (err2) {\n                          return console.error('Couldn\\'t play the video from TimingSrcConnector. Error:', err2);\n                        });\n                      case 5:\n                      case \"end\":\n                        return _context4.stop();\n                    }\n                  }, _callee4);\n                }));\n                return function (_x11) {\n                  return _ref2.apply(this, arguments);\n                };\n              }());\n            case 18:\n              this.updatePlayer(timingObject, player, options);\n            case 19:\n              _context5.next = 21;\n              return player.getPlaybackRate();\n            case 21:\n              _context5.t2 = _context5.sent;\n              _context5.t3 = velocity;\n              if (!(_context5.t2 !== _context5.t3)) {\n                _context5.next = 25;\n                break;\n              }\n              player.setPlaybackRate(velocity);\n            case 25:\n            case \"end\":\n              return _context5.stop();\n          }\n        }, _callee5, this);\n      }));\n      function updatePlayer(_x8, _x9, _x10) {\n        return _updatePlayer.apply(this, arguments);\n      }\n      return updatePlayer;\n    }()\n    /**\n     * Since video players do not play with 100% time precision, we need to closely monitor\n     * our player to be sure it remains in sync with the TimingObject.\n     *\n     * If out of sync, we use the current conditions and the options provided to determine\n     * whether to re-sync via setting currentTime or adjusting the playbackRate\n     *\n     * @param {TimingObject} timingObject\n     * @param {PlayerControls} player\n     * @param {TimingSrcConnectorOptions} options\n     * @return {{cancel: (function(): void)}}\n     */\n  }, {\n    key: \"maintainPlaybackPosition\",\n    value: function maintainPlaybackPosition(timingObject, player, options) {\n      var _this3 = this;\n      var allowedDrift = options.allowedDrift,\n        maxAllowedDrift = options.maxAllowedDrift,\n        minCheckInterval = options.minCheckInterval,\n        maxRateAdjustment = options.maxRateAdjustment,\n        maxTimeToCatchUp = options.maxTimeToCatchUp;\n      var syncInterval = Math.min(maxTimeToCatchUp, Math.max(minCheckInterval, maxAllowedDrift)) * 1000;\n      var check = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee6() {\n          var diff, diffAbs, min, max, adjustment;\n          return _regeneratorRuntime().wrap(function _callee6$(_context6) {\n            while (1) switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.t0 = timingObject.query().velocity === 0;\n                if (_context6.t0) {\n                  _context6.next = 6;\n                  break;\n                }\n                _context6.next = 4;\n                return player.getPaused();\n              case 4:\n                _context6.t1 = _context6.sent;\n                _context6.t0 = _context6.t1 === true;\n              case 6:\n                if (!_context6.t0) {\n                  _context6.next = 8;\n                  break;\n                }\n                return _context6.abrupt(\"return\");\n              case 8:\n                _context6.t2 = timingObject.query().position;\n                _context6.next = 11;\n                return player.getCurrentTime();\n              case 11:\n                _context6.t3 = _context6.sent;\n                diff = _context6.t2 - _context6.t3;\n                diffAbs = Math.abs(diff);\n                _this3.log(\"Drift: \".concat(diff));\n                if (!(diffAbs > maxAllowedDrift)) {\n                  _context6.next = 22;\n                  break;\n                }\n                _context6.next = 18;\n                return _this3.adjustSpeed(player, 0);\n              case 18:\n                player.setCurrentTime(timingObject.query().position);\n                _this3.log('Resync by currentTime');\n                _context6.next = 29;\n                break;\n              case 22:\n                if (!(diffAbs > allowedDrift)) {\n                  _context6.next = 29;\n                  break;\n                }\n                min = diffAbs / maxTimeToCatchUp;\n                max = maxRateAdjustment;\n                adjustment = min < max ? (max - min) / 2 : max;\n                _context6.next = 28;\n                return _this3.adjustSpeed(player, adjustment * Math.sign(diff));\n              case 28:\n                _this3.log('Resync by playbackRate');\n              case 29:\n              case \"end\":\n                return _context6.stop();\n            }\n          }, _callee6);\n        }));\n        return function check() {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      var interval = setInterval(function () {\n        return check();\n      }, syncInterval);\n      return {\n        cancel: function cancel() {\n          return clearInterval(interval);\n        }\n      };\n    }\n\n    /**\n     * @param {string} msg\n     */\n  }, {\n    key: \"log\",\n    value: function log(msg) {\n      var _this$logger;\n      (_this$logger = this.logger) === null || _this$logger === void 0 ? void 0 : _this$logger.call(this, \"TimingSrcConnector: \".concat(msg));\n    }\n  }, {\n    key: \"waitForTOReadyState\",\n    value:\n    /**\n     * @param {TimingObject} timingObject\n     * @param {TConnectionState} state\n     * @return {Promise<void>}\n     */\n    function waitForTOReadyState(timingObject, state) {\n      return new Promise(function (resolve) {\n        var check = function check() {\n          if (timingObject.readyState === state) {\n            resolve();\n          } else {\n            timingObject.addEventListener('readystatechange', check, {\n              once: true\n            });\n          }\n        };\n        check();\n      });\n    }\n  }]);\n  return TimingSrcConnector;\n}( /*#__PURE__*/_wrapNativeSuper(EventTarget));\n\nvar playerMap = new WeakMap();\nvar readyMap = new WeakMap();\nvar screenfull = {};\nvar Player = /*#__PURE__*/function () {\n  /**\n   * Create a Player.\n   *\n   * @param {(HTMLIFrameElement|HTMLElement|string|jQuery)} element A reference to the Vimeo\n   *        player iframe, and id, or a jQuery object.\n   * @param {object} [options] oEmbed parameters to use when creating an embed in the element.\n   * @return {Player}\n   */\n  function Player(element) {\n    var _this = this;\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    _classCallCheck(this, Player);\n    /* global jQuery */\n    if (window.jQuery && element instanceof jQuery) {\n      if (element.length > 1 && window.console && console.warn) {\n        console.warn('A jQuery object with multiple elements was passed, using the first element.');\n      }\n      element = element[0];\n    }\n\n    // Find an element by ID\n    if (typeof document !== 'undefined' && typeof element === 'string') {\n      element = document.getElementById(element);\n    }\n\n    // Not an element!\n    if (!isDomElement(element)) {\n      throw new TypeError('You must pass either a valid element or a valid id.');\n    }\n\n    // Already initialized an embed in this div, so grab the iframe\n    if (element.nodeName !== 'IFRAME') {\n      var iframe = element.querySelector('iframe');\n      if (iframe) {\n        element = iframe;\n      }\n    }\n\n    // iframe url is not a Vimeo url\n    if (element.nodeName === 'IFRAME' && !isVimeoUrl(element.getAttribute('src') || '')) {\n      throw new Error('The player element passed isn’t a Vimeo embed.');\n    }\n\n    // If there is already a player object in the map, return that\n    if (playerMap.has(element)) {\n      return playerMap.get(element);\n    }\n    this._window = element.ownerDocument.defaultView;\n    this.element = element;\n    this.origin = '*';\n    var readyPromise = new npo_src(function (resolve, reject) {\n      _this._onMessage = function (event) {\n        if (!isVimeoUrl(event.origin) || _this.element.contentWindow !== event.source) {\n          return;\n        }\n        if (_this.origin === '*') {\n          _this.origin = event.origin;\n        }\n        var data = parseMessageData(event.data);\n        var isError = data && data.event === 'error';\n        var isReadyError = isError && data.data && data.data.method === 'ready';\n        if (isReadyError) {\n          var error = new Error(data.data.message);\n          error.name = data.data.name;\n          reject(error);\n          return;\n        }\n        var isReadyEvent = data && data.event === 'ready';\n        var isPingResponse = data && data.method === 'ping';\n        if (isReadyEvent || isPingResponse) {\n          _this.element.setAttribute('data-ready', 'true');\n          resolve();\n          return;\n        }\n        processData(_this, data);\n      };\n      _this._window.addEventListener('message', _this._onMessage);\n      if (_this.element.nodeName !== 'IFRAME') {\n        var params = getOEmbedParameters(element, options);\n        var url = getVimeoUrl(params);\n        getOEmbedData(url, params, element).then(function (data) {\n          var iframe = createEmbed(data, element);\n          // Overwrite element with the new iframe,\n          // but store reference to the original element\n          _this.element = iframe;\n          _this._originalElement = element;\n          swapCallbacks(element, iframe);\n          playerMap.set(_this.element, _this);\n          return data;\n        }).catch(reject);\n      }\n    });\n\n    // Store a copy of this Player in the map\n    readyMap.set(this, readyPromise);\n    playerMap.set(this.element, this);\n\n    // Send a ping to the iframe so the ready promise will be resolved if\n    // the player is already ready.\n    if (this.element.nodeName === 'IFRAME') {\n      postMessage(this, 'ping');\n    }\n    if (screenfull.isEnabled) {\n      var exitFullscreen = function exitFullscreen() {\n        return screenfull.exit();\n      };\n      this.fullscreenchangeHandler = function () {\n        if (screenfull.isFullscreen) {\n          storeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        } else {\n          removeCallback(_this, 'event:exitFullscreen', exitFullscreen);\n        }\n        // eslint-disable-next-line\n        _this.ready().then(function () {\n          postMessage(_this, 'fullscreenchange', screenfull.isFullscreen);\n        });\n      };\n      screenfull.on('fullscreenchange', this.fullscreenchangeHandler);\n    }\n    return this;\n  }\n\n  /**\n   * Get a promise for a method.\n   *\n   * @param {string} name The API method to call.\n   * @param {...(string|number|object|Array)} args Arguments to send via postMessage.\n   * @return {Promise}\n   */\n  _createClass(Player, [{\n    key: \"callMethod\",\n    value: function callMethod(name) {\n      var _this2 = this;\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (name === undefined || name === null) {\n        throw new TypeError('You must pass a method name.');\n      }\n      return new npo_src(function (resolve, reject) {\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this2.ready().then(function () {\n          storeCallback(_this2, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this2, name, args);\n        }).catch(reject);\n      });\n    }\n    /**\n     * Get a promise for the value of a player property.\n     *\n     * @param {string} name The property name\n     * @return {Promise}\n     */\n  }, {\n    key: \"get\",\n    value: function get(name) {\n      var _this3 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'get');\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this3.ready().then(function () {\n          storeCallback(_this3, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this3, name);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Get a promise for setting the value of a player property.\n     *\n     * @param {string} name The API method to call.\n     * @param {mixed} value The value to set.\n     * @return {Promise}\n     */\n  }, {\n    key: \"set\",\n    value: function set(name, value) {\n      var _this4 = this;\n      return new npo_src(function (resolve, reject) {\n        name = getMethodName(name, 'set');\n        if (value === undefined || value === null) {\n          throw new TypeError('There must be a value to set.');\n        }\n\n        // We are storing the resolve/reject handlers to call later, so we\n        // can’t return here.\n        // eslint-disable-next-line promise/always-return\n        return _this4.ready().then(function () {\n          storeCallback(_this4, name, {\n            resolve: resolve,\n            reject: reject\n          });\n          postMessage(_this4, name, value);\n        }).catch(reject);\n      });\n    }\n\n    /**\n     * Add an event listener for the specified event. Will call the\n     * callback with a single parameter, `data`, that contains the data for\n     * that event.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function(*)} callback The function to call when the event fires.\n     * @return {void}\n     */\n  }, {\n    key: \"on\",\n    value: function on(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (!callback) {\n        throw new TypeError('You must pass a callback function.');\n      }\n      if (typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var callbacks = getCallbacks(this, \"event:\".concat(eventName));\n      if (callbacks.length === 0) {\n        this.callMethod('addEventListener', eventName).catch(function () {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n      storeCallback(this, \"event:\".concat(eventName), callback);\n    }\n\n    /**\n     * Remove an event listener for the specified event. Will remove all\n     * listeners for that event if a `callback` isn’t passed, or only that\n     * specific callback if it is passed.\n     *\n     * @param {string} eventName The name of the event.\n     * @param {function} [callback] The specific callback to remove.\n     * @return {void}\n     */\n  }, {\n    key: \"off\",\n    value: function off(eventName, callback) {\n      if (!eventName) {\n        throw new TypeError('You must pass an event name.');\n      }\n      if (callback && typeof callback !== 'function') {\n        throw new TypeError('The callback must be a function.');\n      }\n      var lastCallback = removeCallback(this, \"event:\".concat(eventName), callback);\n\n      // If there are no callbacks left, remove the listener\n      if (lastCallback) {\n        this.callMethod('removeEventListener', eventName).catch(function (e) {\n          // Ignore the error. There will be an error event fired that\n          // will trigger the error callback if they are listening.\n        });\n      }\n    }\n\n    /**\n     * A promise to load a new video.\n     *\n     * @promise LoadVideoPromise\n     * @fulfill {number} The video with this id or url successfully loaded.\n     * @reject {TypeError} The id was not a number.\n     */\n    /**\n     * Load a new video into this embed. The promise will be resolved if\n     * the video is successfully loaded, or it will be rejected if it could\n     * not be loaded.\n     *\n     * @param {number|string|object} options The id of the video, the url of the video, or an object with embed options.\n     * @return {LoadVideoPromise}\n     */\n  }, {\n    key: \"loadVideo\",\n    value: function loadVideo(options) {\n      return this.callMethod('loadVideo', options);\n    }\n\n    /**\n     * A promise to perform an action when the Player is ready.\n     *\n     * @todo document errors\n     * @promise LoadVideoPromise\n     * @fulfill {void}\n     */\n    /**\n     * Trigger a function when the player iframe has initialized. You do not\n     * need to wait for `ready` to trigger to begin adding event listeners\n     * or calling other methods.\n     *\n     * @return {ReadyPromise}\n     */\n  }, {\n    key: \"ready\",\n    value: function ready() {\n      var readyPromise = readyMap.get(this) || new npo_src(function (resolve, reject) {\n        reject(new Error('Unknown player. Probably unloaded.'));\n      });\n      return npo_src.resolve(readyPromise);\n    }\n\n    /**\n     * A promise to add a cue point to the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point to use for removeCuePoint.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Add a cue point to the player.\n     *\n     * @param {number} time The time for the cue point.\n     * @param {object} [data] Arbitrary data to be returned with the cue point.\n     * @return {AddCuePointPromise}\n     */\n  }, {\n    key: \"addCuePoint\",\n    value: function addCuePoint(time) {\n      var data = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return this.callMethod('addCuePoint', {\n        time: time,\n        data: data\n      });\n    }\n\n    /**\n     * A promise to remove a cue point from the player.\n     *\n     * @promise AddCuePointPromise\n     * @fulfill {string} The id of the cue point that was removed.\n     * @reject {InvalidCuePoint} The cue point with the specified id was not\n     *         found.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Remove a cue point from the video.\n     *\n     * @param {string} id The id of the cue point to remove.\n     * @return {RemoveCuePointPromise}\n     */\n  }, {\n    key: \"removeCuePoint\",\n    value: function removeCuePoint(id) {\n      return this.callMethod('removeCuePoint', id);\n    }\n\n    /**\n     * A representation of a text track on a video.\n     *\n     * @typedef {Object} VimeoTextTrack\n     * @property {string} language The ISO language code.\n     * @property {string} kind The kind of track it is (captions or subtitles).\n     * @property {string} label The human‐readable label for the track.\n     */\n    /**\n     * A promise to enable a text track.\n     *\n     * @promise EnableTextTrackPromise\n     * @fulfill {VimeoTextTrack} The text track that was enabled.\n     * @reject {InvalidTrackLanguageError} No track was available with the\n     *         specified language.\n     * @reject {InvalidTrackError} No track was available with the specified\n     *         language and kind.\n     */\n    /**\n     * Enable the text track with the specified language, and optionally the\n     * specified kind (captions or subtitles).\n     *\n     * When set via the API, the track language will not change the viewer’s\n     * stored preference.\n     *\n     * @param {string} language The two‐letter language code.\n     * @param {string} [kind] The kind of track to enable (captions or subtitles).\n     * @return {EnableTextTrackPromise}\n     */\n  }, {\n    key: \"enableTextTrack\",\n    value: function enableTextTrack(language, kind) {\n      if (!language) {\n        throw new TypeError('You must pass a language.');\n      }\n      return this.callMethod('enableTextTrack', {\n        language: language,\n        kind: kind\n      });\n    }\n\n    /**\n     * A promise to disable the active text track.\n     *\n     * @promise DisableTextTrackPromise\n     * @fulfill {void} The track was disabled.\n     */\n    /**\n     * Disable the currently-active text track.\n     *\n     * @return {DisableTextTrackPromise}\n     */\n  }, {\n    key: \"disableTextTrack\",\n    value: function disableTextTrack() {\n      return this.callMethod('disableTextTrack');\n    }\n\n    /**\n     * A promise to pause the video.\n     *\n     * @promise PausePromise\n     * @fulfill {void} The video was paused.\n     */\n    /**\n     * Pause the video if it’s playing.\n     *\n     * @return {PausePromise}\n     */\n  }, {\n    key: \"pause\",\n    value: function pause() {\n      return this.callMethod('pause');\n    }\n\n    /**\n     * A promise to play the video.\n     *\n     * @promise PlayPromise\n     * @fulfill {void} The video was played.\n     */\n    /**\n     * Play the video if it’s paused. **Note:** on iOS and some other\n     * mobile devices, you cannot programmatically trigger play. Once the\n     * viewer has tapped on the play button in the player, however, you\n     * will be able to use this function.\n     *\n     * @return {PlayPromise}\n     */\n  }, {\n    key: \"play\",\n    value: function play() {\n      return this.callMethod('play');\n    }\n\n    /**\n     * Request that the player enters fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestFullscreen\",\n    value: function requestFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.request(this.element);\n      }\n      return this.callMethod('requestFullscreen');\n    }\n\n    /**\n     * Request that the player exits fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitFullscreen\",\n    value: function exitFullscreen() {\n      if (screenfull.isEnabled) {\n        return screenfull.exit();\n      }\n      return this.callMethod('exitFullscreen');\n    }\n\n    /**\n     * Returns true if the player is currently fullscreen.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getFullscreen\",\n    value: function getFullscreen() {\n      if (screenfull.isEnabled) {\n        return npo_src.resolve(screenfull.isFullscreen);\n      }\n      return this.get('fullscreen');\n    }\n\n    /**\n     * Request that the player enters picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"requestPictureInPicture\",\n    value: function requestPictureInPicture() {\n      return this.callMethod('requestPictureInPicture');\n    }\n\n    /**\n     * Request that the player exits picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"exitPictureInPicture\",\n    value: function exitPictureInPicture() {\n      return this.callMethod('exitPictureInPicture');\n    }\n\n    /**\n     * Returns true if the player is currently picture-in-picture.\n     * @return {Promise}\n     */\n  }, {\n    key: \"getPictureInPicture\",\n    value: function getPictureInPicture() {\n      return this.get('pictureInPicture');\n    }\n\n    /**\n     * A promise to prompt the viewer to initiate remote playback.\n     *\n     * @promise RemotePlaybackPromptPromise\n     * @fulfill {void}\n     * @reject {NotFoundError} No remote playback device is available.\n     */\n    /**\n     * Request to prompt the user to initiate remote playback.\n     *\n     * @return {RemotePlaybackPromptPromise}\n     */\n  }, {\n    key: \"remotePlaybackPrompt\",\n    value: function remotePlaybackPrompt() {\n      return this.callMethod('remotePlaybackPrompt');\n    }\n\n    /**\n     * A promise to unload the video.\n     *\n     * @promise UnloadPromise\n     * @fulfill {void} The video was unloaded.\n     */\n    /**\n     * Return the player to its initial state.\n     *\n     * @return {UnloadPromise}\n     */\n  }, {\n    key: \"unload\",\n    value: function unload() {\n      return this.callMethod('unload');\n    }\n\n    /**\n     * Cleanup the player and remove it from the DOM\n     *\n     * It won't be usable and a new one should be constructed\n     *  in order to do any operations.\n     *\n     * @return {Promise}\n     */\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      var _this5 = this;\n      return new npo_src(function (resolve) {\n        readyMap.delete(_this5);\n        playerMap.delete(_this5.element);\n        if (_this5._originalElement) {\n          playerMap.delete(_this5._originalElement);\n          _this5._originalElement.removeAttribute('data-vimeo-initialized');\n        }\n        if (_this5.element && _this5.element.nodeName === 'IFRAME' && _this5.element.parentNode) {\n          // If we've added an additional wrapper div, remove that from the DOM.\n          // If not, just remove the iframe element.\n          if (_this5.element.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== _this5.element.parentNode) {\n            _this5.element.parentNode.parentNode.removeChild(_this5.element.parentNode);\n          } else {\n            _this5.element.parentNode.removeChild(_this5.element);\n          }\n        }\n\n        // If the clip is private there is a case where the element stays the\n        // div element. Destroy should reset the div and remove the iframe child.\n        if (_this5.element && _this5.element.nodeName === 'DIV' && _this5.element.parentNode) {\n          _this5.element.removeAttribute('data-vimeo-initialized');\n          var iframe = _this5.element.querySelector('iframe');\n          if (iframe && iframe.parentNode) {\n            // If we've added an additional wrapper div, remove that from the DOM.\n            // If not, just remove the iframe element.\n            if (iframe.parentNode.parentNode && _this5._originalElement && _this5._originalElement !== iframe.parentNode) {\n              iframe.parentNode.parentNode.removeChild(iframe.parentNode);\n            } else {\n              iframe.parentNode.removeChild(iframe);\n            }\n          }\n        }\n        _this5._window.removeEventListener('message', _this5._onMessage);\n        if (screenfull.isEnabled) {\n          screenfull.off('fullscreenchange', _this5.fullscreenchangeHandler);\n        }\n        resolve();\n      });\n    }\n\n    /**\n     * A promise to get the autopause behavior of the video.\n     *\n     * @promise GetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get the autopause behavior for this player.\n     *\n     * @return {GetAutopausePromise}\n     */\n  }, {\n    key: \"getAutopause\",\n    value: function getAutopause() {\n      return this.get('autopause');\n    }\n\n    /**\n     * A promise to set the autopause behavior of the video.\n     *\n     * @promise SetAutopausePromise\n     * @fulfill {boolean} Whether autopause is turned on or off.\n     * @reject {UnsupportedError} Autopause is not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Enable or disable the autopause behavior of this player.\n     *\n     * By default, when another video is played in the same browser, this\n     * player will automatically pause. Unless you have a specific reason\n     * for doing so, we recommend that you leave autopause set to the\n     * default (`true`).\n     *\n     * @param {boolean} autopause\n     * @return {SetAutopausePromise}\n     */\n  }, {\n    key: \"setAutopause\",\n    value: function setAutopause(autopause) {\n      return this.set('autopause', autopause);\n    }\n\n    /**\n     * A promise to get the buffered property of the video.\n     *\n     * @promise GetBufferedPromise\n     * @fulfill {Array} Buffered Timeranges converted to an Array.\n     */\n    /**\n     * Get the buffered property of the video.\n     *\n     * @return {GetBufferedPromise}\n     */\n  }, {\n    key: \"getBuffered\",\n    value: function getBuffered() {\n      return this.get('buffered');\n    }\n\n    /**\n     * @typedef {Object} CameraProperties\n     * @prop {number} props.yaw - Number between 0 and 360.\n     * @prop {number} props.pitch - Number between -90 and 90.\n     * @prop {number} props.roll - Number between -180 and 180.\n     * @prop {number} props.fov - The field of view in degrees.\n     */\n    /**\n     * A promise to get the camera properties of the player.\n     *\n     * @promise GetCameraPromise\n     * @fulfill {CameraProperties} The camera properties.\n     */\n    /**\n     * For 360° videos get the camera properties for this player.\n     *\n     * @return {GetCameraPromise}\n     */\n  }, {\n    key: \"getCameraProps\",\n    value: function getCameraProps() {\n      return this.get('cameraProps');\n    }\n\n    /**\n     * A promise to set the camera properties of the player.\n     *\n     * @promise SetCameraPromise\n     * @fulfill {Object} The camera was successfully set.\n     * @reject {RangeError} The range was out of bounds.\n     */\n    /**\n     * For 360° videos set the camera properties for this player.\n     *\n     * @param {CameraProperties} camera The camera properties\n     * @return {SetCameraPromise}\n     */\n  }, {\n    key: \"setCameraProps\",\n    value: function setCameraProps(camera) {\n      return this.set('cameraProps', camera);\n    }\n\n    /**\n     * A representation of a chapter.\n     *\n     * @typedef {Object} VimeoChapter\n     * @property {number} startTime The start time of the chapter.\n     * @property {object} title The title of the chapter.\n     * @property {number} index The place in the order of Chapters. Starts at 1.\n     */\n    /**\n     * A promise to get chapters for the video.\n     *\n     * @promise GetChaptersPromise\n     * @fulfill {VimeoChapter[]} The chapters for the video.\n     */\n    /**\n     * Get an array of all the chapters for the video.\n     *\n     * @return {GetChaptersPromise}\n     */\n  }, {\n    key: \"getChapters\",\n    value: function getChapters() {\n      return this.get('chapters');\n    }\n\n    /**\n     * A promise to get the currently active chapter.\n     *\n     * @promise GetCurrentChaptersPromise\n     * @fulfill {VimeoChapter|undefined} The current chapter for the video.\n     */\n    /**\n     * Get the currently active chapter for the video.\n     *\n     * @return {GetCurrentChaptersPromise}\n     */\n  }, {\n    key: \"getCurrentChapter\",\n    value: function getCurrentChapter() {\n      return this.get('currentChapter');\n    }\n\n    /**\n     * A promise to get the accent color of the player.\n     *\n     * @promise GetColorPromise\n     * @fulfill {string} The hex color of the player.\n     */\n    /**\n     * Get the accent color for this player. Note this is deprecated in place of `getColorTwo`.\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColor\",\n    value: function getColor() {\n      return this.get('color');\n    }\n\n    /**\n     * A promise to get all colors for the player in an array.\n     *\n     * @promise GetColorsPromise\n     * @fulfill {string[]} The hex colors of the player.\n     */\n    /**\n     * Get all the colors for this player in an array: [colorOne, colorTwo, colorThree, colorFour]\n     *\n     * @return {GetColorPromise}\n     */\n  }, {\n    key: \"getColors\",\n    value: function getColors() {\n      return npo_src.all([this.get('colorOne'), this.get('colorTwo'), this.get('colorThree'), this.get('colorFour')]);\n    }\n\n    /**\n     * A promise to set the accent color of the player.\n     *\n     * @promise SetColorPromise\n     * @fulfill {string} The color was successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the accent color of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * Note this is deprecated in place of `setColorTwo`.\n     *\n     * @param {string} color The hex or rgb color string to set.\n     * @return {SetColorPromise}\n     */\n  }, {\n    key: \"setColor\",\n    value: function setColor(color) {\n      return this.set('color', color);\n    }\n\n    /**\n     * A promise to set all colors for the player.\n     *\n     * @promise SetColorsPromise\n     * @fulfill {string[]} The colors were successfully set.\n     * @reject {TypeError} The string was not a valid hex or rgb color.\n     * @reject {ContrastError} The color was set, but the contrast is\n     *         outside of the acceptable range.\n     * @reject {EmbedSettingsError} The owner of the player has chosen to\n     *         use a specific color.\n     */\n    /**\n     * Set the colors of this player to a hex or rgb string. Setting the\n     * color may fail if the owner of the video has set their embed\n     * preferences to force a specific color.\n     * The colors should be passed in as an array: [colorOne, colorTwo, colorThree, colorFour].\n     * If a color should not be set, the index in the array can be left as null.\n     *\n     * @param {string[]} colors Array of the hex or rgb color strings to set.\n     * @return {SetColorsPromise}\n     */\n  }, {\n    key: \"setColors\",\n    value: function setColors(colors) {\n      if (!Array.isArray(colors)) {\n        return new npo_src(function (resolve, reject) {\n          return reject(new TypeError('Argument must be an array.'));\n        });\n      }\n      var nullPromise = new npo_src(function (resolve) {\n        return resolve(null);\n      });\n      var colorPromises = [colors[0] ? this.set('colorOne', colors[0]) : nullPromise, colors[1] ? this.set('colorTwo', colors[1]) : nullPromise, colors[2] ? this.set('colorThree', colors[2]) : nullPromise, colors[3] ? this.set('colorFour', colors[3]) : nullPromise];\n      return npo_src.all(colorPromises);\n    }\n\n    /**\n     * A representation of a cue point.\n     *\n     * @typedef {Object} VimeoCuePoint\n     * @property {number} time The time of the cue point.\n     * @property {object} data The data passed when adding the cue point.\n     * @property {string} id The unique id for use with removeCuePoint.\n     */\n    /**\n     * A promise to get the cue points of a video.\n     *\n     * @promise GetCuePointsPromise\n     * @fulfill {VimeoCuePoint[]} The cue points added to the video.\n     * @reject {UnsupportedError} Cue points are not supported with the current\n     *         player or browser.\n     */\n    /**\n     * Get an array of the cue points added to the video.\n     *\n     * @return {GetCuePointsPromise}\n     */\n  }, {\n    key: \"getCuePoints\",\n    value: function getCuePoints() {\n      return this.get('cuePoints');\n    }\n\n    /**\n     * A promise to get the current time of the video.\n     *\n     * @promise GetCurrentTimePromise\n     * @fulfill {number} The current time in seconds.\n     */\n    /**\n     * Get the current playback position in seconds.\n     *\n     * @return {GetCurrentTimePromise}\n     */\n  }, {\n    key: \"getCurrentTime\",\n    value: function getCurrentTime() {\n      return this.get('currentTime');\n    }\n\n    /**\n     * A promise to set the current time of the video.\n     *\n     * @promise SetCurrentTimePromise\n     * @fulfill {number} The actual current time that was set.\n     * @reject {RangeError} the time was less than 0 or greater than the\n     *         video’s duration.\n     */\n    /**\n     * Set the current playback position in seconds. If the player was\n     * paused, it will remain paused. Likewise, if the player was playing,\n     * it will resume playing once the video has buffered.\n     *\n     * You can provide an accurate time and the player will attempt to seek\n     * to as close to that time as possible. The exact time will be the\n     * fulfilled value of the promise.\n     *\n     * @param {number} currentTime\n     * @return {SetCurrentTimePromise}\n     */\n  }, {\n    key: \"setCurrentTime\",\n    value: function setCurrentTime(currentTime) {\n      return this.set('currentTime', currentTime);\n    }\n\n    /**\n     * A promise to get the duration of the video.\n     *\n     * @promise GetDurationPromise\n     * @fulfill {number} The duration in seconds.\n     */\n    /**\n     * Get the duration of the video in seconds. It will be rounded to the\n     * nearest second before playback begins, and to the nearest thousandth\n     * of a second after playback begins.\n     *\n     * @return {GetDurationPromise}\n     */\n  }, {\n    key: \"getDuration\",\n    value: function getDuration() {\n      return this.get('duration');\n    }\n\n    /**\n     * A promise to get the ended state of the video.\n     *\n     * @promise GetEndedPromise\n     * @fulfill {boolean} Whether or not the video has ended.\n     */\n    /**\n     * Get the ended state of the video. The video has ended if\n     * `currentTime === duration`.\n     *\n     * @return {GetEndedPromise}\n     */\n  }, {\n    key: \"getEnded\",\n    value: function getEnded() {\n      return this.get('ended');\n    }\n\n    /**\n     * A promise to get the loop state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the player is set to loop.\n     */\n    /**\n     * Get the loop state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getLoop\",\n    value: function getLoop() {\n      return this.get('loop');\n    }\n\n    /**\n     * A promise to set the loop state of the player.\n     *\n     * @promise SetLoopPromise\n     * @fulfill {boolean} The loop state that was set.\n     */\n    /**\n     * Set the loop state of the player. When set to `true`, the player\n     * will start over immediately once playback ends.\n     *\n     * @param {boolean} loop\n     * @return {SetLoopPromise}\n     */\n  }, {\n    key: \"setLoop\",\n    value: function setLoop(loop) {\n      return this.set('loop', loop);\n    }\n\n    /**\n     * A promise to set the muted state of the player.\n     *\n     * @promise SetMutedPromise\n     * @fulfill {boolean} The muted state that was set.\n     */\n    /**\n     * Set the muted state of the player. When set to `true`, the player\n     * volume will be muted.\n     *\n     * @param {boolean} muted\n     * @return {SetMutedPromise}\n     */\n  }, {\n    key: \"setMuted\",\n    value: function setMuted(muted) {\n      return this.set('muted', muted);\n    }\n\n    /**\n     * A promise to get the muted state of the player.\n     *\n     * @promise GetMutedPromise\n     * @fulfill {boolean} Whether or not the player is muted.\n     */\n    /**\n     * Get the muted state of the player.\n     *\n     * @return {GetMutedPromise}\n     */\n  }, {\n    key: \"getMuted\",\n    value: function getMuted() {\n      return this.get('muted');\n    }\n\n    /**\n     * A promise to get the paused state of the player.\n     *\n     * @promise GetLoopPromise\n     * @fulfill {boolean} Whether or not the video is paused.\n     */\n    /**\n     * Get the paused state of the player.\n     *\n     * @return {GetLoopPromise}\n     */\n  }, {\n    key: \"getPaused\",\n    value: function getPaused() {\n      return this.get('paused');\n    }\n\n    /**\n     * A promise to get the playback rate of the player.\n     *\n     * @promise GetPlaybackRatePromise\n     * @fulfill {number} The playback rate of the player on a scale from 0 to 2.\n     */\n    /**\n     * Get the playback rate of the player on a scale from `0` to `2`.\n     *\n     * @return {GetPlaybackRatePromise}\n     */\n  }, {\n    key: \"getPlaybackRate\",\n    value: function getPlaybackRate() {\n      return this.get('playbackRate');\n    }\n\n    /**\n     * A promise to set the playbackrate of the player.\n     *\n     * @promise SetPlaybackRatePromise\n     * @fulfill {number} The playback rate was set.\n     * @reject {RangeError} The playback rate was less than 0 or greater than 2.\n     */\n    /**\n     * Set the playback rate of the player on a scale from `0` to `2`. When set\n     * via the API, the playback rate will not be synchronized to other\n     * players or stored as the viewer's preference.\n     *\n     * @param {number} playbackRate\n     * @return {SetPlaybackRatePromise}\n     */\n  }, {\n    key: \"setPlaybackRate\",\n    value: function setPlaybackRate(playbackRate) {\n      return this.set('playbackRate', playbackRate);\n    }\n\n    /**\n     * A promise to get the played property of the video.\n     *\n     * @promise GetPlayedPromise\n     * @fulfill {Array} Played Timeranges converted to an Array.\n     */\n    /**\n     * Get the played property of the video.\n     *\n     * @return {GetPlayedPromise}\n     */\n  }, {\n    key: \"getPlayed\",\n    value: function getPlayed() {\n      return this.get('played');\n    }\n\n    /**\n     * A promise to get the qualities available of the current video.\n     *\n     * @promise GetQualitiesPromise\n     * @fulfill {Array} The qualities of the video.\n     */\n    /**\n     * Get the qualities of the current video.\n     *\n     * @return {GetQualitiesPromise}\n     */\n  }, {\n    key: \"getQualities\",\n    value: function getQualities() {\n      return this.get('qualities');\n    }\n\n    /**\n     * A promise to get the current set quality of the video.\n     *\n     * @promise GetQualityPromise\n     * @fulfill {string} The current set quality.\n     */\n    /**\n     * Get the current set quality of the video.\n     *\n     * @return {GetQualityPromise}\n     */\n  }, {\n    key: \"getQuality\",\n    value: function getQuality() {\n      return this.get('quality');\n    }\n\n    /**\n     * A promise to set the video quality.\n     *\n     * @promise SetQualityPromise\n     * @fulfill {number} The quality was set.\n     * @reject {RangeError} The quality is not available.\n     */\n    /**\n     * Set a video quality.\n     *\n     * @param {string} quality\n     * @return {SetQualityPromise}\n     */\n  }, {\n    key: \"setQuality\",\n    value: function setQuality(quality) {\n      return this.set('quality', quality);\n    }\n\n    /**\n     * A promise to get the remote playback availability.\n     *\n     * @promise RemotePlaybackAvailabilityPromise\n     * @fulfill {boolean} Whether remote playback is available.\n     */\n    /**\n     * Get the availability of remote playback.\n     *\n     * @return {RemotePlaybackAvailabilityPromise}\n     */\n  }, {\n    key: \"getRemotePlaybackAvailability\",\n    value: function getRemotePlaybackAvailability() {\n      return this.get('remotePlaybackAvailability');\n    }\n\n    /**\n     * A promise to get the current remote playback state.\n     *\n     * @promise RemotePlaybackStatePromise\n     * @fulfill {string} The state of the remote playback: connecting, connected, or disconnected.\n     */\n    /**\n     * Get the current remote playback state.\n     *\n     * @return {RemotePlaybackStatePromise}\n     */\n  }, {\n    key: \"getRemotePlaybackState\",\n    value: function getRemotePlaybackState() {\n      return this.get('remotePlaybackState');\n    }\n\n    /**\n     * A promise to get the seekable property of the video.\n     *\n     * @promise GetSeekablePromise\n     * @fulfill {Array} Seekable Timeranges converted to an Array.\n     */\n    /**\n     * Get the seekable property of the video.\n     *\n     * @return {GetSeekablePromise}\n     */\n  }, {\n    key: \"getSeekable\",\n    value: function getSeekable() {\n      return this.get('seekable');\n    }\n\n    /**\n     * A promise to get the seeking property of the player.\n     *\n     * @promise GetSeekingPromise\n     * @fulfill {boolean} Whether or not the player is currently seeking.\n     */\n    /**\n     * Get if the player is currently seeking.\n     *\n     * @return {GetSeekingPromise}\n     */\n  }, {\n    key: \"getSeeking\",\n    value: function getSeeking() {\n      return this.get('seeking');\n    }\n\n    /**\n     * A promise to get the text tracks of a video.\n     *\n     * @promise GetTextTracksPromise\n     * @fulfill {VimeoTextTrack[]} The text tracks associated with the video.\n     */\n    /**\n     * Get an array of the text tracks that exist for the video.\n     *\n     * @return {GetTextTracksPromise}\n     */\n  }, {\n    key: \"getTextTracks\",\n    value: function getTextTracks() {\n      return this.get('textTracks');\n    }\n\n    /**\n     * A promise to get the embed code for the video.\n     *\n     * @promise GetVideoEmbedCodePromise\n     * @fulfill {string} The `<iframe>` embed code for the video.\n     */\n    /**\n     * Get the `<iframe>` embed code for the video.\n     *\n     * @return {GetVideoEmbedCodePromise}\n     */\n  }, {\n    key: \"getVideoEmbedCode\",\n    value: function getVideoEmbedCode() {\n      return this.get('videoEmbedCode');\n    }\n\n    /**\n     * A promise to get the id of the video.\n     *\n     * @promise GetVideoIdPromise\n     * @fulfill {number} The id of the video.\n     */\n    /**\n     * Get the id of the video.\n     *\n     * @return {GetVideoIdPromise}\n     */\n  }, {\n    key: \"getVideoId\",\n    value: function getVideoId() {\n      return this.get('videoId');\n    }\n\n    /**\n     * A promise to get the title of the video.\n     *\n     * @promise GetVideoTitlePromise\n     * @fulfill {number} The title of the video.\n     */\n    /**\n     * Get the title of the video.\n     *\n     * @return {GetVideoTitlePromise}\n     */\n  }, {\n    key: \"getVideoTitle\",\n    value: function getVideoTitle() {\n      return this.get('videoTitle');\n    }\n\n    /**\n     * A promise to get the native width of the video.\n     *\n     * @promise GetVideoWidthPromise\n     * @fulfill {number} The native width of the video.\n     */\n    /**\n     * Get the native width of the currently‐playing video. The width of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoWidthPromise}\n     */\n  }, {\n    key: \"getVideoWidth\",\n    value: function getVideoWidth() {\n      return this.get('videoWidth');\n    }\n\n    /**\n     * A promise to get the native height of the video.\n     *\n     * @promise GetVideoHeightPromise\n     * @fulfill {number} The native height of the video.\n     */\n    /**\n     * Get the native height of the currently‐playing video. The height of\n     * the highest‐resolution available will be used before playback begins.\n     *\n     * @return {GetVideoHeightPromise}\n     */\n  }, {\n    key: \"getVideoHeight\",\n    value: function getVideoHeight() {\n      return this.get('videoHeight');\n    }\n\n    /**\n     * A promise to get the vimeo.com url for the video.\n     *\n     * @promise GetVideoUrlPromise\n     * @fulfill {number} The vimeo.com url for the video.\n     * @reject {PrivacyError} The url isn’t available because of the video’s privacy setting.\n     */\n    /**\n     * Get the vimeo.com url for the video.\n     *\n     * @return {GetVideoUrlPromise}\n     */\n  }, {\n    key: \"getVideoUrl\",\n    value: function getVideoUrl() {\n      return this.get('videoUrl');\n    }\n\n    /**\n     * A promise to get the volume level of the player.\n     *\n     * @promise GetVolumePromise\n     * @fulfill {number} The volume level of the player on a scale from 0 to 1.\n     */\n    /**\n     * Get the current volume level of the player on a scale from `0` to `1`.\n     *\n     * Most mobile devices do not support an independent volume from the\n     * system volume. In those cases, this method will always return `1`.\n     *\n     * @return {GetVolumePromise}\n     */\n  }, {\n    key: \"getVolume\",\n    value: function getVolume() {\n      return this.get('volume');\n    }\n\n    /**\n     * A promise to set the volume level of the player.\n     *\n     * @promise SetVolumePromise\n     * @fulfill {number} The volume was set.\n     * @reject {RangeError} The volume was less than 0 or greater than 1.\n     */\n    /**\n     * Set the volume of the player on a scale from `0` to `1`. When set\n     * via the API, the volume level will not be synchronized to other\n     * players or stored as the viewer’s preference.\n     *\n     * Most mobile devices do not support setting the volume. An error will\n     * *not* be triggered in that situation.\n     *\n     * @param {number} volume\n     * @return {SetVolumePromise}\n     */\n  }, {\n    key: \"setVolume\",\n    value: function setVolume(volume) {\n      return this.set('volume', volume);\n    }\n\n    /** @typedef {import('./lib/timing-object.types').TimingObject} TimingObject */\n    /** @typedef {import('./lib/timing-src-connector.types').TimingSrcConnectorOptions} TimingSrcConnectorOptions */\n    /** @typedef {import('./lib/timing-src-connector').TimingSrcConnector} TimingSrcConnector */\n\n    /**\n     * Connects a TimingObject to the video player (https://webtiming.github.io/timingobject/)\n     *\n     * @param {TimingObject} timingObject\n     * @param {TimingSrcConnectorOptions} options\n     *\n     * @return {Promise<TimingSrcConnector>}\n     */\n  }, {\n    key: \"setTimingSrc\",\n    value: function () {\n      var _setTimingSrc = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(timingObject, options) {\n        var _this6 = this;\n        var connector;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              if (timingObject) {\n                _context.next = 2;\n                break;\n              }\n              throw new TypeError('A Timing Object must be provided.');\n            case 2:\n              _context.next = 4;\n              return this.ready();\n            case 4:\n              connector = new TimingSrcConnector(this, timingObject, options);\n              postMessage(this, 'notifyTimingObjectConnect');\n              connector.addEventListener('disconnect', function () {\n                return postMessage(_this6, 'notifyTimingObjectDisconnect');\n              });\n              return _context.abrupt(\"return\", connector);\n            case 8:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee, this);\n      }));\n      function setTimingSrc(_x, _x2) {\n        return _setTimingSrc.apply(this, arguments);\n      }\n      return setTimingSrc;\n    }()\n  }]);\n  return Player;\n}(); // Setup embed only if this is not a node environment\nif (!isNode) {\n  screenfull = initializeScreenfull();\n  initializeEmbeds();\n  resizeEmbeds();\n  initAppendVideoMetadata();\n  checkUrlTimeParam();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Player);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@vimeo/player/dist/player.es.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/vimeo-video-element/dist/react.js":
/*!********************************************************!*\
  !*** ./node_modules/vimeo-video-element/dist/react.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _vimeo_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./vimeo-video-element.js */ \"(app-pages-browser)/./node_modules/vimeo-video-element/dist/vimeo-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"vimeo-video\",\n    elementClass: _vimeo_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/vimeo-video-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/vimeo-video-element/dist/vimeo-video-element.js":
/*!**********************************************************************!*\
  !*** ./node_modules/vimeo-video-element/dist/vimeo-video-element.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ vimeo_video_element_default)\n/* harmony export */ });\n/* harmony import */ var _vimeo_player_dist_player_es_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @vimeo/player/dist/player.es.js */ \"(app-pages-browser)/./node_modules/@vimeo/player/dist/player.es.js\");\n\nconst EMBED_BASE = \"https://player.vimeo.com/video\";\nconst MATCH_SRC = /vimeo\\.com\\/(?:video\\/)?(\\d+)(?:\\/([\\w-]+))?/;\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: 0,\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n      }\n      :host(:not([controls])) {\n        pointer-events: none;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  if (!attrs.src) return;\n  const matches = attrs.src.match(MATCH_SRC);\n  const srcId = matches && matches[1];\n  const hParam = matches && matches[2];\n  const params = {\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : 0,\n    autoplay: attrs.autoplay,\n    loop: attrs.loop,\n    muted: attrs.muted,\n    playsinline: attrs.playsinline,\n    preload: attrs.preload ?? \"metadata\",\n    transparent: false,\n    autopause: attrs.autopause,\n    h: hParam,\n    // This param is required when the video is Unlisted.\n    ...props.config\n  };\n  return `${EMBED_BASE}/${srcId}?${serialize(params)}`;\n}\nclass VimeoVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\n    \"autoplay\",\n    \"controls\",\n    \"crossorigin\",\n    \"loop\",\n    \"muted\",\n    \"playsinline\",\n    \"poster\",\n    \"preload\",\n    \"src\"\n  ];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #isInit;\n  #currentTime = 0;\n  #duration = NaN;\n  #muted = false;\n  #paused = !this.autoplay;\n  #playbackRate = 1;\n  #progress = 0;\n  #readyState = 0;\n  #seeking = false;\n  #volume = 1;\n  #videoWidth = NaN;\n  #videoHeight = NaN;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  requestFullscreen() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.requestFullscreen) == null ? void 0 : _b.call(_a);\n  }\n  exitFullscreen() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.exitFullscreen) == null ? void 0 : _b.call(_a);\n  }\n  requestPictureInPicture() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.requestPictureInPicture) == null ? void 0 : _b.call(_a);\n  }\n  exitPictureInPicture() {\n    var _a, _b;\n    return (_b = (_a = this.api) == null ? void 0 : _a.exitPictureInPicture) == null ? void 0 : _b.call(_a);\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    var _a;\n    if (this.#loadRequested) return;\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#currentTime = 0;\n    this.#duration = NaN;\n    this.#muted = false;\n    this.#paused = !this.autoplay;\n    this.#playbackRate = 1;\n    this.#progress = 0;\n    this.#readyState = 0;\n    this.#seeking = false;\n    this.#volume = 1;\n    this.#readyState = 0;\n    this.#videoWidth = NaN;\n    this.#videoHeight = NaN;\n    this.dispatchEvent(new Event(\"emptied\"));\n    let oldApi = this.api;\n    this.api = null;\n    if (!this.src) {\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    const options = {\n      autoplay: this.autoplay,\n      controls: this.controls,\n      loop: this.loop,\n      muted: this.defaultMuted,\n      playsinline: this.playsInline,\n      preload: this.preload ?? \"metadata\",\n      transparent: false,\n      autopause: this.hasAttribute(\"autopause\"),\n      ...this.#config\n    };\n    const onLoaded = async () => {\n      this.#readyState = 1;\n      this.dispatchEvent(new Event(\"loadedmetadata\"));\n      if (this.api) {\n        this.#muted = await this.api.getMuted();\n        this.#volume = await this.api.getVolume();\n        this.dispatchEvent(new Event(\"volumechange\"));\n        this.#duration = await this.api.getDuration();\n        this.dispatchEvent(new Event(\"durationchange\"));\n      }\n      this.dispatchEvent(new Event(\"loadcomplete\"));\n      this.loadComplete.resolve();\n    };\n    if (this.#isInit) {\n      this.api = oldApi;\n      await this.api.loadVideo({\n        ...options,\n        url: this.src\n      });\n      await onLoaded();\n      await this.loadComplete;\n      return;\n    }\n    this.#isInit = true;\n    let iframe = (_a = this.shadowRoot) == null ? void 0 : _a.querySelector(\"iframe\");\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n      this.shadowRoot.innerHTML = getTemplateHTML(namedNodeMapToObject(this.attributes), this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.api = new _vimeo_player_dist_player_es_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](iframe);\n    const onceLoaded = () => {\n      this.api.off(\"loaded\", onceLoaded);\n      onLoaded();\n    };\n    this.api.on(\"loaded\", onceLoaded);\n    this.api.on(\"bufferstart\", () => {\n      if (this.#paused) {\n        this.#paused = false;\n        this.dispatchEvent(new Event(\"play\"));\n      }\n      this.dispatchEvent(new Event(\"waiting\"));\n    });\n    this.api.on(\"play\", () => {\n      if (!this.#paused) return;\n      this.#paused = false;\n      this.dispatchEvent(new Event(\"play\"));\n    });\n    this.api.on(\"playing\", () => {\n      this.#readyState = 3;\n      this.#paused = false;\n      this.dispatchEvent(new Event(\"playing\"));\n    });\n    this.api.on(\"seeking\", () => {\n      this.#seeking = true;\n      this.dispatchEvent(new Event(\"seeking\"));\n    });\n    this.api.on(\"seeked\", () => {\n      this.#seeking = false;\n      this.dispatchEvent(new Event(\"seeked\"));\n    });\n    this.api.on(\"pause\", () => {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"pause\"));\n    });\n    this.api.on(\"ended\", () => {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"ended\"));\n    });\n    this.api.on(\"ratechange\", ({ playbackRate }) => {\n      this.#playbackRate = playbackRate;\n      this.dispatchEvent(new Event(\"ratechange\"));\n    });\n    this.api.on(\"volumechange\", async ({ volume }) => {\n      this.#volume = volume;\n      if (this.api) {\n        this.#muted = await this.api.getMuted();\n      }\n      this.dispatchEvent(new Event(\"volumechange\"));\n    });\n    this.api.on(\"durationchange\", ({ duration }) => {\n      this.#duration = duration;\n      this.dispatchEvent(new Event(\"durationchange\"));\n    });\n    this.api.on(\"timeupdate\", ({ seconds }) => {\n      this.#currentTime = seconds;\n      this.dispatchEvent(new Event(\"timeupdate\"));\n    });\n    this.api.on(\"progress\", ({ seconds }) => {\n      this.#progress = seconds;\n      this.dispatchEvent(new Event(\"progress\"));\n    });\n    this.api.on(\"resize\", ({ videoWidth, videoHeight }) => {\n      this.#videoWidth = videoWidth;\n      this.#videoHeight = videoHeight;\n      this.dispatchEvent(new Event(\"resize\"));\n    });\n    await this.loadComplete;\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"autoplay\":\n      case \"controls\":\n      case \"src\": {\n        this.load();\n        return;\n      }\n    }\n    await this.loadComplete;\n    switch (attrName) {\n      case \"loop\": {\n        this.api.setLoop(this.loop);\n        break;\n      }\n    }\n  }\n  async play() {\n    var _a;\n    this.#paused = false;\n    this.dispatchEvent(new Event(\"play\"));\n    await this.loadComplete;\n    try {\n      await ((_a = this.api) == null ? void 0 : _a.play());\n    } catch (error) {\n      this.#paused = true;\n      this.dispatchEvent(new Event(\"pause\"));\n      throw error;\n    }\n  }\n  async pause() {\n    var _a;\n    await this.loadComplete;\n    return (_a = this.api) == null ? void 0 : _a.pause();\n  }\n  get ended() {\n    return this.#currentTime >= this.#duration;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get videoWidth() {\n    return this.#videoWidth;\n  }\n  get videoHeight() {\n    return this.#videoHeight;\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(val) {\n    if (this.src == val) return;\n    this.setAttribute(\"src\", val);\n  }\n  get paused() {\n    return this.#paused;\n  }\n  get duration() {\n    return this.#duration;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get buffered() {\n    if (this.#progress > 0) {\n      return createTimeRanges(0, this.#progress);\n    }\n    return createTimeRanges();\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    return this.#currentTime;\n  }\n  set currentTime(val) {\n    if (this.currentTime == val) return;\n    this.#currentTime = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setCurrentTime(val).catch(() => {\n      });\n    });\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    if (this.defaultMuted == val) return;\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    if (this.loop == val) return;\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    if (this.muted == val) return;\n    this.#muted = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setMuted(val).catch(() => {\n      });\n    });\n  }\n  get playbackRate() {\n    return this.#playbackRate;\n  }\n  set playbackRate(val) {\n    if (this.playbackRate == val) return;\n    this.#playbackRate = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setPlaybackRate(val).catch(() => {\n      });\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    if (this.playsInline == val) return;\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  get poster() {\n    return this.getAttribute(\"poster\");\n  }\n  set poster(val) {\n    if (this.poster == val) return;\n    this.setAttribute(\"poster\", `${val}`);\n  }\n  get volume() {\n    return this.#volume;\n  }\n  set volume(val) {\n    if (this.volume == val) return;\n    this.#volume = val;\n    this.loadComplete.then(() => {\n      var _a;\n      (_a = this.api) == null ? void 0 : _a.setVolume(val).catch(() => {\n      });\n    });\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(boolToBinary(props)));\n}\nfunction boolToBinary(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = 1;\n    else if (val === false) p[key] = 0;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"vimeo-video\")) {\n  globalThis.customElements.define(\"vimeo-video\", VimeoVideoElement);\n}\nvar vimeo_video_element_default = VimeoVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/vimeo-video-element/dist/vimeo-video-element.js\n"));

/***/ })

}]);