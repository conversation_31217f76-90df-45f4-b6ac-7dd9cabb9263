"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerWistia"],{

/***/ "(app-pages-browser)/./node_modules/super-media-element/super-media-element.js":
/*!*****************************************************************!*\
  !*** ./node_modules/super-media-element/super-media-element.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Events: () => (/* binding */ Events),\n/* harmony export */   SuperAudioElement: () => (/* binding */ SuperAudioElement),\n/* harmony export */   SuperMediaMixin: () => (/* binding */ SuperMediaMixin),\n/* harmony export */   SuperVideoElement: () => (/* binding */ SuperVideoElement),\n/* harmony export */   template: () => (/* binding */ template)\n/* harmony export */ });\n/**\n * Super Media Element\n * Based on https://github.com/muxinc/custom-video-element - Mux - MIT License\n *\n * The goal is to create an element that works just like the video element\n * but can be extended/sub-classed, because native elements cannot be\n * extended today across browsers. Support for extending async loaded video\n * like API's. e.g. video players.\n */\n\n// The onevent like props are weirdly set on the HTMLElement prototype with other\n// generic events making it impossible to pick these specific to HTMLMediaElement.\nconst Events = [\n  'abort',\n  'canplay',\n  'canplaythrough',\n  'durationchange',\n  'emptied',\n  'encrypted',\n  'ended',\n  'error',\n  'loadeddata',\n  'loadedmetadata',\n  'loadstart',\n  'pause',\n  'play',\n  'playing',\n  'progress',\n  'ratechange',\n  'seeked',\n  'seeking',\n  'stalled',\n  'suspend',\n  'timeupdate',\n  'volumechange',\n  'waiting',\n  'waitingforkey',\n  'resize',\n  'enterpictureinpicture',\n  'leavepictureinpicture',\n  'webkitbeginfullscreen',\n  'webkitendfullscreen',\n  'webkitpresentationmodechanged',\n];\n\nconst template = globalThis.document?.createElement('template');\n\nif (template) {\n  template.innerHTML = /*html*/`\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n      }\n\n      video,\n      audio {\n        max-width: 100%;\n        max-height: 100%;\n        min-width: 100%;\n        min-height: 100%;\n      }\n    </style>\n    <slot></slot>\n  `;\n}\n\n/**\n * @see https://justinfagnani.com/2015/12/21/real-mixins-with-javascript-classes/\n */\nconst SuperMediaMixin = (superclass, { tag, is }) => {\n\n  const nativeElTest = globalThis.document?.createElement(tag, { is });\n  const nativeElProps = nativeElTest ? getNativeElProps(nativeElTest) : [];\n\n  return class SuperMedia extends superclass {\n    static Events = Events;\n    static template = template;\n    static skipAttributes = [];\n    static #isDefined;\n\n    static get observedAttributes() {\n      SuperMedia.#define();\n\n      // Include any attributes from the custom built-in.\n      const natAttrs = nativeElTest?.constructor?.observedAttributes ?? [];\n\n      return [\n        ...natAttrs,\n        'autopictureinpicture',\n        'disablepictureinpicture',\n        'disableremoteplayback',\n        'autoplay',\n        'controls',\n        'controlslist',\n        'crossorigin',\n        'loop',\n        'muted',\n        'playsinline',\n        'poster',\n        'preload',\n        'src',\n      ];\n    }\n\n    static #define() {\n      if (this.#isDefined) return;\n      this.#isDefined = true;\n\n      const propsToAttrs = new Set(this.observedAttributes);\n      // defaultMuted maps to the muted attribute, handled manually below.\n      propsToAttrs.delete('muted');\n\n      // Passthrough native el functions from the custom el to the native el\n      for (let prop of nativeElProps) {\n        if (prop in this.prototype) continue;\n\n        const type = typeof nativeElTest[prop];\n        if (type == 'function') {\n          // Function\n          this.prototype[prop] = function (...args) {\n            this.#init();\n\n            const fn = () => {\n              if (this.call) return this.call(prop, ...args);\n              return this.nativeEl[prop].apply(this.nativeEl, args);\n            };\n\n            if (this.loadComplete && !this.isLoaded) {\n              return this.loadComplete.then(fn);\n            }\n            return fn();\n          };\n        } else {\n          // Some properties like src, preload, defaultMuted are handled manually.\n\n          // Getter\n          let config = {\n            get() {\n              this.#init();\n\n              let attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                const val = this.getAttribute(attr);\n                return val === null ? false : val === '' ? true : val;\n              }\n\n              return this.get?.(prop) ?? this.nativeEl?.[prop] ?? this.#standinEl[prop];\n            },\n          };\n\n          if (prop !== prop.toUpperCase()) {\n            // Setter (not a CONSTANT)\n            config.set = async function (val) {\n              this.#init();\n\n              let attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                if (val === true || val === false || val == null) {\n                  this.toggleAttribute(attr, Boolean(val));\n                } else {\n                  this.setAttribute(attr, val);\n                }\n                return;\n              }\n\n              if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n              if (this.set) {\n                this.set(prop, val);\n                return;\n              }\n\n              this.nativeEl[prop] = val;\n            };\n          }\n\n          Object.defineProperty(this.prototype, prop, config);\n        }\n      }\n    }\n\n    #isInit;\n    #loadComplete;\n    #hasLoaded = false;\n    #isLoaded = false;\n    #nativeEl;\n    #standinEl;\n\n    constructor() {\n      super();\n\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: 'open' });\n        this.shadowRoot.append(this.constructor.template.content.cloneNode(true));\n      }\n\n      // If a load method is provided in the child class create a load promise.\n      if (this.load !== SuperMedia.prototype.load) {\n        this.loadComplete = new PublicPromise();\n      }\n\n      // If the custom element is defined before the custom element's HTML is parsed\n      // no attributes will be available in the constructor (construction process).\n      // Wait until initializing in the attributeChangedCallback or\n      // connectedCallback or accessing any properties.\n    }\n\n    get loadComplete() {\n      return this.#loadComplete;\n    }\n\n    set loadComplete(promise) {\n      this.#isLoaded = false;\n      this.#loadComplete = promise;\n      promise?.then(() => {\n        this.#isLoaded = true;\n      });\n    }\n\n    get isLoaded() {\n      return this.#isLoaded;\n    }\n\n    get nativeEl() {\n      return this.#nativeEl\n        ?? this.shadowRoot.querySelector(tag)\n        ?? this.querySelector(tag);\n    }\n\n    set nativeEl(val) {\n      this.#nativeEl = val;\n    }\n\n    get defaultMuted() {\n      return this.hasAttribute('muted');\n    }\n\n    set defaultMuted(val) {\n      this.toggleAttribute('muted', Boolean(val));\n    }\n\n    get src() {\n      return this.getAttribute('src');\n    }\n\n    set src(val) {\n      this.setAttribute('src', `${val}`);\n    }\n\n    get preload() {\n      return this.getAttribute('preload') ?? this.nativeEl?.preload;\n    }\n\n    set preload(val) {\n      this.setAttribute('preload', `${val}`);\n    }\n\n    async #init() {\n      if (this.#isInit) return;\n      this.#isInit = true;\n\n      this.#initStandinEl();\n      this.#initNativeEl();\n\n      for (let prop of nativeElProps)\n        this.#upgradeProperty(prop);\n\n      // Keep some native child elements like track and source in sync.\n      const childMap = new Map();\n      // An unnamed <slot> will be filled with all of the custom element's\n      // top-level child nodes that do not have the slot attribute.\n      const slotEl = this.shadowRoot.querySelector('slot:not([name])');\n      slotEl?.addEventListener('slotchange', () => {\n        const removeNativeChildren = new Map(childMap);\n        slotEl\n          .assignedElements()\n          .filter((el) => ['track', 'source'].includes(el.localName))\n          .forEach(async (el) => {\n            // If the source or track is still in the assigned elements keep it.\n            removeNativeChildren.delete(el);\n            // Re-use clones if possible.\n            let clone = childMap.get(el);\n            if (!clone) {\n              clone = el.cloneNode();\n              childMap.set(el, clone);\n            }\n            if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n            this.nativeEl.append?.(clone);\n          });\n        removeNativeChildren.forEach((el) => el.remove());\n      });\n\n      // The video events are dispatched on the SuperMediaElement instance.\n      // This makes it possible to add event listeners before the element is upgraded.\n      for (let type of this.constructor.Events) {\n        this.shadowRoot.addEventListener?.(type, (evt) => {\n          if (evt.target !== this.nativeEl) return;\n          this.dispatchEvent(new CustomEvent(evt.type, { detail: evt.detail }));\n        }, true);\n      }\n    }\n\n    #upgradeProperty(prop) {\n      // Sets properties that are set before the custom element is upgraded.\n      // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        // Delete the set property from this instance.\n        delete this[prop];\n        // Set the value again via the (prototype) setter on this class.\n        this[prop] = value;\n      }\n    }\n\n    #initStandinEl() {\n      // Neither Chrome or Firefox support setting the muted attribute\n      // after using document.createElement.\n      // Get around this by setting the muted property manually.\n      const dummyEl = document.createElement(tag, { is });\n      dummyEl.muted = this.hasAttribute('muted');\n\n      for (let { name, value } of this.attributes) {\n        dummyEl.setAttribute(name, value);\n      }\n\n      this.#standinEl = {};\n      for (let name of getNativeElProps(dummyEl)) {\n        this.#standinEl[name] = dummyEl[name];\n      }\n\n      // unload dummy video element\n      dummyEl.removeAttribute('src');\n      dummyEl.load();\n    }\n\n    async #initNativeEl() {\n      if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n      // If there is no nativeEl by now, create it our bloody selves.\n      if (!this.nativeEl) {\n        const nativeEl = document.createElement(tag, { is });\n        nativeEl.part = tag;\n        this.shadowRoot.append(nativeEl);\n      }\n\n      // Neither Chrome or Firefox support setting the muted attribute\n      // after using document.createElement.\n      // Get around this by setting the muted property manually.\n      this.nativeEl.muted = this.hasAttribute('muted');\n    }\n\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      // Initialize right after construction when the attributes become available.\n      this.#init();\n\n      // Only call loadSrc when the super class has a load method.\n      if (attrName === 'src' && this.load !== SuperMedia.prototype.load) {\n        this.#loadSrc();\n      }\n\n      this.#forwardAttribute(attrName, oldValue, newValue);\n    }\n\n    async #loadSrc() {\n      // The first time we use the Promise created in the constructor.\n      if (this.#hasLoaded) this.loadComplete = new PublicPromise();\n      this.#hasLoaded = true;\n\n      // Wait 1 tick to allow other attributes to be set.\n      await Promise.resolve();\n      await this.load();\n\n      this.loadComplete?.resolve();\n      await this.loadComplete;\n    }\n\n    async #forwardAttribute(attrName, oldValue, newValue) {\n      if (this.loadComplete && !this.isLoaded) await this.loadComplete;\n\n      // Ignore a few that don't need to be passed & skipped attributes.\n      // e.g. src: native element is using MSE and has a blob url as src attribute.\n      if (['id', 'class', ...this.constructor.skipAttributes].includes(attrName)) {\n        return;\n      }\n\n      if (newValue === null) {\n        this.nativeEl.removeAttribute?.(attrName);\n      } else {\n        this.nativeEl.setAttribute?.(attrName, newValue);\n      }\n    }\n\n    connectedCallback() {\n      this.#init();\n    }\n  };\n};\n\nfunction getNativeElProps(nativeElTest) {\n  // Map all native element properties to the custom element\n  // so that they're applied to the native element.\n  // Skipping HTMLElement because of things like \"attachShadow\"\n  // causing issues. Most of those props still need to apply to\n  // the custom element.\n  let nativeElProps = [];\n\n  // Walk the prototype chain up to HTMLElement.\n  // This will grab all super class props in between.\n  // i.e. VideoElement and MediaElement\n  for (\n    let proto = Object.getPrototypeOf(nativeElTest);\n    proto && proto !== HTMLElement.prototype;\n    proto = Object.getPrototypeOf(proto)\n  ) {\n    nativeElProps.push(...Object.getOwnPropertyNames(proto));\n  }\n\n  return nativeElProps;\n}\n\n/**\n * A utility to create Promises with convenient public resolve and reject methods.\n * @return {Promise}\n */\nclass PublicPromise extends Promise {\n  constructor(executor = () => {}) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\n\nconst SuperVideoElement = globalThis.document ? SuperMediaMixin(HTMLElement, { tag: 'video' }) : class {};\n\nconst SuperAudioElement = globalThis.document ? SuperMediaMixin(HTMLElement, { tag: 'audio' }) : class {};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/super-media-element/super-media-element.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/wistia-video-element/dist/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/wistia-video-element/dist/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _wistia_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./wistia-video-element.js */ \"(app-pages-browser)/./node_modules/wistia-video-element/dist/wistia-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"wistia-video\",\n    elementClass: _wistia_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/wistia-video-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/wistia-video-element/dist/wistia-video-element.js":
/*!************************************************************************!*\
  !*** ./node_modules/wistia-video-element/dist/wistia-video-element.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ wistia_video_element_default),\n/* harmony export */   uniqueId: () => (/* binding */ uniqueId)\n/* harmony export */ });\n/* harmony import */ var super_media_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! super-media-element */ \"(app-pages-browser)/./node_modules/super-media-element/super-media-element.js\");\nvar _a, _b;\n\nconst templateLightDOM = (_a = globalThis.document) == null ? void 0 : _a.createElement(\"template\");\nif (templateLightDOM) {\n  templateLightDOM.innerHTML = /*html*/\n  `\n  <div class=\"wistia_embed\"></div>\n  `;\n}\nconst templateShadowDOM = (_b = globalThis.document) == null ? void 0 : _b.createElement(\"template\");\nif (templateShadowDOM) {\n  templateShadowDOM.innerHTML = /*html*/\n  `\n  <style>\n    :host {\n      display: inline-block;\n      min-width: 300px;\n      min-height: 150px;\n      position: relative;\n    }\n    ::slotted(.wistia_embed) {\n      position: absolute;\n      width: 100%;\n      height: 100%;\n    }\n  </style>\n  <slot></slot>\n  `;\n}\nclass WistiaVideoElement extends super_media_element__WEBPACK_IMPORTED_MODULE_0__.SuperVideoElement {\n  static template = templateShadowDOM;\n  static skipAttributes = [\"src\"];\n  get nativeEl() {\n    var _a2;\n    return ((_a2 = this.api) == null ? void 0 : _a2.elem()) ?? this.querySelector(\"video\");\n  }\n  async load() {\n    var _a2;\n    (_a2 = this.querySelector(\".wistia_embed\")) == null ? void 0 : _a2.remove();\n    if (!this.src) {\n      return;\n    }\n    await new Promise((resolve) => setTimeout(resolve, 50));\n    const MATCH_SRC = /(?:wistia\\.com|wi\\.st)\\/(?:medias|embed)\\/(.*)$/i;\n    const id = this.src.match(MATCH_SRC)[1];\n    const options = {\n      autoPlay: this.autoplay,\n      preload: this.preload ?? \"metadata\",\n      playsinline: this.playsInline,\n      endVideoBehavior: this.loop && \"loop\",\n      chromeless: !this.controls,\n      playButton: this.controls,\n      muted: this.defaultMuted\n    };\n    this.append(templateLightDOM.content.cloneNode(true));\n    const div = this.querySelector(\".wistia_embed\");\n    if (!div.id) div.id = uniqueId(id);\n    div.classList.add(`wistia_async_${id}`);\n    const scriptUrl = \"https://fast.wistia.com/assets/external/E-v1.js\";\n    await loadScript(scriptUrl, \"Wistia\");\n    this.api = await new Promise((onReady) => {\n      globalThis._wq.push({\n        id: div.id,\n        onReady,\n        options\n      });\n    });\n  }\n  async attributeChangedCallback(attrName, oldValue, newValue) {\n    if (attrName === \"controls\") {\n      await this.loadComplete;\n      switch (attrName) {\n        case \"controls\":\n          this.api.bigPlayButtonEnabled(this.controls);\n          this.controls ? this.api.releaseChromeless() : this.api.requestChromeless();\n          break;\n      }\n      return;\n    }\n    super.attributeChangedCallback(attrName, oldValue, newValue);\n  }\n  // Override some methods w/ defaults if the video element is not ready yet when called.\n  // Some methods require the Wistia API instead of the native video element API.\n  get duration() {\n    var _a2;\n    return (_a2 = this.api) == null ? void 0 : _a2.duration();\n  }\n  play() {\n    this.api.play();\n    return new Promise((resolve) => this.addEventListener(\"playing\", resolve));\n  }\n}\nconst loadScriptCache = {};\nasync function loadScript(src, globalName) {\n  if (!globalName) return import(\n    /* webpackIgnore: true */\n    src\n  );\n  if (loadScriptCache[src]) return loadScriptCache[src];\n  if (self[globalName]) return self[globalName];\n  return loadScriptCache[src] = new Promise((resolve, reject) => {\n    const script = document.createElement(\"script\");\n    script.defer = true;\n    script.src = src;\n    script.onload = () => resolve(self[globalName]);\n    script.onerror = reject;\n    document.head.append(script);\n  });\n}\nlet idCounter = 0;\nfunction uniqueId(prefix) {\n  const id = ++idCounter;\n  return `${prefix}${id}`;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"wistia-video\")) {\n  globalThis.customElements.define(\"wistia-video\", WistiaVideoElement);\n}\nvar wistia_video_element_default = WistiaVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/wistia-video-element/dist/wistia-video-element.js\n"));

/***/ })

}]);