"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerTwitch"],{

/***/ "(app-pages-browser)/./node_modules/twitch-video-element/dist/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/twitch-video-element/dist/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _twitch_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./twitch-video-element.js */ \"(app-pages-browser)/./node_modules/twitch-video-element/dist/twitch-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"twitch-video\",\n    elementClass: _twitch_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/twitch-video-element/dist/react.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/twitch-video-element/dist/twitch-video-element.js":
/*!************************************************************************!*\
  !*** ./node_modules/twitch-video-element/dist/twitch-video-element.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ twitch_video_element_default)\n/* harmony export */ });\nconst EMBED_BASE = \"https://player.twitch.tv\";\nconst MATCH_VIDEO = /(?:www\\.|go\\.)?twitch\\.tv\\/(?:videos?\\/|\\?video=)(\\d+)($|\\?)/;\nconst MATCH_CHANNEL = /(?:www\\.|go\\.)?twitch\\.tv\\/([a-zA-Z0-9_]+)($|\\?)/;\nconst PlaybackState = {\n  IDLE: \"Idle\",\n  READY: \"Ready\",\n  BUFFERING: \"Buffering\",\n  PLAYING: \"Playing\",\n  ENDED: \"Ended\"\n};\nconst PlayerCommands = {\n  DISABLE_CAPTIONS: 0,\n  ENABLE_CAPTIONS: 1,\n  PAUSE: 2,\n  PLAY: 3,\n  SEEK: 4,\n  SET_CHANNEL: 5,\n  SET_CHANNEL_ID: 6,\n  SET_COLLECTION: 7,\n  SET_QUALITY: 8,\n  SET_VIDEO: 9,\n  SET_MUTED: 10,\n  SET_VOLUME: 11\n};\nfunction getTemplateHTML(attrs, props = {}) {\n  const iframeAttrs = {\n    src: serializeIframeUrl(attrs, props),\n    frameborder: \"0\",\n    width: \"100%\",\n    height: \"100%\",\n    allow: \"accelerometer; fullscreen; autoplay; encrypted-media; picture-in-picture;\",\n    sandbox: \"allow-modals allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox\",\n    scrolling: \"no\"\n  };\n  if (props.config) {\n    iframeAttrs[\"data-config\"] = JSON.stringify(props.config);\n  }\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        min-width: 300px;\n        min-height: 150px;\n        position: relative;\n      }\n      iframe {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n      }\n      :host(:not([controls])) {\n        pointer-events: none;\n      }\n    </style>\n    <iframe${serializeAttributes(iframeAttrs)}></iframe>\n  `\n  );\n}\nfunction serializeIframeUrl(attrs, props) {\n  var _a;\n  if (!attrs.src) return;\n  const videoMatch = attrs.src.match(MATCH_VIDEO);\n  const channelMatch = attrs.src.match(MATCH_CHANNEL);\n  const params = {\n    parent: (_a = globalThis.location) == null ? void 0 : _a.hostname,\n    // ?controls=true is enabled by default in the iframe\n    controls: attrs.controls === \"\" ? null : false,\n    autoplay: attrs.autoplay === \"\" ? null : false,\n    muted: attrs.muted,\n    preload: attrs.preload,\n    ...props.config\n  };\n  if (videoMatch) {\n    const videoId = videoMatch[1];\n    return `${EMBED_BASE}/?video=v${videoId}&${serialize(params)}`;\n  } else if (channelMatch) {\n    const channel = channelMatch[1];\n    return `${EMBED_BASE}/?channel=${channel}&${serialize(params)}`;\n  }\n  return \"\";\n}\nclass TwitchVideoElement extends (globalThis.HTMLElement ?? class {\n}) {\n  static getTemplateHTML = getTemplateHTML;\n  static shadowRootOptions = { mode: \"open\" };\n  static observedAttributes = [\"autoplay\", \"controls\", \"loop\", \"muted\", \"playsinline\", \"preload\", \"src\"];\n  loadComplete = new PublicPromise();\n  #loadRequested;\n  #hasLoaded;\n  #iframe;\n  #playerState = {};\n  #currentTime = 0;\n  #muted = false;\n  #volume = 1;\n  #paused = !this.autoplay;\n  #seeking = false;\n  #readyState = 0;\n  #config = null;\n  constructor() {\n    super();\n    this.#upgradeProperty(\"config\");\n  }\n  get config() {\n    return this.#config;\n  }\n  set config(value) {\n    this.#config = value;\n  }\n  async load() {\n    if (this.#loadRequested) return;\n    if (!this.shadowRoot) {\n      this.attachShadow({ mode: \"open\" });\n    }\n    const isFirstLoad = !this.#hasLoaded;\n    if (this.#hasLoaded) {\n      this.loadComplete = new PublicPromise();\n    }\n    this.#hasLoaded = true;\n    await (this.#loadRequested = Promise.resolve());\n    this.#loadRequested = null;\n    this.#readyState = 0;\n    this.dispatchEvent(new Event(\"emptied\"));\n    if (!this.src) {\n      this.shadowRoot.innerHTML = \"\";\n      globalThis.removeEventListener(\"message\", this.#onMessage);\n      return;\n    }\n    this.dispatchEvent(new Event(\"loadstart\"));\n    let iframe = this.shadowRoot.querySelector(\"iframe\");\n    const attrs = namedNodeMapToObject(this.attributes);\n    if (isFirstLoad && iframe) {\n      this.#config = JSON.parse(iframe.getAttribute(\"data-config\") || \"{}\");\n    }\n    if (!(iframe == null ? void 0 : iframe.src) || iframe.src !== serializeIframeUrl(attrs, this)) {\n      this.shadowRoot.innerHTML = getTemplateHTML(attrs, this);\n      iframe = this.shadowRoot.querySelector(\"iframe\");\n    }\n    this.#iframe = iframe;\n    globalThis.addEventListener(\"message\", this.#onMessage);\n  }\n  attributeChangedCallback(attrName, oldValue, newValue) {\n    if (oldValue === newValue) return;\n    switch (attrName) {\n      case \"src\":\n      case \"controls\": {\n        this.load();\n        break;\n      }\n    }\n  }\n  get src() {\n    return this.getAttribute(\"src\");\n  }\n  set src(value) {\n    this.setAttribute(\"src\", value);\n  }\n  get readyState() {\n    return this.#readyState;\n  }\n  get seeking() {\n    return this.#seeking;\n  }\n  get buffered() {\n    var _a, _b;\n    return createTimeRanges(0, ((_b = (_a = this.#playerState.stats) == null ? void 0 : _a.videoStats) == null ? void 0 : _b.bufferSize) ?? 0);\n  }\n  get paused() {\n    if (!this.#playerState.playback) return this.#paused;\n    return this.#playerState.playback === PlaybackState.IDLE;\n  }\n  get ended() {\n    if (!this.#playerState.playback) return false;\n    return this.#playerState.playback === PlaybackState.ENDED;\n  }\n  get duration() {\n    return this.#playerState.duration ?? NaN;\n  }\n  get autoplay() {\n    return this.hasAttribute(\"autoplay\");\n  }\n  set autoplay(val) {\n    if (this.autoplay == val) return;\n    this.toggleAttribute(\"autoplay\", Boolean(val));\n  }\n  get controls() {\n    return this.hasAttribute(\"controls\");\n  }\n  set controls(val) {\n    if (this.controls == val) return;\n    this.toggleAttribute(\"controls\", Boolean(val));\n  }\n  get currentTime() {\n    if (!this.#playerState.currentTime) return this.#currentTime;\n    return this.#playerState.currentTime;\n  }\n  set currentTime(val) {\n    this.#currentTime = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SEEK, val);\n    });\n  }\n  get defaultMuted() {\n    return this.hasAttribute(\"muted\");\n  }\n  set defaultMuted(val) {\n    this.toggleAttribute(\"muted\", Boolean(val));\n  }\n  get loop() {\n    return this.hasAttribute(\"loop\");\n  }\n  set loop(val) {\n    this.toggleAttribute(\"loop\", Boolean(val));\n  }\n  get muted() {\n    return this.#muted;\n  }\n  set muted(val) {\n    this.#muted = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SET_MUTED, val);\n    });\n  }\n  get volume() {\n    return this.#volume;\n  }\n  set volume(val) {\n    this.#volume = val;\n    this.loadComplete.then(() => {\n      this.#sendCommand(PlayerCommands.SET_VOLUME, val);\n    });\n  }\n  get playsInline() {\n    return this.hasAttribute(\"playsinline\");\n  }\n  set playsInline(val) {\n    this.toggleAttribute(\"playsinline\", Boolean(val));\n  }\n  play() {\n    this.#paused = false;\n    this.#sendCommand(PlayerCommands.PLAY);\n  }\n  pause() {\n    this.#paused = true;\n    this.#sendCommand(PlayerCommands.PAUSE);\n  }\n  #onMessage = async (event) => {\n    var _a, _b, _c, _d;\n    if (!this.#iframe.contentWindow) return;\n    const { data, source } = event;\n    const isFromEmbedWindow = source === this.#iframe.contentWindow;\n    if (!isFromEmbedWindow) return;\n    if (data.namespace === \"twitch-embed\") {\n      await new Promise((resolve) => setTimeout(resolve, 10));\n      if (data.eventName === \"ready\") {\n        this.dispatchEvent(new Event(\"loadcomplete\"));\n        this.loadComplete.resolve();\n        this.#readyState = 1;\n        this.dispatchEvent(new Event(\"loadedmetadata\"));\n      } else if (data.eventName === \"seek\") {\n        this.#seeking = true;\n        this.dispatchEvent(new Event(\"seeking\"));\n      } else if (data.eventName === \"playing\") {\n        if (this.#seeking) {\n          this.#seeking = false;\n          this.dispatchEvent(new Event(\"seeked\"));\n        }\n        this.#readyState = 3;\n        this.dispatchEvent(new Event(\"playing\"));\n      } else {\n        this.dispatchEvent(new Event(data.eventName));\n      }\n    } else if (data.namespace === \"twitch-embed-player-proxy\" && data.eventName === \"UPDATE_STATE\") {\n      const oldDuration = this.#playerState.duration;\n      const oldCurrentTime = this.#playerState.currentTime;\n      const oldVolume = this.#playerState.volume;\n      const oldMuted = this.#playerState.muted;\n      const oldBuffered = (_b = (_a = this.#playerState.stats) == null ? void 0 : _a.videoStats) == null ? void 0 : _b.bufferSize;\n      this.#playerState = { ...this.#playerState, ...data.params };\n      if (oldDuration !== this.#playerState.duration) {\n        this.dispatchEvent(new Event(\"durationchange\"));\n      }\n      if (oldCurrentTime !== this.#playerState.currentTime) {\n        this.dispatchEvent(new Event(\"timeupdate\"));\n      }\n      if (oldVolume !== this.#playerState.volume || oldMuted !== this.#playerState.muted) {\n        this.dispatchEvent(new Event(\"volumechange\"));\n      }\n      if (oldBuffered !== ((_d = (_c = this.#playerState.stats) == null ? void 0 : _c.videoStats) == null ? void 0 : _d.bufferSize)) {\n        this.dispatchEvent(new Event(\"progress\"));\n      }\n    }\n  };\n  #sendCommand(command, params) {\n    if (!this.#iframe.contentWindow) return;\n    const message = {\n      eventName: command,\n      params,\n      namespace: \"twitch-embed-player-proxy\"\n    };\n    this.#iframe.contentWindow.postMessage(message, EMBED_BASE);\n  }\n  // This is a pattern to update property values that are set before\n  // the custom element is upgraded.\n  // https://web.dev/custom-elements-best-practices/#make-properties-lazy\n  #upgradeProperty(prop) {\n    if (Object.prototype.hasOwnProperty.call(this, prop)) {\n      const value = this[prop];\n      delete this[prop];\n      this[prop] = value;\n    }\n  }\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  let obj = {};\n  for (let attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    const value = attrs[key];\n    if (value === \"\") html += ` ${escapeHtml(key)}`;\n    else html += ` ${escapeHtml(key)}=\"${escapeHtml(`${value}`)}\"`;\n  }\n  return html;\n}\nfunction escapeHtml(str) {\n  return str.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&apos;\").replace(/`/g, \"&#x60;\");\n}\nfunction serialize(props) {\n  return String(new URLSearchParams(filterParams(props)));\n}\nfunction filterParams(props) {\n  let p = {};\n  for (let key in props) {\n    let val = props[key];\n    if (val === true || val === \"\") p[key] = true;\n    else if (val === false) p[key] = false;\n    else if (val != null) p[key] = val;\n  }\n  return p;\n}\nclass PublicPromise extends Promise {\n  constructor(executor = () => {\n  }) {\n    let res, rej;\n    super((resolve, reject) => {\n      executor(resolve, reject);\n      res = resolve;\n      rej = reject;\n    });\n    this.resolve = res;\n    this.reject = rej;\n  }\n}\nfunction createTimeRanges(start, end) {\n  if (Array.isArray(start)) {\n    return createTimeRangesObj(start);\n  } else if (start == null || end == null || start === 0 && end === 0) {\n    return createTimeRangesObj([[0, 0]]);\n  }\n  return createTimeRangesObj([[start, end]]);\n}\nfunction createTimeRangesObj(ranges) {\n  Object.defineProperties(ranges, {\n    start: {\n      value: (i) => ranges[i][0]\n    },\n    end: {\n      value: (i) => ranges[i][1]\n    }\n  });\n  return ranges;\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"twitch-video\")) {\n  globalThis.customElements.define(\"twitch-video\", TwitchVideoElement);\n}\nvar twitch_video_element_default = TwitchVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/twitch-video-element/dist/twitch-video-element.js\n"));

/***/ })

}]);