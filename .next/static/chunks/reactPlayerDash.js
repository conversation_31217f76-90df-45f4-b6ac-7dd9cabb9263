"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerDash"],{

/***/ "(app-pages-browser)/./node_modules/custom-media-element/dist/custom-media-element.js":
/*!************************************************************************!*\
  !*** ./node_modules/custom-media-element/dist/custom-media-element.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Attributes: () => (/* binding */ Attributes),\n/* harmony export */   CustomAudioElement: () => (/* binding */ CustomAudioElement),\n/* harmony export */   CustomMediaMixin: () => (/* binding */ CustomMediaMixin),\n/* harmony export */   CustomVideoElement: () => (/* binding */ CustomVideoElement),\n/* harmony export */   Events: () => (/* binding */ Events)\n/* harmony export */ });\nconst Events = [\n  \"abort\",\n  \"canplay\",\n  \"canplaythrough\",\n  \"durationchange\",\n  \"emptied\",\n  \"encrypted\",\n  \"ended\",\n  \"error\",\n  \"loadeddata\",\n  \"loadedmetadata\",\n  \"loadstart\",\n  \"pause\",\n  \"play\",\n  \"playing\",\n  \"progress\",\n  \"ratechange\",\n  \"seeked\",\n  \"seeking\",\n  \"stalled\",\n  \"suspend\",\n  \"timeupdate\",\n  \"volumechange\",\n  \"waiting\",\n  \"waitingforkey\",\n  \"resize\",\n  \"enterpictureinpicture\",\n  \"leavepictureinpicture\",\n  \"webkitbeginfullscreen\",\n  \"webkitendfullscreen\",\n  \"webkitpresentationmodechanged\"\n];\nconst Attributes = [\n  \"autopictureinpicture\",\n  \"disablepictureinpicture\",\n  \"disableremoteplayback\",\n  \"autoplay\",\n  \"controls\",\n  \"controlslist\",\n  \"crossorigin\",\n  \"loop\",\n  \"muted\",\n  \"playsinline\",\n  \"poster\",\n  \"preload\",\n  \"src\"\n];\nfunction getAudioTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-flex;\n        line-height: 0;\n        flex-direction: column;\n        justify-content: end;\n      }\n\n      audio {\n        width: 100%;\n      }\n    </style>\n    <slot name=\"media\">\n      <audio${serializeAttributes(attrs)}></audio>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction getVideoTemplateHTML(attrs) {\n  return (\n    /*html*/\n    `\n    <style>\n      :host {\n        display: inline-block;\n        line-height: 0;\n      }\n\n      video {\n        max-width: 100%;\n        max-height: 100%;\n        min-width: 100%;\n        min-height: 100%;\n        object-fit: var(--media-object-fit, contain);\n        object-position: var(--media-object-position, 50% 50%);\n      }\n\n      video::-webkit-media-text-track-container {\n        transform: var(--media-webkit-text-track-transform);\n        transition: var(--media-webkit-text-track-transition);\n      }\n    </style>\n    <slot name=\"media\">\n      <video${serializeAttributes(attrs)}></video>\n    </slot>\n    <slot></slot>\n  `\n  );\n}\nfunction CustomMediaMixin(superclass, { tag, is }) {\n  const nativeElTest = globalThis.document?.createElement?.(tag, { is });\n  const nativeElProps = nativeElTest ? getNativeElProps(nativeElTest) : [];\n  return class CustomMedia extends superclass {\n    static getTemplateHTML = tag.endsWith(\"audio\") ? getAudioTemplateHTML : getVideoTemplateHTML;\n    static shadowRootOptions = { mode: \"open\" };\n    static Events = Events;\n    static #isDefined = false;\n    static get observedAttributes() {\n      CustomMedia.#define();\n      const natAttrs = nativeElTest?.constructor?.observedAttributes ?? [];\n      return [\n        ...natAttrs,\n        ...Attributes\n      ];\n    }\n    static #define() {\n      if (this.#isDefined) return;\n      this.#isDefined = true;\n      const propsToAttrs = new Set(this.observedAttributes);\n      propsToAttrs.delete(\"muted\");\n      for (const prop of nativeElProps) {\n        if (prop in this.prototype) continue;\n        if (typeof nativeElTest[prop] === \"function\") {\n          this.prototype[prop] = function(...args) {\n            this.#init();\n            const fn = () => {\n              if (this.call) return this.call(prop, ...args);\n              const nativeFn = this.nativeEl?.[prop];\n              return nativeFn?.apply(this.nativeEl, args);\n            };\n            return fn();\n          };\n        } else {\n          const config = {\n            get() {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                const val = this.getAttribute(attr);\n                return val === null ? false : val === \"\" ? true : val;\n              }\n              return this.get?.(prop) ?? this.nativeEl?.[prop];\n            }\n          };\n          if (prop !== prop.toUpperCase()) {\n            config.set = function(val) {\n              this.#init();\n              const attr = prop.toLowerCase();\n              if (propsToAttrs.has(attr)) {\n                if (val === true || val === false || val == null) {\n                  this.toggleAttribute(attr, Boolean(val));\n                } else {\n                  this.setAttribute(attr, val);\n                }\n                return;\n              }\n              if (this.set) {\n                this.set(prop, val);\n                return;\n              }\n              if (this.nativeEl) {\n                this.nativeEl[prop] = val;\n              }\n            };\n          }\n          Object.defineProperty(this.prototype, prop, config);\n        }\n      }\n    }\n    // Private fields\n    #isInit = false;\n    #nativeEl = null;\n    #childMap = /* @__PURE__ */ new Map();\n    #childObserver;\n    get;\n    set;\n    call;\n    // If the custom element is defined before the custom element's HTML is parsed\n    // no attributes will be available in the constructor (construction process).\n    // Wait until initializing in the attributeChangedCallback or\n    // connectedCallback or accessing any properties.\n    get nativeEl() {\n      this.#init();\n      return this.#nativeEl ?? this.querySelector(\":scope > [slot=media]\") ?? this.querySelector(tag) ?? this.shadowRoot?.querySelector(tag) ?? null;\n    }\n    set nativeEl(val) {\n      this.#nativeEl = val;\n    }\n    get defaultMuted() {\n      return this.hasAttribute(\"muted\");\n    }\n    set defaultMuted(val) {\n      this.toggleAttribute(\"muted\", val);\n    }\n    get src() {\n      return this.getAttribute(\"src\");\n    }\n    set src(val) {\n      this.setAttribute(\"src\", `${val}`);\n    }\n    get preload() {\n      return this.getAttribute(\"preload\") ?? this.nativeEl?.preload;\n    }\n    set preload(val) {\n      this.setAttribute(\"preload\", `${val}`);\n    }\n    #init() {\n      if (this.#isInit) return;\n      this.#isInit = true;\n      this.init();\n    }\n    init() {\n      if (!this.shadowRoot) {\n        this.attachShadow({ mode: \"open\" });\n        const attrs = namedNodeMapToObject(this.attributes);\n        if (is) attrs.is = is;\n        if (tag) attrs.part = tag;\n        this.shadowRoot.innerHTML = this.constructor.getTemplateHTML(attrs);\n      }\n      this.nativeEl.muted = this.hasAttribute(\"muted\");\n      for (const prop of nativeElProps) {\n        this.#upgradeProperty(prop);\n      }\n      this.#childObserver = new MutationObserver(this.#syncMediaChildAttribute.bind(this));\n      this.shadowRoot.addEventListener(\"slotchange\", () => this.#syncMediaChildren());\n      this.#syncMediaChildren();\n      for (const type of this.constructor.Events) {\n        this.shadowRoot.addEventListener(type, this, true);\n      }\n    }\n    handleEvent(event) {\n      if (event.target === this.nativeEl) {\n        this.dispatchEvent(new CustomEvent(event.type, { detail: event.detail }));\n      }\n    }\n    #syncMediaChildren() {\n      const removeNativeChildren = new Map(this.#childMap);\n      const defaultSlot = this.shadowRoot?.querySelector(\"slot:not([name])\");\n      const mediaChildren = defaultSlot?.assignedElements({ flatten: true }).filter((el) => [\"track\", \"source\"].includes(el.localName));\n      mediaChildren.forEach((el) => {\n        removeNativeChildren.delete(el);\n        let clone = this.#childMap.get(el);\n        if (!clone) {\n          clone = el.cloneNode();\n          this.#childMap.set(el, clone);\n          this.#childObserver?.observe(el, { attributes: true });\n        }\n        this.nativeEl?.append(clone);\n        this.#enableDefaultTrack(clone);\n      });\n      removeNativeChildren.forEach((clone, el) => {\n        clone.remove();\n        this.#childMap.delete(el);\n      });\n    }\n    #syncMediaChildAttribute(mutations) {\n      for (const mutation of mutations) {\n        if (mutation.type === \"attributes\") {\n          const { target, attributeName } = mutation;\n          const clone = this.#childMap.get(target);\n          if (clone && attributeName) {\n            clone.setAttribute(attributeName, target.getAttribute(attributeName) ?? \"\");\n            this.#enableDefaultTrack(clone);\n          }\n        }\n      }\n    }\n    #enableDefaultTrack(trackEl) {\n      if (trackEl && trackEl.localName === \"track\" && trackEl.default && (trackEl.kind === \"chapters\" || trackEl.kind === \"metadata\") && trackEl.track.mode === \"disabled\") {\n        trackEl.track.mode = \"hidden\";\n      }\n    }\n    #upgradeProperty(prop) {\n      if (Object.prototype.hasOwnProperty.call(this, prop)) {\n        const value = this[prop];\n        delete this[prop];\n        this[prop] = value;\n      }\n    }\n    attributeChangedCallback(attrName, oldValue, newValue) {\n      this.#init();\n      this.#forwardAttribute(attrName, oldValue, newValue);\n    }\n    #forwardAttribute(attrName, _oldValue, newValue) {\n      if ([\"id\", \"class\"].includes(attrName)) return;\n      if (!CustomMedia.observedAttributes.includes(attrName) && this.constructor.observedAttributes.includes(attrName)) {\n        return;\n      }\n      if (newValue === null) {\n        this.nativeEl?.removeAttribute(attrName);\n      } else if (this.nativeEl?.getAttribute(attrName) !== newValue) {\n        this.nativeEl?.setAttribute(attrName, newValue);\n      }\n    }\n    connectedCallback() {\n      this.#init();\n    }\n  };\n}\nfunction getNativeElProps(nativeElTest) {\n  const nativeElProps = [];\n  for (let proto = Object.getPrototypeOf(nativeElTest); proto && proto !== HTMLElement.prototype; proto = Object.getPrototypeOf(proto)) {\n    const props = Object.getOwnPropertyNames(proto);\n    nativeElProps.push(...props);\n  }\n  return nativeElProps;\n}\nfunction serializeAttributes(attrs) {\n  let html = \"\";\n  for (const key in attrs) {\n    if (!Attributes.includes(key)) continue;\n    const value = attrs[key];\n    if (value === \"\") html += ` ${key}`;\n    else html += ` ${key}=\"${value}\"`;\n  }\n  return html;\n}\nfunction namedNodeMapToObject(namedNodeMap) {\n  const obj = {};\n  for (const attr of namedNodeMap) {\n    obj[attr.name] = attr.value;\n  }\n  return obj;\n}\nconst CustomVideoElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"video\"\n});\nconst CustomAudioElement = CustomMediaMixin(globalThis.HTMLElement ?? class {\n}, {\n  tag: \"audio\"\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/custom-media-element/dist/custom-media-element.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dash-video-element/dist/dash-video-element.js":
/*!********************************************************************!*\
  !*** ./node_modules/dash-video-element/dist/dash-video-element.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ dash_video_element_default)\n/* harmony export */ });\n/* harmony import */ var custom_media_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! custom-media-element */ \"(app-pages-browser)/./node_modules/custom-media-element/dist/custom-media-element.js\");\n\nclass DashVideoElement extends custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement {\n  static shadowRootOptions = { ...custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement.shadowRootOptions };\n  static getTemplateHTML = (attrs) => {\n    const { src, ...rest } = attrs;\n    return custom_media_element__WEBPACK_IMPORTED_MODULE_0__.CustomVideoElement.getTemplateHTML(rest);\n  };\n  #apiInit;\n  attributeChangedCallback(attrName, oldValue, newValue) {\n    if (attrName !== \"src\") {\n      super.attributeChangedCallback(attrName, oldValue, newValue);\n    }\n    if (attrName === \"src\" && oldValue != newValue) {\n      this.load();\n    }\n  }\n  async load() {\n    if (!this.#apiInit) {\n      this.#apiInit = true;\n      const Dash = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_dashjs_dist_modern_esm_dash_all_min_js\").then(__webpack_require__.bind(__webpack_require__, /*! dashjs */ \"(app-pages-browser)/./node_modules/dashjs/dist/modern/esm/dash.all.min.js\"));\n      this.api = Dash.MediaPlayer().create();\n      this.api.initialize(this.nativeEl, this.src, this.autoplay);\n    } else {\n      this.api.attachSource(this.src);\n    }\n  }\n}\nif (globalThis.customElements && !globalThis.customElements.get(\"dash-video\")) {\n  globalThis.customElements.define(\"dash-video\", DashVideoElement);\n}\nvar dash_video_element_default = DashVideoElement;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dash-video-element/dist/dash-video-element.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/dash-video-element/dist/react.js":
/*!*******************************************************!*\
  !*** ./node_modules/dash-video-element/dist/react.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ react_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _dash_video_element_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dash-video-element.js */ \"(app-pages-browser)/./node_modules/dash-video-element/dist/dash-video-element.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ // dist/react.ts\n\n\n// ../../node_modules/ce-la-react/dist/ce-la-react.js\nvar reservedReactProps = /* @__PURE__ */ new Set([\n    \"style\",\n    \"children\",\n    \"ref\",\n    \"key\",\n    \"suppressContentEditableWarning\",\n    \"suppressHydrationWarning\",\n    \"dangerouslySetInnerHTML\"\n]);\nvar reactPropToAttrNameMap = {\n    className: \"class\",\n    htmlFor: \"for\"\n};\nfunction defaultToAttributeName(propName) {\n    return propName.toLowerCase();\n}\nfunction defaultToAttributeValue(propValue) {\n    if (typeof propValue === \"boolean\") return propValue ? \"\" : void 0;\n    if (typeof propValue === \"function\") return void 0;\n    if (typeof propValue === \"object\" && propValue !== null) return void 0;\n    return propValue;\n}\nfunction createComponent(param) {\n    let { react: React2, tagName, elementClass, events, displayName, defaultProps, toAttributeName = defaultToAttributeName, toAttributeValue = defaultToAttributeValue } = param;\n    var _s = $RefreshSig$();\n    const IS_REACT_19_OR_NEWER = Number.parseInt(React2.version) >= 19;\n    const ReactComponent = React2.forwardRef(_s((props, ref)=>{\n        _s();\n        var _a, _b;\n        const elementRef = React2.useRef(null);\n        const prevElemPropsRef = React2.useRef(/* @__PURE__ */ new Map());\n        const eventProps = {};\n        const attrs = {};\n        const reactProps = {};\n        const elementProps = {};\n        for (const [k, v] of Object.entries(props)){\n            if (reservedReactProps.has(k)) {\n                reactProps[k] = v;\n                continue;\n            }\n            var _reactPropToAttrNameMap_k;\n            const attrName = toAttributeName((_reactPropToAttrNameMap_k = reactPropToAttrNameMap[k]) !== null && _reactPropToAttrNameMap_k !== void 0 ? _reactPropToAttrNameMap_k : k);\n            var _ref;\n            if (k in elementClass.prototype && !(k in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) && !((_b = elementClass.observedAttributes) == null ? void 0 : _b.some((attr)=>attr === attrName))) {\n                elementProps[k] = v;\n                continue;\n            }\n            if (k.startsWith(\"on\")) {\n                eventProps[k] = v;\n                continue;\n            }\n            const attrValue = toAttributeValue(v);\n            if (attrName && attrValue != null) {\n                attrs[attrName] = String(attrValue);\n                if (!IS_REACT_19_OR_NEWER) {\n                    reactProps[attrName] = attrValue;\n                }\n            }\n            if (attrName && IS_REACT_19_OR_NEWER) {\n                const attrValueFromDefault = defaultToAttributeValue(v);\n                if (attrValue !== attrValueFromDefault) {\n                    reactProps[attrName] = attrValue;\n                } else {\n                    reactProps[attrName] = v;\n                }\n            }\n        }\n        if (typeof window !== \"undefined\") {\n            for(const propName in eventProps){\n                const callback = eventProps[propName];\n                const useCapture = propName.endsWith(\"Capture\");\n                var _ref1;\n                const eventName = ((_ref1 = events == null ? void 0 : events[propName]) !== null && _ref1 !== void 0 ? _ref1 : propName.slice(2).toLowerCase()).slice(0, useCapture ? -7 : void 0);\n                React2.useLayoutEffect({\n                    \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                        const eventTarget = elementRef == null ? void 0 : elementRef.current;\n                        if (!eventTarget || typeof callback !== \"function\") return;\n                        eventTarget.addEventListener(eventName, callback, useCapture);\n                        return ({\n                            \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                                eventTarget.removeEventListener(eventName, callback, useCapture);\n                            }\n                        })[\"createComponent.ReactComponent.useLayoutEffect\"];\n                    }\n                }[\"createComponent.ReactComponent.useLayoutEffect\"], [\n                    elementRef == null ? void 0 : elementRef.current,\n                    callback\n                ]);\n            }\n            React2.useLayoutEffect({\n                \"createComponent.ReactComponent.useLayoutEffect\": ()=>{\n                    if (elementRef.current === null) return;\n                    const newElemProps = /* @__PURE__ */ new Map();\n                    for(const key in elementProps){\n                        setProperty(elementRef.current, key, elementProps[key]);\n                        prevElemPropsRef.current.delete(key);\n                        newElemProps.set(key, elementProps[key]);\n                    }\n                    for (const [key, _value] of prevElemPropsRef.current){\n                        setProperty(elementRef.current, key, void 0);\n                    }\n                    prevElemPropsRef.current = newElemProps;\n                }\n            }[\"createComponent.ReactComponent.useLayoutEffect\"]);\n        }\n        if (typeof window === \"undefined\" && (elementClass == null ? void 0 : elementClass.getTemplateHTML) && (elementClass == null ? void 0 : elementClass.shadowRootOptions)) {\n            const { mode, delegatesFocus } = elementClass.shadowRootOptions;\n            const templateShadowRoot = React2.createElement(\"template\", {\n                shadowrootmode: mode,\n                shadowrootdelegatesfocus: delegatesFocus,\n                dangerouslySetInnerHTML: {\n                    __html: elementClass.getTemplateHTML(attrs, props)\n                }\n            });\n            reactProps.children = [\n                templateShadowRoot,\n                reactProps.children\n            ];\n        }\n        return React2.createElement(tagName, {\n            ...defaultProps,\n            ...reactProps,\n            ref: React2.useCallback({\n                \"createComponent.ReactComponent.useCallback\": (node)=>{\n                    elementRef.current = node;\n                    if (typeof ref === \"function\") {\n                        ref(node);\n                    } else if (ref !== null) {\n                        ref.current = node;\n                    }\n                }\n            }[\"createComponent.ReactComponent.useCallback\"], [\n                ref\n            ])\n        });\n    }, \"9mplyF7vgg8XUtKXUe3PLSYpH8g=\"));\n    ReactComponent.displayName = displayName !== null && displayName !== void 0 ? displayName : elementClass.name;\n    return ReactComponent;\n}\nfunction setProperty(node, name, value) {\n    var _a;\n    node[name] = value;\n    var _ref;\n    if (value == null && name in ((_ref = (_a = globalThis.HTMLElement) == null ? void 0 : _a.prototype) !== null && _ref !== void 0 ? _ref : {})) {\n        node.removeAttribute(name);\n    }\n}\n// dist/react.ts\nvar react_default = createComponent({\n    react: react__WEBPACK_IMPORTED_MODULE_0__,\n    tagName: \"dash-video\",\n    elementClass: _dash_video_element_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    toAttributeName (propName) {\n        if (propName === \"muted\") return \"\";\n        if (propName === \"defaultMuted\") return \"muted\";\n        return defaultToAttributeName(propName);\n    }\n});\n /*! Bundled license information:\n\nce-la-react/dist/ce-la-react.js:\n  (**\n   * @license\n   * Copyright 2018 Google LLC\n   * SPDX-License-Identifier: BSD-3-Clause\n   *\n   * Modified version of `@lit/react` for vanilla custom elements with support for SSR.\n   *)\n*/ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/dash-video-element/dist/react.js\n"));

/***/ })

}]);