"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["reactPlayerPreview"],{

/***/ "(app-pages-browser)/./node_modules/react-player/dist/Preview.js":
/*!***************************************************!*\
  !*** ./node_modules/react-player/dist/Preview.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Preview_default)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nconst ICON_SIZE = \"64px\";\nconst cache = {};\nconst Preview = ({\n  src,\n  light,\n  oEmbedUrl,\n  onClickPreview,\n  playIcon,\n  previewTabIndex,\n  previewAriaLabel\n}) => {\n  const [image, setImage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!src || !light || !oEmbedUrl) return;\n    fetchImage({ src, light, oEmbedUrl });\n  }, [src, light, oEmbedUrl]);\n  const fetchImage = async ({\n    src: src2,\n    light: light2,\n    oEmbedUrl: oEmbedUrl2\n  }) => {\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(light2)) {\n      return;\n    }\n    if (typeof light2 === \"string\") {\n      setImage(light2);\n      return;\n    }\n    if (cache[src2]) {\n      setImage(cache[src2]);\n      return;\n    }\n    setImage(null);\n    const response = await fetch(oEmbedUrl2.replace(\"{url}\", src2));\n    const data = await response.json();\n    if (data.thumbnail_url) {\n      const fetchedImage = data.thumbnail_url.replace(\"height=100\", \"height=480\").replace(\"-d_295x166\", \"-d_640\");\n      setImage(fetchedImage);\n      cache[src2] = fetchedImage;\n    }\n  };\n  const handleKeyPress = (e) => {\n    if (e.key === \"Enter\" || e.key === \" \") {\n      onClickPreview == null ? void 0 : onClickPreview(e);\n    }\n  };\n  const handleClick = (e) => {\n    onClickPreview == null ? void 0 : onClickPreview(e);\n  };\n  const isElement = react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(light);\n  const flexCenter = {\n    display: \"flex\",\n    alignItems: \"center\",\n    justifyContent: \"center\"\n  };\n  const styles = {\n    preview: {\n      width: \"100%\",\n      height: \"100%\",\n      backgroundImage: image && !isElement ? `url(${image})` : void 0,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\",\n      cursor: \"pointer\",\n      ...flexCenter\n    },\n    shadow: {\n      background: \"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)\",\n      borderRadius: ICON_SIZE,\n      width: ICON_SIZE,\n      height: ICON_SIZE,\n      position: isElement ? \"absolute\" : void 0,\n      ...flexCenter\n    },\n    playIcon: {\n      borderStyle: \"solid\",\n      borderWidth: \"16px 0 16px 26px\",\n      borderColor: \"transparent transparent transparent white\",\n      marginLeft: \"7px\"\n    }\n  };\n  const defaultPlayIcon = /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.shadow, className: \"react-player__shadow\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { style: styles.playIcon, className: \"react-player__play-icon\" }));\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    \"div\",\n    {\n      style: styles.preview,\n      className: \"react-player__preview\",\n      tabIndex: previewTabIndex,\n      onClick: handleClick,\n      onKeyPress: handleKeyPress,\n      ...previewAriaLabel ? { \"aria-label\": previewAriaLabel } : {}\n    },\n    isElement ? light : null,\n    playIcon || defaultPlayIcon\n  );\n};\nvar Preview_default = Preview;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-player/dist/Preview.js\n"));

/***/ })

}]);