"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/use-message-handler.ts":
/*!******************************************!*\
  !*** ./src/hooks/use-message-handler.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMessageHandler: () => (/* binding */ useMessageHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMessageHandler = ()=>{\n    const [sessionStatus, setSessionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const [processingError, setProcessingError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [receivedActions, setReceivedActions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    // Handle incoming WebSocket messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[handleMessage]\": (message)=>{\n            console.log('Received WebSocket message:', message);\n            switch(message.type){\n                case 'session_ready':\n                    console.log('✅ Session is ready! Setting status to ready');\n                    setSessionStatus('ready');\n                    setProcessingError(null);\n                    break;\n                case 'processing_error':\n                    console.error('Processing error received:', message);\n                    console.error('Full error details:', JSON.stringify(message, null, 2));\n                    const error = {\n                        error_code: message.error_code || 'UNKNOWN_ERROR',\n                        message: message.message || 'An unknown error occurred'\n                    };\n                    setProcessingError(error);\n                    setSessionStatus('error');\n                    break;\n                case 'ai_action':\n                    console.log('Received AI action message:', message);\n                    if (message.action && isValidSingleAction(message.action)) {\n                        const actionData = message.action;\n                        const action = {\n                            action_type: actionData.action_type,\n                            id: actionData.id,\n                            trigger_time: actionData.trigger_timestamp || actionData.trigger_time,\n                            comment: actionData.comment,\n                            text: actionData.text,\n                            audio: actionData.audio,\n                            target_time: actionData.target_time\n                        };\n                        setReceivedActions({\n                            \"useMessageHandler.useCallback[handleMessage]\": (prev)=>{\n                                // Insert action in the correct position based on trigger_time\n                                const newActions = [\n                                    ...prev,\n                                    action\n                                ];\n                                return newActions.sort({\n                                    \"useMessageHandler.useCallback[handleMessage]\": (a, b)=>a.trigger_time - b.trigger_time\n                                }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                            }\n                        }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                    } else {\n                        console.warn('Received invalid ai_action message:', message);\n                    }\n                    break;\n                default:\n                    console.log('Unhandled message type:', message.type);\n                    break;\n            }\n        }\n    }[\"useMessageHandler.useCallback[handleMessage]\"], []);\n    // Clear current error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[clearError]\": ()=>{\n            setProcessingError(null);\n            if (sessionStatus === 'error') {\n                setSessionStatus('idle');\n            }\n        }\n    }[\"useMessageHandler.useCallback[clearError]\"], [\n        sessionStatus\n    ]);\n    // Reset all handler state\n    const resetHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[resetHandler]\": ()=>{\n            setSessionStatus('idle');\n            setProcessingError(null);\n            setReceivedActions([]);\n        }\n    }[\"useMessageHandler.useCallback[resetHandler]\"], []);\n    const isSessionReady = sessionStatus === 'ready';\n    // Debug: Log session status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessageHandler.useEffect\": ()=>{\n            console.log('📊 Message handler session status changed:', sessionStatus, 'isReady:', isSessionReady);\n        }\n    }[\"useMessageHandler.useEffect\"], [\n        sessionStatus,\n        isSessionReady\n    ]);\n    return {\n        sessionStatus,\n        processingError,\n        receivedActions,\n        isSessionReady,\n        handleMessage,\n        clearError,\n        resetHandler\n    };\n};\n// Helper function to validate single action data (for ai_action type)\nfunction isValidSingleAction(actionData) {\n    return actionData && typeof actionData.action_type === 'string' && [\n        'PAUSE',\n        'RESUME',\n        'SPEAK',\n        'SEEK',\n        'END'\n    ].includes(actionData.action_type) && typeof actionData.id === 'string' && (typeof actionData.trigger_time === 'number' || typeof actionData.trigger_timestamp === 'number') && typeof actionData.comment === 'string';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-message-handler.ts\n"));

/***/ })

});