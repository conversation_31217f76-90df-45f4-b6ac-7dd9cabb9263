"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/use-message-handler.ts":
/*!******************************************!*\
  !*** ./src/hooks/use-message-handler.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMessageHandler: () => (/* binding */ useMessageHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMessageHandler = ()=>{\n    const [sessionStatus, setSessionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const [processingError, setProcessingError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [receivedActions, setReceivedActions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    // Handle incoming WebSocket messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[handleMessage]\": (message)=>{\n            console.log('Received WebSocket message:', message);\n            switch(message.type){\n                case 'session_ready':\n                    console.log('✅ Session is ready! Setting status to ready');\n                    setSessionStatus('ready');\n                    setProcessingError(null);\n                    break;\n                case 'processing_error':\n                    console.error('Processing error received:', message);\n                    console.error('Full error details:', JSON.stringify(message, null, 2));\n                    const error = {\n                        error_code: message.error_code || 'UNKNOWN_ERROR',\n                        message: message.message || 'An unknown error occurred'\n                    };\n                    setProcessingError(error);\n                    setSessionStatus('error');\n                    break;\n                case 'ai_action':\n                    console.log('Received AI action message:', message);\n                    if (message.action && isValidSingleAction(message.action)) {\n                        const actionData = message.action;\n                        const action = {\n                            action_type: actionData.action_type,\n                            id: actionData.id,\n                            trigger_timestamp: actionData.trigger_timestamp,\n                            comment: actionData.comment,\n                            // SPEAK fields\n                            text: actionData.text,\n                            audio: actionData.audio,\n                            pause_video: actionData.pause_video,\n                            // PAUSE fields\n                            duration_seconds: actionData.duration_seconds,\n                            // SEEK fields\n                            target_timestamp: actionData.target_timestamp,\n                            post_seek_behavior: actionData.post_seek_behavior,\n                            // REPLAY_SEGMENT fields\n                            start_timestamp: actionData.start_timestamp,\n                            end_timestamp: actionData.end_timestamp,\n                            post_replay_behavior: actionData.post_replay_behavior\n                        };\n                        setReceivedActions({\n                            \"useMessageHandler.useCallback[handleMessage]\": (prev)=>{\n                                // Insert action in the correct position based on trigger_time\n                                const newActions = [\n                                    ...prev,\n                                    action\n                                ];\n                                return newActions.sort({\n                                    \"useMessageHandler.useCallback[handleMessage]\": (a, b)=>a.trigger_time - b.trigger_time\n                                }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                            }\n                        }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                    } else {\n                        console.warn('Received invalid ai_action message:', message);\n                    }\n                    break;\n                default:\n                    console.log('Unhandled message type:', message.type);\n                    break;\n            }\n        }\n    }[\"useMessageHandler.useCallback[handleMessage]\"], []);\n    // Clear current error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[clearError]\": ()=>{\n            setProcessingError(null);\n            if (sessionStatus === 'error') {\n                setSessionStatus('idle');\n            }\n        }\n    }[\"useMessageHandler.useCallback[clearError]\"], [\n        sessionStatus\n    ]);\n    // Reset all handler state\n    const resetHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[resetHandler]\": ()=>{\n            setSessionStatus('idle');\n            setProcessingError(null);\n            setReceivedActions([]);\n        }\n    }[\"useMessageHandler.useCallback[resetHandler]\"], []);\n    const isSessionReady = sessionStatus === 'ready';\n    // Debug: Log session status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessageHandler.useEffect\": ()=>{\n            console.log('📊 Message handler session status changed:', sessionStatus, 'isReady:', isSessionReady);\n        }\n    }[\"useMessageHandler.useEffect\"], [\n        sessionStatus,\n        isSessionReady\n    ]);\n    return {\n        sessionStatus,\n        processingError,\n        receivedActions,\n        isSessionReady,\n        handleMessage,\n        clearError,\n        resetHandler\n    };\n};\n// Helper function to validate single action data (for ai_action type)\nfunction isValidSingleAction(actionData) {\n    return actionData && typeof actionData.action_type === 'string' && [\n        'PAUSE',\n        'RESUME',\n        'SPEAK',\n        'SEEK',\n        'END'\n    ].includes(actionData.action_type) && typeof actionData.id === 'string' && (typeof actionData.trigger_time === 'number' || typeof actionData.trigger_timestamp === 'number') && typeof actionData.comment === 'string';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-message-handler.ts\n"));

/***/ })

});