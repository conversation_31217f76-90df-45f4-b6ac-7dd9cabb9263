"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/hooks/use-message-handler.ts":
/*!******************************************!*\
  !*** ./src/hooks/use-message-handler.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMessageHandler: () => (/* binding */ useMessageHandler)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMessageHandler = ()=>{\n    const [sessionStatus, setSessionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('idle');\n    const [processingError, setProcessingError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [receivedActions, setReceivedActions] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    // Handle incoming WebSocket messages\n    const handleMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[handleMessage]\": (message)=>{\n            console.log('Received WebSocket message:', message);\n            switch(message.type){\n                case 'session_ready':\n                    console.log('✅ Session is ready! Setting status to ready');\n                    setSessionStatus('ready');\n                    setProcessingError(null);\n                    break;\n                case 'processing_error':\n                    console.error('Processing error received:', message);\n                    console.error('Full error details:', JSON.stringify(message, null, 2));\n                    const error = {\n                        error_code: message.error_code || 'UNKNOWN_ERROR',\n                        message: message.message || 'An unknown error occurred'\n                    };\n                    setProcessingError(error);\n                    setSessionStatus('error');\n                    break;\n                case 'ai_action':\n                    console.log('Received AI action message:', message);\n                    if (message.action && isValidSingleAction(message.action)) {\n                        const actionData = message.action;\n                        const action = {\n                            action_type: actionData.action_type,\n                            id: actionData.id,\n                            trigger_time: actionData.trigger_timestamp || actionData.trigger_time,\n                            comment: actionData.comment,\n                            text: actionData.text,\n                            audio: actionData.audio,\n                            target_time: actionData.target_time\n                        };\n                        setReceivedActions({\n                            \"useMessageHandler.useCallback[handleMessage]\": (prev)=>{\n                                // Insert action in the correct position based on trigger_time\n                                const newActions = [\n                                    ...prev,\n                                    action\n                                ];\n                                return newActions.sort({\n                                    \"useMessageHandler.useCallback[handleMessage]\": (a, b)=>a.trigger_time - b.trigger_time\n                                }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                            }\n                        }[\"useMessageHandler.useCallback[handleMessage]\"]);\n                    } else {\n                        console.warn('Received invalid ai_action message:', message);\n                    }\n                    break;\n                default:\n                    console.log('Unhandled message type:', message.type);\n                    break;\n            }\n        }\n    }[\"useMessageHandler.useCallback[handleMessage]\"], []);\n    // Clear current error\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[clearError]\": ()=>{\n            setProcessingError(null);\n            if (sessionStatus === 'error') {\n                setSessionStatus('idle');\n            }\n        }\n    }[\"useMessageHandler.useCallback[clearError]\"], [\n        sessionStatus\n    ]);\n    // Reset all handler state\n    const resetHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageHandler.useCallback[resetHandler]\": ()=>{\n            setSessionStatus('idle');\n            setProcessingError(null);\n            setReceivedActions([]);\n        }\n    }[\"useMessageHandler.useCallback[resetHandler]\"], []);\n    const isSessionReady = sessionStatus === 'ready';\n    // Debug: Log session status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessageHandler.useEffect\": ()=>{\n            console.log('📊 Message handler session status changed:', sessionStatus, 'isReady:', isSessionReady);\n        }\n    }[\"useMessageHandler.useEffect\"], [\n        sessionStatus,\n        isSessionReady\n    ]);\n    return {\n        sessionStatus,\n        processingError,\n        receivedActions,\n        isSessionReady,\n        handleMessage,\n        clearError,\n        resetHandler\n    };\n};\n// Helper function to validate single action data (for ai_action type)\nfunction isValidSingleAction(actionData) {\n    return actionData && typeof actionData.action_type === 'string' && [\n        'PAUSE',\n        'RESUME',\n        'SPEAK',\n        'SEEK',\n        'END'\n    ].includes(actionData.action_type) && typeof actionData.id === 'string' && (typeof actionData.trigger_time === 'number' || typeof actionData.trigger_timestamp === 'number') && typeof actionData.comment === 'string';\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-message-handler.ts\n"));

/***/ })

});